package service

import (
	"context"
	"database/sql"
	"errors"
	"fmt"

	"github.com/gogf/gf/v2/container/garray"
	"github.com/gogf/gf/v2/container/gmap"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/text/gstr"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/imhuso/lookah-erp/admin/modules/pms/model"
	"github.com/imhuso/lookah-erp/admin/yc"
)

// 0:草稿 1:待处理 2:已完成
const (
	DeliveryNoteStatusDraft DeliveryNoteStatus = iota
	DeliveryNoteStatusPending
	DeliveryNoteStatusCompleted
)

type DeliveryNoteStatus int

type PmsDeliveryNoteService struct {
	*yc.Service
}

// 同步送货单数据
func syncDeliveryNoteData(ctx context.Context, id int64) (err error) {
	// 查询送货单信息
	deliveryNote := &model.PmsDeliveryNote{}
	err = yc.DBM(model.NewDeliveryNote()).Ctx(ctx).Where("id", id).Scan(deliveryNote)
	inboundType := deliveryNote.InboundType // 1: 送货单入库 9：补退货入库
	if err != nil {
		return gerror.New("查询送货单信息失败: " + err.Error())
	}

	// 查询送货单产品信息
	deliveryNoteProduct := make([]*model.PmsDeliveryNoteProduct, 0)
	err = yc.DBM(model.NewDeliveryNoteProduct()).Ctx(ctx).Where("inbound_id", id).Scan(&deliveryNoteProduct)
	if len(deliveryNoteProduct) == 0 {
		return gerror.New("送货单没有产品信息")
	}
	type materialInboundProductType struct {
		InboundOutboundKey int     `json:"inbound_outbound_key"`
		ContractId         int64   `json:"contractId"`
		MaterialId         int64   `json:"materialId"`
		Quantity           float64 `json:"quantity"`
		WarehouseId        int64   `json:"warehouseId"`
		Address            string  `json:"address"`
	}
	// 获取产品
	materialInboundProductsInput := make([]materialInboundProductType, 0)
	products := make([]*model.PmsMaterialInboundProduct, 0)
	err = gconv.Scan(deliveryNoteProduct, &materialInboundProductsInput)
	if err != nil {
		return
	}
	contractIds := garray.New()
	// 遍历产品
	for _, v := range materialInboundProductsInput {
		if inboundType == 1 {
			if v.ContractId == 0 {
				return gerror.New("选择的物料有误")
			}
			// 如果合同ID已存在，则报错
			if contractIds.Contains(v.ContractId) {
				return gerror.New("选择的物料有重复的项，请检查")
			}
			contractIds.Append(v.ContractId)
		}
		if v.Quantity <= 0 {
			return gerror.New("送货单物料数量必须大于0")
		}
		products = append(products, &model.PmsMaterialInboundProduct{
			MaterialId:         v.MaterialId,
			ContractId:         v.ContractId,
			Quantity:           v.Quantity,
			WarehouseId:        v.WarehouseId,
			Address:            v.Address,
			InboundOutboundKey: v.InboundOutboundKey,
		})
	}
	orderId := deliveryNote.OrderId
	err = NewPmsMaterialInboundService().checkInboundPurchaseOrder(ctx, orderId, inboundType, products)
	if err != nil {
		return err
	}

	// 开启事务
	err = yc.DB(NewPmsDeliveryNoteService().Model.GroupName()).Ctx(ctx).Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 创建入库信息
		inboundId, err := NewPmsMaterialInboundService().CreateInbound(ctx, &model.PmsMaterialInbound{
			ID:              0,
			Type:            inboundType,
			OrderId:         orderId,
			Status:          int(MaterialInboundStatusDraft),
			Remark:          deliveryNote.Remark,
			WorkOrderId:     0,
			InboundTime:     gtime.Now(),
			CompleteTime:    gtime.Now(),
			Products:        products,
			Voucher:         deliveryNote.Voucher,
			DeliveryNoteId:  deliveryNote.ID,
			InternalOrderNo: deliveryNote.InternalOrderNo,
		}, false)

		if err != nil {
			return err
		}
		// 更新入库信息
		// 获取入库单信息
		inbound := &model.PmsMaterialInbound{}
		err = yc.DBM(model.NewPmsMaterialInbound()).Ctx(ctx).Where("id", inboundId).
			With(&model.PmsMaterialInboundProduct{}).
			Scan(&inbound)
		if err != nil {
			return err
		}
		if inbound == nil {
			return gerror.New("入库单不存在")
		}
		if inbound.Status != int(MaterialInboundStatusDraft) {
			return gerror.New("只有草稿状态的入库单才能提交审批")
		}
		// 触发采购订单的审核流程
		_, err2 := NewPmsMaterialInboundService().AddAudit(ctx, inboundId)
		if err2 != nil {
			return err2
		}
		auditLog := &model.PmsPurchaseOrderAuditLog{
			PurchaseId:   inboundId,
			Source:       2,
			OperatorId:   yc.GetAdmin(ctx).UserId,
			OperatorName: yc.GetAdmin(ctx).UserName,
			Type:         0,
			OperatorNote: "提交物料入库单审批",
		}
		_, err = NewPmsMaterialInboundService().AuditPassV2(ctx, inbound)
		if err != nil {
			return err
		}
		// result 转map
		auditLog.OperatorNote = "提交入库单审核, 并自动跳过审核"

		// 增加审核记录
		NewPmsPurchaseOrderAuditLogService().AddLog(ctx, auditLog)
		return nil

	})

	if err != nil {
		return
	}
	return
}

// Complete 完成送货单
func (s *PmsDeliveryNoteService) Complete(ctx context.Context, id int64) (data interface{}, err error) {
	if id <= 0 {
		return nil, gerror.New("ID不能为空")
	}

	count, err := yc.DBM(s.Model).Ctx(ctx).
		Where("id", id).
		Where("status", DeliveryNoteStatusPending).
		Count()
	if err != nil {
		return nil, err
	}
	if count <= 0 {
		return nil, gerror.New("没有找到待确认的送货单")
	}

	// 开启事务
	err = yc.DB(s.Model.GroupName()).Ctx(ctx).Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 更新送货单状态为已完成
		affected, err := yc.DBM(s.Model).Ctx(ctx).Where("id", id).Data(
			g.Map{"status": DeliveryNoteStatusCompleted, "complete_time": gtime.Now()}).UpdateAndGetAffected()
		if err != nil {
			return err
		}
		if affected == 0 {
			return gerror.New("更新送货单状态失败")
		}

		err = syncDeliveryNoteData(ctx, id)
		if err != nil {
			return gerror.New("同步送货单数据失败: " + err.Error())
		}

		return nil
	})

	return id, err
}

// GetDeliveryInfo 获取送货单信息
func (s *PmsDeliveryNoteService) GetDeliveryInfo(ctx context.Context, id int64) (data interface{}, err error) {
	if id <= 0 {
		return nil, err
	}
	// 获取送货单信息
	deliveryNote := &model.PmsDeliveryNoteOutput{}
	err = yc.DBM(s.Model).Ctx(ctx).
		LeftJoin("pms_supplier", "pms_delivery_note.supplier_id=pms_supplier.id").
		LeftJoin("pms_purchase_order", "pms_delivery_note.order_id=pms_purchase_order.id").
		Fields("pms_delivery_note.*,pms_supplier.name as supplier_name,pms_purchase_order.order_no").
		Where("id", id).Scan(deliveryNote)
	if err != nil {
		return nil, err
	}

	err = yc.DBM(model.NewDeliveryNoteProduct()).Ctx(ctx).WithAll().
		Where("inbound_id", deliveryNote.ID).Scan(&deliveryNote.Products)
	if err != nil {
		return nil, err
	}

	// 如果是补退货入库，需要额外获取出库单的产品信息
	if deliveryNote.InboundType == 9 && deliveryNote.OrderId > 0 {
		// 获取出库单的产品信息，包含退货数量和已补货数量
		outboundProducts := make([]*model.MaterialOutboundProductOutput, 0)
		err = yc.DBM(model.NewPmsMaterialOutboundProduct()).Ctx(ctx).WithAll().
			Where("outbound_id", deliveryNote.OrderId).Scan(&outboundProducts)
		if err != nil {
			return nil, err
		}

		// 创建出库产品的映射，方便查找
		outboundProductMap := make(map[int64]*model.MaterialOutboundProductOutput)
		for _, outboundProduct := range outboundProducts {
			outboundProductMap[outboundProduct.MaterialId] = outboundProduct
		}

		// 为送货单产品填充退货数量和已补货数量
		for i, product := range deliveryNote.Products {
			if outboundProduct, exists := outboundProductMap[product.MaterialId]; exists {
				// 设置退货数量和已补货数量到物料输出结构中
				if product.PmsMaterialOutput != nil {
					product.PmsMaterialOutput.ExpectedInbound = outboundProduct.Quantity  // 退货数量
					product.PmsMaterialOutput.ReceivedQty = outboundProduct.RestockingQty // 已补货数量
				}
				deliveryNote.Products[i] = product
			}
		}
	}

	return deliveryNote, err
}

// Revoke 撤销送货单
func (s *PmsDeliveryNoteService) Revoke(ctx context.Context, deliveryNote *model.PmsDeliveryNote) (data interface{}, err error) {
	if deliveryNote.ID <= 0 {
		return nil, err
	}
	if deliveryNote.Status <= int(DeliveryNoteStatusDraft) {
		return nil, errors.New("送货单已是草稿状态，无需撤销")
	}

	m := yc.DBM(s.Model).Ctx(ctx).Where("id", deliveryNote.ID)

	// 如果是完成状态撤销,完成事件字段置零
	if deliveryNote.Status == int(DeliveryNoteStatusCompleted) {
		m = m.Data(g.Map{"status": DeliveryNoteStatusPending, "complete_time": nil})
	}

	// 如果是已提交状态撤销,入库时间字段置零
	if deliveryNote.Status == int(DeliveryNoteStatusPending) {
		m = m.Data(g.Map{"status": DeliveryNoteStatusDraft, "inbound_time": nil})
	}

	// 如果是已完成状态下撤销,需要同步旧系统的数据
	if deliveryNote.Status == int(DeliveryNoteStatusCompleted) {
		// 开启事务
		err = yc.DB(s.Model.GroupName()).Ctx(ctx).Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
			affected, err := m.UpdateAndGetAffected()
			if err != nil {
				return err
			}
			if affected == 0 {
				return gerror.New("撤销送货单失败，可能是送货单不存在")
			}
			count, err := yc.DBM(model.NewPmsMaterialInbound()).Ctx(ctx).Where("delivery_note_id", deliveryNote.ID).Ctx(ctx).Count()
			if err != nil {
				return err
			}
			if count != 1 {
				return gerror.New("撤销送货单失败，可能是送货单不存在或存在多条数据")
			}
			// 获取入库单信息
			inbound := &model.PmsMaterialInbound{}
			err = yc.DBM(model.NewPmsMaterialInbound()).Ctx(ctx).Where("delivery_note_id", deliveryNote.ID).Scan(inbound)
			if err != nil {
				return gerror.New("获取入库单信息失败: " + err.Error())
			}

			_, err = NewPmsMaterialInboundService().Revoke(ctx, inbound.ID)
			if err != nil {
				return err
			}
			// 删除入库信息
			_, err = yc.DBM(model.NewPmsMaterialInbound()).Ctx(ctx).Where("id", inbound.ID).Delete()
			if err != nil {
				return err
			}
			// 删除入库物料信息
			_, err = yc.DBM(model.NewPmsMaterialInboundProduct()).Ctx(ctx).Where("inbound_id", inbound.ID).Delete()
			if err != nil {
				return err
			}

			var info *model.PmsDeliveryNote
			//	直接退回草稿状态 重置内部订单号
			err = yc.DBM(s.Model).Ctx(ctx).Where("id", deliveryNote.ID).Scan(&info)
			if info.Status == int(DeliveryNoteStatusPending) {
				affected, err := yc.DBM(s.Model).Ctx(ctx).Where("id", deliveryNote.ID).
					Data(g.Map{"status": DeliveryNoteStatusDraft, "inbound_time": nil, "internal_order_no": ""}).UpdateAndGetAffected()
				if err != nil {
					return err
				}
				if affected == 0 {
					return gerror.New("撤销送货单失败，可能是送货单不存在")
				}
			}
			return nil
		})
	} else if deliveryNote.Status == int(DeliveryNoteStatusPending) {
		affected, err := m.UpdateAndGetAffected()
		if err != nil {
			return 0, err
		}
		if affected == 0 {
			return 0, gerror.New("撤销送货单失败，可能是送货单不存在")
		}
	}

	return deliveryNote.ID, err
}

// Submit 提交送货单
func (s *PmsDeliveryNoteService) Submit(ctx context.Context) (data interface{}, err error) {
	r := g.RequestFromCtx(ctx)
	rMap := r.GetMap()
	id := gconv.Int64(rMap["id"])
	voucher := gconv.String(rMap["voucher"])
	type MaterialType struct {
		Id                 int64  `json:"id"`
		WarehouseId        int    `json:"warehouseId"`
		Address            string `json:"address"`
		InboundOutboundKey int    `json:"inboundOutboundKey"`
	}
	materialInput := make([]MaterialType, 0)
	err = gconv.Scan(rMap["materials"], &materialInput)
	if err != nil {
		return nil, err
	}
	if len(materialInput) == 0 {
		return nil, gerror.New("送货单物料信息不能为空")
	}
	if id <= 0 {
		return nil, gerror.New("ID不能为空")
	}
	if voucher == "" {
		return nil, gerror.New("送货单不能为空")
	}
	count, err := yc.DBM(s.Model).Ctx(ctx).
		Where("id", id).
		Where("status", DeliveryNoteStatusDraft).
		Count()
	if err != nil {
		return nil, err
	}
	if count <= 0 {
		return nil, gerror.New("送货单不存在或已提交")
	}

	// 开启事务
	err = yc.DB(s.Model.GroupName()).Ctx(ctx).Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 更新送货单状态为待处理，更新送货单图片
		affected, err := yc.DBM(s.Model).Ctx(ctx).
			Where("id", id).
			Data(g.Map{"status": DeliveryNoteStatusPending, "inbound_time": gtime.Now(), "voucher": voucher}).UpdateAndGetAffected()
		if err != nil {
			return err
		}
		if affected == 0 {
			return gerror.New("更新送货单状态失败")
		}

		// 更新送货单物料状态
		for _, item := range materialInput {
			if item.Id <= 0 {
				return gerror.New("送货单物料ID不能为空")
			}
			// 更新送货单物料状态为待处理
			affected, err = yc.DBM(model.NewDeliveryNoteProduct()).Ctx(ctx).
				Where("id", item.Id).
				Data(g.Map{"inbound_outbound_key": item.InboundOutboundKey, "warehouse_id": item.WarehouseId, "address": item.Address}).
				UpdateAndGetAffected()
			if err != nil {
				return err
			}
		}

		// 修改为直接完成这个送货单
		_, err = s.Complete(ctx, id)
		if err != nil {
			return err
		}
		return nil
	})

	if err != nil {
		return nil, err
	}

	return id, err
}

func (s *PmsDeliveryNoteService) ServiceUpdate(ctx context.Context, _ *yc.UpdateReq) (data interface{}, err error) {
	// 更新直接调用Add方法
	return s.ServiceAdd(ctx, nil)
}

func (s *PmsDeliveryNoteService) ServiceAdd(ctx context.Context, _ *yc.AddReq) (data interface{}, err error) {
	r := g.RequestFromCtx(ctx)
	rMap := r.GetMap()

	type DeliveryNoteType struct {
		Id              int64  `json:"id"`
		No              string `json:"no"`
		OrderId         int64  `json:"orderId"`
		Po              string `json:"po"`
		Remark          string `json:"remark"`
		Voucher         string `json:"voucher"`
		SupplierId      int64  `json:"supplierId"`
		InboundType     int    `json:"inboundType"`
		SupplierOrderNo string `json:"supplierOrderNo"`
	}

	type MaterialType struct {
		ContractId     int64   `json:"contractId"`
		MaterialId     int64   `json:"materialId"`
		Quantity       float64 `json:"quantity"`
		DeliveryNoteId int64   `json:"deliveryNoteId"`
		Remark         string  `json:"remark"`
	}

	deliveryNoteInput := &DeliveryNoteType{}
	materialInput := make([]MaterialType, 0)

	err = gconv.Scan(rMap["delivery_note"], &deliveryNoteInput)
	if err != nil {
		return nil, err
	}

	err = gconv.Scan(rMap["materials"], &materialInput)
	if err != nil {
		return nil, err
	}

	// 创建时，查询供应商单号是否重复
	if deliveryNoteInput.Id <= 0 {
		count, err := yc.DBM(s.Model).
			Ctx(ctx).Where("supplier_order_no", deliveryNoteInput.SupplierOrderNo).
			WhereNot("supplier_order_no", "").
			Count()
		if err != nil {
			return nil, err
		}
		if count > 0 {
			return nil, gerror.New("供应商单号已存在")
		}
	} else {
		count, err := yc.DBM(s.Model).Ctx(ctx).
			WhereNot("id", deliveryNoteInput.Id).
			Where("supplier_order_no", deliveryNoteInput.SupplierOrderNo).
			WhereNot("supplier_order_no", "").
			Count()
		if err != nil {
			return nil, err
		}
		if count > 0 {
			return nil, gerror.New("供应商单号已存在")
		}
	}

	// 计算总数量
	products := make([]*model.PmsDeliveryNoteProduct, 0)

	for _, item := range materialInput {
		product := &model.PmsDeliveryNoteProduct{
			ContractId: item.ContractId,
			MaterialId: item.MaterialId,
			Quantity:   item.Quantity,
			Remark:     item.Remark,
		}
		products = append(products, product)
	}

	if deliveryNoteInput.OrderId <= 0 || (deliveryNoteInput.InboundType == 1 && deliveryNoteInput.Po == "") || deliveryNoteInput.SupplierId == 0 {
		return nil, fmt.Errorf("请输入完整的入库单信息")
	}

	if len(materialInput) == 0 {
		return nil, fmt.Errorf("请输入送货单物料信息")
	}
	isUpdate := false
	if deliveryNoteInput.Id > 0 {
		isUpdate = true
	}

	deliveryNote := &model.PmsDeliveryNote{
		ID:              deliveryNoteInput.Id,
		InboundType:     deliveryNoteInput.InboundType,
		SupplierOrderNo: deliveryNoteInput.SupplierOrderNo,
		No:              deliveryNoteInput.No,
		Status:          0,
		Remark:          deliveryNoteInput.Remark,
		Voucher:         deliveryNoteInput.Voucher,
		OrderId:         deliveryNoteInput.OrderId,
		Po:              deliveryNoteInput.Po,
		SupplierId:      deliveryNoteInput.SupplierId,
	}

	InboundId := deliveryNote.ID

	// 开启事务
	err = yc.DB(s.Model.GroupName()).Ctx(ctx).Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		InboundId, err = s.CreateDeliveryNote(ctx, deliveryNote, products, isUpdate)
		return nil
	})

	if err != nil {
		return nil, err
	}

	return InboundId, nil
}

func (s *PmsDeliveryNoteService) CreateDeliveryNote(ctx context.Context, deliveryNote *model.PmsDeliveryNote, products []*model.PmsDeliveryNoteProduct,
	isUpdate bool) (id int64, err error) {
	total := 0.0
	// 计算总数量
	for _, product := range products {
		total += product.Quantity
	}
	deliveryNote.TotalQuantity = total

	deliveryNote.CreateTime = gtime.Now()
	id = deliveryNote.ID
	if isUpdate {
		// 更新送货单
		_, err = yc.DBM(s.Model).Ctx(ctx).Where("id", id).Update(deliveryNote)
		if err != nil {
			return 0, err
		}
		// 删除旧的送货单物料
		_, err = yc.DBM(model.NewDeliveryNoteProduct()).Ctx(ctx).Where("inbound_id", id).Delete()
		if err != nil {
			return 0, err
		}
	} else {
		todayOrderCount := 0
		todayStart := gtime.Now().StartOfDay()
		todayOrderCount, err = yc.DBM(s.Model).Ctx(ctx).WhereGT("createTime", todayStart).Unscoped().Count()
		if err != nil {
			return 0, err
		}

		todayOrderCountStr := fmt.Sprintf("%d", todayOrderCount+1)
		// 订单数量不足3位数，前面补0
		if len(todayOrderCountStr) < 3 {
			todayOrderCountStr = fmt.Sprintf("%03s", todayOrderCountStr)
		}
		no := fmt.Sprintf("DNSN%s%s", gconv.String(gtime.Now().Format("Ymd")), todayOrderCountStr)
		// 判断是否存在
		count, err := yc.DBM(s.Model).Ctx(ctx).Where("no", no).Count()
		if err != nil {
			return 0, err
		}
		if count > 0 {
			return 0, gerror.New("创建入库单失败")
		}
		deliveryNote.No = no
		id, err = yc.DBM(s.Model).Ctx(ctx).Data(deliveryNote).InsertAndGetId()
		if err != nil {
			return 0, err
		}
	}
	if id == 0 {
		return 0, gerror.New("创建入库单失败")
	}

	// 创建产品
	if len(products) > 0 {
		// 遍历添加id
		for _, product := range products {
			product.InboundId = id
		}
		// 添加产品
		_, err = yc.DBM(model.NewDeliveryNoteProduct()).Ctx(ctx).Data(products).Insert()
		if err != nil {
			return 0, err
		}
	}

	return id, nil
}
func getDeliveryNoteCount(ctx context.Context, m *gdb.Model, whereMap *gmap.StrAnyMap) (res interface{}, err error) {
	data := gmap.NewIntIntMap()
	// 遍历whereMap,删除status条件
	for key := range whereMap.Map() {
		// 如果key等于status或者是带表前缀的status
		if key == "status" || gstr.HasSuffix(key, ".status") {
			whereMap.Remove(key)
		}
	}
	// 读取 DeliveryNoteStatus 所有枚举值
	statusList := []DeliveryNoteStatus{
		DeliveryNoteStatusDraft,
		DeliveryNoteStatusPending,
		DeliveryNoteStatusCompleted,
	}

	// 获取订单数量
	countModel := m.Clone().Ctx(ctx).Where(whereMap.Map())
	for _, status := range statusList {
		// 判断whereBuilder是否存在status条件
		count, err := countModel.Clone().Where("status", status).Count()
		if err != nil {
			return nil, err
		}
		// 向map中添加数据
		data.Set(int(status), count)
	}

	return data, nil
}

func NewPmsDeliveryNoteService() *PmsDeliveryNoteService {
	return &PmsDeliveryNoteService{
		&yc.Service{
			Model: model.NewDeliveryNote(),
			PageQueryOp: &yc.QueryOp{
				FieldEQ:      []string{"status", "supplier_id"},
				KeyWordField: []string{"no", "po", "supplier_order_no"},
				AdditionFields: []*yc.AdditionField{
					{
						FieldName: "count",
						FieldFunc: getDeliveryNoteCount,
					},
				},
				With: []interface{}{
					&model.MaterialInboundProductOutput{},
					&model.PmsMaterialOutput{},
				},
				ResultModify: func(ctx context.Context, data interface{}) (res interface{}, err error) {
					deliveryNote := make([]*model.PmsDeliveryNoteOutput, 0)
					err = gconv.Structs(data, &deliveryNote)
					if err != nil {
						return nil, err
					}
					if len(deliveryNote) > 0 {
						orderIds := make([]int64, 0)
						orderList := make([]*model.PmsPurchaseOrder, 0)
						supplierIds := make([]int64, 0)
						supplierList := make([]*model.PmsSupplier, 0)
						for _, item := range deliveryNote {
							supplierIds = append(supplierIds, item.SupplierId)
							orderIds = append(orderIds, item.OrderId)
						}
						err = yc.DBM(model.NewPmsPurchaseOrder()).Ctx(ctx).WhereIn("id", orderIds).
							Scan(&orderList)
						if err != nil {
							return nil, err
						}
						mOrders := make(map[int64]string)
						for _, item := range orderList {
							mOrders[item.ID] = item.OrderNo
						}

						// 查询供应商名称
						err = yc.DBM(model.NewPmsSupplier()).Ctx(ctx).WhereIn("id", supplierIds).Scan(&supplierList)
						if err != nil {
							return nil, err
						}
						supplierIdMap := make(map[int64]string, 0)
						for _, item := range supplierList {
							supplierIdMap[item.ID] = item.Name
						}

						for i, item := range deliveryNote {
							if v, ok := mOrders[item.OrderId]; ok {
								item.OrderNo = v
							}
							if v, ok := supplierIdMap[item.SupplierId]; ok {
								item.SupplierName = v
							}
							err = yc.DBM(model.NewDeliveryNoteProduct()).Ctx(ctx).WithAll().
								Where("inbound_id", item.ID).Scan(&item.Products)
							if err != nil {
								return nil, err
							}
							if item.InboundType == 9 {
								if item.OrderId > 0 {
									outbound := model.NewPmsMaterialOutbound()
									err := yc.DBM(outbound).Ctx(ctx).Where("id", item.OrderId).Scan(&outbound)
									if err != nil && !errors.Is(err, sql.ErrNoRows) {
										return nil, err
									}
									deliveryNote[i].OrderNo = outbound.No
								}
							}
						}
					}
					return deliveryNote, err
				},
			},
		},
	}
}
