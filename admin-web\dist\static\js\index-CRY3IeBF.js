import{i as s}from"./index-BtOcqcNl.js";import{c as m,b as p,e as c,x as f,z as b,h as u,i as n,P as g,m as v}from"./.pnpm-hVqhwuVC.js";const C=m({name:"ClSelect",props:{modelValue:[String,Number,Array],options:{type:[Array,Object],default:()=>[]},prop:String,labelKey:{type:String,default:"label"},valueKey:{type:String,default:"value"}},emits:["update:modelValue","change"],setup(l,{emit:o}){const r=s.useCrud(),t=p(),i=c(()=>f(l.options)?l.options.value:l.options);function d(e){var a;o("update:modelValue",e),o("change",e),l.prop&&((a=r.value)==null||a.refresh({page:1,[l.prop]:e===""?void 0:e}))}return b(()=>l.modelValue,e=>{t.value=e},{immediate:!0}),()=>u(n("el-select"),{modelValue:t.value,"onUpdate:modelValue":e=>t.value=e,clearable:!0,filterable:!0,onChange:d},{default:()=>{var e;return[(e=i.value)==null?void 0:e.map(a=>g(a)?u(n("el-option"),v(a,{label:a[l.labelKey],value:a[l.valueKey]}),null):u(n("el-option"),{label:a,value:a},null))]}})}});export{C as default};
