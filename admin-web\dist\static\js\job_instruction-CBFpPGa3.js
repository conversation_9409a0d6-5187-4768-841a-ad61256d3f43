import{c as z,b as T,q as U,w as r,B as P,h as l,i as n,y as m,t as j,f as y,j as x,v as k,al as O,am as V,a3 as R,Y as $,W as A,F as N,o as f,E as C}from"./.pnpm-hVqhwuVC.js";import{i as E}from"./index-BtOcqcNl.js";import{a as G}from"./index-D95m1iJL.js";const H={class:"download-header"},X={class:"download-title"},Y={class:"progress-container"},J={class:"progress-info"},K=m("span",{class:"progress-label"},"下载进度",-1),Q={class:"progress-percentage"},Z={class:"progress-status"},ee={key:0,class:"status-downloading"},se={key:1,class:"status-complete"},te=z({name:"pms-job-instruction"}),ce=z({...te,setup(le){const{service:B}=G(),c=T(!1),i=T(0),d=T(""),M=E.useUpsert({props:{class:"job-instruction-form",labelWidth:"120px"},items:[{label:"标题",prop:"title",required:!0,component:{name:"el-input"}},{label:"上传文件",prop:"url",required:!0,component:{name:"cl-upload",props:{accept:"image/*,.pdf,.doc,.docx,.xls,.xlsx,.zip,.rar",limitSize:1e3,text:"上传文件",type:"file",limit:1,disabled:!1,isPrivate:!1}}}],async onInfo(t,{done:o}){o(t)}}),S=E.useTable({columns:[{label:"ID",prop:"id",width:60},{label:"创建时间",prop:"createTime",width:180},{label:"标题",prop:"title"},{type:"op",label:"操作",buttons:["edit","delete","slot-btn-download"],width:300}]}),L=E.useCrud({service:B.pms.jobInstruction,async onRefresh(t,{next:o,render:s}){const{list:a,pagination:b}=await o(t);s(a,b)}},t=>{t.refresh()}),W=E.useSearch({items:[{label:"供应商",prop:"keyWord",props:{labelWidth:"120px"},component:{name:"el-input",props:{clearable:!1,onChange(t){var o;(o=L.value)==null||o.refresh({keyWord:t.trim(),page:1})}}}}]});async function q(t){if(c.value){C.warning("正在下载中，请稍候...");return}if(!t.url){C.error("文件URL不存在");return}try{c.value=!0,i.value=0,d.value=t.title||"作业指导书";const o=t.url.includes("127.0.0.1")||t.url.includes("localhost")||t.url.startsWith("/public/")||t.url.includes(":8001");let s;if(o)try{const e=await B.pms.jobInstruction.request({url:"/download",method:"POST",data:{id:t.id},responseType:"blob"});if(e instanceof Blob)s=e;else if(e&&typeof e=="object"&&e.data instanceof Blob)s=e.data;else if(e&&typeof e=="object"&&e.blob)s=e.blob;else throw new Error("响应不是有效的Blob对象");i.value=100}catch(e){throw console.error("本地文件下载失败:",e),e}else{const e=new XMLHttpRequest;e.addEventListener("progress",p=>{p.lengthComputable&&(i.value=Math.round(p.loaded/p.total*100))}),s=await new Promise((p,v)=>{e.onload=()=>{e.status===200?p(e.response):v(new Error(`下载失败: ${e.status}`))},e.onerror=()=>v(new Error("网络错误")),e.open("GET",t.url),e.responseType="blob",e.send()}),i.value=100}let a=t.title||"作业指导书";const b=t.url.split("/"),h=b[b.length-1];if(h&&h.includes("_")){const e=h.substring(h.indexOf("_")+1);e&&(a=decodeURIComponent(e))}if(!a.includes("."))if(s.type.includes("pdf"))a+=".pdf";else if(s.type.includes("zip"))a+=".zip";else if(s.type.includes("word"))a+=".docx";else if(s.type.includes("excel")||s.type.includes("spreadsheet"))a+=".xlsx";else{const e=t.url.match(/\.([^.?]+)(\?|$)/);e?a+=`.${e[1]}`:a+=".file"}const g=window.URL.createObjectURL(s),u=document.createElement("a");u.href=g,u.download=a,document.body.appendChild(u),u.click(),document.body.removeChild(u),window.URL.revokeObjectURL(g),C.success("下载成功")}catch(o){console.error("下载失败:",o);let s="下载失败";o.message.includes("网络错误")?s="网络连接失败，请检查网络连接后重试":o.message.includes("404")?s="文件不存在或已被删除":o.message.includes("403")?s="没有权限下载此文件":o.message.includes("500")?s="服务器错误，请稍后重试":o.message&&(s=o.message),C.error(s)}finally{setTimeout(()=>{c.value=!1,i.value=0,d.value=""},1e3)}}return(t,o)=>{const s=n("el-icon"),a=n("el-progress"),b=n("el-card"),h=n("cl-refresh-btn"),g=n("cl-add-btn"),u=n("cl-flex1"),e=n("cl-search"),w=n("el-row"),p=n("el-button"),v=n("cl-table"),I=n("cl-pagination"),D=n("cl-upsert"),F=n("cl-crud");return f(),U(F,{ref_key:"Crud",ref:L},{default:r(()=>[c.value?(f(),U(b,{key:0,class:"download-progress-card",shadow:"always",style:{"margin-bottom":"15px"}},{header:r(()=>[m("div",H,[l(s,{class:"download-icon",size:20},{default:r(()=>[l(k(R))]),_:1}),m("span",X,"正在下载: "+j(d.value),1)])]),default:r(()=>[m("div",Y,[m("div",J,[K,m("span",Q,j(i.value)+"%",1)]),l(a,{percentage:i.value,"stroke-width":14,"show-text":!1,status:i.value===100?"success":void 0,striped:"","striped-flow":"",class:"enhanced-progress"},null,8,["percentage","status"]),m("div",Z,[i.value<100?(f(),y("span",ee,[l(s,{class:"rotating"},{default:r(()=>[l(k(O))]),_:1}),x(" 下载中... ")])):(f(),y("span",se,[l(s,null,{default:r(()=>[l(k(V))]),_:1}),x(" 下载完成 ")]))])])]),_:1})):P("",!0),l(w,null,{default:r(()=>[l(h),l(g),l(u),l(e,{ref_key:"Search",ref:W},null,512)]),_:1}),l(w,{style:{"margin-top":"10px"}},{default:r(()=>[l(v,{ref_key:"Table",ref:S},{"slot-btn-download":r(({scope:_})=>[l(p,{type:"primary",class:A(["download-btn",{downloading:c.value&&d.value===(_.row.title||"作业指导书"),"disabled-download":c.value&&d.value!==(_.row.title||"作业指导书")}]),loading:c.value&&d.value===(_.row.title||"作业指导书"),disabled:c.value&&d.value!==(_.row.title||"作业指导书"),onClick:$(oe=>q(_.row),["stop"])},{default:r(()=>[!c.value||d.value!==(_.row.title||"作业指导书")?(f(),U(s,{key:0},{default:r(()=>[l(k(R))]),_:1})):P("",!0),c.value&&d.value===(_.row.title||"作业指导书")?(f(),y(N,{key:1},[x(j(i.value)+"% ",1)],64)):(f(),y(N,{key:2},[x(" 下载 ")],64))]),_:2},1032,["class","loading","disabled","onClick"])]),_:1},512)]),_:1}),l(w,null,{default:r(()=>[l(u),l(I)]),_:1}),l(D,{ref_key:"Upsert",ref:M},null,512)]),_:1},512)}}});export{ce as default};
