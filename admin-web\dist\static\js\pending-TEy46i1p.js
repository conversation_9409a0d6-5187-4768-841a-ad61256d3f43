import{_ as e}from"./purchase-pending-order.vue_vue_type_script_setup_true_lang-C7vO2wrD.js";import{c as o,q as t,o as r}from"./.pnpm-hVqhwuVC.js";import"./index-BtOcqcNl.js";import"./index-D95m1iJL.js";import"./index-CBanFtSc.js";import"./material-table-columns.vue_vue_type_script_setup_true_lang-BFX8eStE.js";const m=o({name:"undefined"}),u=o({...m,setup(n){return(p,_)=>(r(),t(e,{"show-completed":""}))}});export{u as default};
