import{k as m}from"./index-BtOcqcNl.js";import{c as _,b as h,f as e,y as a,t as c,v as k,h as r,w as i,j as d,i as b,o}from"./.pnpm-hVqhwuVC.js";import{a as g}from"./index-D95m1iJL.js";import{_ as v}from"./_plugin-vue_export-helper-DlAUqK2U.js";const y={class:"error-page"},C={class:"code"},x={class:"desc"},B={key:0,class:"btns"},N={key:1,class:"btns"},E=_({name:"undefined"}),V=_({...E,props:{code:Number,desc:String},setup(s){const{router:t}=g(),{user:l}=m(),u=h(!1);function p(){t.push("/login")}function f(){t.push("/")}return(w,L)=>{const n=b("el-button");return o(),e("div",y,[a("h1",C,c(s.code),1),a("p",x,c(s.desc),1),k(l).token||u.value?(o(),e("div",B,[r(n,{onClick:f},{default:i(()=>[d(" 回到首页 ")]),_:1})])):(o(),e("div",N,[r(n,{type:"primary",onClick:p},{default:i(()=>[d(" 返回登录页 ")]),_:1})]))])}}}),P=v(V,[["__scopeId","data-v-ee99d6eb"]]);export{P as E};
