import{m as f}from"./index-DkYL1aws.js";import{a as _}from"./index-C6cm1h61.js";import{c as p,b as n,z as v,S as V,i as b,f as h,o as g,h as j}from"./.pnpm-hVqhwuVC.js";import{_ as C}from"./_plugin-vue_export-helper-DlAUqK2U.js";const k={class:"cl-menu-perms"},x=p({name:"cl-menu-perms"}),y=p({...x,props:{modelValue:{type:String,default:""}},emits:["update:modelValue"],setup(r,{emit:c}){const m=r,i=c,{service:u}=_(),s=n([]),l=n([]);function d(e){i("update:modelValue",e.map(t=>t.join(":")).join(","))}return v(()=>m.modelValue,e=>{s.value=e?e.split(",").map(o=>o.split(":")):[]},{immediate:!0}),V(()=>{const e=[];function o(t){if(typeof t=="object")for(const a in t)a!=="permission"?o(t[a]):e.push(...Object.values(t[a]))}o(u),l.value=f(e,":")}),(e,o)=>{const t=b("el-cascader");return g(),h("div",k,[j(t,{modelValue:s.value,"onUpdate:modelValue":o[0]||(o[0]=a=>s.value=a),separator:":",clearable:"",filterable:"","collapse-tags":"","collapse-tags-tooltip":"",options:l.value,props:{multiple:!0},onChange:d},null,8,["modelValue","options"])])}}}),E=C(y,[["__scopeId","data-v-2738baf9"]]);export{E as default};
