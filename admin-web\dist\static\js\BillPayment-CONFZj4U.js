import{i as m}from"./index-BtOcqcNl.js";import{c as M,b as d,aa as s,q as g,w as c,h as a,i as l,f as N,s as P,F as B,o as h}from"./.pnpm-hVqhwuVC.js";import{a as j}from"./index-D95m1iJL.js";const z=M({name:"pms-bill-payment"}),K=M({...z,setup(E){const{service:D}=j(),_=d([]),i=d([]);async function w(){try{const e=await D.pms.supplier.request({url:"/list",method:"POST"});_.value=e}catch(e){console.error(e)}}w();const u=m.useUpsert({props:{class:"bill-payment-form",labelWidth:"120px"},items:[{label:"付款日期",prop:"payment_date",required:!0,component:{name:"el-date-picker",props:{type:"date",placeholder:"选择日期",clearable:!0,format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",disabledDate:e=>e.getTime()>Date.now()}}},{label:"付款银行",prop:"paying_bank",component:{name:"el-input"}},{label:"结算时段",prop:"start_date",required:!0,component:{name:"slot-start_date"}},{label:"收款银行",prop:"recipient_bank",component:{name:"el-input"}},{label:"付款金额",prop:"amount",required:!0,component:{name:"el-input-number"}},{label:"银行流水号",prop:"serial_number",component:{name:"el-input"}},{label:"收款单位",prop:"supplier_id",required:!0,component:{name:"slot-supplier-select"}},{label:"付款账号",prop:"payment_account",component:{name:"el-input"}},{label:"收款账号",prop:"shroff_account",component:{name:"el-input"}},{label:"摘要",prop:"description",component:{name:"el-input",props:{type:"textarea"}}},{label:"扣款凭证",prop:"voucher",component:{name:"cl-upload",props:{multiple:!0,limit:5,accept:"image/jpg,image/jpeg,image/png",text:"上传入库凭证",type:"image",disabled:!1,isPrivate:!1}}}],async onOpen(){i.value=[]},async onOpened(){},async onClose(e,t){t()},async onInfo(e,{done:t}){if(e.start_date&&e.end_date){const o=s(e.start_date).format("YYYY-MM-DD"),n=s(e.end_date).format("YYYY-MM-DD");i.value=[o,n]}else i.value=[];t(e)}}),k=m.useTable({columns:[{label:"ID",prop:"id",width:60},{label:"创建时间",prop:"createTime",width:180},{label:"付款日期",prop:"payment_date",width:120,formatter(e){return e.payment_date?s(e.payment_date).format("YYYY-MM-DD"):""}},{label:"付款时段",width:220,formatter(e){const t=e.start_date?s(e.start_date).format("YYYY-MM-DD"):"",o=e.end_date?s(e.end_date).format("YYYY-MM-DD"):"";return`${t} ~ ${o}`}},{label:"收款单位",prop:"supplier_name",showOverflowTooltip:!0},{label:"收款金额",prop:"amount",showOverflowTooltip:!0},{label:"付款账号",prop:"payment_account",width:160,showOverflowTooltip:!0},{label:"收款账号",prop:"shroff_account",width:160,showOverflowTooltip:!0},{label:"付款银行",prop:"paying_bank",showOverflowTooltip:!0},{label:"收款银行",prop:"recipient_bank",showOverflowTooltip:!0},{label:"付款单",prop:"voucher",width:120,component:{name:"cl-image",props:{fit:"cover",lazy:!0,size:[50,50]}}},{label:"流水号",prop:"serial_number",width:160,showOverflowTooltip:!0},{type:"op",buttons:["edit","delete"]}]}),f=m.useCrud({service:D.pms.finance.bill_payment,async onRefresh(e,{next:t,render:o}){const{list:n,pagination:r}=await t(e);o(n,r)}},e=>{e.refresh()});function T(e){var n,r;const t=e[0],o=e[1];(n=u.value)==null||n.setForm("start_date",s(t).format("YYYY-MM-DD")),(r=u.value)==null||r.setForm("end_date",s(o).format("YYYY-MM-DD"))}async function x(e){var n;let t="";const o=_.value.find(r=>r.id===e);t=o.supplierName?o.supplierName:"",(n=u.value)==null||n.setForm("supplier_name",t)}const v=d([]),y=d(""),C=m.useSearch({items:[{label:"供应商",prop:"keyWord",props:{labelWidth:"120px"},component:{name:"el-input",props:{clearable:!1,onChange(e){var t;y.value=e.trim(),(t=f.value)==null||t.refresh({keyWord:e.trim(),dateRange:v.value,page:1})}}}},{label:"付款时间",prop:"dateRange",props:{labelWidth:"100px"},component:{name:"el-date-picker",props:{type:"daterange","value-format":"YYYY-MM-DD",format:"YYYY-MM-DD",rangeSeparator:"至",startPlaceholder:"开始日期",endPlaceholder:"结束日期",clearable:!0,onChange(e){var t;v.value=e,(t=f.value)==null||t.refresh({keyWord:y.value,dateRange:e,page:1})}}}}]});return(e,t)=>{const o=l("cl-refresh-btn"),n=l("cl-add-btn"),r=l("cl-flex1"),O=l("cl-search"),b=l("el-row"),V=l("cl-table"),S=l("cl-pagination"),W=l("el-option"),q=l("el-select"),R=l("el-date-picker"),U=l("cl-upsert"),F=l("cl-crud");return h(),g(F,{ref_key:"Crud",ref:f},{default:c(()=>[a(b,null,{default:c(()=>[a(o),a(n),a(r),a(O,{ref_key:"Search",ref:C},null,512)]),_:1}),a(b,{style:{"margin-top":"10px"}},{default:c(()=>[a(V,{ref_key:"Table",ref:k},null,512)]),_:1}),a(b,null,{default:c(()=>[a(r),a(S)]),_:1}),a(U,{ref_key:"Upsert",ref:u},{"slot-supplier-select":c(({scope:Y})=>[a(q,{modelValue:Y.supplier_id,"onUpdate:modelValue":p=>Y.supplier_id=p,placeholder:"请选择事故物料供应商",filterable:"",onChange:x},{default:c(()=>[(h(!0),N(B,null,P(_.value,p=>(h(),g(W,{key:p.id,label:p.supplierName,value:p.id},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),"slot-start_date":c(({scope:Y})=>[a(R,{modelValue:i.value,"onUpdate:modelValue":t[0]||(t[0]=p=>i.value=p),type:"daterange",placeholder:"选择日期",clearable:"",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD","disabled-date":p=>p.getTime()>Date.now(),onChange:T},null,8,["modelValue","disabled-date"])]),_:1},512)]),_:1},512)}}});export{K as default};
