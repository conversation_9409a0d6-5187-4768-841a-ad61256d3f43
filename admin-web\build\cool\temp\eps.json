[{"name": "BaseCommInfoEntity", "prefix": "/admin/base/comm", "api": [{"path": "/appUpdate", "method": "POST"}, {"path": "/logout", "method": "POST"}, {"path": "/permmenu", "method": "GET"}, {"path": "/person", "method": "GET"}, {"path": "/personUpdate", "method": "POST"}, {"path": "/upload", "method": "POST"}, {"path": "/uploadMode", "method": "GET"}, {"path": "/version", "method": "GET"}]}, {"name": "BaseOpenInfoEntity", "prefix": "/admin/base/open", "api": [{"path": "/captcha", "method": "GET"}, {"path": "/eps", "method": "GET"}, {"path": "/login", "method": "POST"}, {"path": "/refreshToken", "method": "GET"}]}, {"name": "BaseSysDepartmentInfoEntity", "prefix": "/admin/base/sys/department", "api": [{"path": "/add", "method": "POST"}, {"path": "/delete", "method": "POST"}, {"path": "/info", "method": "GET"}, {"path": "/list", "method": "POST"}, {"path": "/order", "method": "GET"}, {"path": "/page", "method": "POST"}, {"path": "/update", "method": "POST"}]}, {"name": "BaseSysLogInfoEntity", "prefix": "/admin/base/sys/log", "api": [{"path": "/add", "method": "POST"}, {"path": "/clear", "method": "POST"}, {"path": "/delete", "method": "POST"}, {"path": "/getKeep", "method": "GET"}, {"path": "/info", "method": "GET"}, {"path": "/list", "method": "POST"}, {"path": "/page", "method": "POST"}, {"path": "/setKeep", "method": "POST"}, {"path": "/update", "method": "POST"}]}, {"name": "BaseSysMenuInfoEntity", "prefix": "/admin/base/sys/menu", "api": [{"path": "/add", "method": "POST"}, {"path": "/delete", "method": "POST"}, {"path": "/info", "method": "GET"}, {"path": "/list", "method": "POST"}, {"path": "/page", "method": "POST"}, {"path": "/update", "method": "POST"}]}, {"name": "BaseSysParamInfoEntity", "prefix": "/admin/base/sys/param", "api": [{"path": "/add", "method": "POST"}, {"path": "/delete", "method": "POST"}, {"path": "/info", "method": "GET"}, {"path": "/list", "method": "POST"}, {"path": "/page", "method": "POST"}, {"path": "/update", "method": "POST"}]}, {"name": "BaseSysRoleInfoEntity", "prefix": "/admin/base/sys/role", "api": [{"path": "/add", "method": "POST"}, {"path": "/delete", "method": "POST"}, {"path": "/info", "method": "GET"}, {"path": "/list", "method": "POST"}, {"path": "/page", "method": "POST"}, {"path": "/update", "method": "POST"}]}, {"name": "BaseSysUserInfoEntity", "prefix": "/admin/base/sys/user", "api": [{"path": "/add", "method": "POST"}, {"path": "/delete", "method": "POST"}, {"path": "/info", "method": "GET"}, {"path": "/list", "method": "POST"}, {"path": "/move", "method": "GET"}, {"path": "/page", "method": "POST"}, {"path": "/update", "method": "POST"}]}, {"name": "DictInfoEntity", "prefix": "/admin/dict/info", "api": [{"path": "/add", "method": "POST"}, {"path": "/data", "method": "POST"}, {"path": "/delete", "method": "POST"}, {"path": "/info", "method": "GET"}, {"path": "/list", "method": "POST"}, {"path": "/page", "method": "POST"}, {"path": "/update", "method": "POST"}]}, {"name": "DictTypeInfoEntity", "prefix": "/admin/dict/type", "api": [{"path": "/add", "method": "POST"}, {"path": "/delete", "method": "POST"}, {"path": "/info", "method": "GET"}, {"path": "/list", "method": "POST"}, {"path": "/page", "method": "POST"}, {"path": "/update", "method": "POST"}]}, {"name": "PimsAutidInfoEntity", "prefix": "/admin/pims/autid", "api": [{"path": "/add", "method": "POST"}, {"path": "/delete", "method": "POST"}, {"path": "/getAutidByWorkitemId", "method": "POST"}, {"path": "/info", "method": "GET"}, {"path": "/list", "method": "POST"}, {"path": "/page", "method": "POST"}, {"path": "/update", "method": "POST"}]}, {"name": "PimsConfigInfoEntity", "prefix": "/admin/pims/config", "api": [{"path": "/add", "method": "POST"}, {"path": "/delete", "method": "POST"}, {"path": "/info", "method": "GET"}, {"path": "/list", "method": "POST"}, {"path": "/page", "method": "POST"}, {"path": "/update", "method": "POST"}]}, {"name": "PimsDaliyReportInfoEntity", "prefix": "/admin/pims/daliy<PERSON>ep<PERSON>", "api": [{"path": "/add", "method": "POST"}, {"path": "/delete", "method": "POST"}, {"path": "/info", "method": "GET"}, {"path": "/list", "method": "POST"}, {"path": "/page", "method": "POST"}, {"path": "/readReport", "method": "POST"}, {"path": "/submitReport", "method": "POST"}, {"path": "/update", "method": "POST"}]}, {"name": "PimsProgressInfoEntity", "prefix": "/admin/pims/progress", "api": [{"path": "/add", "method": "POST"}, {"path": "/delete", "method": "POST"}, {"path": "/info", "method": "GET"}, {"path": "/list", "method": "POST"}, {"path": "/page", "method": "POST"}, {"path": "/update", "method": "POST"}]}, {"name": "PimsWorkitemInfoEntity", "prefix": "/admin/pims/workitem", "api": [{"path": "/add", "method": "POST"}, {"path": "/audit", "method": "POST"}, {"path": "/delete", "method": "POST"}, {"path": "/info", "method": "GET"}, {"path": "/list", "method": "POST"}, {"path": "/listByIds", "method": "POST"}, {"path": "/listByUser", "method": "POST"}, {"path": "/logDetail", "method": "POST"}, {"path": "/myList", "method": "POST"}, {"path": "/page", "method": "POST"}, {"path": "/queryInfo", "method": "POST"}, {"path": "/readLog", "method": "POST"}, {"path": "/readTask", "method": "POST"}, {"path": "/remove", "method": "POST"}, {"path": "/submit", "method": "POST"}, {"path": "/systemUserList", "method": "POST"}, {"path": "/update", "method": "POST"}, {"path": "/updateSort", "method": "POST"}, {"path": "/workHourByIds", "method": "POST"}, {"path": "/attachment", "service": true}]}, {"name": "PimsWorkitemAttachmentInfoEntity", "prefix": "/admin/pims/workitem/attachment", "api": [{"path": "/add", "method": "POST"}, {"path": "/delete", "method": "POST"}, {"path": "/download", "method": "GET"}, {"path": "/info", "method": "GET"}, {"path": "/list", "method": "POST"}, {"path": "/page", "method": "POST"}, {"path": "/remove", "method": "POST"}, {"path": "/update", "method": "POST"}, {"path": "/upload", "method": "POST"}]}, {"name": "PimsWorkitemLogInfoEntity", "prefix": "/admin/pims/workitem/log", "api": [{"path": "/add", "method": "POST"}, {"path": "/delete", "method": "POST"}, {"path": "/info", "method": "GET"}, {"path": "/list", "method": "POST"}, {"path": "/page", "method": "POST"}, {"path": "/queryPage", "method": "POST"}, {"path": "/top", "method": "POST"}, {"path": "/update", "method": "POST"}]}, {"name": "PmsAbnormalWorkingHoursInfoEntity", "prefix": "/admin/pms/AbnormalWorkingHours", "api": [{"path": "/add", "method": "POST"}, {"path": "/delete", "method": "POST"}, {"path": "/export", "method": "GET"}, {"path": "/importAbnormalData", "method": "POST"}, {"path": "/info", "method": "GET"}, {"path": "/list", "method": "POST"}, {"path": "/page", "method": "POST"}, {"path": "/update", "method": "POST"}]}, {"name": "PmsDailyProductionReportInfoEntity", "prefix": "/admin/pms/DailyProductionReport", "api": [{"path": "/GetCapacitySummary", "method": "POST"}, {"path": "/add", "method": "POST"}, {"path": "/audit", "method": "POST"}, {"path": "/delete", "method": "POST"}, {"path": "/export", "method": "GET"}, {"path": "/exportSummary", "method": "POST"}, {"path": "/importDailyProductionData", "method": "POST"}, {"path": "/info", "method": "GET"}, {"path": "/list", "method": "POST"}, {"path": "/page", "method": "POST"}, {"path": "/update", "method": "POST"}]}, {"name": "PmsPartsCapacityInfoEntity", "prefix": "/admin/pms/PartsCapacity", "api": [{"path": "/add", "method": "POST"}, {"path": "/delete", "method": "POST"}, {"path": "/info", "method": "GET"}, {"path": "/list", "method": "POST"}, {"path": "/page", "method": "POST"}, {"path": "/update", "method": "POST"}]}, {"name": "PmsStandardCapacityInfoEntity", "prefix": "/admin/pms/StandardCapacity", "api": [{"path": "/add", "method": "POST"}, {"path": "/addPartsCapacity", "method": "POST"}, {"path": "/delete", "method": "POST"}, {"path": "/deletePartsCapacity", "method": "POST"}, {"path": "/importStandardData", "method": "POST"}, {"path": "/info", "method": "GET"}, {"path": "/list", "method": "POST"}, {"path": "/page", "method": "POST"}, {"path": "/summaryExport", "method": "GET"}, {"path": "/update", "method": "POST"}, {"path": "/updatePartsCapacity", "method": "POST"}]}, {"name": "PmsAllDataCompareInfoEntity", "prefix": "/admin/pms/allDataCompare", "api": [{"path": "/add", "method": "POST"}, {"path": "/delete", "method": "POST"}, {"path": "/downloadLatestColumnData", "method": "GET"}, {"path": "/exportSummaryData", "method": "GET"}, {"path": "/getHeaderColumns", "method": "GET"}, {"path": "/info", "method": "GET"}, {"path": "/list", "method": "POST"}, {"path": "/page", "method": "POST"}, {"path": "/update", "method": "POST"}, {"path": "/uploadColumnData", "method": "POST"}]}, {"name": "PmsAuditProcessInfoEntity", "prefix": "/admin/pms/audit/process", "api": [{"path": "/add", "method": "POST"}, {"path": "/auditRecordList", "method": "POST"}, {"path": "/delete", "method": "POST"}, {"path": "/info", "method": "GET"}, {"path": "/list", "method": "POST"}, {"path": "/nodes", "method": "GET"}, {"path": "/page", "method": "POST"}, {"path": "/saveNodes", "method": "POST"}, {"path": "/update", "method": "POST"}, {"path": "/updateNode", "method": "POST"}]}, {"name": "PmsAuditProcessorInfoEntity", "prefix": "/admin/pms/audit/processor", "api": [{"path": "/add", "method": "POST"}, {"path": "/delete", "method": "POST"}, {"path": "/info", "method": "GET"}, {"path": "/list", "method": "POST"}, {"path": "/page", "method": "POST"}, {"path": "/update", "method": "POST"}, {"path": "/node", "service": true}]}, {"name": "PmsAuditProcessorNodeInfoEntity", "prefix": "/admin/pms/audit/processor/node", "api": [{"path": "/add", "method": "POST"}, {"path": "/deal", "method": "POST"}, {"path": "/delete", "method": "POST"}, {"path": "/info", "method": "GET"}, {"path": "/list", "method": "POST"}, {"path": "/page", "method": "POST"}, {"path": "/update", "method": "POST"}]}, {"name": "PmsBomInfoEntity", "prefix": "/admin/pms/bom", "api": [{"path": "/GetBomById", "method": "GET"}, {"path": "/GetBomMaterialByProductId", "method": "GET"}, {"path": "/add", "method": "POST"}, {"path": "/delete", "method": "POST"}, {"path": "/export", "method": "GET"}, {"path": "/importBom", "method": "POST"}, {"path": "/info", "method": "GET"}, {"path": "/list", "method": "POST"}, {"path": "/page", "method": "POST"}, {"path": "/saveBom", "method": "POST"}, {"path": "/update", "method": "POST"}]}, {"name": "PmsClearanceOrderInfoEntity", "prefix": "/admin/pms/clearance/order", "api": [{"path": "/add", "method": "POST"}, {"path": "/confirm", "method": "POST"}, {"path": "/delete", "method": "POST"}, {"path": "/download", "method": "POST"}, {"path": "/finish", "method": "POST"}, {"path": "/info", "method": "GET"}, {"path": "/information", "method": "POST"}, {"path": "/list", "method": "POST"}, {"path": "/page", "method": "POST"}, {"path": "/update", "method": "POST"}]}, {"name": "PmsDailyReportDataInfoEntity", "prefix": "/admin/pms/daily_report_data", "api": [{"path": "/add", "method": "POST"}, {"path": "/delete", "method": "POST"}, {"path": "/export", "method": "GET"}, {"path": "/getEmployeeWorking", "method": "POST"}, {"path": "/importDailyReportData", "method": "POST"}, {"path": "/info", "method": "GET"}, {"path": "/list", "method": "POST"}, {"path": "/page", "method": "POST"}, {"path": "/update", "method": "POST"}]}, {"name": "PmsDataDiffInfoEntity", "prefix": "/admin/pms/dataDiff", "api": [{"path": "/add", "method": "POST"}, {"path": "/delete", "method": "POST"}, {"path": "/exportTestDataToJson", "method": "POST"}, {"path": "/importTestData", "method": "POST"}, {"path": "/info", "method": "GET"}, {"path": "/list", "method": "POST"}, {"path": "/mergeMaterialData", "method": "POST"}, {"path": "/page", "method": "POST"}, {"path": "/update", "method": "POST"}, {"path": "/uploadDataDiff", "method": "POST"}, {"path": "/uploadProductDataDiff", "method": "POST"}]}, {"name": "PmsDeliveryNoteInfoEntity", "prefix": "/admin/pms/delivery_note", "api": [{"path": "/add", "method": "POST"}, {"path": "/complete", "method": "POST"}, {"path": "/delete", "method": "POST"}, {"path": "/getDeliveryInfo", "method": "POST"}, {"path": "/info", "method": "GET"}, {"path": "/list", "method": "POST"}, {"path": "/page", "method": "POST"}, {"path": "/revoke", "method": "POST"}, {"path": "/submit", "method": "POST"}, {"path": "/update", "method": "POST"}]}, {"name": "PmsDrawingInfoEntity", "prefix": "/admin/pms/drawing", "api": [{"path": "/add", "method": "POST"}, {"path": "/delete", "method": "POST"}, {"path": "/download", "method": "GET"}, {"path": "/downloadHistory", "method": "GET"}, {"path": "/getDownloadURL", "method": "GET"}, {"path": "/getHistoryDownloadURL", "method": "GET"}, {"path": "/getProductsByDrawingId", "method": "POST"}, {"path": "/info", "method": "GET"}, {"path": "/list", "method": "POST"}, {"path": "/page", "method": "POST"}, {"path": "/removeDrawingProduct", "method": "POST"}, {"path": "/saveProducts", "method": "POST"}, {"path": "/update", "method": "POST"}]}, {"name": "PmsFinanceInfoEntity", "prefix": "/admin/pms/finance", "api": [{"path": "/add", "method": "POST"}, {"path": "/delete", "method": "POST"}, {"path": "/info", "method": "GET"}, {"path": "/list", "method": "POST"}, {"path": "/materialInboundExportExcel", "method": "GET"}, {"path": "/materialInboundPage", "method": "POST"}, {"path": "/materialOutboundExportExcel", "method": "POST"}, {"path": "/materialOutboundPage", "method": "POST"}, {"path": "/page", "method": "POST"}, {"path": "/productInboundExportExcel", "method": "GET"}, {"path": "/productInboundPage", "method": "POST"}, {"path": "/productOutboundExportExcel", "method": "GET"}, {"path": "/productOutboundPage", "method": "POST"}, {"path": "/productionOrderExportExcel", "method": "GET"}, {"path": "/productionOrderPage", "method": "POST"}, {"path": "/purchaseOrderExportExcel", "method": "GET"}, {"path": "/purchaseOrderPage", "method": "POST"}, {"path": "/salesOrderExportExcel", "method": "GET"}, {"path": "/salesOrderPage", "method": "POST"}, {"path": "/update", "method": "POST"}, {"path": "/bill_payment", "service": true}]}, {"name": "PmsFinanceBillPaymentInfoEntity", "prefix": "/admin/pms/finance/bill_payment", "api": [{"path": "/add", "method": "POST"}, {"path": "/delete", "method": "POST"}, {"path": "/info", "method": "GET"}, {"path": "/list", "method": "POST"}, {"path": "/page", "method": "POST"}, {"path": "/update", "method": "POST"}]}, {"name": "PmsFreightForwarderInfoEntity", "prefix": "/admin/pms/freight/forwarder", "api": [{"path": "/add", "method": "POST"}, {"path": "/delete", "method": "POST"}, {"path": "/info", "method": "GET"}, {"path": "/list", "method": "POST"}, {"path": "/page", "method": "POST"}, {"path": "/update", "method": "POST"}, {"path": "/order", "service": true}]}, {"name": "PmsFreightForwarderOrderInfoEntity", "prefix": "/admin/pms/freight/forwarder/order", "api": [{"path": "/add", "method": "POST"}, {"path": "/confirm", "method": "POST"}, {"path": "/delete", "method": "POST"}, {"path": "/info", "method": "GET"}, {"path": "/list", "method": "POST"}, {"path": "/merge", "method": "POST"}, {"path": "/page", "method": "POST"}, {"path": "/prepare", "method": "POST"}, {"path": "/revoke", "method": "POST"}, {"path": "/send", "method": "POST"}, {"path": "/supplement", "method": "POST"}, {"path": "/update", "method": "POST"}, {"path": "/upload", "method": "POST"}]}, {"name": "PmsJobInstructionInfoEntity", "prefix": "/admin/pms/jobInstruction", "api": [{"path": "/add", "method": "POST"}, {"path": "/delete", "method": "POST"}, {"path": "/download", "method": "POST"}, {"path": "/info", "method": "GET"}, {"path": "/list", "method": "POST"}, {"path": "/page", "method": "POST"}, {"path": "/update", "method": "POST"}]}, {"name": "PmsMaterialInfoEntity", "prefix": "/admin/pms/material", "api": [{"path": "/add", "method": "POST"}, {"path": "/addMaterialAddress", "method": "POST"}, {"path": "/delete", "method": "POST"}, {"path": "/deleteMaterialAddress", "method": "POST"}, {"path": "/export", "method": "GET"}, {"path": "/getMaterialAddress", "method": "GET"}, {"path": "/getMaterialById", "method": "GET"}, {"path": "/getMaterialByName", "method": "GET"}, {"path": "/importExcel", "method": "POST"}, {"path": "/importMaterialAddressData", "method": "POST"}, {"path": "/info", "method": "GET"}, {"path": "/list", "method": "POST"}, {"path": "/page", "method": "POST"}, {"path": "/setMaterialLevel", "method": "POST"}, {"path": "/summaryExport", "method": "GET"}, {"path": "/update", "method": "POST"}, {"path": "/bundle", "service": true}, {"path": "/drawing", "service": true}, {"path": "/inbound", "service": true}, {"path": "/outbound", "service": true}, {"path": "/price", "service": true}]}, {"name": "PmsMaterialBomChangeLogInfoEntity", "prefix": "/admin/pms/material/bom/change/log", "api": [{"path": "/add", "method": "POST"}, {"path": "/delete", "method": "POST"}, {"path": "/info", "method": "GET"}, {"path": "/list", "method": "POST"}, {"path": "/page", "method": "POST"}, {"path": "/update", "method": "POST"}]}, {"name": "PmsMaterialBundleInfoEntity", "prefix": "/admin/pms/material/bundle", "api": [{"path": "/add", "method": "POST"}, {"path": "/delete", "method": "POST"}, {"path": "/info", "method": "GET"}, {"path": "/list", "method": "POST"}, {"path": "/page", "method": "POST"}, {"path": "/saveBundle", "method": "POST"}, {"path": "/update", "method": "POST"}]}, {"name": "PmsMaterialDrawingInfoEntity", "prefix": "/admin/pms/material/drawing", "api": [{"path": "/add", "method": "POST"}, {"path": "/delete", "method": "POST"}, {"path": "/download", "method": "GET"}, {"path": "/info", "method": "GET"}, {"path": "/list", "method": "POST"}, {"path": "/page", "method": "POST"}, {"path": "/setAsDefault", "method": "POST"}, {"path": "/update", "method": "POST"}]}, {"name": "PmsMaterialInboundInfoEntity", "prefix": "/admin/pms/material/inbound", "api": [{"path": "/add", "method": "POST"}, {"path": "/complete", "method": "POST"}, {"path": "/confirm", "method": "POST"}, {"path": "/delete", "method": "POST"}, {"path": "/detail", "method": "POST"}, {"path": "/importInboundRecord", "method": "POST"}, {"path": "/info", "method": "GET"}, {"path": "/list", "method": "POST"}, {"path": "/materialInboundExportExcel", "method": "GET"}, {"path": "/materialInboundPage", "method": "POST"}, {"path": "/page", "method": "POST"}, {"path": "/revoke", "method": "POST"}, {"path": "/revokeAndSync", "method": "POST"}, {"path": "/start", "method": "POST"}, {"path": "/submit", "method": "POST"}, {"path": "/summary", "method": "GET"}, {"path": "/update", "method": "POST"}]}, {"name": "PmsMaterialOutboundInfoEntity", "prefix": "/admin/pms/material/outbound", "api": [{"path": "/add", "method": "POST"}, {"path": "/complete", "method": "POST"}, {"path": "/confirm", "method": "POST"}, {"path": "/delete", "method": "POST"}, {"path": "/detail", "method": "POST"}, {"path": "/exportScrap", "method": "POST"}, {"path": "/getPrintData", "method": "POST"}, {"path": "/info", "method": "GET"}, {"path": "/list", "method": "POST"}, {"path": "/materialOutboundExportExcel", "method": "POST"}, {"path": "/materialOutboundPage", "method": "POST"}, {"path": "/page", "method": "POST"}, {"path": "/revoke", "method": "POST"}, {"path": "/start", "method": "POST"}, {"path": "/submit", "method": "POST"}, {"path": "/update", "method": "POST"}]}, {"name": "PmsMaterialPriceInfoEntity", "prefix": "/admin/pms/material/price", "api": [{"path": "/add", "method": "POST"}, {"path": "/delete", "method": "POST"}, {"path": "/getPrices", "method": "GET"}, {"path": "/info", "method": "GET"}, {"path": "/list", "method": "POST"}, {"path": "/page", "method": "POST"}, {"path": "/update", "method": "POST"}]}, {"name": "PmsMaterialStockLogInfoEntity", "prefix": "/admin/pms/materialStockLog", "api": [{"path": "/add", "method": "POST"}, {"path": "/delete", "method": "POST"}, {"path": "/info", "method": "GET"}, {"path": "/list", "method": "POST"}, {"path": "/page", "method": "POST"}, {"path": "/queryMaterialStockLogPage", "method": "POST"}, {"path": "/update", "method": "POST"}]}, {"name": "PmsMaterialAnomalyInfoEntity", "prefix": "/admin/pms/material_anomaly", "api": [{"path": "/ImportMaterialAnomaly", "method": "POST"}, {"path": "/add", "method": "POST"}, {"path": "/delete", "method": "POST"}, {"path": "/export", "method": "GET"}, {"path": "/info", "method": "GET"}, {"path": "/list", "method": "POST"}, {"path": "/page", "method": "POST"}, {"path": "/update", "method": "POST"}]}, {"name": "PmsMaterialTestInfoEntity", "prefix": "/admin/pms/material_test", "api": [{"path": "/add", "method": "POST"}, {"path": "/delete", "method": "POST"}, {"path": "/export", "method": "POST"}, {"path": "/info", "method": "GET"}, {"path": "/list", "method": "POST"}, {"path": "/page", "method": "POST"}, {"path": "/update", "method": "POST"}]}, {"name": "PmsOutsourceInboundInfoEntity", "prefix": "/admin/pms/outsource/inbound", "api": [{"path": "/add", "method": "POST"}, {"path": "/delete", "method": "POST"}, {"path": "/getOutsourceByWorkOrder", "method": "POST"}, {"path": "/info", "method": "GET"}, {"path": "/list", "method": "POST"}, {"path": "/outsourceInboundPage", "method": "POST"}, {"path": "/page", "method": "POST"}, {"path": "/revoke", "method": "POST"}, {"path": "/submit", "method": "POST"}, {"path": "/update", "method": "POST"}]}, {"name": "PmsPaymentInfoEntity", "prefix": "/admin/pms/payment", "api": [{"path": "/add", "method": "POST"}, {"path": "/batchExport", "method": "POST"}, {"path": "/delete", "method": "POST"}, {"path": "/export", "method": "POST"}, {"path": "/financePaymentPage", "method": "POST"}, {"path": "/info", "method": "GET"}, {"path": "/list", "method": "POST"}, {"path": "/page", "method": "POST"}, {"path": "/summaryExport", "method": "POST"}, {"path": "/update", "method": "POST"}]}, {"name": "PmsProductInfoEntity", "prefix": "/admin/pms/product", "api": [{"path": "/add", "method": "POST"}, {"path": "/delete", "method": "POST"}, {"path": "/exportInboundOutboundDetails", "method": "GET"}, {"path": "/exportProduct", "method": "GET"}, {"path": "/getAllProduct", "method": "GET"}, {"path": "/import_data", "method": "POST"}, {"path": "/inboundSummary", "method": "GET"}, {"path": "/info", "method": "GET"}, {"path": "/list", "method": "POST"}, {"path": "/page", "method": "POST"}, {"path": "/summaryExport", "method": "GET"}, {"path": "/update", "method": "POST"}, {"path": "/group", "service": true}]}, {"name": "PmsProductGroupInfoEntity", "prefix": "/admin/pms/product/group", "api": [{"path": "/add", "method": "POST"}, {"path": "/delete", "method": "POST"}, {"path": "/info", "method": "GET"}, {"path": "/list", "method": "POST"}, {"path": "/page", "method": "POST"}, {"path": "/update", "method": "POST"}]}, {"name": "PmsProductStockLogInfoEntity", "prefix": "/admin/pms/product/stock/log", "api": [{"path": "/add", "method": "POST"}, {"path": "/delete", "method": "POST"}, {"path": "/info", "method": "GET"}, {"path": "/list", "method": "POST"}, {"path": "/page", "method": "POST"}, {"path": "/update", "method": "POST"}]}, {"name": "PmsProductionManHourDeductInfoEntity", "prefix": "/admin/pms/production/ManHourDeduct", "api": [{"path": "/add", "method": "POST"}, {"path": "/delete", "method": "POST"}, {"path": "/info", "method": "GET"}, {"path": "/list", "method": "POST"}, {"path": "/page", "method": "POST"}, {"path": "/update", "method": "POST"}]}, {"name": "PmsProductionMaterialDeductInfoEntity", "prefix": "/admin/pms/production/MaterialDeduct", "api": [{"path": "/add", "method": "POST"}, {"path": "/delete", "method": "POST"}, {"path": "/getMaterialListBySupplierId", "method": "POST"}, {"path": "/info", "method": "GET"}, {"path": "/list", "method": "POST"}, {"path": "/page", "method": "POST"}, {"path": "/update", "method": "POST"}]}, {"name": "PmsProductionPurchaseOrderInfoEntity", "prefix": "/admin/pms/production/purchase/order", "api": [{"path": "/add", "method": "POST"}, {"path": "/auditLog", "method": "GET"}, {"path": "/delete", "method": "POST"}, {"path": "/exportSummary", "method": "GET"}, {"path": "/getProductByProductionOrder", "method": "GET"}, {"path": "/info", "method": "GET"}, {"path": "/list", "method": "POST"}, {"path": "/page", "method": "POST"}, {"path": "/purchaseOrderExportExcel", "method": "POST"}, {"path": "/purchaseOrderPage", "method": "POST"}, {"path": "/revoke", "method": "POST"}, {"path": "/submit", "method": "POST"}, {"path": "/update", "method": "POST"}, {"path": "/updateExtra", "method": "POST"}]}, {"name": "PmsProductionSaleOrderInfoEntity", "prefix": "/admin/pms/production/sale/order", "api": [{"path": "/add", "method": "POST"}, {"path": "/delete", "method": "POST"}, {"path": "/info", "method": "GET"}, {"path": "/list", "method": "POST"}, {"path": "/lockStock", "method": "POST"}, {"path": "/outbound", "method": "POST"}, {"path": "/page", "method": "POST"}, {"path": "/revoke", "method": "POST"}, {"path": "/schedule", "method": "POST"}, {"path": "/update", "method": "POST"}]}, {"name": "PmsProductionScheduleInfoEntity", "prefix": "/admin/pms/production/schedule", "api": [{"path": "/add", "method": "POST"}, {"path": "/confirm", "method": "POST"}, {"path": "/delete", "method": "POST"}, {"path": "/export", "method": "POST"}, {"path": "/exportOrder", "method": "POST"}, {"path": "/getOrderProductQuantity", "method": "POST"}, {"path": "/getProductListByOrderId", "method": "POST"}, {"path": "/info", "method": "GET"}, {"path": "/list", "method": "POST"}, {"path": "/page", "method": "POST"}, {"path": "/revoke", "method": "POST"}, {"path": "/summary", "method": "GET"}, {"path": "/update", "method": "POST"}, {"path": "/plan", "service": true}, {"path": "/product", "service": true}, {"path": "/purchaseOrder", "service": true}]}, {"name": "PmsProductionSchedulePlanInfoEntity", "prefix": "/admin/pms/production/schedule/plan", "api": [{"path": "/add", "method": "POST"}, {"path": "/create", "method": "POST"}, {"path": "/delete", "method": "POST"}, {"path": "/info", "method": "GET"}, {"path": "/list", "method": "POST"}, {"path": "/page", "method": "POST"}, {"path": "/update", "method": "POST"}]}, {"name": "PmsProductionScheduleProductInfoEntity", "prefix": "/admin/pms/production/schedule/product", "api": [{"path": "/add", "method": "POST"}, {"path": "/delete", "method": "POST"}, {"path": "/info", "method": "GET"}, {"path": "/list", "method": "POST"}, {"path": "/page", "method": "POST"}, {"path": "/update", "method": "POST"}]}, {"name": "PmsProductionSchedulePurchaseOrderInfoEntity", "prefix": "/admin/pms/production/schedule/purchaseOrder", "api": [{"path": "/detail", "method": "GET"}, {"path": "/page", "method": "POST"}]}, {"name": "PmsProductionDataIncomingInfoEntity", "prefix": "/admin/pms/productionData/incoming", "api": [{"path": "/add", "method": "POST"}, {"path": "/delete", "method": "POST"}, {"path": "/import", "method": "POST"}, {"path": "/info", "method": "GET"}, {"path": "/list", "method": "POST"}, {"path": "/listByMonth", "method": "POST"}, {"path": "/materialList", "method": "POST"}, {"path": "/page", "method": "POST"}, {"path": "/supplierList", "method": "POST"}, {"path": "/update", "method": "POST"}]}, {"name": "PmsProductionDataProcessAbnormalityInfoEntity", "prefix": "/admin/pms/productionData/processAbnormality", "api": [{"path": "/add", "method": "POST"}, {"path": "/delete", "method": "POST"}, {"path": "/getProductList", "method": "GET"}, {"path": "/import", "method": "POST"}, {"path": "/info", "method": "GET"}, {"path": "/list", "method": "POST"}, {"path": "/page", "method": "POST"}, {"path": "/queryDeptList", "method": "GET"}, {"path": "/update", "method": "POST"}]}, {"name": "PmsPurchaseContractInfoEntity", "prefix": "/admin/pms/purchase/contract", "api": [{"path": "/add", "method": "POST"}, {"path": "/delete", "method": "POST"}, {"path": "/getContractListByPoAndSupplierId", "method": "GET"}, {"path": "/getUnfinishedPo", "method": "GET"}, {"path": "/importContractExcelData", "method": "POST"}, {"path": "/info", "method": "GET"}, {"path": "/list", "method": "POST"}, {"path": "/page", "method": "POST"}, {"path": "/purchaseContractImport", "method": "POST"}, {"path": "/purchaseContractOutboundImport", "method": "POST"}, {"path": "/queryPage", "method": "POST"}, {"path": "/update", "method": "POST"}, {"path": "/updateContractStatus", "method": "POST"}]}, {"name": "PmsPurchaseOrderInfoEntity", "prefix": "/admin/pms/purchase/order", "api": [{"path": "/add", "method": "POST"}, {"path": "/auditData", "method": "GET"}, {"path": "/auditLog", "method": "GET"}, {"path": "/confirm", "method": "POST"}, {"path": "/contract", "method": "GET"}, {"path": "/createContract", "method": "POST"}, {"path": "/delete", "method": "POST"}, {"path": "/deleteSuborder", "method": "POST"}, {"path": "/downloadContract", "method": "POST"}, {"path": "/export", "method": "GET"}, {"path": "/exportDetail", "method": "GET"}, {"path": "/exportSummary", "method": "GET"}, {"path": "/getInboundReceivedQuantity", "method": "GET"}, {"path": "/info", "method": "GET"}, {"path": "/list", "method": "POST"}, {"path": "/page", "method": "POST"}, {"path": "/rejectProduction", "method": "POST"}, {"path": "/removeContract", "method": "POST"}, {"path": "/revoke", "method": "POST"}, {"path": "/saveContract", "method": "POST"}, {"path": "/summary", "method": "GET"}, {"path": "/transfer", "method": "POST"}, {"path": "/update", "method": "POST"}, {"path": "/detail", "service": true}, {"path": "/pending", "service": true}]}, {"name": "PmsPurchaseOrderDetailInfoEntity", "prefix": "/admin/pms/purchase/order/detail", "api": [{"path": "/add", "method": "POST"}, {"path": "/delete", "method": "POST"}, {"path": "/info", "method": "GET"}, {"path": "/list", "method": "POST"}, {"path": "/page", "method": "POST"}, {"path": "/update", "method": "POST"}]}, {"name": "PmsPurchaseOrderPendingInfoEntity", "prefix": "/admin/pms/purchase/order/pending", "api": [{"path": "/add", "method": "POST"}, {"path": "/delete", "method": "POST"}, {"path": "/export", "method": "POST"}, {"path": "/info", "method": "GET"}, {"path": "/list", "method": "POST"}, {"path": "/page", "method": "POST"}, {"path": "/update", "method": "POST"}]}, {"name": "PmsSaleDeliveryOrderInfoEntity", "prefix": "/admin/pms/sale/delivery/order", "api": [{"path": "/add", "method": "POST"}, {"path": "/confirm", "method": "POST"}, {"path": "/delete", "method": "POST"}, {"path": "/info", "method": "GET"}, {"path": "/list", "method": "POST"}, {"path": "/page", "method": "POST"}, {"path": "/update", "method": "POST"}]}, {"name": "PmsSaleOrderInfoEntity", "prefix": "/admin/pms/sale/order", "api": [{"path": "/add", "method": "POST"}, {"path": "/arrived", "method": "POST"}, {"path": "/confirm", "method": "POST"}, {"path": "/delete", "method": "POST"}, {"path": "/download", "method": "POST"}, {"path": "/downloadSign", "method": "POST"}, {"path": "/export", "method": "POST"}, {"path": "/info", "method": "GET"}, {"path": "/list", "method": "POST"}, {"path": "/page", "method": "POST"}, {"path": "/update", "method": "POST"}]}, {"name": "PmsSupplierInfoEntity", "prefix": "/admin/pms/supplier", "api": [{"path": "/add", "method": "POST"}, {"path": "/delete", "method": "POST"}, {"path": "/exportSupplier", "method": "GET"}, {"path": "/info", "method": "GET"}, {"path": "/list", "method": "POST"}, {"path": "/page", "method": "POST"}, {"path": "/update", "method": "POST"}]}, {"name": "PmsSupplierAccountInfoEntity", "prefix": "/admin/pms/supplier_account", "api": [{"path": "/add", "method": "POST"}, {"path": "/bindSupplier", "method": "POST"}, {"path": "/delete", "method": "POST"}, {"path": "/getSupplierAccountList", "method": "GET"}, {"path": "/getUserBindSupplier", "method": "GET"}, {"path": "/getUserRole", "method": "POST"}, {"path": "/info", "method": "GET"}, {"path": "/list", "method": "POST"}, {"path": "/page", "method": "POST"}, {"path": "/update", "method": "POST"}]}, {"name": "PmsWarehouseDestinationInboundInfoEntity", "prefix": "/admin/pms/warehouse/destination/inbound", "api": [{"path": "/add", "method": "POST"}, {"path": "/confirm", "method": "POST"}, {"path": "/delete", "method": "POST"}, {"path": "/export", "method": "POST"}, {"path": "/finish", "method": "POST"}, {"path": "/info", "method": "GET"}, {"path": "/list", "method": "POST"}, {"path": "/page", "method": "POST"}, {"path": "/start", "method": "POST"}, {"path": "/update", "method": "POST"}]}, {"name": "PmsWarehouseDestinationOutboundInfoEntity", "prefix": "/admin/pms/warehouse/destination/outbound", "api": [{"path": "/add", "method": "POST"}, {"path": "/complete", "method": "POST"}, {"path": "/delete", "method": "POST"}, {"path": "/info", "method": "GET"}, {"path": "/list", "method": "POST"}, {"path": "/page", "method": "POST"}, {"path": "/ship", "method": "POST"}, {"path": "/update", "method": "POST"}]}, {"name": "PmsWarehouseDestinationStockInfoEntity", "prefix": "/admin/pms/warehouse/destination/stock", "api": [{"path": "/add", "method": "POST"}, {"path": "/delete", "method": "POST"}, {"path": "/export", "method": "POST"}, {"path": "/info", "method": "GET"}, {"path": "/list", "method": "POST"}, {"path": "/page", "method": "POST"}, {"path": "/update", "method": "POST"}, {"path": "/record", "service": true}]}, {"name": "PmsWarehouseDestinationStockRecordInfoEntity", "prefix": "/admin/pms/warehouse/destination/stock/record", "api": [{"path": "/add", "method": "POST"}, {"path": "/delete", "method": "POST"}, {"path": "/info", "method": "GET"}, {"path": "/list", "method": "POST"}, {"path": "/page", "method": "POST"}, {"path": "/update", "method": "POST"}]}, {"name": "PmsWarehousePointInfoEntity", "prefix": "/admin/pms/warehouse/point", "api": [{"path": "/add", "method": "POST"}, {"path": "/delete", "method": "POST"}, {"path": "/info", "method": "GET"}, {"path": "/list", "method": "POST"}, {"path": "/page", "method": "POST"}, {"path": "/update", "method": "POST"}]}, {"name": "PmsWarehouseSourceInboundInfoEntity", "prefix": "/admin/pms/warehouse/source/inbound", "api": [{"path": "/add", "method": "POST"}, {"path": "/compareInboundAndOutbound", "method": "POST"}, {"path": "/complete", "method": "POST"}, {"path": "/confirm", "method": "POST"}, {"path": "/delete", "method": "POST"}, {"path": "/download", "method": "POST"}, {"path": "/info", "method": "GET"}, {"path": "/list", "method": "POST"}, {"path": "/page", "method": "POST"}, {"path": "/revoke", "method": "POST"}, {"path": "/update", "method": "POST"}]}, {"name": "PmsWarehouseSourceOutboundInfoEntity", "prefix": "/admin/pms/warehouse/source/outbound", "api": [{"path": "/add", "method": "POST"}, {"path": "/delete", "method": "POST"}, {"path": "/export", "method": "POST"}, {"path": "/finish", "method": "POST"}, {"path": "/info", "method": "GET"}, {"path": "/list", "method": "POST"}, {"path": "/pack", "method": "POST"}, {"path": "/page", "method": "POST"}, {"path": "/revoke", "method": "POST"}, {"path": "/ship", "method": "POST"}, {"path": "/update", "method": "POST"}]}, {"name": "PmsWorkOrderInfoEntity", "prefix": "/admin/pms/workOrder", "api": [{"path": "/add", "method": "POST"}, {"path": "/addWorkOrder", "method": "POST"}, {"path": "/complete", "method": "POST"}, {"path": "/delWorkOrder", "method": "POST"}, {"path": "/delete", "method": "POST"}, {"path": "/info", "method": "GET"}, {"path": "/list", "method": "POST"}, {"path": "/page", "method": "POST"}, {"path": "/queryPage", "method": "POST"}, {"path": "/update", "method": "POST"}, {"path": "/updateWorkOrder", "method": "POST"}]}, {"name": "SpaceInfoEntity", "prefix": "/admin/space/info", "api": [{"path": "/add", "method": "POST"}, {"path": "/delete", "method": "POST"}, {"path": "/getConfig", "method": "GET"}, {"path": "/info", "method": "GET"}, {"path": "/list", "method": "POST"}, {"path": "/page", "method": "POST"}, {"path": "/update", "method": "POST"}]}, {"name": "SpaceTypeInfoEntity", "prefix": "/admin/space/type", "api": [{"path": "/add", "method": "POST"}, {"path": "/delete", "method": "POST"}, {"path": "/info", "method": "GET"}, {"path": "/list", "method": "POST"}, {"path": "/page", "method": "POST"}, {"path": "/update", "method": "POST"}]}, {"name": "TaskInfoEntity", "prefix": "/admin/task/info", "api": [{"path": "/add", "method": "POST"}, {"path": "/delete", "method": "POST"}, {"path": "/info", "method": "GET"}, {"path": "/list", "method": "POST"}, {"path": "/log", "method": "GET"}, {"path": "/once", "method": "GET"}, {"path": "/page", "method": "POST"}, {"path": "/start", "method": "GET"}, {"path": "/stop", "method": "GET"}, {"path": "/update", "method": "POST"}]}]