import{_ as re}from"./material-excel-import.vue_vue_type_script_setup_true_lang-CuhJXQJh.js";import{_ as la}from"./material-selector.vue_vue_type_script_name_product-selector_setup_true_lang-zZ4LX5YI.js";import{_ as ta}from"./material-table-columns.vue_vue_type_script_setup_true_lang-BFX8eStE.js";import{_ as ua}from"./purchase-pending-order.vue_vue_type_script_setup_true_lang-ARi7Gu3g.js";import{c as ee,l as na,q as m,m as da,v as p,o as v,ae as ra,b as g,A as oe,e as oa,f as I,y as h,h as s,w as c,B as b,i as $,t as M,j as q,F as N,s as E,W as ie,a_ as ia,E as x,T as S,U as P,af as sa,ag as va}from"./.pnpm-hVqhwuVC.js";import{n as ke}from"./index-CBanFtSc.js";import{_ as qe}from"./select-dict.vue_vue_type_script_setup_true_name_select-dict_lang-uUMI05Bz.js";/* empty css              */import{_ as ca}from"./OutboundTable.vue_vue_type_script_setup_true_name_OutboundTable_lang-DQuvyBaK.js";import pa from"./index-qPlz9-2i.js";import"./index-DkYL1aws.js";import{a as ma}from"./index-C6cm1h61.js";import{_ as ya}from"./_plugin-vue_export-helper-DlAUqK2U.js";const fa=ee({name:"undefined"}),ba=ee({...fa,setup(O){const A=na();return(Q,se)=>(v(),m(ua,da({"is-selector":"","page-size":10},p(A)),null,16))}}),K=O=>(sa("data-v-d87e0921"),O=O(),va(),O),ha={class:"cl-crud inbound-create"},_a={class:"order-create-body"},wa=K(()=>h("div",{class:"inbound-create-header"}," 基础信息 ",-1)),ga={class:"is-order-label"},Ia={key:0},xa={key:1},ka={key:0,"text-green-6":""},qa={key:1,"text-red":""},Va={key:0,flex:"~"},$a={"text-green-6":""},Qa={pl:"50px"},Ma=K(()=>h("span",{pr:"12px"},"工单号",-1)),Ca={"text-green-6":""},Sa={pl:"50px"},Ua=K(()=>h("span",{pr:"12px"},"产品名称",-1)),Ta={"text-green-6":""},Oa={pl:"50px"},za=K(()=>h("span",{pr:"12px"},"计划生产数量",-1)),Ba={"text-green-6":""},Da={key:1,"text-red":""},Na={class:"inbound-create-header"},Ea={flex:"~ justify-between items-center"},Pa={key:0,flex:"~ "},Ka={key:1,flex:"~ items-center"},Aa={key:2,flex:"~ items-center"},La={key:3,flex:"~"},Fa={key:4,flex:"~"},Ra={key:0,style:{display:"flex","align-items":"center"}},Ya={key:1,style:{display:"flex","align-items":"center"}},Wa={key:2,style:{display:"flex","align-items":"center"}},ja={key:3,style:{display:"flex","align-items":"center"}},Ga=K(()=>h("span",{style:{color:"var(--el-color-danger)"}},"*",-1)),Ha=K(()=>h("span",{style:{color:"var(--el-color-danger)"}},"*",-1)),Ja={flex:"~ items-center"},Xa={class:"dialog-footer"},Za=ee({name:"undefined"}),el=ee({...Za,props:{type:{}},setup(O){const A=O,Q=88,{height:se}=ra(),{service:L,router:F}=ma(),V=g(!1),i=A.type,ve=g([]),ae=A.type==="inbound"?"入库":"出库",Ve=A.type==="inbound"?0:-1,$e=0,R=g({}),t=g({id:null,type:i==="outbound"?0:1,orderId:0,remark:"",materials:[],productId:0,workOrder:"",workOrderId:0}),H=g(!1),U=g(""),Qe=g(!1);oe(()=>{i==="outbound"&&(H.value=t.value.type===1&&t.value.orderId>0)});const Me=g({buttons:["slot-btn-outbound"],width:160}),ce=i==="inbound"?"/pms/material/inbound":"/pms/material/outbound",le=i==="inbound"?L.pms.material.inbound:L.pms.material.outbound,z=g(!1),Y=g(!1),J=g(!1),te=g(),B=g(!1),X=g(!1),W=g(!1),pe=g(void 0),j=g(!1),d=g([]),G=g([]),y=g([]),D=g(!1);async function Ce(){te.value&&await te.value.validate(a=>{if(a){if(Y.value=!0,J.value=!0,d.value.length===0){x.error({message:"请添加物料"});return}if(d.value=d.value.filter(l=>l.id||l.materialId),d.value.find(l=>t.value.type===2?!1:l.quantity===null||l.quantity===void 0||l.quantity===0||l.quantity<0)){x.error({message:"请填写正确的物料数量"});return}if(i==="outbound"&&t.value.type===3&&d.value.find(e=>e.total_quantity===null||e.total_quantity===void 0||e.total_quantity===0||e.total_quantity<0||e.total_quantity<e.quantity)){x.error({message:"请填写正确的物料总量"});return}i==="inbound"&&d.value.forEach(l=>{if(!l.warehouseId)throw x.error({message:"请选择仓库"}),new Error("请选择仓库")}),Y.value=!1,J.value=!1,t.value.materials=d.value.map(l=>(Array.isArray(l.address)&&(l.address=l.address.join(",")),{materiaLdetail:l,contractId:t.value.type===1?l.contractId:null,materialId:t.value.type===1?l.materialId:l.id,quantity:l.quantity,warehouseId:l.warehouseId,address:l.address,scrap_date:l.scrap_date,product_group_id:l.product_group_id,responsible:l.responsible,handling_method:l.handling_method,inbound_outbound_key:l.inbound_outbound_key,total_quantity:l.total_quantity})),z.value=!0,V.value&&t.value.id&&t.value.id>0?le.update(t.value).then(()=>{F.push(`${ce}?tab=0`),x.success({message:"保存成功",onClose:()=>{z.value=!1}})}).catch(l=>{x.error({message:l.message}),z.value=!1}):(delete t.value.id,le.add(t.value).then(l=>{const e=l==null?void 0:l.id;F.push(`${ce}?tab=0&expand=${e}`),x.success({message:"保存成功",onClose:()=>{z.value=!1}})}).catch(l=>{x.error({message:l.message}),z.value=!1}))}})}function Se(a){if(a)return D.value=!0,L.pms.material.list({keyWord:a}).then(u=>{u=u||[];const l=new Set(d.value.map(k=>k.id)),e=y.value.filter(k=>l.has(k.value)),_=[...u.map(k=>({...k,value:k.id,uniqueKey:`${k.id}`,label:`${k.name}`,disabled:d.value.some(T=>T.id===k.id)})),...e],w=new Set,f=_.filter(k=>w.has(k.id)?!1:(w.add(k.id),!0));y.value=f}).finally(()=>{D.value=!1})}const me=g(null);function ye(){G.value=[],B.value=!0}function Ue(){G.value=[],X.value=!0}const Te=oa(()=>t.value.type===9?1:4);function Oe(a){const u=y.value.find(e=>(e.address=e.address_name!==""&&e.address_name!==void 0?e.address_name.split(","):"",Number.parseInt(e.uniqueKey)===a));if(!u)return;const l=d.value.findIndex(e=>e.id===a);y.value=y.value.map(e=>(Number.parseInt(e.uniqueKey)!==a&&(e.disabled=!1),e)),l!==-1?d.value[l]={...u,index:d.value[l].index,contractId:u.value,id:u.value,warehouseId:u.warehouseId||Q}:d.value.push({...u,index:new Date().getTime()+Math.floor(Math.random()*1e3),id:u.value,contractId:u.value,quantity:null,warehouseId:u.warehouseId||Q})}function ze(a){const u=y.value.find(e=>(e.address=e.address_name!==""&&e.address_name!==void 0?e.address_name.split(","):"",Number.parseInt(e.materialId)===a));if(!u)return;const l=d.value.findIndex(e=>e.materialId===a);y.value=y.value.map(e=>(Number.parseInt(e.materialId)!==a&&(e.disabled=!1),e)),l!==-1?d.value[l]={...u,index:d.value[l].index,contractId:u.value,id:u.value,warehouseId:u.warehouseId||Q}:d.value.push({...u,index:new Date().getTime()+Math.floor(Math.random()*1e3),id:u.value,contractId:u.value,quantity:null,warehouseId:u.warehouseId||Q})}function fe(a){const u=y.value.find(e=>(e.address=e.address_name!==""&&e.address_name!==void 0?e.address_name.split(","):"",e.value===a));if(!u)return;const l=d.value.findIndex(e=>e.id===a);y.value=y.value.map(e=>(e.value!==a&&(e.disabled=!1),e)),l!==-1?d.value[l]={...u,index:d.value[l].index,id:u.value,warehouseId:u.warehouseId||Q}:d.value.push({...u,index:new Date().getTime()+Math.floor(Math.random()*1e3),id:u.value,quantity:null,warehouseId:u.warehouseId||Q})}function Be(){j.value=!0}function De(){B.value=!1,G.value.forEach(a=>{if(!d.value.find(l=>l.id===a.id)){y.value.push({...a,value:a.id,disabled:!1,label:`${a.name}`}),a.address=a.address_name!==""&&a.address_name!==void 0?a.address_name.split(","):"";const l={...a,index:new Date().getTime()+Math.floor(Math.random()*1e3),orderQuantity:a.quantity,quantity:null,warehouseId:0,address_arr:a.address_name!==""&&a.address_name!==void 0?a.address_name.split(","):[]};i==="inbound"&&(l.warehouseId=Q),d.value.push(l)}})}function Ne(a){d.value=d.value.filter(u=>u.index!==a)}function be(){!(t.value.type===1&&t.value.orderId)&&!(i==="outbound"&&t.value.type===0)&&!(i==="outbound"&&t.value.type===2)&&!(i==="outbound"&&t.value.type===5)&&!(i==="inbound"&&t.value.type===3)&&!(i==="inbound"&&t.value.type===4)&&!(i==="inbound"&&t.value.type===9)&&(y.value=[]);const a={index:new Date().getTime()+Math.floor(Math.random()*1e3),id:null,name:"",model:"",inventory:0,unit:"",code:"",expectedInbound:0,quantity:null,coverColor:"",size:"",material:"",process:"",receivedQuantity:0,orderQuantity:0,returnOrderQuantity:0,restockingQty:0,transfer:0,uniqueKey:"",warehouseId:0};i==="inbound"&&(a.warehouseId=Q),d.value.push(a)}function Ee(){S.confirm("确定清空已选择的物料列表吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{d.value=[],me.value.resetData()}).catch(()=>{})}function Pe(a){le.info({id:a}).then(async u=>{t.value.type=(u==null?void 0:u.type)||0,t.value.remark=(u==null?void 0:u.remark)||"";const l=(u==null?void 0:u.orderId)||0;t.value.orderId=l;const e=(u==null?void 0:u.products)||[];e.forEach(o=>{o.Po&&!o.po&&(o.po=o.Po)}),l>0&&t.value.type!==4&&t.value.type!==9?(i==="outbound"&&(t.value.type===0||t.value.type===5)?(await he(u),d.value=e==null?void 0:e.map(o=>{const _=y.value.find(w=>w.id===o.materialId||w.materialId===o.materialId);return{...o,..._,id:o.materialId,index:new Date().getTime()+Math.floor(Math.random()*1e3),uniqueKey:o.contractId}})):i==="inbound"&&t.value.type===3?(await he(u),d.value=e==null?void 0:e.map(o=>{const _=y.value.find(w=>w.id===o.materialId||w.materialId===o.materialId);return{...o,..._,id:o.materialId,index:new Date().getTime()+Math.floor(Math.random()*1e3),uniqueKey:o.contractId}})):await _e(l.toString(),!1),d.value=e==null?void 0:e.map(o=>({...y.value.find(w=>w.materialId===o.materialId),...o,index:new Date().getTime()+Math.floor(Math.random()*1e3),uniqueKey:o.contractId}))):(y.value=e==null?void 0:e.map(o=>{const _=P(o);return{...o,returnOrderQuantity:_.returnOrderQuantity,value:o.materialId?o.materialId:o.id,uniqueKey:o.contractId??o.id,orderQuantity:o.quantity,label:`${o.name}`}}),d.value=e==null?void 0:e.map(o=>{const _=P(o);return{...o,returnOrderQuantity:_.returnOrderQuantity,id:o.materialId,index:new Date().getTime()+Math.floor(Math.random()*1e3),orderQuantity:o.quantity,uniqueKey:o.id}})),d.value=d.value.map(o=>(o.address&&(o.address=o.address.split(",")),o))}).catch(u=>{x.error({message:u.message||"获取入库单信息失败"})})}async function he(a){try{Ke(a.extras,a.workOrder,a.products)}catch(u){x.error({message:u.message||"获取生产订单信息失败"})}}function Ke(a,u,l){!a||!u||!l||(R.value=P(u),U.value=u.productionScheduleSn,t.value.workOrderId=u.id,d.value.length>0&&S.confirm("当前物料列表中存在已选数据，如果确定，让先清除列表中的数据，是否继续").then(e=>{e&&(d.value=[])}).catch(()=>{}),d.value=[],y.value=a.map(e=>({...e,value:e.id,materialId:e.id,uniqueKey:`${e.id}`,label:`${e.name}`,index:new Date().getTime()+Math.floor(Math.random()*1e3),disabled:d.value.some(o=>o.id===e.id)})))}async function _e(a,u=!0){try{const l=await L.pms.purchase.order.pending.info({id:a});await ge(l,u)}catch(l){x.error({message:l.message||"获取采购单信息失败"})}}function we(a,u){const l=e=>{x.error({message:e}),d.value=[]};if(d.value.length>0){x.error({message:"当前物料列表中存在已选数据，如需导入，请先清除列表中的数据"});return}u.some(e=>{const o=y.value.find(_=>_.code.toUpperCase().trim()===e.code.toUpperCase().trim());if(!o||!o.materialId)return l(`物料编码：${e.code} 不在采购单中`),!0;if(d.value.some(_=>_.id===o.value))return l(`物料编码：${e.code} 重复添加`),!0;if(e.quantity===null||e.quantity===void 0||e.quantity===0)return l(`物料编码：${e.code} 数量不能为空`),!0;if(i==="outbound"){if(H.value&&e.quantity>o.receivedQuantity)return l(`物料编码：${e.code} 数量不能大于已收数量，退货数量：${e.quantity}，已收数量：${o.receivedQuantity}`),!0}else{const _=ke(Math.max(o.orderQuantity-o.transfer-o.receivedQuantity,0));if(e.quantity>_+$e)return l(`物料编码：${e.code} 数量不能大于可入库数量，入库数量：${e.quantity}，可入库数量：${_}`),!0}return d.value.push({...o,index:new Date().getTime()+Math.floor(Math.random()*1e3),id:o.value,quantity:e.quantity}),!1})}const ue=g(!1);function Ae(a,u){const l=a.map(e=>{var w;if(d.value.some(f=>f.id===e.id))return!1;const o=(w=u.find(f=>f.code===e.code))==null?void 0:w.quantity,_={...e,index:new Date().getTime()+Math.floor(Math.random()*1e3),uniqueKey:e.id,orderQuantity:e.quantity,quantity:o};return i==="inbound"&&(_.warehouseId=Q),_});ue.value=!0,d.value.push(...l)}function Le(){W.value=!0,i==="outbound"&&t.value.type===1&&(pe.value=0)}const C=g(!1);async function ge(a,u=!0){if(!a)return;if(t.value.orderId=a.id,U.value=a.orderNo,Qe.value=a.parentOrderId>0,d.value.length>0)try{if(!await S.confirm("当前物料列表中存在已选数据，如果确定，让先清除列表中的数据，是否继续"))return}catch{return}if(d.value=[],u)try{const e=await S.confirm("是否需要自动填充该订单的全部物料","提示",{confirmButtonText:"自动填充",cancelButtonText:"手动输入",type:"warning",showClose:!1});C.value=e==="confirm"}catch{C.value=!1,d.value=[],ne()}const l=a==null?void 0:a.contracts.map(e=>(e.address=e.address_name!==""&&e.address_name!==void 0?e.address_name.split(","):"",{...e,contractId:e.id,quantity:null,uniqueKey:e.id,orderQuantity:e.quantity,index:new Date().getTime()+Math.floor(Math.random()*1e3)}));y.value=l.map(e=>{const o=e.id;return{...e,value:o,uniqueKey:o,label:`${e.name}`,disabled:C.value}}),C.value&&(i==="outbound"?d.value=l.filter(e=>e.receivedQuantity>0):d.value=l,i==="inbound"&&d.value.length>0&&d.value.forEach(e=>{e.address=e.address_name!==""&&e.address_name!==void 0?e.address_name.split(","):"",e.warehouseId||(e.warehouseId=86)}))}function ne(a=10){for(let u=0;u<a;u++)be()}async function Fe(a){await ge(a),W.value=!1}async function Re(a){R.value=P(a),await Ye(a),j.value=!1}async function Ye(a,u=!0){var l;if(t.value.orderId=a.productionScheduleId,t.value.workOrderId=a.id,t.value.workOrder=a.workOrderNo,U.value=a.sn,!a.extras){x.error({message:"该生产订单没有物料"});return}if(d.value.length>0)try{if(!await S.confirm("当前物料列表中存在已选数据，如果确定，让先清除列表中的数据，是否继续"))return}catch{return}if(y.value=[],d.value=[],y.value=(l=a.extras)==null?void 0:l.map(e=>(e.address=e.address_name!==""&&e.address_name!==void 0?e.address_name.split(","):"",{...e,value:e.id,materialId:e.id,uniqueKey:`${e.id}`,label:`${e.name}`,index:new Date().getTime()+Math.floor(Math.random()*1e3),disabled:d.value.some(o=>o.id===e.id)})),u){try{await S.confirm("是否需要自动填充该订单的全部物料","提示",{confirmButtonText:"自动填充",cancelButtonText:"手动输入",type:"warning",showClose:!1}),C.value=!0}catch{ne(),C.value=!1}C.value&&(i==="outbound"?(d.value=P(y.value),d.value=d.value.filter(e=>e.inventory>0),t.value.type===0&&(d.value.forEach(e=>{e.quantity=e.calcBomQuantity-e.outboundQuantity>e.inventory?e.inventory:e.calcBomQuantity-e.outboundQuantity}),d.value=d.value.filter(e=>e.outboundQuantity!==e.calcBomQuantity))):i==="inbound"&&t.value.type===3&&(d.value=P(y.value),d.value=d.value.filter(e=>e.outboundQuantity>0),d.value.length>0&&d.value.forEach(e=>{e.warehouseId||(e.warehouseId=Q)})))}}async function We(a){if(a!==t.value.type){if(d.value.length===0){t.value.type=a;return}try{await S.confirm("切换入库类型后，已选择的物料将被清空，是否继续")&&(t.value.orderId=null,U.value="",t.value.type=a,t.value.orderId=null,d.value=[])}catch{}}}function je(a){let u=!1;(a.quantity>a.total_quantity||a.total_quantity==0||!a.total_quantity)&&(x.error({message:"请重新输入物料总量"}),u=!0),u&&(a.total_quantity=null,J.value=!0)}function Ie(a){let u=!1;if(i==="outbound")[0,2,3,4,5].includes(t.value.type)?a.quantity>a.inventory&&(x.error({message:"出库数量不能大于库存数量"}),u=!0):H.value&&t.value.orderId&&a.quantity>a.receivedQuantity&&(x.error({message:"退货数量不能大于已收数量"}),u=!0);else if(i==="inbound"){if(t.value.type===1||t.value.type===5){const l=xe(a);a.quantity>l&&(x.error({message:"入库数量不能大于可入库数量"}),u=!0)}if(t.value.type===3&&a.quantity>a.outQty&&(x.error({message:"退料数量不能大于领料数量"}),u=!0),t.value.type===4||t.value.type===9){const l=a.returnOrderQuantity-a.restockingQty;a.quantity>l&&(x.error({message:"入库数量不能大于可入库数量"}),u=!0)}}u&&(a.quantity=null,Y.value=!0)}function Ge({row:a}){return!a.id||t.value.type!==1&&t.value.type!==5?"":a.receivedQuantity>=a.orderQuantity-a.transfer?"success-row":""}oe(()=>{var u;const a=(u=d.value)==null?void 0:u.map(l=>l.uniqueKey);y.value=y.value.map(l=>(a.includes(l.uniqueKey)?l.disabled=!0:l.disabled=!1,l)),d.value=d.value.map(l=>(l.address_arr=l.address_name!==""&&l.address_name!==void 0?l.address_name.split(","):[],l.address=l.address?l.address:"",l))}),oe(()=>{Je();const a=F.currentRoute.value.query.id,u=F.currentRoute.value.query.orderId;F.currentRoute.value.query.return==="1"&&(t.value.type=1,H.value=!0),a?(V.value=!0,t.value.id=Number.parseInt(a),Pe(a)):u&&_e(u)});function xe(a){let u=0;return a.transfer>0?u=a.orderQuantity-a.transfer-a.receivedQuantity:u=a.orderQuantity-a.receivedQuantity,u>0?ke(u):0}async function He(a){var l;const{row:u}=a;if(u.products.length===0)return x.error({message:"该退货单没有物料"});if(t.value.orderId=u.id,d.value.length>0)try{if(!await S.confirm("当前物料列表中存在已选数据，如果确定，让先清除列表中的数据，是否继续"))return}catch{return}y.value=[],d.value=[],y.value=(l=u.products)==null?void 0:l.map(e=>({...e,id:e.materialId,value:e.materialId,materialId:e.materialId,uniqueKey:`${e.materialId}`,label:`${e.name}`,returnOrderQuantity:e.quantity,quantity:0,index:new Date().getTime()+Math.floor(Math.random()*1e3),disabled:d.value.some(o=>o.id===e.id)}));try{await S.confirm("是否需要自动填充该订单的全部物料","提示",{confirmButtonText:"自动填充",cancelButtonText:"手动输入",type:"warning",showClose:!1}),C.value=!0}catch{ne(),C.value=!1}C.value&&(d.value=y.value.filter(e=>e.orderQuantity>0),d.value.length>0&&d.value.forEach(e=>{e.warehouseId||(e.warehouseId=Q)})),d.value.map(e=>{e.address=e.address_name!==""&&e.address_name!==void 0?e.address_name.split(","):""}),X.value=!1}async function Je(){try{ve.value=await L.pms.product.group.request({url:"/list",method:"POST"})}catch(a){console.error(a)}}return(a,u)=>{const l=$("el-radio"),e=$("el-radio-group"),o=$("el-form-item"),_=$("el-input"),w=$("el-button"),f=$("el-table-column"),k=$("el-option"),T=$("el-select"),de=$("el-input-number"),Xe=$("el-date-picker"),Ze=$("el-table"),ea=$("el-row"),Z=$("el-dialog");return v(),I("div",null,[h("div",ha,[s(p(ia),{ref_key:"inboundForm",ref:te,model:t.value,"label-width":"90px",size:"large","status-icon":""},{default:c(()=>[h("div",_a,[wa,s(o,{label:`${p(ae)}类型`,prop:"type"},{label:c(()=>[h("div",ga,[h("span",null,M(`${p(ae)}类型`),1)])]),default:c(()=>[s(e,{"model-value":t.value.type,mr:"50px","onUpdate:modelValue":We},{default:c(()=>[p(i)==="inbound"?(v(),I("div",Ia,[s(l,{value:2,label:"盘盈调帐",disabled:V.value},null,8,["disabled"]),s(l,{value:3,label:"生产退料",disabled:V.value},null,8,["disabled"]),s(l,{value:0,label:"生产退良品",disabled:V.value},null,8,["disabled"]),s(l,{value:1,label:"采购单入库",disabled:V.value},null,8,["disabled"]),s(l,{value:4,label:"库存退货入库",disabled:V.value},null,8,["disabled"]),s(l,{value:8,label:"备品入库",disabled:V.value},null,8,["disabled"]),s(l,{value:9,label:"补退货",disabled:V.value},null,8,["disabled"])])):b("",!0),p(i)==="outbound"?(v(),I("div",xa,[s(l,{value:0,label:"领料出库",disabled:V.value},null,8,["disabled"]),s(l,{value:1,label:"退货出库",disabled:V.value},null,8,["disabled"]),s(l,{value:4,label:"库存退货出库",disabled:V.value},null,8,["disabled"]),s(l,{value:2,label:"其他领用",disabled:V.value},null,8,["disabled"]),s(l,{value:3,label:"报废",disabled:V.value},null,8,["disabled"]),s(l,{value:5,label:"委外出库",disabled:V.value},null,8,["disabled"])])):b("",!0)]),_:1},8,["model-value"]),t.value.type===1?(v(),m(o,{key:0,label:"采购单号",prop:"orderId",pb:"7px"},{default:c(()=>[U.value?(v(),I("div",ka,M(U.value),1)):(v(),I("div",qa," 请先选择采购单 "))]),_:1})):b("",!0),p(i)==="outbound"&&(t.value.type===0||t.value.type===5)||p(i)==="inbound"&&t.value.type===3?(v(),m(o,{key:1,pb:"7px",label:"生产单号",prop:"orderId"},{default:c(()=>[U.value?(v(),I("div",Va,[h("span",$a,M(U.value),1),h("div",Qa,[Ma,h("span",Ca,M(R.value.workOrderNo),1)]),h("div",Sa,[Ua,h("span",Ta,M(R.value.productName),1)]),h("div",Oa,[za,h("span",Ba,M(R.value.quantity),1)])])):(v(),I("div",Da," 请先选择生产订单 "))]),_:1})):b("",!0)]),_:1},8,["label"]),s(o,{label:"备注",prop:"remark",style:{width:"550px"},pb:"7px"},{default:c(()=>[s(_,{modelValue:t.value.remark,"onUpdate:modelValue":u[0]||(u[0]=r=>t.value.remark=r),type:"textarea",rows:2},null,8,["modelValue"])]),_:1}),h("div",Na,M(`物料${p(ae)}信息`),1),h("div",Ea,[p(i)==="inbound"&&t.value.type===0||p(i)==="inbound"&&t.value.type===8||t.value.type===2||t.value.type===3&&p(i)==="outbound"?(v(),I("div",Pa,[s(w,{type:"primary",class:"mb-10px mr-10px",size:"default",onClick:ye},{default:c(()=>[q(" 选择物料 ")]),_:1}),s(re,{onSuccess:Ae})])):b("",!0),p(i)==="outbound"&&(t.value.type===0||t.value.type===5)||p(i)==="inbound"&&t.value.type===3?(v(),I("div",Ka,[s(w,{disabled:t.value.orderId>0&&d.value.length>0,type:"primary",class:"mb-10px mr-10px",size:"default",onClick:Be},{default:c(()=>[q(" 选择工单 ")]),_:1},8,["disabled"]),s(re,{disabled:!t.value.orderId,onSuccess:we},null,8,["disabled"])])):b("",!0),t.value.type===1?(v(),I("div",Aa,[s(w,{type:"primary",disabled:t.value.orderId>0&&d.value.length>0,class:"mb-10px mr-10px",size:"default",onClick:Le},{default:c(()=>[q(" 选择采购单 ")]),_:1},8,["disabled"]),s(re,{disabled:!t.value.orderId,onSuccess:we},null,8,["disabled"])])):b("",!0),p(i)==="outbound"&&t.value.type===4?(v(),I("div",La,[s(w,{type:"warning",class:"mb-10px mr-10px",disabled:d.value.length>0,size:"default",onClick:ye},{default:c(()=>[q(" 选择物料 ")]),_:1},8,["disabled"])])):b("",!0),p(i)==="inbound"&&(t.value.type===4||t.value.type===9)?(v(),I("div",Fa,[s(w,{type:"warning",class:"mb-10px mr-10px",disabled:d.value.length>0,size:"default",onClick:Ue},{default:c(()=>[q(" 选择退货单 ")]),_:1},8,["disabled"])])):b("",!0),s(w,{type:"danger",disabled:d.value.length===0,class:"mb-10px mr-10px",size:"default",onClick:Ee},{default:c(()=>[q(" 清空列表 ")]),_:1},8,["disabled"])]),s(Ze,{data:d.value,style:{width:"100%"},border:"",size:"small","row-class-name":Ge,"max-height":Math.max(p(se)-680,300)},{default:c(()=>[ue.value?(v(),m(f,{key:0,prop:"=code",label:"物料编码",width:"150",align:"center"},{default:c(r=>{var n;return[q(M(((n=r.row)==null?void 0:n.code)||""),1)]}),_:1})):(v(),m(f,{key:1,prop:"code",label:"选择物料",width:"450",align:"center"},{default:c(r=>[t.value.type===1&&t.value.orderId&&p(i)==="outbound"?(v(),I("div",Ra,[s(T,{modelValue:r.row.materialId,"onUpdate:modelValue":n=>r.row.materialId=n,filterable:"","reserve-keyword":"",loading:D.value,placeholder:"请选择物料",style:{width:"400px"},size:"small",onChange:ze},{default:c(()=>[(v(!0),I(N,null,E(y.value,n=>(v(),m(k,{key:n.value,size:"small",disabled:n.disabled,label:`${n.code} / ${n.name} / ${n.model} ${n!=null&&n.po?` / ${n.po}`:""} ${n!=null&&n.supplierName?` / ${n.supplierName}`:""}`,value:n.materialId},null,8,["disabled","label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","loading"])])):t.value.type===1&&t.value.orderId&&p(i)==="inbound"?(v(),I("div",Ya,[s(T,{modelValue:r.row.id,"onUpdate:modelValue":n=>r.row.id=n,filterable:"","reserve-keyword":"",loading:D.value,placeholder:"请选择物料",style:{width:"400px"},size:"small",onChange:Oe},{default:c(()=>[(v(!0),I(N,null,E(y.value,n=>(v(),m(k,{key:n.value,size:"small",disabled:n.disabled,label:`${n.code} / ${n.name} / ${n.model} ${n!=null&&n.po?` / ${n.po}`:""} ${n!=null&&n.supplierName?` / ${n.supplierName}`:""}`,value:n.value},null,8,["disabled","label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","loading"])])):p(i)==="outbound"&&(t.value.type===0||t.value.type===5)||p(i)==="inbound"&&t.value.type===3?(v(),I("div",Wa,[s(T,{modelValue:r.row.id,"onUpdate:modelValue":n=>r.row.id=n,filterable:"","reserve-keyword":"",loading:D.value,placeholder:"请选择物料",style:{width:"400px"},size:"small",onChange:fe},{default:c(()=>[(v(!0),I(N,null,E(y.value,n=>(v(),m(k,{key:n.value,size:"small",disabled:n.disabled,label:`${n.code} / ${n.name} / ${n.model} ${n!=null&&n.po?` / ${n.po}`:""} ${n!=null&&n.supplierName?` / ${n.supplierName}`:""}`,value:n.value},null,8,["disabled","label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","loading"])])):(v(),I("div",ja,[s(T,{modelValue:r.row.id,"onUpdate:modelValue":n=>r.row.id=n,size:"small",filterable:"","reserve-keyword":"",loading:D.value,placeholder:"请选择物料",style:{width:"400px"},remote:"","remote-method":Se,onChange:fe},{default:c(()=>[(v(!0),I(N,null,E(y.value,n=>(v(),m(k,{key:n.value,disabled:n.disabled,label:`${n.code} / ${n.name} / ${n.model}`,value:n.value},null,8,["disabled","label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","loading"])]))]),_:1})),s(f,{prop:"quantity",label:"*数量",align:"center",width:"150"},{header:c(()=>[Ga,q(" 数量 ")]),default:c(r=>[p(i)==="outbound"&&(t.value.type===0||t.value.type===5)?(v(),I("div",{key:0,class:ie(Y.value&&!(r.row.quantity>0)?"quantity-input-error":"")},[s(de,{modelValue:r.row.quantity,"onUpdate:modelValue":n=>r.row.quantity=n,min:0,placeholder:"请输入物料数量",size:"small",precision:4,onChange:n=>Ie(r.row)},null,8,["modelValue","onUpdate:modelValue","onChange"])],2)):(v(),I("div",{key:1,class:ie(Y.value&&!(r.row.quantity>0)?"quantity-input-error":"")},[s(de,{modelValue:r.row.quantity,"onUpdate:modelValue":n=>r.row.quantity=n,min:p(i)==="inbound"&&(t.value.type===2||t.value.type===8)?void 0:0,placeholder:"请输入物料数量",size:"small",precision:4,onChange:n=>Ie(r.row)},null,8,["modelValue","onUpdate:modelValue","min","onChange"])],2))]),_:1}),p(i)==="outbound"&&t.value.type===3?(v(),m(f,{key:2,prop:"total_quantity",align:"center",width:"160"},{header:c(()=>[Ha,q(" 物料总量 ")]),default:c(r=>[h("div",{class:ie(J.value?"quantity-input-error":"")},[s(de,{modelValue:r.row.total_quantity,"onUpdate:modelValue":n=>r.row.total_quantity=n,min:0,placeholder:"请输入物料总量",size:"small",precision:4,onChange:n=>je(r.row)},null,8,["modelValue","onUpdate:modelValue","onChange"])],2)]),_:1})):b("",!0),p(i)==="outbound"?(v(),m(f,{key:3,prop:"remark",label:"备注",align:"center",width:"150"},{header:c(()=>[q(" 备注 ")]),default:c(r=>[h("div",null,[s(_,{modelValue:r.row.remark,"onUpdate:modelValue":n=>r.row.remark=n,size:"small",placeholder:"请输入备注"},null,8,["modelValue","onUpdate:modelValue"])])]),_:1})):b("",!0),p(i)==="outbound"&&t.value.type===3?(v(),m(f,{key:4,prop:"scrap_date",label:"报废日期",align:"center",width:"150"},{default:c(r=>[h("div",null,[s(Xe,{modelValue:r.row.scrap_date,"onUpdate:modelValue":n=>r.row.scrap_date=n,type:"date",placeholder:"选择报废日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",size:"small",style:{width:"140px"}},null,8,["modelValue","onUpdate:modelValue"])])]),_:1})):b("",!0),p(i)==="outbound"&&t.value.type===3?(v(),m(f,{key:5,prop:"responsible",label:"责任人",align:"center",width:"150"},{header:c(()=>[q(" 责任人 ")]),default:c(r=>[h("div",null,[s(_,{modelValue:r.row.responsible,"onUpdate:modelValue":n=>r.row.responsible=n,size:"small",placeholder:"请输入责任人",type:"textarea"},null,8,["modelValue","onUpdate:modelValue"])])]),_:1})):b("",!0),p(i)==="outbound"&&t.value.type===3?(v(),m(f,{key:6,prop:"handling_method",label:"处理方式",align:"center",width:"160"},{default:c(r=>[h("div",null,[s(_,{modelValue:r.row.handling_method,"onUpdate:modelValue":n=>r.row.handling_method=n,placeholder:"请输入处理方式",size:"small",type:"textarea"},null,8,["modelValue","onUpdate:modelValue"])])]),_:1})):b("",!0),p(i)==="outbound"&&t.value.type===3?(v(),m(f,{key:7,prop:"product_group_id",label:"机型",align:"center",width:"220"},{default:c(r=>[h("div",null,[s(T,{modelValue:r.row.product_group_id,"onUpdate:modelValue":n=>r.row.product_group_id=n,placeholder:"选择机型",size:"small",filterable:""},{default:c(()=>[s(k,{key:"0",value:0,label:"请选择机型"}),(v(!0),I(N,null,E(ve.value,n=>(v(),m(k,{key:n.id,label:n.name,value:n.id},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])])]),_:1})):b("",!0),p(i)==="inbound"&&(t.value.type===4||t.value.type===9)?(v(),m(f,{key:8,prop:"returnOrderQuantity",label:"退货数量",align:"center",width:"100"})):b("",!0),p(i)==="inbound"&&(t.value.type===4||t.value.type===9)?(v(),m(f,{key:9,prop:"restockingQty",label:"已补货数量",align:"center",width:"100"})):b("",!0),p(i)==="inbound"&&(t.value.type===4||t.value.type===9)?(v(),m(f,{key:10,prop:"",label:"剩余数量",align:"center",width:"120"},{default:c(({row:r})=>[q(M(r.returnOrderQuantity-r.restockingQty),1)]),_:1})):b("",!0),p(i)==="inbound"?(v(),m(f,{key:11,prop:"quantity",label:"仓位",align:"center",width:"100"},{default:c(r=>[s(qe,{modelValue:r.row.warehouseId,"onUpdate:modelValue":n=>r.row.warehouseId=n,code:"warehouse_name",size:"small",width:"100px"},null,8,["modelValue","onUpdate:modelValue"])]),_:1})):b("",!0),s(f,{prop:"inbound_outbound_key",label:"关键字",align:"center",width:"120"},{default:c(r=>[s(qe,{modelValue:r.row.inbound_outbound_key,"onUpdate:modelValue":n=>r.row.inbound_outbound_key=n,code:"inbound_outbound_key",size:"small",width:"100px"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),s(f,{label:"位置",align:"left",width:"220","show-overflow-tooltip":""},{default:c(r=>[h("div",Ja,[s(T,{modelValue:r.row.address,"onUpdate:modelValue":n=>r.row.address=n,filterable:"",multiple:"",size:"small",style:{width:"220px"},placeholder:"请选择位置"},{default:c(()=>[(v(!0),I(N,null,E(r.row.address_arr,(n,aa)=>(v(),m(k,{key:aa,label:n,value:n},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])])]),_:1}),p(i)==="inbound"&&t.value.type===1?(v(),m(f,{key:12,prop:"availableQuantity",label:"可入库",width:"80",align:"center","show-overflow-tooltip":""},{default:c(r=>[q(M(xe(r.row)),1)]),_:1})):b("",!0),t.value.type===1?(v(),m(f,{key:13,prop:"receivedQuantity",label:"已收数量",width:"80",align:"center"},{default:c(r=>{var n;return[q(M(((n=r.row)==null?void 0:n.receivedQuantity)||0),1)]}),_:1})):b("",!0),t.value.type===3&&p(i)==="inbound"?(v(),m(f,{key:14,prop:"outQty",label:"可退数量",align:"center",width:"80"})):b("",!0),t.value.type===1?(v(),m(f,{key:15,prop:"po",label:"PO",align:"left",width:"180","show-overflow-tooltip":""})):b("",!0),p(i)==="outbound"?(v(),m(f,{key:16,prop:"inventory",label:"库存",width:"80",align:"center","show-overflow-tooltip":""})):b("",!0),p(i)==="outbound"&&(t.value.type===0||t.value.type===5)?(v(),m(f,{key:17,prop:"calcBomQuantity",label:"Bom用量",width:"80",align:"center","show-overflow-tooltip":""})):b("",!0),p(i)==="outbound"&&(t.value.type===0||t.value.type===5)?(v(),m(f,{key:18,prop:"outboundQuantity",label:"已出库",width:"80",align:"center","show-overflow-tooltip":""})):b("",!0),p(i)==="outbound"&&(t.value.type===0||t.value.type===5)?(v(),m(f,{key:19,prop:"",label:"待发货数量",align:"center",width:"120"},{default:c(({row:r})=>[q(M(r.calcBomQuantity&&(r.outboundQuantity||r.outboundQuantity===0)?(r.calcBomQuantity-r.outboundQuantity).toFixed(4):""),1)]),_:1})):b("",!0),t.value.type===1?(v(),m(f,{key:20,prop:"supplierName",label:"供应商",align:"left",width:"150","show-overflow-tooltip":""})):b("",!0),s(ta,{"auto-width":"","show-code":!1}),s(f,{prop:"unit",label:"单位",align:"center",width:"80","show-overflow-tooltip":""}),s(f,{label:"操作",width:"120",align:"center"},{default:c(r=>[s(w,{type:"danger",size:"small",onClick:n=>Ne(r.row.index)},{default:c(()=>[q(" 删除 ")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data","max-height"]),ue.value?b("",!0):(v(),m(w,{key:0,disabled:!t.value.orderId&&t.value.type===1||p(i)==="outbound"&&(t.value.type===0||t.value.type===5)&&!t.value.orderId,style:{width:"100%"},size:"small",class:"btn-material-add",onClick:be},{default:c(()=>[q(" + 添加物料 ")]),_:1},8,["disabled"])),s(ea,{flex:"~ 1 items-center justify-end"},{default:c(()=>[s(w,{type:"success",style:{"margin-top":"20px"},loading:z.value,onClick:Ce},{default:c(()=>[q(" 保存为草稿 ")]),_:1},8,["loading"])]),_:1})])]),_:1},8,["model"])]),s(Z,{modelValue:B.value,"onUpdate:modelValue":u[3]||(u[3]=r=>B.value=r),title:"选择物料",width:"80%"},{footer:c(()=>[h("span",Xa,[s(w,{onClick:u[2]||(u[2]=r=>B.value=!1)},{default:c(()=>[q("取消")]),_:1}),s(w,{type:"primary",onClick:De},{default:c(()=>[q(" 确认选择 ")]),_:1})])]),default:c(()=>[B.value?(v(),m(la,{key:0,ref_key:"materialSelector",ref:me,modelValue:G.value,"onUpdate:modelValue":u[1]||(u[1]=r=>G.value=r),"has-stock":p(i)==="outbound"},null,8,["modelValue","has-stock"])):b("",!0)]),_:1},8,["modelValue"]),s(Z,{modelValue:W.value,"onUpdate:modelValue":u[4]||(u[4]=r=>W.value=r),title:"选择采购单",width:"80%",height:"600"},{default:c(()=>[W.value?(v(),m(ba,{key:0,status:pe.value,onSelected:Fe},null,8,["status"])):b("",!0)]),_:1},8,["modelValue"]),s(Z,{modelValue:j.value,"onUpdate:modelValue":u[5]||(u[5]=r=>j.value=r),title:"选择工单",width:"80%",height:"600"},{default:c(()=>[j.value?(v(),m(pa,{key:0,"table-op":Me.value,status:p(Ve),onSelected:Re},null,8,["table-op","status"])):b("",!0)]),_:1},8,["modelValue"]),s(Z,{modelValue:X.value,"onUpdate:modelValue":u[6]||(u[6]=r=>X.value=r),title:"选择退货单",width:"80%",height:"600"},{default:c(()=>[s(ca,{"outbound-type":Te.value,onSelected:He},null,8,["outbound-type"])]),_:1},8,["modelValue"])])}}}),ml=ya(el,[["__scopeId","data-v-d87e0921"]]);export{ml as I};
