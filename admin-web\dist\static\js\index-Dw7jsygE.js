import{C as l}from"./.pnpm-hVqhwuVC.js";import{c as f,b as d}from"./index-BtOcqcNl.js";const{getFestival:L,isHoliday:g,isWorkday:v}=l,{user:a}=f(),_=a.info;var c;const y=((c=a.info)==null?void 0:c.id)||null,o=d(),h=o.getDeptList(),p=o.getUserList();function N(e,i,s){if(!i||!s)return"";const r=o.getDictMap("")[e].find(u=>u.id===Number(i));if(!r||!r.children||r.children.length===0)return"";const n=r.children.find(u=>u.id===s);return n?n.name:""}function S(e,i){if(!e||!i)return"";const s=o.getDictMap("");let t=[];if(e==="dept"?t=h:e==="user"?t=p:t=s[e],!t||t.length===0)return"";const r=t.find(n=>n.id===i)||{};return r?r.name:""}function C(e){if(!e)throw new Error("date is required");return g(e)}function x(e){return String.fromCharCode(e+65)}const b=/^\d+(\.\d{1,2})?$/;function U(e){if(!e.value.id)return 0;if(e.value.status===5)return 100;if(e.value.level===0&&e.value.childList.length>0){const i=e.value.childList.filter(r=>r.status===5).length;if(i<=0)return 0;const s=e.value.childList.length+1;let t=i/s*100;return b.test(String(t))||(t=Number(t.toFixed(2))),t}return Number(e.value.progress)}export{y as a,N as b,x as c,C as d,U as e,S as g,_ as u};
