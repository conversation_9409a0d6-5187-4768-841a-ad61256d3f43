import{i as k}from"./index-DkYL1aws.js";import{c as C,b as d,q as h,w as p,h as o,i as n,f as q,s as M,F as V,o as c}from"./.pnpm-hVqhwuVC.js";import{a as $}from"./index-C6cm1h61.js";const j=C({name:"pms-production-deduct-manhour"}),W=C({...j,setup(z){const{service:m}=$(),x=d([]),_=d([]),b=d([]),v=d([]),I=d(0),N=d([{label:"待处理",value:0,type:"info",count:0},{label:"已处理",value:1,type:"info",count:0}]);async function T(){try{const e=await m.pms.product.request({url:"/getAllProduct",method:"GET"});x.value=e==null?void 0:e.map(l=>({group_id:l.groupId,value:l.id,sku:l.sku,label:`${l.sku} ${l.name}`,name:l.name}))}catch(e){console.error(e)}}async function U(){try{const e=await m.pms.material.request({url:"/list",method:"POST"});b.value=e}catch(e){console.error(e)}}async function D(){try{const e=await m.pms.supplier.request({url:"/list",method:"POST"});_.value=e}catch(e){console.error(e)}}D(),T(),U();const f=k.useUpsert({props:{class:"material-deduct-form",labelWidth:"120px"},items:[{label:"时间",prop:"accident_time",required:!0,component:{name:"el-date-picker",props:{type:"datetime",placeholder:"选择日期",clearable:!0,format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss",disabledDate:e=>e.getTime()>Date.now()}}},{label:"不良物料供应商",prop:"supplierId",required:!0,component:{name:"slot-supplier-select"}},{label:"不良物料名称",prop:"badMaterialId",required:!0,component:{name:"slot-bad-material-select"}},{label:"机型",prop:"product_id",required:!0,component:{name:"slot-product-select"}},{label:"人数",prop:"number_of_people",required:!0,component:{name:"el-input-number"}},{label:"工时总计",prop:"man_hour",required:!0,component:{name:"el-input-number"}},{label:"人工费率",prop:"labor_rate",required:!0,component:{name:"el-input-number"}},{label:"描述",prop:"description",required:!0,component:{name:"el-input",props:{type:"textarea"}}},{label:"扣款凭证",prop:"voucher",required:!0,component:{name:"cl-upload",props:{multiple:!0,limit:5,accept:"image/jpg,image/jpeg,image/png",text:"上传入库凭证",type:"image",disabled:!1,isPrivate:!1}}}],async onOpen(){},async onOpened(){v.value=b.value},async onClose(e,l){v.value=[],l()},async onInfo(e,{done:l}){l(e)}}),Y=k.useTable({columns:[{label:"ID",prop:"id",width:60},{label:"创建时间",prop:"createTime",width:180},{label:"事故时间",prop:"accident_time",width:180},{label:"不良物料供应商",prop:"supplierName",showOverflowTooltip:!0,formatter(e){const l=_.value.find(a=>a.id===e.supplierId);return l?l.supplierName:""}},{label:"扣款单号",prop:"no",width:220},{label:"扣款凭证",prop:"voucher",width:120,component:{name:"cl-image",props:{fit:"cover",lazy:!0,size:[50,50]}}},{label:"不良物料名称",prop:"badMaterialName",width:200},{label:"人数",prop:"number_of_people",width:100},{label:"工时总计",prop:"man_hour",width:100},{label:"人工费率(元/h)",prop:"labor_rate",width:130},{label:"扣款总计",prop:"total_amount",width:100},{label:"人均工时",prop:"per_capita_working_hours",width:100},{label:"描述",prop:"description",showOverflowTooltip:!0,width:350},{type:"op",buttons:["edit","delete"]}]}),O=k.useCrud({service:m.pms.production.ManHourDeduct,async onRefresh(e,{next:l,render:a}){const{count:r,list:s,pagination:y}=await l(e);N.value.forEach(i=>{i.count=r&&r[i.value]||0}),a(s,y)}},e=>{e.refresh({status:I})});async function L(e){var r;let l="",a=_.value.find(s=>s.id===e);l=a.supplierName?a.supplierName:"",(r=f.value)==null||r.setForm("supplierName",l)}function F(e){var a;let l="";l=b.value.filter(r=>r.id==e)[0].name,(a=f.value)==null||a.setForm("materialName",l)}function H(e){var a;let l="";l=b.value.filter(r=>r.id==e)[0].name,(a=f.value)==null||a.setForm("badMaterialName",l)}return(e,l)=>{const a=n("cl-refresh-btn"),r=n("cl-add-btn"),s=n("cl-flex1"),y=n("cl-search-key"),i=n("el-row"),P=n("cl-table"),B=n("cl-pagination"),g=n("el-option"),w=n("el-select"),E=n("cl-upsert"),S=n("cl-crud");return c(),h(S,{ref_key:"Crud",ref:O},{default:p(()=>[o(i,null,{default:p(()=>[o(a),o(r),o(s),o(y,{placeholder:"请输入扣款单号/物料名/厂家名"})]),_:1}),o(i,{style:{"margin-top":"-10px"}},{default:p(()=>[o(P,{ref_key:"Table",ref:Y},null,512)]),_:1}),o(i,null,{default:p(()=>[o(s),o(B)]),_:1}),o(E,{ref_key:"Upsert",ref:f},{"slot-supplier-select":p(({scope:u})=>[o(w,{modelValue:u.supplierId,"onUpdate:modelValue":t=>u.supplierId=t,placeholder:"请选择事故物料供应商",filterable:"",onChange:L},{default:p(()=>[(c(!0),q(V,null,M(_.value,t=>(c(),h(g,{key:t.id,label:t.supplierName,value:t.id},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),"slot-bad-material-select":p(({scope:u})=>[o(w,{modelValue:u.badMaterialId,"onUpdate:modelValue":t=>u.badMaterialId=t,placeholder:"请选择报废物料",filterable:"",onChange:H},{default:p(()=>[(c(!0),q(V,null,M(v.value,t=>(c(),h(g,{key:t.id,label:`${t.code}/${t.name}`,value:t.id},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),"slot-product-select":p(({scope:u})=>[o(w,{modelValue:u.product_id,"onUpdate:modelValue":t=>u.product_id=t,placeholder:"请选择报废物料",filterable:"",onChange:F},{default:p(()=>[(c(!0),q(V,null,M(x.value,t=>(c(),h(g,{key:t.value,label:t.label,value:t.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:1},512)]),_:1},512)}}});export{W as default};
