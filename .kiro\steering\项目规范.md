# 项目开发规范

## 项目概述
本项目是一个基于 GoFrame + Cool-Admin-Vue 的 ERP 管理系统，包含：
- 前端：Vue3 + TypeScript + Element Plus + UnoCSS
- 后端：Go + GoFrame 框架
- 数据库：支持 MySQL、SQL Server、SQLite

## 技术栈规范

### 前端技术栈
- **框架**: Vue 3 (Composition API)
- **语言**: TypeScript
- **UI 组件库**: Element Plus
- **CSS 框架**: UnoCSS
- **状态管理**: Pinia
- **路由**: Vue Router 4
- **HTTP 客户端**: Axios
- **构建工具**: Vite
- **包管理器**: pnpm

### 后端技术栈
- **语言**: Go 1.21+
- **框架**: GoFrame v2.7+
- **数据库**: MySQL/SQL Server/SQLite
- **ORM**: GORM + GoFrame ORM
- **缓存**: Redis
- **认证**: JWT

## 代码规范

### 前端代码规范
1. **组件命名**: 使用 PascalCase，文件名使用 kebab-case
2. **变量命名**: 使用 camelCase
3. **常量命名**: 使用 UPPER_SNAKE_CASE
4. **函数命名**: 使用 camelCase，动词开头
5. **类型定义**: 使用 PascalCase，以 Type 或 Interface 结尾

### 后端代码规范
1. **包命名**: 使用小写字母，简短有意义
2. **结构体命名**: 使用 PascalCase
3. **函数命名**: 公开函数使用 PascalCase，私有函数使用 camelCase
4. **常量命名**: 使用 PascalCase 或 UPPER_SNAKE_CASE
5. **接口命名**: 使用 PascalCase，通常以 I 开头或 er 结尾

## 项目结构规范

### 前端目录结构
```
admin-web/
├── src/
│   ├── modules/          # 业务模块
│   │   └── pms/         # 生产管理系统模块
│   ├── components/      # 公共组件
│   ├── utils/          # 工具函数
│   ├── types/          # 类型定义
│   └── styles/         # 样式文件
├── public/             # 静态资源
└── packages/           # 本地包
```

### 后端目录结构
```
admin/
├── internal/           # 内部代码
├── modules/           # 业务模块
├── manifest/          # 配置文件
├── resource/          # 资源文件
└── utility/           # 工具函数
```

## 开发流程规范

### Git 提交规范
使用 Conventional Commits 规范：
- `feat`: 新功能
- `fix`: 修复 bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

### 分支管理
- `main`: 主分支，用于生产环境
- `develop`: 开发分支
- `feature/*`: 功能分支
- `hotfix/*`: 热修复分支

## 性能优化规范

### 前端性能优化
1. 使用 `v-memo` 优化列表渲染
2. 合理使用 `computed` 和 `watch`
3. 组件懒加载
4. 图片懒加载
5. 合理使用 `keep-alive`

### 后端性能优化
1. 数据库查询优化
2. 合理使用缓存
3. 接口响应时间控制
4. 内存使用优化
5. 并发处理优化