<script lang="ts" name="pms-bill-payment" setup>
import {useCrud, useSearch, useTable, useUpsert} from '@cool-vue-p/crud'
import moment from 'moment/moment'
import { ref } from 'vue'
import { useCool } from '/@/cool'

const { service } = useCool()


// cl-table 配置
const Table = useTable({
  columns: [
    { label: 'ID', prop: 'id', width: 60 },
    { label: '创建时间', prop: 'createTime', width: 180 },
  ],
})

// cl-crud 配置
const Crud = useCrud(
  {
    dict: {
      api: { page: 'getEmployeeWorking' },
    },
    service: service.pms.daily_report_data,
    async onRefresh(params, { next, render }) {
      // 1 默认调用
      const { list, pagination } = await next(params)
      render(list, pagination)
    },
  },
  (app) => {
    app.refresh()
  },
)

const Search = useSearch({
  items: [
    {
      label: '供应商',
      prop: 'keyword',
      props: {
        labelWidth: '120px',
      },
      component: {
        name: 'el-input',
        props: {
          clearable: false,
          onChange(keyword: string) {
            Crud.value?.refresh({ keyWord: keyword.trim(), page: 1 })
          },
        },
      },
    },
  ],
})

</script>

<template>
  <cl-crud ref="Crud">
    <el-row>
      <!-- 刷新按钮 -->
      <cl-refresh-btn />
      <cl-flex1 />
      <!-- 关键字搜索 -->
      <cl-search ref="Search" />
    </el-row>
    <el-row style="margin-top: 10px">
      <!-- 数据表格 -->
      <cl-table ref="Table" />
    </el-row>

    <el-row>
      <cl-flex1 />
      <!-- 分页控件 -->
      <cl-pagination />
    </el-row>
  </cl-crud>
</template>

<style lang="scss">
.bill-payment-form {
  .cl-form__items {
    .el-row {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      .full-line {
        grid-column: 1 / -1;
      }
    }

    // 让input-number的提示语靠左显示
    .el-input-number {
      :deep(.el-input__wrapper) {
        padding-left: 11px;
        input {
          text-align: left;
        }
      }
    }
  }
}
</style>
