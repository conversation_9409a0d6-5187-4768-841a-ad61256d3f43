import{bf as _,bg as w,bh as D,bi as E,bj as B,bk as S,bl as i,bm as l,bn as b,bo as V,bp as M,c as v,b as T,z as h,S as j,bq as z,f as O,o as F,V as L,W as N,v as y,a0 as R}from"./.pnpm-hVqhwuVC.js";import{p as q,E as H}from"./index-BtOcqcNl.js";import{a as I}from"./index-D95m1iJL.js";import{_ as J}from"./_plugin-vue_export-helper-DlAUqK2U.js";self.MonacoEnvironment={getWorker(n,o){return o==="json"?new _:o==="css"||o==="scss"||o==="less"?new w:o==="html"||o==="handlebars"||o==="razor"?new D:o==="typescript"||o==="javascript"?new E:new B}};S.defineTheme("default",{base:"vs",inherit:!0,rules:[{background:"ffffff",token:""},{foreground:"6a737d",token:"comment"},{foreground:"6a737d",token:"punctuation.definition.comment"},{foreground:"6a737d",token:"string.comment"},{foreground:"005cc5",token:"constant"},{foreground:"005cc5",token:"entity.name.constant"},{foreground:"005cc5",token:"variable.other.constant"},{foreground:"005cc5",token:"variable.language"},{foreground:"6f42c1",token:"entity"},{foreground:"6f42c1",token:"entity.name"},{foreground:"24292e",token:"variable.parameter.function"},{foreground:"22863a",token:"entity.name.tag"},{foreground:"d73a49",token:"keyword"},{foreground:"d73a49",token:"storage"},{foreground:"d73a49",token:"storage.type"},{foreground:"24292e",token:"storage.modifier.package"},{foreground:"24292e",token:"storage.modifier.import"},{foreground:"24292e",token:"storage.type.java"},{foreground:"032f62",token:"string"},{foreground:"032f62",token:"punctuation.definition.string"},{foreground:"032f62",token:"string punctuation.section.embedded source"},{foreground:"005cc5",token:"support"},{foreground:"005cc5",token:"meta.property-name"},{foreground:"e36209",token:"variable"},{foreground:"24292e",token:"variable.other"},{foreground:"b31d28",fontStyle:"bold italic underline",token:"invalid.broken"},{foreground:"b31d28",fontStyle:"bold italic underline",token:"invalid.deprecated"},{foreground:"fafbfc",background:"b31d28",fontStyle:"italic underline",token:"invalid.illegal"},{foreground:"fafbfc",background:"d73a49",fontStyle:"italic underline",token:"carriage-return"},{foreground:"b31d28",fontStyle:"bold italic underline",token:"invalid.unimplemented"},{foreground:"b31d28",token:"message.error"},{foreground:"24292e",token:"string source"},{foreground:"005cc5",token:"string variable"},{foreground:"032f62",token:"source.regexp"},{foreground:"032f62",token:"string.regexp"},{foreground:"032f62",token:"string.regexp.character-class"},{foreground:"032f62",token:"string.regexp constant.character.escape"},{foreground:"032f62",token:"string.regexp source.ruby.embedded"},{foreground:"032f62",token:"string.regexp string.regexp.arbitrary-repitition"},{foreground:"22863a",fontStyle:"bold",token:"string.regexp constant.character.escape"},{foreground:"005cc5",token:"support.constant"},{foreground:"005cc5",token:"support.variable"},{foreground:"005cc5",token:"meta.module-reference"},{foreground:"735c0f",token:"markup.list"},{foreground:"005cc5",fontStyle:"bold",token:"markup.heading"},{foreground:"005cc5",fontStyle:"bold",token:"markup.heading entity.name"},{foreground:"22863a",token:"markup.quote"},{foreground:"24292e",fontStyle:"italic",token:"markup.italic"},{foreground:"24292e",fontStyle:"bold",token:"markup.bold"},{foreground:"005cc5",token:"markup.raw"},{foreground:"b31d28",background:"ffeef0",token:"markup.deleted"},{foreground:"b31d28",background:"ffeef0",token:"meta.diff.header.from-file"},{foreground:"b31d28",background:"ffeef0",token:"punctuation.definition.deleted"},{foreground:"22863a",background:"f0fff4",token:"markup.inserted"},{foreground:"22863a",background:"f0fff4",token:"meta.diff.header.to-file"},{foreground:"22863a",background:"f0fff4",token:"punctuation.definition.inserted"},{foreground:"e36209",background:"ffebda",token:"markup.changed"},{foreground:"e36209",background:"ffebda",token:"punctuation.definition.changed"},{foreground:"f6f8fa",background:"005cc5",token:"markup.ignored"},{foreground:"f6f8fa",background:"005cc5",token:"markup.untracked"},{foreground:"6f42c1",fontStyle:"bold",token:"meta.diff.range"},{foreground:"005cc5",token:"meta.diff.header"},{foreground:"005cc5",fontStyle:"bold",token:"meta.separator"},{foreground:"005cc5",token:"meta.output"},{foreground:"586069",token:"brackethighlighter.tag"},{foreground:"586069",token:"brackethighlighter.curly"},{foreground:"586069",token:"brackethighlighter.round"},{foreground:"586069",token:"brackethighlighter.square"},{foreground:"586069",token:"brackethighlighter.angle"},{foreground:"586069",token:"brackethighlighter.quote"},{foreground:"b31d28",token:"brackethighlighter.unmatched"},{foreground:"b31d28",token:"sublimelinter.mark.error"},{foreground:"e36209",token:"sublimelinter.mark.warning"},{foreground:"959da5",token:"sublimelinter.gutter-mark"},{foreground:"032f62",fontStyle:"underline",token:"constant.other.reference.link"},{foreground:"032f62",fontStyle:"underline",token:"string.other.link"}],colors:{"editor.foreground":"#24292e","editor.background":"#ffffff","editor.selectionBackground":"#c8c8fa","editor.inactiveSelectionBackground":"#fafbfc","editor.lineHighlightBackground":"#fafbfc","editorCursor.foreground":"#24292e","editorWhitespace.foreground":"#959da5","editorIndentGuide.background":"#959da5","editorIndentGuide.activeBackground":"#24292e","editor.selectionHighlightBorder":"#fafbfc"}});i.typescript.typescriptDefaults.setEagerModelSync(!0);i.typescript.typescriptDefaults.setCompilerOptions({target:i.typescript.ScriptTarget.ES2016,allowNonTsExtensions:!0,moduleResolution:i.typescript.ModuleResolutionKind.NodeJs,module:i.typescript.ModuleKind.CommonJS,experimentalDecorators:!0,noEmit:!0,allowJs:!1,typeRoots:["node_modules/@types"]});i.typescript.typescriptDefaults.setDiagnosticsOptions({noSemanticValidation:!0,noSyntaxValidation:!0});const p={html:{parser:"html",plugins:[V,b,l]},typescript:{parser:"typescript",plugins:[b]},css:{parser:"css",plugins:[l]},scss:{parser:"scss",plugins:[l]}};function P(){function n(){for(const o in p)i.registerDocumentFormattingEditProvider(o,{provideDocumentFormattingEdits(a){let t=a.getValue();try{t=M.format(t,{parser:p[o].parser,plugins:p[o].plugins,semi:!0,printWidth:100,tabWidth:2,useTabs:!1,singleQuote:!0,trailingComma:"none"})}catch(u){console.error("代码格式化失败",u)}return[{range:a.getFullModelRange(),text:t}]}})}return{register:n}}let G="";function K({path:n,content:o}){const a=`file:///node_modules/${n}`,t=i.typescript.typescriptDefaults,u=t.getExtraLibs(),d=Object.keys(u);if(!d.includes(a))try{t.addExtraLib(o,a)}catch(f){throw console.log(f,a,d),f}}function Q(){function n(){K({path:"globals.d.ts",content:G})}return{loadDeclares:n}}const A=v({name:"cl-editor-monaco"}),U=v({...A,props:{modelValue:String,options:Object,height:{type:[String,Number],default:400},autofocus:{type:Boolean,default:!1},autoFormatCode:{type:Boolean,default:!0},language:{type:String,default:"json"},disabled:Boolean},emits:["update:modelValue","change"],setup(n,{expose:o,emit:a}){const t=n,u=a,{refs:d,setRefs:f}=I(),x=P(),C=Q(),k=T(t.height);let e=null;function s(){return e==null?void 0:e.getValue()}function c(r){r!==s()&&(e==null||e.setValue(r||""))}async function m(){var r;return await((r=e==null?void 0:e.getAction("editor.action.formatDocument"))==null?void 0:r.run()),s()}function W(){const r=H({theme:"default",language:t.language,minimap:{enabled:!0},automaticLayout:!0,scrollbar:{verticalScrollbarSize:6},lineNumbersMinChars:4,scrollBeyondLastLine:!1,readOnly:t.disabled,fontSize:14,tabSize:2},t.options);e=S.create(d.editor,r),e.onDidChangeModelContent(()=>{t.height==="auto"&&(k.value=Number(e==null?void 0:e.getContentHeight()));const g=s();u("update:modelValue",g),u("change",g)}),R(()=>{c(t.modelValue),t.autofocus&&(e==null||e.focus()),setTimeout(()=>{x.register(),t.autoFormatCode&&m()},300),C.loadDeclares()})}return h(()=>t.modelValue,c),h(()=>t.disabled,r=>{e==null||e.updateOptions({readOnly:r})}),j(()=>{W()}),z(()=>{e==null||e.dispose()}),o({editor:e,setContent:c,formatCode:m}),(r,g)=>(F(),O("div",{ref:y(f)("editor"),class:N(["cl-editor-monaco",{disabled:n.disabled}]),style:L({height:y(q)(k.value)})},null,6))}}),ee=J(U,[["__scopeId","data-v-b9ba7820"]]);export{ee as default};
