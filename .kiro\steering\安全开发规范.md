---
inclusion: always
---

# 安全开发规范

## 认证和授权

### JWT 认证实现
```go
// internal/service/auth.go
package service

import (
    "context"
    "time"
    
    "github.com/golang-jwt/jwt/v4"
    "github.com/gogf/gf/v2/crypto/gmd5"
    "github.com/gogf/gf/v2/frame/g"
    "golang.org/x/crypto/bcrypt"
)

type sAuth struct{}

func Auth() *sAuth {
    return &sAuth{}
}

// JWTClaims JWT 声明
type JWTClaims struct {
    UserId   uint64 `json:"userId"`
    Username string `json:"username"`
    RoleId   uint64 `json:"roleId"`
    jwt.RegisteredClaims
}

// Login 用户登录
func (s *sAuth) Login(ctx context.Context, username, password string) (string, error) {
    // 获取用户信息
    var user *entity.User
    err := dao.User.Ctx(ctx).
        Where("username = ? AND deleted_at IS NULL", username).
        Scan(&user)
    if err != nil {
        return "", gerror.New("用户名或密码错误")
    }
    
    if user == nil {
        return "", gerror.New("用户不存在")
    }
    
    // 验证密码
    if !s.CheckPassword(password, user.Password) {
        return "", gerror.New("用户名或密码错误")
    }
    
    // 检查用户状态
    if user.Status != 1 {
        return "", gerror.New("用户已被禁用")
    }
    
    // 生成 JWT Token
    token, err := s.GenerateToken(user)
    if err != nil {
        return "", err
    }
    
    // 更新最后登录时间
    dao.User.Ctx(ctx).
        Where("id = ?", user.Id).
        Data(g.Map{
            "last_login_at": gtime.Now(),
            "last_login_ip": g.RequestFromCtx(ctx).GetClientIp(),
        }).
        Update()
    
    return token, nil
}

// GenerateToken 生成 JWT Token
func (s *sAuth) GenerateToken(user *entity.User) (string, error) {
    claims := JWTClaims{
        UserId:   user.Id,
        Username: user.Username,
        RoleId:   user.RoleId,
        RegisteredClaims: jwt.RegisteredClaims{
            ExpiresAt: jwt.NewNumericDate(time.Now().Add(24 * time.Hour)),
            IssuedAt:  jwt.NewNumericDate(time.Now()),
            NotBefore: jwt.NewNumericDate(time.Now()),
            Issuer:    "lookah-erp",
        },
    }
    
    token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
    secretKey := g.Cfg().MustGet(gctx.New(), "jwt.secretKey").String()
    
    return token.SignedString([]byte(secretKey))
}

// ParseToken 解析 JWT Token
func (s *sAuth) ParseToken(tokenString string) (*JWTClaims, error) {
    secretKey := g.Cfg().MustGet(gctx.New(), "jwt.secretKey").String()
    
    token, err := jwt.ParseWithClaims(tokenString, &JWTClaims{}, func(token *jwt.Token) (interface{}, error) {
        return []byte(secretKey), nil
    })
    
    if err != nil {
        return nil, err
    }
    
    if claims, ok := token.Claims.(*JWTClaims); ok && token.Valid {
        return claims, nil
    }
    
    return nil, gerror.New("无效的 token")
}

// HashPassword 密码加密
func (s *sAuth) HashPassword(password string) (string, error) {
    bytes, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
    return string(bytes), err
}

// CheckPassword 密码验证
func (s *sAuth) CheckPassword(password, hash string) bool {
    err := bcrypt.CompareHashAndPassword([]byte(hash), []byte(password))
    return err == nil
}
```

### 权限中间件
```go
// internal/middleware/auth.go
package middleware

import (
    "strings"
    
    "github.com/gogf/gf/v2/frame/g"
    "github.com/gogf/gf/v2/net/ghttp"
)

// Auth 认证中间件
func Auth(r *ghttp.Request) {
    // 获取 token
    token := r.Header.Get("Authorization")
    if token == "" {
        r.Response.WriteJsonExit(g.Map{
            "code": 401,
            "msg":  "未授权访问",
        })
        return
    }
    
    // 移除 Bearer 前缀
    if strings.HasPrefix(token, "Bearer ") {
        token = token[7:]
    }
    
    // 验证 token
    claims, err := service.Auth().ParseToken(token)
    if err != nil {
        r.Response.WriteJsonExit(g.Map{
            "code": 401,
            "msg":  "token 无效",
        })
        return
    }
    
    // 设置用户信息到上下文
    r.SetCtxVar("userId", claims.UserId)
    r.SetCtxVar("username", claims.Username)
    r.SetCtxVar("roleId", claims.RoleId)
    
    r.Middleware.Next()
}

// Permission 权限验证中间件
func Permission(permission string) func(*ghttp.Request) {
    return func(r *ghttp.Request) {
        userId := r.GetCtxVar("userId").Uint64()
        
        // 检查用户权限
        hasPermission, err := service.Permission().CheckUserPermission(r.Context(), userId, permission)
        if err != nil {
            r.Response.WriteJsonExit(g.Map{
                "code": 500,
                "msg":  "权限检查失败",
            })
            return
        }
        
        if !hasPermission {
            r.Response.WriteJsonExit(g.Map{
                "code": 403,
                "msg":  "权限不足",
            })
            return
        }
        
        r.Middleware.Next()
    }
}
```

## 输入验证和过滤

### 参数验证规范
```go
// 使用 GoFrame 验证器
type UserCreateReq struct {
    Username string `json:"username" v:"required|length:3,20|regex:^[a-zA-Z0-9_]+$#用户名不能为空|用户名长度为3-20个字符|用户名只能包含字母、数字和下划线"`
    Email    string `json:"email" v:"required|email#邮箱不能为空|邮箱格式不正确"`
    Password string `json:"password" v:"required|length:8,32|password#密码不能为空|密码长度为8-32个字符|密码强度不够"`
    Phone    string `json:"phone" v:"phone#手机号格式不正确"`
}

// 自定义验证规则
func init() {
    // 密码强度验证
    gvalid.RegisterRule("password", func(ctx context.Context, in gvalid.RuleFuncInput) error {
        password := gconv.String(in.Value.Val())
        
        // 至少包含一个大写字母、一个小写字母、一个数字
        hasUpper := regexp.MustCompile(`[A-Z]`).MatchString(password)
        hasLower := regexp.MustCompile(`[a-z]`).MatchString(password)
        hasDigit := regexp.MustCompile(`\d`).MatchString(password)
        
        if !hasUpper || !hasLower || !hasDigit {
            return errors.New("密码必须包含大写字母、小写字母和数字")
        }
        
        return nil
    })
    
    // 手机号验证
    gvalid.RegisterRule("phone", func(ctx context.Context, in gvalid.RuleFuncInput) error {
        phone := gconv.String(in.Value.Val())
        if phone == "" {
            return nil // 允许为空
        }
        
        matched, _ := regexp.MatchString(`^1[3-9]\d{9}$`, phone)
        if !matched {
            return errors.New("手机号格式不正确")
        }
        
        return nil
    })
}
```

### SQL 注入防护
```go
// 使用参数化查询
func (s *sUser) GetUsersByCondition(ctx context.Context, condition map[string]interface{}) ([]*entity.User, error) {
    query := dao.User.Ctx(ctx).Where("deleted_at IS NULL")
    
    // 安全的条件构建
    if username, ok := condition["username"]; ok && username != "" {
        query = query.Where("username = ?", username)
    }
    
    if status, ok := condition["status"]; ok {
        query = query.Where("status = ?", status)
    }
    
    if dateRange, ok := condition["dateRange"]; ok {
        if dates, ok := dateRange.([]string); ok && len(dates) == 2 {
            query = query.Where("created_at BETWEEN ? AND ?", dates[0], dates[1])
        }
    }
    
    var users []*entity.User
    err := query.Scan(&users)
    return users, err
}

// 避免动态 SQL 拼接
func (s *sUser) BadExample(ctx context.Context, username string) {
    // 错误示例 - 容易 SQL 注入
    sql := fmt.Sprintf("SELECT * FROM users WHERE username = '%s'", username)
    // 不要这样做！
}
```

### XSS 防护
```go
// HTML 转义
import "html"

func (s *sContent) CreateContent(ctx context.Context, req *model.ContentCreateInput) error {
    // 对用户输入进行 HTML 转义
    req.Title = html.EscapeString(req.Title)
    req.Content = html.EscapeString(req.Content)
    
    // 保存到数据库
    _, err := dao.Content.Ctx(ctx).Data(req).Insert()
    return err
}

// 富文本内容过滤
import "github.com/microcosm-cc/bluemonday"

func (s *sContent) SanitizeHTML(content string) string {
    // 创建严格的 HTML 策略
    p := bluemonday.StrictPolicy()
    
    // 或者允许特定标签
    p = bluemonday.UGCPolicy()
    p.AllowElements("p", "br", "strong", "em", "ul", "ol", "li")
    
    return p.Sanitize(content)
}
```

## 前端安全规范

### CSRF 防护
```typescript
// utils/csrf.ts
import { http } from './http'

// 获取 CSRF Token
export function getCSRFToken(): string {
  const token = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
  return token || ''
}

// HTTP 拦截器中添加 CSRF Token
http.interceptors.request.use((config) => {
  const csrfToken = getCSRFToken()
  if (csrfToken) {
    config.headers['X-CSRF-Token'] = csrfToken
  }
  return config
})
```

### 输入验证
```typescript
// utils/validation.ts
export const validators = {
  // 用户名验证
  username: (value: string): boolean => {
    return /^[a-zA-Z0-9_]{3,20}$/.test(value)
  },
  
  // 邮箱验证
  email: (value: string): boolean => {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)
  },
  
  // 密码强度验证
  password: (value: string): boolean => {
    return /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,32}$/.test(value)
  },
  
  // 手机号验证
  phone: (value: string): boolean => {
    return /^1[3-9]\d{9}$/.test(value)
  },
  
  // HTML 标签检测
  hasHTMLTags: (value: string): boolean => {
    return /<[^>]*>/g.test(value)
  },
  
  // SQL 注入检测
  hasSQLInjection: (value: string): boolean => {
    const sqlPatterns = [
      /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/i,
      /(--|\/\*|\*\/|;)/,
      /(\b(OR|AND)\b.*=.*)/i
    ]
    return sqlPatterns.some(pattern => pattern.test(value))
  }
}

// 输入过滤
export function sanitizeInput(input: string): string {
  return input
    .replace(/[<>]/g, '') // 移除尖括号
    .replace(/['"]/g, '') // 移除引号
    .trim()
}
```

### 敏感信息保护
```typescript
// utils/security.ts
export class SecurityUtils {
  // 手机号脱敏
  static maskPhone(phone: string): string {
    if (!phone || phone.length < 7) return phone
    return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
  }
  
  // 邮箱脱敏
  static maskEmail(email: string): string {
    if (!email.includes('@')) return email
    const [username, domain] = email.split('@')
    if (username.length <= 2) return email
    return `${username.slice(0, 2)}***@${domain}`
  }
  
  // 身份证脱敏
  static maskIdCard(idCard: string): string {
    if (!idCard || idCard.length < 8) return idCard
    return idCard.replace(/(\d{4})\d{10}(\d{4})/, '$1**********$2')
  }
  
  // 银行卡脱敏
  static maskBankCard(cardNo: string): string {
    if (!cardNo || cardNo.length < 8) return cardNo
    return cardNo.replace(/(\d{4})\d+(\d{4})/, '$1****$2')
  }
  
  // 清除本地存储的敏感信息
  static clearSensitiveData(): void {
    const sensitiveKeys = ['password', 'token', 'userInfo']
    sensitiveKeys.forEach(key => {
      localStorage.removeItem(key)
      sessionStorage.removeItem(key)
    })
  }
}
```

## 数据加密

### 敏感数据加密
```go
// utility/crypto.go
package utility

import (
    "crypto/aes"
    "crypto/cipher"
    "crypto/rand"
    "encoding/base64"
    "io"
)

type Crypto struct {
    key []byte
}

func NewCrypto(key string) *Crypto {
    return &Crypto{
        key: []byte(key),
    }
}

// Encrypt 加密
func (c *Crypto) Encrypt(plaintext string) (string, error) {
    block, err := aes.NewCipher(c.key)
    if err != nil {
        return "", err
    }
    
    gcm, err := cipher.NewGCM(block)
    if err != nil {
        return "", err
    }
    
    nonce := make([]byte, gcm.NonceSize())
    if _, err = io.ReadFull(rand.Reader, nonce); err != nil {
        return "", err
    }
    
    ciphertext := gcm.Seal(nonce, nonce, []byte(plaintext), nil)
    return base64.StdEncoding.EncodeToString(ciphertext), nil
}

// Decrypt 解密
func (c *Crypto) Decrypt(ciphertext string) (string, error) {
    data, err := base64.StdEncoding.DecodeString(ciphertext)
    if err != nil {
        return "", err
    }
    
    block, err := aes.NewCipher(c.key)
    if err != nil {
        return "", err
    }
    
    gcm, err := cipher.NewGCM(block)
    if err != nil {
        return "", err
    }
    
    nonceSize := gcm.NonceSize()
    if len(data) < nonceSize {
        return "", errors.New("ciphertext too short")
    }
    
    nonce, ciphertext := data[:nonceSize], data[nonceSize:]
    plaintext, err := gcm.Open(nil, nonce, ciphertext, nil)
    if err != nil {
        return "", err
    }
    
    return string(plaintext), nil
}
```

### 数据库字段加密
```go
// 敏感字段加密存储
func (s *sUser) CreateUserWithEncryption(ctx context.Context, req *model.UserCreateInput) error {
    crypto := utility.NewCrypto(g.Cfg().MustGet(ctx, "crypto.key").String())
    
    // 加密敏感信息
    encryptedPhone, err := crypto.Encrypt(req.Phone)
    if err != nil {
        return err
    }
    
    encryptedIdCard, err := crypto.Encrypt(req.IdCard)
    if err != nil {
        return err
    }
    
    // 保存到数据库
    _, err = dao.User.Ctx(ctx).Data(g.Map{
        "username":    req.Username,
        "email":       req.Email,
        "phone":       encryptedPhone,
        "id_card":     encryptedIdCard,
        "password":    s.HashPassword(req.Password),
    }).Insert()
    
    return err
}
```

## 日志和审计

### 安全日志记录
```go
// internal/service/audit.go
package service

import (
    "context"
    "encoding/json"
    
    "github.com/gogf/gf/v2/frame/g"
)

type sAudit struct{}

func Audit() *sAudit {
    return &sAudit{}
}

// AuditLog 审计日志结构
type AuditLog struct {
    UserId    uint64                 `json:"userId"`
    Username  string                 `json:"username"`
    Action    string                 `json:"action"`
    Resource  string                 `json:"resource"`
    Details   map[string]interface{} `json:"details"`
    IP        string                 `json:"ip"`
    UserAgent string                 `json:"userAgent"`
    Timestamp string                 `json:"timestamp"`
}

// LogUserAction 记录用户操作
func (s *sAudit) LogUserAction(ctx context.Context, action, resource string, details map[string]interface{}) {
    userId := gconv.Uint64(g.RequestFromCtx(ctx).GetCtxVar("userId"))
    username := gconv.String(g.RequestFromCtx(ctx).GetCtxVar("username"))
    
    auditLog := AuditLog{
        UserId:    userId,
        Username:  username,
        Action:    action,
        Resource:  resource,
        Details:   details,
        IP:        g.RequestFromCtx(ctx).GetClientIp(),
        UserAgent: g.RequestFromCtx(ctx).Header.Get("User-Agent"),
        Timestamp: gtime.Now().Format("Y-m-d H:i:s"),
    }
    
    // 记录到日志文件
    logData, _ := json.Marshal(auditLog)
    g.Log("audit").Info(ctx, string(logData))
    
    // 记录到数据库（可选）
    dao.AuditLog.Ctx(ctx).Data(auditLog).Insert()
}

// LogSecurityEvent 记录安全事件
func (s *sAudit) LogSecurityEvent(ctx context.Context, event string, level string, details map[string]interface{}) {
    securityLog := map[string]interface{}{
        "event":     event,
        "level":     level,
        "details":   details,
        "ip":        g.RequestFromCtx(ctx).GetClientIp(),
        "timestamp": gtime.Now().Format("Y-m-d H:i:s"),
    }
    
    logData, _ := json.Marshal(securityLog)
    g.Log("security").Info(ctx, string(logData))
}
```

### 操作审计中间件
```go
// internal/middleware/audit.go
package middleware

import (
    "bytes"
    "io"
    
    "github.com/gogf/gf/v2/net/ghttp"
)

// AuditMiddleware 审计中间件
func AuditMiddleware(r *ghttp.Request) {
    // 记录请求开始
    startTime := gtime.Now()
    
    // 读取请求体
    var requestBody []byte
    if r.Method == "POST" || r.Method == "PUT" || r.Method == "PATCH" {
        requestBody, _ = io.ReadAll(r.Body)
        r.Body = io.NopCloser(bytes.NewBuffer(requestBody))
    }
    
    r.Middleware.Next()
    
    // 记录审计日志
    details := map[string]interface{}{
        "method":       r.Method,
        "url":          r.URL.String(),
        "requestBody":  string(requestBody),
        "responseCode": r.Response.Status,
        "duration":     gtime.Now().Sub(startTime).String(),
    }
    
    service.Audit().LogUserAction(
        r.Context(),
        r.Method,
        r.URL.Path,
        details,
    )
}
```

## 安全配置

### 安全头配置
```go
// internal/middleware/security.go
package middleware

import "github.com/gogf/gf/v2/net/ghttp"

// SecurityHeaders 安全头中间件
func SecurityHeaders(r *ghttp.Request) {
    // 防止点击劫持
    r.Response.Header().Set("X-Frame-Options", "DENY")
    
    // 防止 MIME 类型嗅探
    r.Response.Header().Set("X-Content-Type-Options", "nosniff")
    
    // XSS 保护
    r.Response.Header().Set("X-XSS-Protection", "1; mode=block")
    
    // 内容安全策略
    r.Response.Header().Set("Content-Security-Policy", "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'")
    
    // HSTS
    r.Response.Header().Set("Strict-Transport-Security", "max-age=31536000; includeSubDomains")
    
    // 引用策略
    r.Response.Header().Set("Referrer-Policy", "strict-origin-when-cross-origin")
    
    r.Middleware.Next()
}
```

### 限流和防护
```go
// internal/middleware/rate_limit.go
package middleware

import (
    "sync"
    "time"
    
    "github.com/gogf/gf/v2/container/gmap"
    "github.com/gogf/gf/v2/net/ghttp"
)

type RateLimiter struct {
    requests *gmap.Map
    mutex    sync.RWMutex
    limit    int
    window   time.Duration
}

func NewRateLimiter(limit int, window time.Duration) *RateLimiter {
    return &RateLimiter{
        requests: gmap.New(true),
        limit:    limit,
        window:   window,
    }
}

func (rl *RateLimiter) Allow(key string) bool {
    rl.mutex.Lock()
    defer rl.mutex.Unlock()
    
    now := time.Now()
    
    // 获取或创建请求记录
    if !rl.requests.Contains(key) {
        rl.requests.Set(key, []time.Time{now})
        return true
    }
    
    requests := rl.requests.Get(key).([]time.Time)
    
    // 清理过期请求
    var validRequests []time.Time
    for _, req := range requests {
        if now.Sub(req) < rl.window {
            validRequests = append(validRequests, req)
        }
    }
    
    // 检查是否超过限制
    if len(validRequests) >= rl.limit {
        return false
    }
    
    // 添加新请求
    validRequests = append(validRequests, now)
    rl.requests.Set(key, validRequests)
    
    return true
}

// RateLimit 限流中间件
func RateLimit(limiter *RateLimiter) func(*ghttp.Request) {
    return func(r *ghttp.Request) {
        key := r.GetClientIp()
        
        if !limiter.Allow(key) {
            r.Response.WriteJsonExit(g.Map{
                "code": 429,
                "msg":  "请求过于频繁，请稍后再试",
            })
            return
        }
        
        r.Middleware.Next()
    }
}
```

## 安全检查清单

### 代码审查清单
- [ ] 所有用户输入都经过验证和过滤
- [ ] 使用参数化查询防止 SQL 注入
- [ ] 敏感数据已加密存储
- [ ] 实现了适当的认证和授权
- [ ] 添加了安全日志记录
- [ ] 配置了安全响应头
- [ ] 实现了限流保护
- [ ] 错误信息不泄露敏感信息

### 部署安全清单
- [ ] 使用 HTTPS 加密传输
- [ ] 配置防火墙规则
- [ ] 定期更新系统和依赖
- [ ] 配置安全监控和告警
- [ ] 实施数据备份和恢复
- [ ] 进行安全渗透测试
- [ ] 制定安全事件响应计划