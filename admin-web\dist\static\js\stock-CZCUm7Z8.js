import{g as F,i as v,j as R,e as z}from"./index-DkYL1aws.js";import{a as A}from"./index-C6cm1h61.js";/* empty css              */import{c as E,q as x,w as n,h as e,i as l,j as K,y as p,f as b,s as k,F as C,t as h,v as O,E as T,o as c}from"./.pnpm-hVqhwuVC.js";const M={style:{"text-align":"left"}},U={style:{"text-align":"left"}},$={class:"ellipsis"},G=E({name:"pms-warehouse-destination-stock"}),Y=E({...G,setup(H){const{dict:S}=F(),{service:m}=A(),B=S.get("color"),g=v.useTable({columns:[{label:"#",prop:"logs",type:"expand",width:50},{label:"产品名",prop:"product.nameEn",align:"left"},{label:"所属仓库",prop:"warehouse.name",width:150},{label:"产品SKU",prop:"product.sku",width:150},{label:"颜色",prop:"productColor",width:150},{label:"可用库存",prop:"inStock",width:150},{label:"冻结库存",prop:"freezeStock",width:150},{label:"更新时间",prop:"updateTime",width:220}]}),N=v.useCrud({service:m.pms.warehouse.destination.stock},o=>{o.refresh()});function D(o,t){var r;(t==null?void 0:t.type)==="expand"||(t==null?void 0:t.type)==="op"||(r=g.value)==null||r.toggleRowExpansion(o)}function V(o){m.pms.warehouse.destination.stock.request({url:"/export",data:{id:o==null?void 0:o.id},method:"POST",responseType:"blob"}).then(t=>{z(t,"库存清单.xlsx")&&T.success("导出成功")}).catch(t=>{T.error(t.message||"导出失败")})}function w(o){const t=o==null?void 0:o.deliveryOrder;return t?Object.values(t.reduce((r,s)=>(r[s.deliveryOrderId]||(r[s.deliveryOrderId]=s),r),{})):[]}return(o,t)=>{const r=l("cl-refresh-btn"),s=l("el-button"),y=l("cl-flex1"),d=l("el-row"),a=l("el-table-column"),j=l("el-popover"),q=l("el-table"),I=l("cl-table"),L=l("cl-pagination"),P=l("cl-crud");return c(),x(P,{ref_key:"Crud",ref:N},{default:n(()=>[e(d,null,{default:n(()=>[e(r),e(s,{type:"primary",onClick:V},{default:n(()=>[K(" 导出库存清单 ")]),_:1}),e(y)]),_:1}),e(d,null,{default:n(()=>[e(I,{ref_key:"Table",ref:g,"row-key":"id",class:"table-row-pointer",onRowClick:D},{"column-productColor":n(({scope:_})=>[p("span",null,h(O(R)(O(B),parseInt(_.row.product.color))),1)]),"column-logs":n(({scope:_})=>[e(q,{data:_.row.record,style:{width:"100%"},border:""},{default:n(()=>[e(a,{prop:"airway",label:"提单号",align:"center",width:"150"}),e(a,{prop:"cartonPo",label:"外箱PO",align:"center",width:"120"}),e(a,{prop:"carton",label:"箱号",align:"center",width:"80"}),e(a,{prop:"quantity",label:"入库数量",align:"center",width:"120"}),e(a,{prop:"available",label:"可用数量",align:"center",width:"120"}),e(a,{prop:"orderSn",label:"订单号",align:"center",width:"250"},{default:n(u=>[p("div",M,[(c(!0),b(C,null,k(w(u.row),(i,f)=>(c(),b("p",{key:f},h(i.orderSn),1))),128))])]),_:2},1024),e(a,{prop:"orderConsignee",label:"派送地址",align:"center"},{default:n(u=>[p("div",U,[(c(!0),b(C,null,k(w(u.row),(i,f)=>(c(),x(j,{key:f,placement:"top-start",width:300,trigger:"hover",content:i.consignee},{reference:n(()=>[p("span",$,h(i.consignee),1)]),_:2},1032,["content"]))),128))])]),_:2},1024),e(a,{prop:"inbound.completeTime",label:"入仓日期",align:"center",width:"180"})]),_:2},1032,["data"])]),_:1},512)]),_:1}),e(d,null,{default:n(()=>[e(y),e(L)]),_:1})]),_:1},512)}}});export{Y as default};
