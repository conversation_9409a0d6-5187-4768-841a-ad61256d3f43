import{c as ke,b as p,e as Ie,f as y,y as e,h as s,w as n,i as f,G as $,H as be,v as _,q as B,j as z,B as G,t as h,L as Ne,a3 as se,a4 as We,a5 as ge,a6 as Ge,a7 as Me,F as He,s as Ke,a8 as Ae,a9 as Je,a0 as Qe,E as D,o as c,V as Xe,T as Ye}from"./.pnpm-hVqhwuVC.js";import{c as Ze,g as et,i as le,j as ye}from"./index-BtOcqcNl.js";import{a as tt}from"./index-D95m1iJL.js";const ot={class:"drawing-management"},st={class:"left-panel"},lt={class:"right-panel"},at={class:"panel-header"},nt={class:"header-left"},rt={key:0},it={key:1},ct={class:"header-right"},dt={class:"panel-content"},ut={class:"product-selector"},pt={class:"search-bar"},_t={class:"pagination-wrapper"},ht={class:"dialog-footer"},vt={class:"selection-info"},mt={key:0,class:"selected-count"},ft={class:"footer-buttons"},wt={class:"premium-header"},bt=e("div",{class:"header-background"},[e("div",{class:"bg-pattern"}),e("div",{class:"bg-gradient"})],-1),gt={class:"header-content"},yt={class:"header-left"},kt={class:"download-icon"},Ct={class:"icon-circle"},Lt=e("div",{class:"icon-pulse"},null,-1),Ut={class:"header-info"},Tt=e("h2",{class:"dialog-title"}," 图纸下载中心 ",-1),Rt={key:0,class:"dialog-subtitle"},Et={class:"header-right"},xt={class:"premium-content"},Pt={key:0,class:"download-progress-overlay"},Dt={class:"progress-container"},St={class:"progress-header"},Bt={class:"progress-icon"},Vt={class:"progress-info"},jt=e("h4",{class:"progress-title"}," 正在下载图纸 ",-1),$t={class:"progress-filename"},zt={class:"progress-bar-container"},Ot={class:"progress-status"},qt={key:0,class:"status-text"},Ft={key:1,class:"status-text success"},It={key:1,class:"download-workspace"},Nt={class:"featured-version"},Wt={class:"featured-header"},Gt={class:"version-status"},Mt={class:"status-badge latest"},Ht={class:"badge-icon"},Kt=e("span",null,"最新版本",-1),At={class:"version-meta"},Jt={class:"version-label"},Qt={class:"update-date"},Xt={class:"featured-content"},Yt={class:"drawing-name"},Zt={class:"drawing-desc"},eo={class:"download-actions"},to={class:"btn-content"},oo=e("span",{class:"btn-text"},"立即下载",-1),so=e("div",{class:"btn-shine"},null,-1),lo={class:"download-info"},ao={class:"info-item"},no={class:"info-item"},ro=e("span",null,"包含完整技术图纸",-1),io={key:0,class:"history-workspace"},co={class:"workspace-header"},uo={class:"header-left"},po={class:"section-icon"},_o={class:"section-info"},ho=e("h4",{class:"section-title"}," 历史版本 ",-1),vo={class:"section-desc"},mo={class:"header-right"},fo={class:"history-timeline"},wo={class:"timeline-marker"},bo=e("div",{class:"marker-dot"},null,-1),go={key:0,class:"marker-line"},yo={class:"timeline-content"},ko={class:"version-card"},Co={class:"card-header"},Lo={class:"version-info"},Uo={class:"version-number"},To={class:"version-date"},Ro=e("span",null,"下载",-1),Eo={class:"card-body"},xo={class:"version-title"},Po={class:"version-description"},Do={key:1,class:"empty-workspace"},So={class:"empty-illustration"},Bo=e("h4",null,"暂无历史版本",-1),Vo=e("p",null,"当前图纸还没有历史版本记录",-1),jo=ke({name:"pms-bill-payment"}),Io=ke({...jo,setup($o){Ze();const{service:w}=tt(),{dict:he}=et(),ae=he.get("drawing_type"),ve=he.get("color"),X=p([]),ne=p(!1),S=p(null);p("");const Y=p([]),me=p([]),re=p(!1),A=p(!1),Z=p([]),E=p([]),T=p({page:1,size:20,total:0}),O=p(""),ie=p(!1),M=p(!1),ee=p();let ce=!1;const te=p(!1),b=p(null),J=p(!1),k=p(0),V=p(!1),j=p(""),fe=Ie(()=>{var t;if(!((t=b.value)!=null&&t.historyVersions))return[];const o=b.value.historyVersions;return J.value?o:o.slice(0,3)});async function Ce(){try{const o=await w.pms.product.request({url:"/getAllProduct",method:"GET"});me.value=o==null?void 0:o.map(t=>({group_id:t.groupId,value:t.id,sku:t.sku,label:`${t.sku} ${t.name}`}))}catch(o){console.error(o)}}Ce();function de(o){return o?new Date(o).toLocaleDateString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"}):""}const ue=le.useUpsert({props:{class:"drawing-form",labelWidth:"120px"},items:[{prop:"_edit_notice",label:"",hidden:()=>!M.value,component:{name:"el-alert",props:{title:"编辑提醒",description:"您正在编辑图纸信息，修改后将生成新版本。请确认所有信息无误后再提交。",type:"warning",showIcon:!0,closable:!1}},props:{class:"full-line"}},{prop:"title",label:"图纸标题",required:!0,hidden:()=>M.value,component:{name:"el-input"}},{prop:"type",label:"图纸类型",required:!0,hidden:()=>M.value,component:{options:ae,name:"el-select"}},{prop:"description",label:"描述",hidden:()=>M.value,component:{name:"el-input",props:{type:"textarea",rows:3}}},{prop:"drawing_url",label:"图纸文件",required:!0,hidden:()=>ie.value,component:{name:"cl-upload",props:{accept:".zip,.rar",text:"上传图纸文件",limitSize:500,type:"file",disabled:!1,isPrivate:!0}}}],async onClose(o,t){ie.value=!1,M.value=!1,t()}}),Le=le.useTable({columns:[{label:"创建时间",prop:"createTime",width:160,sortable:"desc"},{label:"标题",prop:"title",minWidth:200,showOverflowTooltip:!0},{label:"类型",prop:"type",minWidth:80,formatter:o=>{var t,i;return((i=(t=ae.value)==null?void 0:t.find(v=>v.value===o.type))==null?void 0:i.label)||"-"}},{label:"图纸版本",prop:"version",minWidth:80,align:"center",formatter:o=>`V${o.version}`},{label:"描述",prop:"description",minWidth:160,showOverflowTooltip:!0,sortable:"desc"},{type:"op",label:"操作",width:360,buttons:["slot-btn-update","slot-btn-upload","slot-btn-download","delete"]}]}),oe=le.useCrud({service:w.pms.drawing,async onRefresh(o,{next:t,render:i}){const{list:v,pagination:d}=await t(o);i(v,d)}},o=>{o.refresh()}),Ue=le.useSearch({items:[{label:"产品图纸",prop:"product_id",props:{labelWidth:"100px"},component:{name:"el-select",props:{style:"width: 160px",clearable:!0,filterable:!0,onChange(o){var t;(t=oe.value)==null||t.refresh({product_id:o,page:1})}},options:me}},{label:"图纸类型",prop:"type",props:{labelWidth:"80px"},component:{name:"el-select",props:{style:"width: 160px",clearable:!0,filterable:!0,onChange(o){var t;(t=oe.value)==null||t.refresh({type:o,page:1})}},options:ae}},{label:"图纸标题",prop:"keyword",props:{labelWidth:"80px"},component:{name:"el-input",props:{clearable:!1,onChange(o){var t;(t=oe.value)==null||t.refresh({keyword:o.trim(),page:1})}}}}]});function Te(o){S.value=o,pe(o.id)}async function pe(o){try{ne.value=!0;const t=await w.pms.drawing.request({url:"/getProductsByDrawingId",method:"POST",data:{drawingId:o}});X.value=t||[]}catch(t){console.error("加载图纸产品失败:",t)}finally{ne.value=!1}}async function Re(o){var t,i;try{await Ye.confirm(`确定要移除产品 "${((t=o.product)==null?void 0:t.sku)||o.sku}" 的关联吗？`,"移除关联确认",{confirmButtonText:"确定移除",cancelButtonText:"取消",type:"warning",confirmButtonClass:"el-button--danger"}),await w.pms.drawing.request({url:"/removeDrawingProduct",method:"POST",data:{id:o.id}}),D.success("移除图纸产品成功")}catch(v){v!=="cancel"&&(console.error("移除图纸产品失败:",v),D.error("移除图纸产品失败"))}finally{pe((i=S.value)==null?void 0:i.id)}}async function Q(o={}){var t;try{re.value=!0;const{page:i=T.value.page,keyword:v=O.value}=o,d=await w.pms.product.request({url:"/page",method:"POST",data:{page:i,size:T.value.size,keyword:v}});ce=!0,Y.value=d.list||[],T.value.total=((t=d.pagination)==null?void 0:t.total)||d.total||0,T.value.page=i,Qe(()=>{Se()})}catch(i){console.error("加载产品列表失败:",i)}finally{re.value=!1}}Q();async function Ee(){var t;if(E.value.length===0)return;const o=E.value.map(i=>i.id);await w.pms.drawing.request({url:"/saveProducts",method:"POST",data:{drawingId:S.value.id,productIds:o}}),Z.value=[],E.value=[],A.value=!1,D.success("设置成功"),pe((t=S.value)==null?void 0:t.id)}function xe(o){if(ce)return;const t=Y.value.map(i=>i.id);E.value=E.value.filter(i=>!t.includes(i.id)),o.forEach(i=>{E.value.push(i)}),Z.value=E.value.map(i=>i.id)}function _e(){T.value.page=1,Q({page:1,keyword:O.value})}function Pe(o){Q({page:o,keyword:O.value})}function De(o){T.value.size=o,T.value.page=1,Q({page:1,keyword:O.value})}function Se(){ee.value&&(ee.value.clearSelection(),Y.value.forEach(o=>{Z.value.includes(o.id)&&ee.value.toggleRowSelection(o,!0)}),ce=!1)}function Be(){O.value="",T.value.page=1,E.value=X.value.map(o=>{var t,i,v;return{id:o.productId,sku:((t=o.product)==null?void 0:t.sku)||"",name:((i=o.product)==null?void 0:i.name)||"",color:((v=o.product)==null?void 0:v.color)||"",...o.product}}),Z.value=X.value.map(o=>o.productId),Q({page:1,keyword:""})}function Ve(o){var t;ie.value=!0,(t=ue.value)==null||t.edit(o)}function je(o){b.value=o,te.value=!0}async function $e(o,t,i){var v;try{V.value=!0,k.value=0,j.value=t?`${t}_v${i}`:"图纸文件";const d=await w.pms.drawing.request({url:"/getDownloadURL",method:"GET",params:{id:o}});if(!(d!=null&&d.downloadURL))throw new Error("获取下载链接失败");const{downloadURL:x,fileName:P}=d;if(x.includes("127.0.0.1")||x.includes("localhost")||x.startsWith("/public/"))try{const a=await w.pms.drawing.request({url:"/download",method:"GET",params:{id:o},responseType:"blob"});let l;if(a instanceof Blob)l=a;else if(a&&typeof a=="object"&&a.data instanceof Blob)l=a.data;else if(a&&typeof a=="object"&&a.blob)l=a.blob;else throw new Error("响应不是有效的Blob对象");const u=window.URL.createObjectURL(l),m=document.createElement("a");m.href=u,m.download=P,document.body.appendChild(m),m.click(),document.body.removeChild(m),window.URL.revokeObjectURL(u),D.success("下载成功"),V.value=!1,k.value=0,j.value="";return}catch(a){throw console.error("本地文件下载失败:",a),a}let L;try{L=await fetch(x)}catch(a){console.error("直接fetch下载失败，尝试使用后端代理:",a);try{const l=await w.pms.drawing.request({url:"/download",method:"GET",params:{id:o},responseType:"blob"});let u;if(l instanceof Blob)u=l;else if(l&&typeof l=="object"&&l.data instanceof Blob)u=l.data;else if(l&&typeof l=="object"&&l.blob)u=l.blob;else throw new Error("响应不是有效的Blob对象");const m=window.URL.createObjectURL(u),g=document.createElement("a");g.href=m,g.download=P,document.body.appendChild(g),g.click(),document.body.removeChild(g),window.URL.revokeObjectURL(m),D.success("下载成功"),V.value=!1,k.value=0,j.value="";return}catch(l){console.error("后端代理下载也失败:",l)}throw a}if(!L.ok)throw new Error(`下载失败: ${L.status}`);const q=L.headers.get("content-length"),F=q?Number.parseInt(q,10):0,I=(v=L.body)==null?void 0:v.getReader();if(!I)throw new Error("无法读取响应流");const N=[];let U=0;for(;;){const{done:a,value:l}=await I.read();if(a)break;if(N.push(l),U+=l.length,F>0){const u=Math.round(U*100/F);k.value=Math.min(u,99)}}k.value=100;const H=new Blob(N),W=window.URL.createObjectURL(H),R=document.createElement("a");R.href=W,R.download=P,document.body.appendChild(R),R.click(),document.body.removeChild(R),window.URL.revokeObjectURL(W),D.success("下载成功")}catch(d){console.error("下载失败:",d),D.error("下载失败")}finally{setTimeout(()=>{V.value=!1,k.value=0,j.value=""},1e3)}}async function ze(o,t,i){var v;try{V.value=!0,k.value=0,j.value=t?`${t}_v${i}`:"历史版本图纸";const d=await w.pms.drawing.request({url:"/getHistoryDownloadURL",method:"GET",params:{id:o}});if(!(d!=null&&d.downloadURL))throw new Error("获取下载链接失败");const{downloadURL:x,fileName:P}=d;if(x.includes("127.0.0.1")||x.includes("localhost")||x.startsWith("/public/"))try{const a=await w.pms.drawing.request({url:"/downloadHistory",method:"GET",params:{id:o},responseType:"blob"});let l;if(a instanceof Blob)l=a;else if(a&&typeof a=="object"&&a.data instanceof Blob)l=a.data;else if(a&&typeof a=="object"&&a.blob)l=a.blob;else throw new Error("响应不是有效的Blob对象");const u=window.URL.createObjectURL(l),m=document.createElement("a");m.href=u,m.download=P,document.body.appendChild(m),m.click(),document.body.removeChild(m),window.URL.revokeObjectURL(u),D.success("下载成功"),V.value=!1,k.value=0,j.value="";return}catch(a){throw console.error("历史版本本地文件下载失败:",a),a}let L;try{if(L=await fetch(x),!L.ok)throw new Error(`HTTP ${L.status}: ${L.statusText}`)}catch(a){console.error("直接fetch下载失败，尝试使用后端代理:",a);try{const l=await w.pms.drawing.request({url:"/downloadHistory",method:"GET",params:{id:o},responseType:"blob"});let u;if(l instanceof Blob)u=l;else if(l&&typeof l=="object"&&l.data instanceof Blob)u=l.data;else if(l&&typeof l=="object"&&l.blob)u=l.blob;else throw new Error("响应不是有效的Blob对象");const m=window.URL.createObjectURL(u),g=document.createElement("a");g.href=m,g.download=P,document.body.appendChild(g),g.click(),document.body.removeChild(g),window.URL.revokeObjectURL(m),D.success("下载成功");return}catch(l){console.error("后端代理下载也失败:",l)}throw a}const q=L.headers.get("content-length"),F=q?Number.parseInt(q,10):0,I=(v=L.body)==null?void 0:v.getReader();if(!I)throw new Error("无法读取响应流");const N=[];let U=0;for(;;){const{done:a,value:l}=await I.read();if(a)break;if(N.push(l),U+=l.length,F>0){const u=Math.round(U*100/F);k.value=Math.min(u,99)}}k.value=100;const H=new Blob(N),W=window.URL.createObjectURL(H),R=document.createElement("a");R.href=W,R.download=P,document.body.appendChild(R),R.click(),document.body.removeChild(R),window.URL.revokeObjectURL(W),D.success("下载成功")}catch(d){console.error("下载失败:",d),D.error("下载失败")}finally{setTimeout(()=>{V.value=!1,k.value=0,j.value=""},1e3)}}function Oe(){te.value=!1,b.value=null}function qe(o){var t;M.value=!0,(t=ue.value)==null||t.edit(o)}function Fe(o){je(o)}return(o,t)=>{const i=f("cl-refresh-btn"),v=f("cl-add-btn"),d=f("cl-flex1"),x=f("cl-search"),P=f("el-row"),C=f("el-button"),L=f("cl-table"),q=f("cl-pagination"),F=f("cl-upsert"),I=f("cl-crud"),N=f("el-tag"),U=f("el-table-column"),H=f("el-table"),W=f("el-input"),R=f("el-pagination"),a=f("el-dialog"),l=f("el-icon"),u=f("el-progress"),m=f("Document"),g=be("permission"),we=be("loading");return c(),y("div",ot,[e("div",st,[s(I,{ref_key:"Crud",ref:oe},{default:n(()=>[s(P,null,{default:n(()=>[s(i),$(s(v,null,null,512),[[g,_(w).pms.drawing.permission.add]]),s(d),s(x,{ref_key:"Search",ref:Ue},null,512)]),_:1}),s(P,{style:{"margin-top":"10px"}},{default:n(()=>[s(L,{ref_key:"Table",ref:Le,onRowClick:Te},{"slot-btn-update":n(({scope:r})=>[$((c(),B(C,{type:"success",onClick:K=>Ve(r.row)},{default:n(()=>[z(" 编辑 ")]),_:2},1032,["onClick"])),[[g,_(w).pms.drawing.permission.update]])]),"slot-btn-upload":n(({scope:r})=>[$((c(),B(C,{type:"primary",onClick:K=>qe(r.row)},{default:n(()=>[z(" 更新图纸 ")]),_:2},1032,["onClick"])),[[g,_(w).pms.drawing.permission.update]])]),"slot-btn-download":n(({scope:r})=>[$((c(),B(C,{type:"warning",onClick:K=>Fe(r.row)},{default:n(()=>[z(" 下载图纸 ")]),_:2},1032,["onClick"])),[[g,_(w).pms.drawing.permission.download]])]),_:1},512)]),_:1}),s(P,null,{default:n(()=>[s(d),s(q)]),_:1}),s(F,{ref_key:"Upsert",ref:ue},null,512)]),_:1},512)]),e("div",lt,[e("div",at,[e("div",nt,[S.value?(c(),y("h3",rt,h(S.value.title)+" - 关联产品 ",1)):(c(),y("h3",it," 请选择图纸查看关联产品 ")),S.value?(c(),B(N,{key:2,type:"warning"},{default:n(()=>[z(" 版本: V"+h(S.value.version),1)]),_:1})):G("",!0)]),e("div",ct,[S.value?$((c(),B(C,{key:0,type:"success",size:"small",onClick:t[0]||(t[0]=r=>A.value=!0)},{default:n(()=>[z(" 设置产品关联 ")]),_:1})),[[g,_(w).pms.drawing.permission.saveProducts]]):G("",!0)])]),$((c(),y("div",dt,[s(H,{data:X.value,border:"",height:"100%","empty-text":"暂无关联产品"},{default:n(()=>[s(U,{prop:"product.sku",label:"SKU","min-width":"150"}),s(U,{prop:"product.name",label:"产品名称","min-width":"200","show-overflow-tooltip":""}),s(U,{prop:"product.color",label:"颜色","min-width":"120",align:"center"},{default:n(r=>[e("span",null,h(_(ye)(_(ve),parseInt(r.row.product.color))),1)]),_:1}),s(U,{label:"操作",width:"120",fixed:"right"},{default:n(({row:r})=>[$((c(),B(C,{type:"danger",size:"small",onClick:K=>Re(r)},{default:n(()=>[z(" 移除关联 ")]),_:2},1032,["onClick"])),[[g,_(w).pms.drawing.permission.removeDrawingProduct]])]),_:1})]),_:1},8,["data"])])),[[we,ne.value]])]),s(a,{modelValue:A.value,"onUpdate:modelValue":t[5]||(t[5]=r=>A.value=r),title:"选择关联产品",width:"1200px","close-on-click-modal":!1,onOpen:Be},{footer:n(()=>[e("div",ht,[e("div",vt,[E.value.length>0?(c(),y("span",mt," 已选择 "+h(E.value.length)+" 个产品 ",1)):G("",!0)]),e("div",ft,[s(C,{onClick:t[4]||(t[4]=r=>A.value=!1)},{default:n(()=>[z(" 取消 ")]),_:1}),s(C,{type:"primary",disabled:E.value.length===0,onClick:Ee},{default:n(()=>[z(" 确认设置 ("+h(E.value.length)+") ",1)]),_:1},8,["disabled"])])])]),default:n(()=>[e("div",ut,[e("div",pt,[s(W,{modelValue:O.value,"onUpdate:modelValue":t[1]||(t[1]=r=>O.value=r),placeholder:"请输入产品SKU或名称进行搜索",clearable:"",style:{width:"300px"},onKeyup:Ne(_e,["enter"]),onClear:_e},{append:n(()=>[s(C,{icon:"Search",onClick:_e})]),_:1},8,["modelValue"])]),$((c(),B(H,{ref_key:"productTableRef",ref:ee,data:Y.value,border:"",height:"500px",onSelectionChange:xe},{default:n(()=>[s(U,{type:"selection",width:"55"}),s(U,{prop:"sku",label:"SKU","min-width":"150"}),s(U,{prop:"name",label:"产品名称","min-width":"200","show-overflow-tooltip":""}),s(U,{prop:"color",label:"颜色","min-width":"220",align:"center"},{default:n(r=>[e("span",null,h(_(ye)(_(ve),parseInt(r.row.color))),1)]),_:1})]),_:1},8,["data"])),[[we,re.value]]),e("div",_t,[s(R,{"current-page":T.value.page,"onUpdate:currentPage":t[2]||(t[2]=r=>T.value.page=r),"page-size":T.value.size,"onUpdate:pageSize":t[3]||(t[3]=r=>T.value.size=r),"page-sizes":[20,50,100,200],total:T.value.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:De,onCurrentChange:Pe},null,8,["current-page","page-size","total"])])])]),_:1},8,["modelValue"]),s(a,{modelValue:te.value,"onUpdate:modelValue":t[8]||(t[8]=r=>te.value=r),width:"800px","close-on-click-modal":!1,"show-close":!1,class:"premium-download-dialog","destroy-on-close":""},{header:n(()=>[e("div",wt,[bt,e("div",gt,[e("div",yt,[e("div",kt,[e("div",Ct,[s(l,null,{default:n(()=>[s(_(se))]),_:1})]),Lt]),e("div",Ut,[Tt,b.value?(c(),y("p",Rt,h(b.value.title),1)):G("",!0)])]),e("div",Et,[s(C,{link:"",class:"premium-close-btn",onClick:Oe},{default:n(()=>[s(l,null,{default:n(()=>[s(_(Je))]),_:1})]),_:1})])])])]),default:n(()=>[e("div",xt,[V.value?(c(),y("div",Pt,[e("div",Dt,[e("div",St,[e("div",Bt,[s(l,{class:"rotating"},{default:n(()=>[s(_(se))]),_:1})]),e("div",Vt,[jt,e("p",$t,h(j.value),1)])]),e("div",zt,[s(u,{percentage:k.value,"stroke-width":8,"show-text":!0,format:r=>`${r}%`,status:"success",class:"download-progress-bar"},null,8,["percentage","format"])]),e("div",Ot,[k.value<100?(c(),y("span",qt," 下载中... "+h(k.value)+"% ",1)):(c(),y("span",Ft," 下载完成！正在保存文件... "))])])])):G("",!0),b.value?(c(),y("div",It,[e("div",Nt,[e("div",Wt,[e("div",Gt,[e("div",Mt,[e("div",Ht,[s(l,null,{default:n(()=>[s(_(We))]),_:1})]),Kt]),e("div",At,[e("span",Jt,"v"+h(b.value.version),1),e("span",Qt,h(de(b.value.updateTime)),1)])])]),e("div",Xt,[e("h3",Yt,h(b.value.title),1),e("p",Zt,h(b.value.description),1),e("div",eo,[s(C,{type:"primary",size:"large",class:"primary-download-btn",onClick:t[6]||(t[6]=r=>$e(b.value.id,b.value.title,b.value.version))},{default:n(()=>[e("div",to,[s(l,{class:"download-icon"},{default:n(()=>[s(_(se))]),_:1}),oo]),so]),_:1}),e("div",lo,[e("div",ao,[s(l,null,{default:n(()=>[s(_(ge))]),_:1}),e("span",null,"更新于 "+h(de(b.value.updateTime)),1)]),e("div",no,[s(l,null,{default:n(()=>[s(m)]),_:1}),ro])])])])]),b.value.historyVersions&&b.value.historyVersions.length>0?(c(),y("div",io,[e("div",co,[e("div",uo,[e("div",po,[s(l,null,{default:n(()=>[s(_(ge))]),_:1})]),e("div",_o,[ho,e("p",vo," 共 "+h(b.value.historyVersions.length)+" 个历史版本可供下载 ",1)])]),e("div",mo,[s(C,{link:"",class:"toggle-btn",onClick:t[7]||(t[7]=r=>J.value=!J.value)},{default:n(()=>[e("span",null,h(J.value?"收起":"展开全部"),1),s(l,null,{default:n(()=>[J.value?(c(),B(_(Ge),{key:0})):(c(),B(_(Me),{key:1}))]),_:1})]),_:1})])]),e("div",fo,[(c(!0),y(He,null,Ke(fe.value,(r,K)=>(c(),y("div",{key:r.id,class:"timeline-item animate-in",style:Xe({animationDelay:`${K*.1}s`})},[e("div",wo,[bo,K<fe.value.length-1?(c(),y("div",go)):G("",!0)]),e("div",yo,[e("div",ko,[e("div",Co,[e("div",Lo,[e("span",Uo,"v"+h(r.version),1),e("span",To,h(de(r.createTime)),1)]),s(C,{size:"small",class:"version-download-btn",onClick:zo=>ze(r.id,r.title,r.version)},{default:n(()=>[s(l,null,{default:n(()=>[s(_(se))]),_:1}),Ro]),_:2},1032,["onClick"])]),e("div",Eo,[e("h5",xo,h(r.title),1),e("p",Po,h(r.description),1)])])])],4))),128))])])):(c(),y("div",Do,[e("div",So,[s(l,null,{default:n(()=>[s(_(Ae))]),_:1})]),Bo,Vo]))])):G("",!0)])]),_:1},8,["modelValue"])])}}});export{Io as default};
