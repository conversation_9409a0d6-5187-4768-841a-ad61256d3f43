import{ba as K,R as S,$ as B,aw as H,E as Q,c as C,e as X,S as Z,bb as ee,f as N,h as v,i as h,w as f,j as D,F as I,o as y,q as w,B as q,v as E,G as te,H as oe,am as ne,a9 as le,t as $,s as re,y as ae}from"./.pnpm-hVqhwuVC.js";import{s as V,i as F,m as se,n as pe,D as ie,c as ce,h as ue}from"./index-DkYL1aws.js";import{a as A}from"./index-C6cm1h61.js";const U=["#409EFF","#67C23A","#E6A23C","#F56C6C","#909399","#B0CFEB","#FF9B91","#E6A23C","#BFAD6F","#FB78F2"],me=[{group:["province","city","district"],table:{label:"省市区",formatter(c){return`${c.province}-${c.city}-${c.district}`}},form:{label:"省市区",prop:"pca",hook:"pca",component:{name:"cl-distpicker"}}},{test:["address","addr"],table:{showOverflowTooltip:!0},form:{name:"el-input",props:{type:"textarea",rows:3}}},{test:["createTime"],table:{sortable:"desc",width:160}},{test:["updateTime"],table:{sortable:"custom",width:160}},{test:["avatar","img","image","pic","photo","picture","head","icon"],table:{name:"cl-image",props:{size:60}},form:{name:"cl-upload"}},{test:["avatars","imgs","images","pics","photos","pictures","heads","icons"],table:{name:"cl-image",props:{size:60}},form:{name:"cl-upload",props:{multiple:!0}}},{test:["file","attachment","attach","url","video","music"],table:{name:"cl-link"},form:{name:"cl-upload",props:{type:"file",limit:1}}},{test:["files","attachments","attachs","urls","videos","musics"],table:{name:"cl-link"},form:{name:"cl-upload",props:{type:"file",multiple:!0}}},{test:["enable","status","isDefault"],table:{name:"cl-switch"},form:{name:"cl-switch"}},{test:["type","classify","category"],handler:"dict"},{test:["types","classifys","categorys"],handler:"dict_multiple"},{test:["dates","dateRange","dateScope"],table:{name:"cl-date-text",props:{format:"YYYY-MM-DD"}},form:{name:"el-date-picker",props:{type:"daterange",valueFormat:"YYYY-MM-DD"}}},{test:["times","timeRange","timeScope"],form:{name:"el-date-picker",props:{type:"datetimerange",valueFormat:"YYYY-MM-DD HH:mm:ss",defaultTime:[new Date(2e3,1,1,0,0,0),new Date(2e3,1,1,23,59,59)]}}},{test:["time"],form:{name:"el-date-picker",props:{type:"datetime",valueFormat:"YYYY-MM-DD HH:mm:ss"}}},{test:["star","stars"],table:{name:"el-rate",props:{disabled:!0}},form:{name:"el-rate"}},{test:["progress","rate","ratio"],table:{name:"el-progress"},form:{name:"el-slider",props:{style:{width:"200px"}}}},{test:["num","price","age","amount","stock"],form:{hook:{bind:["number"]},component:{name:"el-input-number",props:{min:0}}}},{test:["remark","desc"],table:{showOverflowTooltip:!0},form:{name:"el-input",props:{type:"textarea",rows:4}}},{test:["rich","text","html","content","introduce"],form:{name:"cl-editor-wang"}},{test:["code","codes"],form:{name:"cl-editor-monaco"}},{test:["date","day"],table:{name:"cl-date-text",props:{format:"YYYY-MM-DD"}},form:{name:"el-date-picker",props:{type:"date",valueFormat:"YYYY-MM-DD"}}}],de={dict({comment:c}){const[i,...l]=c.split(" "),r=l.map((m,d)=>{const[n,b]=m.split("-"),o={label:b,value:isNaN(Number(n))?n:Number(n),color:void 0};return d>0&&U[d]&&(o.color=U[d]),o}),s={table:{label:i,dict:r},form:{label:i,component:{name:"",options:r}}};return r[0]&&(s.form.value=r[0].value),s.form.component.name=l.length>4?"el-select":"el-radio-group",s},dict_multiple(c){var r,s;const{table:i,form:l}=this.dict(c);switch((r=l.component)!=null&&r.props||(l.component.props={}),l.value||(l.value=[]),(s=l.component)==null?void 0:s.name){case"el-select":l.component.props.multiple=!0,l.component.props.filterable=!0;break;case"el-radio-group":l.component.name="el-checkbox-group";break}return{table:i,form:l}}};function fe(c,i){const l=c.propertyName;let r=c.comment,s,m=!1;me.find(n=>{let b=!1;if(n.test&&(b=!!n.test.find(o=>K(o)?o.test(l):S(o)?o(l):B(o)?o==l?!0:new RegExp(`${o}$`).test(l.toLocaleLowerCase()):!1)),n.group&&n.group.includes(l)&&n.group.some(o=>i.find(a=>a.propertyName==o))&&(n.group[0]==l?b=!0:m=!0),b)if(n.handler){const o=B(n.handler)?de[n.handler]:n.handler;S(o)&&(s=o(c))}else s={...n,test:void 0};return b});function d(n){return r=r.split(" ")[0],n!=null&&n.name?{prop:l,label:r,component:n}:{prop:l,label:r,...n}}return{column:d(s==null?void 0:s.table),item:d(s==null?void 0:s.form),isHidden:m}}function R(c){const i=[];let l=JSON.stringify(c,(r,s)=>{if(S(s)){const m=s.toString();return i.push([JSON.stringify({[r]:m}),m]),m}else return s});return i.forEach(r=>{l=l.replace(r[0].substring(1,r[0].length-1),r[1])}),l}function be(){function c({router:i="",columns:l=[],prefix:r="",api:s=[],module:m}){const d={items:[]},n={columns:[]};l.forEach(e=>{var k,g,Y;const{item:t,column:p,isHidden:_}=fe(e,l);if(_)return!1;if(e.nullable||(t.required=!0),["createTime","updateTime","id","endTime","endDate"].includes(t.prop||"")||(t.component||(t.component={name:"el-input"}),d.items.push(t)),["startTime","startDate"].includes(t.prop)){const T=t.prop.replace("start","");if(l.find(x=>x.propertyName==`end${T}`)){t.prop=T.toLocaleLowerCase();const x=t.prop=="time";t.label=x?"时间范围":"日期范围",t.hook="datetimeRange",t.component={name:"el-date-picker",props:{type:x?"datetimerange":"daterange",valueFormat:x?"YYYY-MM-DD HH:mm:ss":"YYYY-MM-DD 00:00:00",defaultTime:[new Date(2e3,1,1,0,0,0),new Date(2e3,1,1,23,59,59)]}}}}(g=(k=t.component)==null?void 0:k.name)!=null&&g.includes("cl-editor-")&&(n.columns.push(p),p.component={name:"cl-editor-preview",props:{name:H((Y=t.component)==null?void 0:Y.name.split("-"))}}),n.columns.push(p)});const b=r.replace("/admin","service").split("/");b.includes(m)||b.splice(1,0,m);const o=s.map(e=>e.path),a={add:o.includes("/add"),del:o.includes("/delete"),update:o.includes("/info")&&o.includes("/update"),page:o.includes("/page"),upsert:!0};if(a.upsert=a.add||a.update,a.del||a.upsert){const e=[];a.upsert&&e.push("edit"),a.del&&e.push("delete"),n.columns.push({type:"op",buttons:e})}return a.del?n.columns.unshift({type:"selection"}):n.columns.unshift({label:"#",type:"index"}),`<template>
            <cl-crud ref="Crud">
                <cl-row>
                    <!-- 刷新按钮 -->
                    <cl-refresh-btn />
                    ${a.add?`<!-- 新增按钮 -->
<cl-add-btn />`:""}
                    ${a.del?`<!-- 删除按钮 -->
<cl-multi-delete-btn />`:""}
                    <cl-flex1 />
                    <!-- 关键字搜索 -->
                    <cl-search-key />
                </cl-row>

                <cl-row>
                    <!-- 数据表格 -->
                    <cl-table ref="Table" />
                </cl-row>

                <cl-row>
                    <cl-flex1 />
                    <!-- 分页控件 -->
                    <cl-pagination />
                </cl-row>

                <!-- 新增、编辑 -->
                <cl-upsert ref="Upsert" />
            </cl-crud>
        </template>

        <script lang="ts" name="${i.replace(/^\//,"").replace(/\//g,"-")}" setup>
        import { useCrud, useTable, useUpsert } from "@cool-vue/crud";
        import { useCool } from "/@/cool";

        const { service } = useCool();

        // cl-upsert
        const Upsert = useUpsert(${R(d)});

        // cl-table
        const Table = useTable(${R(n)});

        // cl-crud
        const Crud = useCrud(
            {
                service: ${b.join(".")}
            },
            (app) => {
                app.refresh();
            }
        );
        <\/script>`}return{createVue:c}}function he(){const{createVue:c}=be(),i={add:"新增",delete:"删除",info:"查看",list:"所有列表",page:"分页列表",update:"更新"};function l(r){return new Promise((s,m)=>{var d;r.viewPath=`modules/${r.module}/views/${H((d=r.router)==null?void 0:d.split("/"))}.vue`,V.base.sys.menu.add({type:1,isShow:!0,keepAlive:!0,...r,api:void 0,code:void 0,columns:void 0}).then(n=>{var o;const b=(o=r.api)==null?void 0:o.map(a=>{var t;const e={type:2,parentId:n.id,name:i[a.summary]||a.summary,perms:[a.path]};return a.path==="/update"&&(t=r.api)!=null&&t.find(p=>p.path==="/info")&&e.perms.push("/info"),{...e,perms:e.perms.map(p=>{var _;return(((_=r.prefix)==null?void 0:_.replace("/admin/",""))+p).replace(/\//g,":")}).join(",")}});V.base.sys.menu.add(b).then(()=>{s(()=>{V.request({url:"/__cool_createMenu",method:"POST",proxy:!1,data:{code:c(r),...r}}).then(()=>{location.reload()})})})}).catch(n=>{Q.error(n.message),m(n)})})}return{create:l,createVue:c}}const _e=C({name:"menu-create"}),ye=C({..._e,setup(c){const{service:i,mitt:l}=A(),r=he(),s=F.useForm(),m=[],d=X(()=>se(m.map(o=>o.value)));function n(){var o;(o=s.value)==null||o.open({title:"快速创建",width:"800px",items:[{prop:"module",label:"选择模块",span:10,component:{name:"cl-select",props:{filterable:!0,clearable:!0,placeholder:"请选择模块",allowCreate:!0,defaultFirstOption:!0,options:pe.dirs}},required:!0},{prop:"entity",label:"数据结构",span:14,component:{name:"slot-entity"},required:!0},{prop:"name",label:"菜单名称",span:10,component:{name:"el-input",props:{placeholder:"请输入菜单名称"}},required:!0},{prop:"router",label:"菜单路由",span:14,component:{name:"el-input",props:{placeholder:"请输入菜单路由，如：/test"}}},{prop:"parentId",label:"上级节点",component:{name:"cl-menu-select",props:{type:1}}},{prop:"keepAlive",value:0,label:"路由缓存",component:{name:"el-radio-group",options:[{label:"开启",value:1},{label:"关闭",value:0}]}},{prop:"icon",label:"菜单图标",component:{name:"cl-menu-icon"}},{prop:"orderNum",label:"排序号",component:{name:"el-input-number",props:{placeholder:"请填写排序号",min:0,max:99,"controls-position":"right"}}},{prop:"isCreateFile",label:"是否创建文件",value:1,component:{name:"el-radio-group",options:[{label:"是",value:1},{label:"否",value:0}]}}],on:{submit(a,{done:e,close:t}){const{api:p,prefix:_,columns:k}=m.find(g=>g.value===a.entity.filter(Boolean).join("/"));r.create({...a,router:a.router,module:a.module,prefix:_,api:p,columns:k}).then(g=>{a.isCreateFile&&g(),l.emit("magic.createMenu"),t()}).catch(()=>{e()})}}})}function b(o){var t;const a=Object.values(o),e=m.find(p=>p.value===a.join("/")||`${p.value}/`===a.join("/"));e&&((t=s.value)==null||t.setForm("router",`/${e.value}`))}return Z(()=>{i.base.open.eps().then(o=>{for(const a in o)o[a].forEach(e=>{ee(e.columns)||m.push({value:e.prefix.substring(7),...e})})})}),(o,a)=>{const e=h("el-button"),t=h("el-cascader"),p=h("cl-form");return y(),N(I,null,[v(e,{type:"success",onClick:n},{default:f(()=>[D(" 快速创建 ")]),_:1}),v(p,{ref_key:"Form",ref:s},{"slot-entity":f(({scope:_})=>[v(t,{modelValue:_.entity,"onUpdate:modelValue":k=>_.entity=k,filterable:"",separator:".",options:d.value,onChange:b},null,8,["modelValue","onUpdate:modelValue","options"])]),_:1},512)],64)}}}),ve=C({name:"auto-menu"}),ge=C({...ve,setup(c){return A(),(i,l)=>E(ie)?(y(),w(ye,{key:0})):q("",!0)}}),we={key:1},ke={key:1},xe=C({name:"sys-menu"}),Fe=C({...xe,setup(c){const{service:i,mitt:l}=A(),{menu:r}=ce(),s=F.useTable({contextMenu:[e=>({label:"新增",hidden:!(e.type!=2&&i.base.sys.user._permission.add),callback(t){o(e),t()}}),"update","delete",e=>({label:"添加权限",hidden:!(e.type==1&&i.base.sys.user._permission.add),callback(t){a(e),t()}})],columns:[{type:"selection"},{prop:"name",label:"名称",align:"left",width:200,fixed:"left"},{prop:"icon",label:"图标",width:80},{prop:"type",label:"类型",width:100,dict:[{label:"目录",value:0},{label:"菜单",value:1,type:"success"},{label:"权限",value:2,type:"danger"}]},{prop:"router",label:"节点路由",minWidth:160},{prop:"keepAlive",label:"路由缓存",width:100},{prop:"viewPath",label:"文件路径",minWidth:200,showOverflowTooltip:!0},{prop:"perms",label:"权限",headerAlign:"center",minWidth:300},{prop:"orderNum",label:"排序号",width:90,fixed:"right"},{prop:"updateTime",label:"更新时间",sortable:"custom",width:160},{label:"操作",type:"op",width:250,buttons:["slot-add","edit","delete"]}]}),m=F.useUpsert({dialog:{width:"800px"},items:[{prop:"type",value:0,label:"节点类型",required:!0,component:{name:"el-radio-group",options:[{label:"目录",value:0},{label:"菜单",value:1},{label:"权限",value:2}]}},{prop:"name",label:"节点名称",component:{name:"el-input"},required:!0},{prop:"parentId",label:"上级节点",hook:"empty",component:{name:"slot-parentId"}},{prop:"router",label:"节点路由",hidden:({scope:e})=>e.type!==1,component:{name:"el-input",props:{placeholder:"请输入节点路由，如：/test"}}},{prop:"keepAlive",value:0,label:"路由缓存",hidden:({scope:e})=>e.type!==1,component:{name:"el-radio-group",options:[{label:"开启",value:1},{label:"关闭",value:0}]}},{prop:"isShow",label:"是否显示",value:!0,hidden:({scope:e})=>e.type===2,flex:!1,component:{name:"el-switch",props:{activeValue:1,inactiveValue:0}}},{prop:"viewPath",label:"文件路径",hidden:({scope:e})=>e.type!==1,component:{name:"cl-menu-file"}},{prop:"icon",label:"节点图标",hidden:({scope:e})=>e.type===2,component:{name:"cl-menu-icon"}},{prop:"orderNum",label:"排序号",component:{name:"el-input-number",props:{placeholder:"请填写排序号",min:0,max:99,"controls-position":"right"}}},{prop:"perms",label:"权限",hidden:({scope:e})=>e.type!==2,component:{name:"cl-menu-perms"}}],plugins:[F.setFocus("name")]}),d=F.useCrud({service:i.base.sys.menu,onRefresh(e,{render:t}){i.base.sys.menu.list().then(p=>{p.map(_=>{_.permList=_.perms?_.perms.split(","):[]}),t(ue(p)),r.get()})}},e=>{e.refresh()});function n(e){var t;(t=d.value)==null||t.refresh(e)}function b(e,t){var p;t!=null&&t.property&&e.children&&((p=s.value)==null||p.toggleRowExpansion(e))}function o({type:e,id:t}){var p;(p=d.value)==null||p.rowAppend({parentId:t,parentType:e,type:e+1,keepAlive:1,isShow:1})}function a({id:e}){var t;(t=d.value)==null||t.rowAppend({parentId:e,type:2})}return l.on("magic.createMenu",n),(e,t)=>{const p=h("cl-refresh-btn"),_=h("cl-add-btn"),k=h("cl-multi-delete-btn"),g=h("cl-row"),Y=h("el-tag"),T=h("cl-svg"),x=h("el-link"),L=h("el-icon"),O=h("el-button"),P=h("cl-table"),j=h("cl-menu-select"),z=h("cl-upsert"),W=h("cl-crud"),J=oe("permission");return y(),w(W,{ref_key:"Crud",ref:d},{default:f(()=>[v(g,null,{default:f(()=>[v(p),v(_),v(k),v(ge)]),_:1}),v(g,null,{default:f(()=>[v(P,{ref_key:"Table",ref:s,"row-key":"id",onRowClick:b},{"column-name":f(({scope:u})=>[ae("span",null,$(u.row.name),1),u.row.isShow?q("",!0):(y(),w(Y,{key:0,effect:"dark",type:"danger",size:"small","disable-transitions":"",style:{"margin-left":"10px"}},{default:f(()=>[D(" 隐藏 ")]),_:1}))]),"column-icon":f(({scope:u})=>[v(T,{name:u.row.icon,size:"16px",style:{"margin-top":"5px"}},null,8,["name"])]),"column-perms":f(({scope:u})=>[(y(!0),N(I,null,re(u.row.permList,(M,G)=>(y(),w(Y,{key:G,effect:"plain",size:"small",style:{margin:"2px","letter-spacing":"0.5px"}},{default:f(()=>[D($(M),1)]),_:2},1024))),128))]),"column-router":f(({scope:u})=>[u.row.type==1?(y(),w(x,{key:0,type:"success",href:u.row.router},{default:f(()=>[D($(u.row.router),1)]),_:2},1032,["href"])):(y(),N("span",we,$(u.row.router),1))]),"column-keepAlive":f(({scope:u})=>[u.row.type===1?(y(),w(L,{key:0},{default:f(()=>[u.row.keepAlive?(y(),w(E(ne),{key:0})):(y(),w(E(le),{key:1}))]),_:2},1024)):(y(),N("span",ke))]),"slot-add":f(({scope:u})=>[te((y(),w(O,{type:"success",text:"",bg:"",onClick:M=>o(u.row)},{default:f(()=>[D(" 新增 ")]),_:2},1032,["onClick"])),[[J,{and:[E(i).base.sys.menu.permission.add,u.row.type!=2]}]])]),_:1},512)]),_:1}),v(z,{ref_key:"Upsert",ref:m},{"slot-parentId":f(({scope:u})=>[v(j,{modelValue:u.parentId,"onUpdate:modelValue":M=>u.parentId=M,type:u.type},null,8,["modelValue","onUpdate:modelValue","type"])]),_:1},512)]),_:1},512)}}});export{Fe as default};
