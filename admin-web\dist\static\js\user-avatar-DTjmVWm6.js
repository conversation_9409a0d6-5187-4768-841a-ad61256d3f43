import{_ as c}from"./index.vue_vue_type_script_setup_true_name_cl-avatar_lang-Dwbve7L9.js";import{_ as f}from"./text-overflow-tooltip.vue_vue_type_script_setup_true_name_text-overflow-tooltip_lang-D_kIBTxD.js";import{c as m,f as i,h as t,i as d,w as v,W as h,o as u}from"./.pnpm-hVqhwuVC.js";import{_ as z}from"./_plugin-vue_export-helper-DlAUqK2U.js";const y={key:0},k=m({name:"user-avatar"}),w=m({...k,props:{user:{type:Object,default:()=>({})},size:{type:Number,default:30},inline:{type:Boolean,default:!0},showName:{type:Boolean,default:!0}},setup(_){const e=_;return(x,B)=>{var a,s,r,o;const p=d("el-tooltip");return e.showName?(u(),i("div",{key:1,class:h(["user-avatar",{inline:e.inline}])},[t(c,{src:(s=e.user)==null?void 0:s.headImg,title:(r=e.user)==null?void 0:r.name,size:e.size,"cursor-default":"",class:"user-avatar-avatar"},null,8,["src","title","size"]),t(f,{content:((o=e.user)==null?void 0:o.name)||"系统管理员",class:"user-avatar-title"},null,8,["content"])],2)):(u(),i("div",y,[t(p,{content:((a=e.user)==null?void 0:a.name)||"系统管理员"},{default:v(()=>{var n,l;return[t(c,{src:(n=e.user)==null?void 0:n.headImg,title:(l=e.user)==null?void 0:l.name,size:e.size,"cursor-default":"",class:"user-avatar-avatar"},null,8,["src","title","size"])]}),_:1},8,["content"])]))}}}),g=z(w,[["__scopeId","data-v-3dff6aa1"]]);export{g as U};
