import{c as v,b as g,S as M,f,E as m,o as h,r as P,y as a,h as i,w as u,i as x,v as c,G as V,b7 as k,L as B,j as $,t as E,af as N,ag as z}from"./.pnpm-hVqhwuVC.js";import{L as D,_ as H}from"./lang-select-BplMTWvo.js";import{k as K,z as j}from"./index-BtOcqcNl.js";import{a as I}from"./index-D95m1iJL.js";import{_ as L}from"./_plugin-vue_export-helper-DlAUqK2U.js";const G=["innerHTML"],q=["src"],A=v({name:"undefined"}),F=v({...A,emits:["update:modelValue","change"],setup(p,{expose:b,emit:w}){const _=w,{service:y}=I(),o=g(""),s=g("");function t(){y.base.open.captcha({height:40,width:150}).then(({captchaId:e,data:l})=>{l.includes(";base64,")?o.value=l:s.value=l,_("update:modelValue",e),_("change",{base64:o,svg:s,captchaId:e})}).catch(e=>{m.error(e.message)})}return M(()=>{t()}),b({refresh:t}),(e,l)=>(h(),f("div",{class:"captcha",onClick:t},[s.value?(h(),f("div",{key:0,class:"svg",innerHTML:s.value},null,8,G)):(h(),f("img",{key:1,class:"base64",src:o.value,alt:""},null,8,q))]))}}),J=L(F,[["__scopeId","data-v-c15399b7"]]),O=p=>(N("data-v-9310d48a"),p=p(),z(),p),Q={class:"page-login"},R={class:"box"},W={class:"login-header"},X=O(()=>a("div",{class:"login-logo"},[a("img",{src:H,alt:"Logo"})],-1)),Y=["placeholder"],Z=["placeholder"],ee={class:"row"},oe=["placeholder"],ae={class:"op"},se=v({name:"login"}),te=v({...se,setup(p){const{router:b,service:w}=I(),{user:_,app:y}=K(),{t:o}=j(),s=g(null),t=g(!1),e=P({username:"",password:"",captchaId:"",verifyCode:""});async function l(){if(!e.username)return m.error(o("base.login.Username can not be empty"));if(!e.password)return m.error(o("base.login.Password can not be empty"));if(!e.verifyCode)return m.error(o("base.login.Image verification code cannot be empty"));t.value=!0;try{await w.base.open.login(e).then(d=>{_.setToken(d),window.location.reload()}),await Promise.all(y.events.hasToken.map(d=>d())),b.push("/")}catch(d){s.value&&s.value.refresh(),m.error(d.message)}t.value=!1}function S(){e.verifyCode=""}return(d,n)=>{const C=x("el-form-item"),T=x("el-button"),U=x("el-form");return h(),f("div",Q,[a("div",R,[a("div",W,[X,i(D)]),i(U,{"label-position":"top",class:"form",disabled:t.value,size:"large"},{default:u(()=>[i(C,{label:c(o)("base.login.Username")},{default:u(()=>[V(a("input",{"onUpdate:modelValue":n[0]||(n[0]=r=>e.username=r),placeholder:c(o)("base.login.Please enter user name"),maxlength:"20",autocomplete:"on"},null,8,Y),[[k,e.username]])]),_:1},8,["label"]),i(C,{label:c(o)("base.login.Password")},{default:u(()=>[V(a("input",{"onUpdate:modelValue":n[1]||(n[1]=r=>e.password=r),type:"password",placeholder:c(o)("base.login.Please enter password"),maxlength:"20",autocomplete:"off"},null,8,Z),[[k,e.password]])]),_:1},8,["label"]),i(C,{label:c(o)("base.login.Verification code")},{default:u(()=>[a("div",ee,[V(a("input",{"onUpdate:modelValue":n[2]||(n[2]=r=>e.verifyCode=r),placeholder:c(o)("base.login.Image verification code"),maxlength:"4",onKeyup:B(l,["enter"])},null,40,oe),[[k,e.verifyCode]]),i(J,{ref_key:"captcha",ref:s,modelValue:e.captchaId,"onUpdate:modelValue":n[3]||(n[3]=r=>e.captchaId=r),onChange:S},null,8,["modelValue"])])]),_:1},8,["label"]),a("div",ae,[i(T,{round:"",loading:t.value,onClick:l},{default:u(()=>[$(E(c(o)("base.login.Login")),1)]),_:1},8,["loading"])])]),_:1},8,["disabled"])])])}}}),de=L(te,[["__scopeId","data-v-9310d48a"]]);export{de as default};
