import{c as v,b as h,z as k,S as x,aO as b,i as V,f as H,o as q,y as w,h as g,v as d,W as y}from"./.pnpm-hVqhwuVC.js";import{p as M}from"./index-BtOcqcNl.js";import{a as S}from"./index-D95m1iJL.js";import{_ as B}from"./_plugin-vue_export-helper-DlAUqK2U.js";const E=v({name:"cl-editor-quill"}),z=v({...E,props:{modelValue:null,options:Object,height:{type:[String,Number],default:400},disabled:Boolean},emits:["update:modelValue","load"],setup(c,{emit:_}){const l=c,u=_,{refs:m,setRefs:n}=S();let e=null;const i=h(""),s=h(0);function f(o){if(l.disabled)return!1;const t=e.getSelection();t&&(s.value=t.index),m[o].open()}function p(o){o.length>0&&(o.forEach((t,a)=>{["image","video"].includes(t.type)&&e.insertEmbed(s.value+a,t.type,t.url,b.sources.USER)}),e.setSelection(s.value+o.length))}function r(o){e.root.innerHTML=o||""}function C(){e.container.style.height=M(l.height)}return k(()=>l.modelValue,o=>{o?o!==i.value&&r(o):r("")}),x(()=>{e=new b(m.editor,{theme:"snow",placeholder:"输入内容",modules:{toolbar:[["bold","italic","underline","strike"],["blockquote","code-block"],[{header:1},{header:2}],[{list:"ordered"},{list:"bullet"}],[{script:"sub"},{script:"super"}],[{indent:"-1"},{indent:"+1"}],[{direction:"rtl"}],[{size:["small",!1,"large","huge"]}],[{header:[1,2,3,4,5,6,!1]}],[{color:[]},{background:[]}],[{font:[]}],[{align:[]}],["clean"],["link","video","image"]]},...l.options}),e.getModule("toolbar").addHandler("image",()=>{f("image")}),e.getModule("toolbar").addHandler("video",()=>{f("video")}),e.on("text-change",()=>{i.value=e.root.innerHTML,u("update:modelValue",i.value)}),r(l.modelValue),C(),e.enable(!l.disabled),u("load",e)}),(o,t)=>{const a=V("cl-upload-space");return q(),H("div",{class:y(["cl-editor-quill",{disabled:c.disabled}])},[w("div",{ref:d(n)("editor"),class:"editor"},null,512),g(a,{ref:d(n)("image"),accept:"image/*","show-btn":!1,onConfirm:p},null,512),g(a,{ref:d(n)("video"),accept:"video/*","show-btn":!1,onConfirm:p},null,512)],2)}}}),O=B(z,[["__scopeId","data-v-6a9f4f51"]]);export{O as default};
