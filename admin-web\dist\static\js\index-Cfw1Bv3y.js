import{c as J,ay as Z,r as ee,X as se,az as te,aA as oe,b as g,S as le,i as w,H as j,f as _,o as n,y as r,h as a,J as C,B as I,G as $,t as B,w as c,v,aB as ae,q as k,an as ne,F as ie,s as re,W as A,aC as ce,I as ue,aD as H,aE as de,a0 as _e,E as b,T as ve}from"./.pnpm-hVqhwuVC.js";import{i as N,u as fe}from"./index-BtOcqcNl.js";import{_ as pe}from"./_plugin-vue_export-helper-DlAUqK2U.js";const me={class:"cl-view-group__wrap"},he={class:"cl-view-group__left"},ge={class:"scope"},we={class:"head"},Ce={class:"list"},ke=["infinite-scroll-disabled"],be=["onClick","onContextmenu"],ye={class:"cl-view-group__right"},xe={class:"head"},Me={class:"title"},Ee={key:0,class:"content"},$e=J({name:"cl-view-group"}),Be=J({...$e,setup(Se,{expose:L}){Z(e=>({"5e683d44":t.leftWidth}));const{browser:y,onScreenChange:P}=fe(),X=se(),O=N.useForm(),t=ee(Object.assign({label:"组",title:"列表",leftWidth:"300px",service:{}},te("useViewGroup__options"))),T=!!X.left;oe.isEmpty(t.service)&&!T&&console.error("[cl-view-group] 参数 service 不能为空");const S=g(!1),d=g([]),f=g(!0),i=g();function p(e){f.value=e===void 0?!f.value:e}function x(e){e||(e=d.value[0]),i.value=e,_e(()=>{e&&(y.isMini&&p(!1),t.onSelect&&t.onSelect(e))})}function F(e){var s,o;(o=O.value)==null||o.open({title:(e?"编辑":"添加")+t.label,form:{...e},on:{submit(u,{close:E,done:z}){t.service[e?"update":"add"](u).then(()=>{b.success("保存成功"),e?Object.assign(e,u):m(),E()}).catch(D=>{b.error(D.message),z()})}},...(s=t.onEdit)==null?void 0:s.call(t,e)},[N.setFocus()])}function W(e){ve.confirm("此操作将会删除选择的数据，是否继续？","提示",{type:"warning"}).then(()=>{t.service.delete({ids:[e.id]}).then(async()=>{var s;b.success("删除成功"),await m(),((s=i.value)==null?void 0:s.id)===e.id&&x()}).catch(s=>{b.error(s.message)})}).catch(()=>null)}const M={order:"createTime",sort:"asc",page:1,size:20},q=g(!1);async function m(e){Object.assign(M,e),S.value=!0,await t.service.page(M).then(s=>{var u;const o=((u=t.onData)==null?void 0:u.call(t,s.list))||s.list;M.page===1?d.value=o:d.value.push(...o),i.value||x(d.value[0]),q.value=s.pagination.total<=d.value.length}).catch(s=>{b.error(s.message)}),S.value=!1}function K(){m({page:M.page+1})}function Q(e,s){N.ContextMenu.open(e,{hover:{target:"item"},list:[{label:"编辑",hidden:!t.service._permission.update,callback(o){o(),F(s)}},{label:"删除",hidden:!t.service._permission.delete,callback(o){o(),W(s)}}],...t.onContextMenu&&t.onContextMenu(s)})}return P(()=>{p(!y.isMini)}),L({selected:i,isExpand:f,expand:p,select:x,browser:y,edit:F,remove:W}),le(()=>{T||m()}),(e,s)=>{const o=w("el-icon"),u=w("el-tooltip"),E=w("el-empty"),z=w("el-scrollbar"),D=w("cl-form"),R=j("permission"),U=j("infinite-scroll"),Y=j("loading");return n(),_("div",{class:A(["cl-view-group",[f.value?"is-expand":"is-collapse"]])},[r("div",me,[r("div",he,[C(e.$slots,"left",{},()=>[r("div",ge,[r("div",we,[r("span",null,B(t.label),1),a(u,{content:"刷新"},{default:c(()=>[a(o,{onClick:s[0]||(s[0]=l=>m())},{default:c(()=>[a(v(ae))]),_:1})]),_:1}),a(u,{content:"添加"},{default:c(()=>[$((n(),k(o,{onClick:s[1]||(s[1]=l=>F())},{default:c(()=>[a(v(ne))]),_:1})),[[R,t.service.permission.add]])]),_:1})]),$((n(),_("div",Ce,[a(z,null,{default:c(()=>[$((n(),_("ul",{"infinite-scroll-immediate":!1,"infinite-scroll-disabled":q.value},[(n(!0),_(ie,null,re(d.value,(l,V)=>(n(),_("li",{key:V,onClick:h=>x(l),onContextmenu:h=>{Q(h,l)}},[C(e.$slots,"item",{item:l,selected:i.value,index:V},()=>{var h,G;return[r("div",{class:A(["item",{"is-active":((h=i.value)==null?void 0:h.id)===l.id}])},[C(e.$slots,"item-name",{item:l,selected:i.value,index:V},()=>[r("span",null,B(l.name),1)],!0),$(a(o,{class:"arrow-right"},{default:c(()=>[a(v(ce))]),_:2},1536),[[ue,((G=i.value)==null?void 0:G.id)===l.id]])],2)]},!0)],40,be))),128)),d.value.length===0?(n(),k(E,{key:0,"image-size":80})):I("",!0)],8,ke)),[[U,K]])]),_:3})])),[[Y,S.value]])])],!0),v(y).isMini?(n(),_("div",{key:0,class:"collapse-btn",onClick:s[2]||(s[2]=l=>p(!1))},[a(o,null,{default:c(()=>[a(v(H))]),_:1})])):I("",!0)]),r("div",ye,[r("div",xe,[r("div",{class:"icon",onClick:s[3]||(s[3]=l=>p())},[f.value?(n(),k(o,{key:0},{default:c(()=>[a(v(de))]),_:1})):(n(),k(o,{key:1},{default:c(()=>[a(v(H))]),_:1}))]),C(e.$slots,"title",{selected:i.value},()=>{var l;return[r("span",Me,B(t.title)+"（"+B(((l=i.value)==null?void 0:l.name)||"未选择")+"） ",1)]},!0)]),i.value?(n(),_("div",Ee,[C(e.$slots,"right",{},void 0,!0)])):(n(),k(E,{key:1,"image-size":80}))])]),a(D,{ref_key:"Form",ref:O},null,512)],2)}}}),Ve=pe(Be,[["__scopeId","data-v-155820a7"]]);export{Ve as default};
