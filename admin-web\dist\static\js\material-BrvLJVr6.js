import{i as M}from"./index-DkYL1aws.js";import{c as I,b as d,q as h,w as p,h as o,i as n,f as V,s as k,F as q,o as u}from"./.pnpm-hVqhwuVC.js";import{a as S}from"./index-C6cm1h61.js";const $=I({name:"pms-production-deduct-material"}),W=I({...$,setup(E){const{service:v}=S(),m=d([]),_=d([]),f=d([]),x=d(0),C=d([{label:"待处理",value:0,type:"info",count:0},{label:"已处理",value:1,type:"info",count:0}]);async function N(){try{const e=await v.pms.material.request({url:"/list",method:"POST"});m.value=e}catch(e){console.error(e)}}async function T(){try{const e=await v.pms.supplier.request({url:"/list",method:"POST"});f.value=e}catch(e){console.error(e)}}T(),N();const b=M.useUpsert({props:{class:"material-deduct-form",labelWidth:"120px"},items:[{label:"时间",prop:"accident_time",required:!0,component:{name:"el-date-picker",props:{type:"datetime",placeholder:"选择日期",clearable:!0,format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss",disabledDate:e=>e.getTime()>Date.now()}}},{label:"不良物料供应商",prop:"supplierId",required:!0,component:{name:"slot-supplier-select"}},{label:"不良物料名称",prop:"badMaterialId",required:!0,component:{name:"slot-bad-material-select"}},{label:"报废物料名称",prop:"materialId",required:!0,component:{name:"slot-material-select"}},{label:"损耗数量",prop:"quantity",required:!0,component:{name:"el-input-number",props:{min:1}}},{label:"描述",prop:"description",required:!0,component:{name:"el-input",props:{type:"textarea"}}},{label:"扣款凭证",prop:"voucher",required:!0,component:{name:"cl-upload",props:{multiple:!0,limit:5,accept:"image/jpg,image/jpeg,image/png",text:"上传入库凭证",type:"image",disabled:!1,isPrivate:!1}}}],async onOpen(){},async onOpened(){_.value=m.value},async onClose(e,a){_.value=[],a()},async onInfo(e,{done:a}){a(e)}}),U=M.useTable({columns:[{label:"ID",prop:"id",width:60},{label:"创建时间",prop:"createTime",width:180},{label:"事故时间",prop:"accident_time",width:180},{label:"不良物料供应商",prop:"supplierName",showOverflowTooltip:!0,formatter(e){const a=f.value.find(t=>t.id===e.supplierId);return a?a.supplierName:""}},{label:"不良物料名称",prop:"badMaterialName",width:200},{label:"扣款单号",prop:"no",width:220},{label:"扣款凭证",prop:"voucher",width:120,component:{name:"cl-image",props:{fit:"cover",lazy:!0,size:[50,50]}}},{label:"报废物料名称",prop:"materialName",width:200},{label:"损耗数量",prop:"quantity",width:100},{label:"单价",prop:"unitPrice",width:80},{label:"扣款小计",prop:"total_amount",width:100},{label:"描述",prop:"description",showOverflowTooltip:!0,width:350},{type:"op",buttons:["edit","delete"]}]}),D=M.useCrud({service:v.pms.production.MaterialDeduct,async onRefresh(e,{next:a,render:t}){const{count:r,list:s,pagination:y}=await a(e);C.value.forEach(c=>{c.count=r&&r[c.value]||0}),t(s,y)}},e=>{e.refresh({status:x})});async function Y(e){var r;let a="",t=f.value.find(s=>s.id===e);a=t.supplierName?t.supplierName:"",(r=b.value)==null||r.setForm("supplierName",a)}function O(e){var t;let a="";a=m.value.filter(r=>r.id==e)[0].name,(t=b.value)==null||t.setForm("materialName",a)}function F(e){var t;let a="";a=m.value.filter(r=>r.id==e)[0].name,(t=b.value)==null||t.setForm("badMaterialName",a)}return(e,a)=>{const t=n("cl-refresh-btn"),r=n("cl-add-btn"),s=n("cl-flex1"),y=n("cl-search-key"),c=n("el-row"),B=n("cl-table"),H=n("cl-pagination"),w=n("el-option"),g=n("el-select"),L=n("cl-upsert"),P=n("cl-crud");return u(),h(P,{ref_key:"Crud",ref:D},{default:p(()=>[o(c,null,{default:p(()=>[o(t),o(r),o(s),o(y,{placeholder:"请输入扣款单号/物料名/厂家名"})]),_:1}),o(c,{style:{"margin-top":"-10px"}},{default:p(()=>[o(B,{ref_key:"Table",ref:U},null,512)]),_:1}),o(c,null,{default:p(()=>[o(s),o(H)]),_:1}),o(L,{ref_key:"Upsert",ref:b},{"slot-supplier-select":p(({scope:i})=>[o(g,{modelValue:i.supplierId,"onUpdate:modelValue":l=>i.supplierId=l,placeholder:"请选择事故物料供应商",filterable:"",onChange:Y},{default:p(()=>[(u(!0),V(q,null,k(f.value,l=>(u(),h(w,{key:l.id,label:l.supplierName,value:l.id},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),"slot-bad-material-select":p(({scope:i})=>[o(g,{modelValue:i.badMaterialId,"onUpdate:modelValue":l=>i.badMaterialId=l,placeholder:"请选择报废物料",filterable:"",onChange:F},{default:p(()=>[(u(!0),V(q,null,k(_.value,l=>(u(),h(w,{key:l.id,label:`${l.code}/${l.name}`,value:l.id},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),"slot-material-select":p(({scope:i})=>[o(g,{modelValue:i.materialId,"onUpdate:modelValue":l=>i.materialId=l,placeholder:"请选择报废物料",filterable:"",onChange:O},{default:p(()=>[(u(!0),V(q,null,k(_.value,l=>(u(),h(w,{key:l.id,label:`${l.code}/${l.name}`,value:l.id},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:1},512)]),_:1},512)}}});export{W as default};
