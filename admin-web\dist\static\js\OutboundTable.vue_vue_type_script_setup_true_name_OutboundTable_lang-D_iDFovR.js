import{i,s as T}from"./index-BtOcqcNl.js";import{c as _,z as C,q as I,w as n,h as e,i as t,o as v}from"./.pnpm-hVqhwuVC.js";const q=_({name:"OutboundTable"}),Q=_({...q,props:{outboundType:{default:4},supplierId:{default:0}},emits:["selected"],setup(d,{emit:m}){const r=d,b=m,f=i.useTable({columns:[{label:"#",prop:"products",type:"expand"},{label:"出库单号",prop:"no",width:220},{label:"出库总数量",prop:"totalQuantity"},{label:"创建时间",prop:"createTime"},{label:"备注",prop:"remark",width:150,showOverflowTooltip:!0},{type:"op",label:"操作",width:80,buttons:[{label:"选择",type:"primary",onClick:o=>{b("selected",o.scope)}}]}]}),h=i.useCrud({service:T.pms.material.outbound,async onRefresh(o,{next:c,render:s}){const{_:p,list:u,pagination:a}=await c(o);s(u,a)}},o=>{o.refresh({status:3,supplierId:r.supplierId,type:r.outboundType,finished:!0}),C(()=>r.outboundType,c=>{o.refresh({status:3,supplierId:r.supplierId,type:c,finished:!0})})});return(o,c)=>{const s=t("cl-refresh-btn"),p=t("cl-flex1"),u=t("cl-search-key"),a=t("el-row"),l=t("el-table-column"),y=t("el-table"),w=t("cl-table"),g=t("cl-pagination"),k=t("cl-crud");return v(),I(k,{ref_key:"Crud",ref:h},{default:n(()=>[e(a,null,{default:n(()=>[e(s),e(p),e(u)]),_:1}),e(a,null,{default:n(()=>[e(w,{ref_key:"Table",ref:f,"row-key":"id","auto-height":!1,style:"height:600px"},{"column-products":n(({scope:x})=>[e(y,{data:x.row.products,style:{width:"100%"},border:""},{default:n(()=>[e(l,{label:"物料代码",prop:"code",align:"center"}),e(l,{label:"物料名称",prop:"name",align:"center"}),e(l,{label:"型号",prop:"model",align:"center"}),e(l,{label:"退货数量",prop:"quantity",align:"center"}),e(l,{label:"已补数量",prop:"restockingQty",align:"center"}),e(l,{label:"单位",prop:"unit",align:"center"})]),_:2},1032,["data"])]),_:1},512)]),_:1}),e(a,null,{default:n(()=>[e(p),e(g)]),_:1})]),_:1},512)}}});export{Q as _};
