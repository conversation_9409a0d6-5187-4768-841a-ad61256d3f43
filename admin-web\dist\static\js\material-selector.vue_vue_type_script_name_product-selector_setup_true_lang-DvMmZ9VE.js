import{i as u}from"./index-BtOcqcNl.js";import{u as K}from"./material-CnT3-AWx.js";import{n as L}from"./index-CBanFtSc.js";import{c as x,b as p,e as M,q as g,i as l,w as r,h as t,G as P,B as Q,H as X,v as Y,j as Z,t as $,y as ee,o as y}from"./.pnpm-hVqhwuVC.js";import{a as te}from"./index-D95m1iJL.js";const oe={flex:"~ items-center"},le=x({name:"product-selector"}),ce=x({...le,props:{modelValue:Array,canAdd:{type:Boolean,default:!1},hasStock:{type:Boolean,default:!1},disabledMaterials:{type:Array,default:()=>[]}},emits:["update:modelValue"],setup(k,{expose:C,emit:S}){const d=k,V=S,f=p(),{service:h}=te(),s=p(d.hasStock),T=p(d.canAdd),a=M(()=>d.disabledMaterials);function v(e){var o,c;return(o=a==null?void 0:a.value)!=null&&o.length?!((c=a==null?void 0:a.value)!=null&&c.some(m=>m===e.id)):!0}u.useTable({autoHeight:!0,columns:[{type:"selection",reserveSelection:!0,selectable(e){return v(e)}},{prop:"code",label:"代码",width:150,align:"left",showOverflowTooltip:!0},{prop:"name",label:"名称",showOverflowTooltip:!0,align:"left",width:200},{prop:"model",label:"型号",showOverflowTooltip:!0,align:"left"},{prop:"size",label:"尺寸",width:100,align:"left",showOverflowTooltip:!0},{prop:"material",label:"材质",width:100,align:"left",showOverflowTooltip:!0},{prop:"process",label:"工艺",width:100,align:"left",showOverflowTooltip:!0},{prop:"coverColor",label:"颜色",width:100,align:"left",showOverflowTooltip:!0},{prop:"inventory",label:"库存",width:100,sortable:!0,align:"left",showOverflowTooltip:!0},{prop:"unit",label:"单位",align:"left",width:100},{prop:"inventory",label:"库存",sortable:!0,width:150},{prop:"expectedInbound",label:"在途",sortable:!0,width:150}]});const i=u.useCrud({service:h.pms.material},e=>{e.refresh({size:100,hasStock:s.value})}),{materialUpsertOptions:U}=K(),O=u.useUpsert(U),B=u.useSearch({items:[{label:"物料代码/名称",prop:"keyword",props:{labelWidth:"150px"},component:{name:"el-input",props:{clearable:!1,onChange(e){var o;(o=i.value)==null||o.refresh({keyWord:e.trim(),page:1})}}}}]});function I(e){V("update:modelValue",e)}function A(e){v(e)&&f.value.toggleRowSelection(e)}function D(){var e;(e=i.value)==null||e.refresh({hasStock:s.value})}const N=p();function z(){var e,o;f.value.clearSelection(),(e=i.value)==null||e.refresh({unit:void 0,keyWord:void 0,page:1}),(o=N.value)==null||o.reset()}function E(e){return L(e.inventory-e.lockedInventory+e.deductibleExpectedInbound-e.usedExpectedInbound)}return C({resetData:z}),(e,o)=>{const c=l("cl-refresh-btn"),m=l("cl-add-btn"),b=l("el-switch"),H=l("el-form-item"),w=l("cl-flex1"),R=l("cl-search"),_=l("el-row"),W=l("cl-table"),G=l("cl-pagination"),j=l("cl-upsert"),q=l("cl-crud"),F=X("permission");return y(),g(q,{ref_key:"Crud",ref:i,style:{height:"600px"}},{default:r(()=>[t(_,null,{default:r(()=>[t(c),T.value?P((y(),g(m,{key:0},null,512)),[[F,Y(h).pms.material.permission.add]]):Q("",!0),t(H,{label:"只显示有库存",style:{"margin-left":"10px"}},{default:r(()=>[t(b,{modelValue:s.value,"onUpdate:modelValue":o[0]||(o[0]=n=>s.value=n),"active-text":"是","inactive-text":"否","inline-prompt":"",onChange:D},null,8,["modelValue"])]),_:1}),t(w),t(R,{ref_key:"Search",ref:B},null,512)]),_:1}),t(_,null,{default:r(()=>[t(W,{ref_key:"productTable",ref:f,onSelectionChange:I,onRowClick:A},{"column-availableInventory":r(({scope:n})=>[Z($(E(n.row)),1)]),_:1},512)]),_:1}),t(_,null,{default:r(()=>[t(w),t(G,{"page-size":100})]),_:1}),t(j,{ref_key:"Upsert",ref:O},{"slot-bind-user":r(({scope:n})=>[ee("div",oe,[t(b,{modelValue:n.isBindUser,"onUpdate:modelValue":J=>n.isBindUser=J,"active-text":(n==null?void 0:n.bindUserId)>0?"更换绑定":"是","inactive-text":"否","active-color":"#13ce66"},null,8,["modelValue","onUpdate:modelValue","active-text"])])]),_:1},512)]),_:1},512)}}});export{ce as _};
