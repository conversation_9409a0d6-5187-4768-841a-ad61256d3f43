import{i,s as T}from"./index-DkYL1aws.js";import{c as u,z as C,q as v,w as n,h as e,i as t,o as q}from"./.pnpm-hVqhwuVC.js";const B=u({name:"OutboundTable"}),z=u({...B,props:{outboundType:{default:4}},emits:["selected"],setup(d,{emit:m}){const _=d,b=m,f=i.useTable({columns:[{label:"#",prop:"products",type:"expand"},{label:"出库单号",prop:"no",width:220},{label:"出库总数量",prop:"totalQuantity"},{label:"创建时间",prop:"createTime"},{label:"备注",prop:"remark",width:150,showOverflowTooltip:!0},{type:"op",label:"操作",width:80,buttons:[{label:"选择",type:"primary",onClick:o=>{b("selected",o.scope)}}]}]}),h=i.useCrud({service:T.pms.material.outbound,async onRefresh(o,{next:c,render:r}){const{_:s,list:p,pagination:a}=await c(o);r(p,a)}},o=>{o.refresh({status:3,type:_.outboundType,finished:!0}),C(()=>_.outboundType,c=>{o.refresh({status:3,type:c,finished:!0})})});return(o,c)=>{const r=t("cl-refresh-btn"),s=t("cl-flex1"),p=t("cl-search-key"),a=t("el-row"),l=t("el-table-column"),y=t("el-table"),w=t("cl-table"),g=t("cl-pagination"),k=t("cl-crud");return q(),v(k,{ref_key:"Crud",ref:h},{default:n(()=>[e(a,null,{default:n(()=>[e(r),e(s),e(p)]),_:1}),e(a,null,{default:n(()=>[e(w,{ref_key:"Table",ref:f,"row-key":"id","auto-height":!1,style:"height:600px"},{"column-products":n(({scope:x})=>[e(y,{data:x.row.products,style:{width:"100%"},border:""},{default:n(()=>[e(l,{label:"物料代码",prop:"code",align:"center"}),e(l,{label:"物料名称",prop:"name",align:"center"}),e(l,{label:"型号",prop:"model",align:"center"}),e(l,{label:"退货数量",prop:"quantity",align:"center"}),e(l,{label:"已补数量",prop:"restockingQty",align:"center"}),e(l,{label:"单位",prop:"unit",align:"center"})]),_:2},1032,["data"])]),_:1},512)]),_:1}),e(a,null,{default:n(()=>[e(s),e(g)]),_:1})]),_:1},512)}}});export{z as _};
