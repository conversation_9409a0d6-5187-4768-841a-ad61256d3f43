import{c as P,b as V,q as w,w as n,h as e,i as l,f as k,s as X,F as Z,y as i,t as h,v as g,G as S,B as F,H as ee,Y as M,j as N,o as u,E as y,T as te}from"./.pnpm-hVqhwuVC.js";import{g as oe,i as x,j as ne}from"./index-DkYL1aws.js";import{a as ae}from"./index-C6cm1h61.js";/* empty css              */const le={key:0},se={key:1},re={class:"ellipsis"},ce={class:"cl-table__expand-footer"},ie={class:"table-summary-container"},pe=i("span",{class:"cl-table__expand-footer-title"},"总数量：",-1),ue={class:"cl-table__expand-footer-value"},de=P({name:"pms-warehouse-destination-outbound"}),we=P({...de,setup(_e){const{service:_}=ae(),{dict:Y}=oe(),$=Y.get("color"),d=V(1),C=V([{label:"待发货",value:1,count:0,type:"danger"},{label:"已发货",value:2,count:0,type:"warning"},{label:"已完成",value:3,count:0,type:"success"}]),T=x.useTable({columns:[{label:"#",prop:"products",type:"expand",width:50},{label:"订单号",prop:"orderSn",width:180,align:"left"},{label:"派送地址",prop:"consignee"},{label:"是否付款",prop:"isPaid",width:120,dict:[{label:"已付款",value:1,type:"success"},{label:"未付款",value:0,type:"danger"}]},{label:"运费",prop:"trackingCost",width:80},{label:"承运商",prop:"carrier",width:100},{label:"跟踪号码",prop:"pro",width:150},{label:"发货日期",prop:"dispatchDate",width:180},{label:"预计送达时间",prop:"eta",width:120,component:{name:"cl-date-text",props:{format:"YYYY-MM-DD"}}},{label:"完成时间",prop:"completeTime",width:180},{type:"op",buttons:["slot-btn-ship","slot-btn-complete"],width:100}]}),B=x.useCrud({service:_.pms.warehouse.destination.outbound,async onRefresh(a,{next:t,render:s}){const{count:m,list:b,pagination:c}=await t(a);C.value.forEach(f=>{f.count=m[f.value]||0}),s(b,c)}},a=>{a.refresh({status:d})});function L(a){var t;d.value=a,(t=B.value)==null||t.refresh()}function R(a,t){var s;(t==null?void 0:t.type)==="expand"||(t==null?void 0:t.type)==="op"||(s=T.value)==null||s.toggleRowExpansion(a)}function U(a){return/^(\d+)((?:\.\d{0,4})?)$/.test(a)?a:a.slice(0,-1)}const D=x.useForm();function j(a){var t;(t=D.value)==null||t.open({title:"填写发货信息",width:"700px",props:{labelWidth:"115px"},dialog:{controls:["close"]},items:[{label:"是否付款",prop:"isPaid",required:!0,component:{name:"el-radio-group",props:{size:"mini"},children:[{label:"是",value:1},{label:"否",value:0}]}},{label:"运费",prop:"trackingCost",required:!0,component:{name:"el-input",props:{type:"number",formatter:U}}},{label:"承运商",prop:"carrier",required:!0,component:{name:"el-input"}},{label:"跟踪号码",prop:"pro",required:!0,component:{name:"el-input"}},{label:"预计送达时间",prop:"eta",required:!0,component:{name:"el-date-picker",type:"date",props:{placeholder:"请选择预计送达时间","disabled-date":s=>s.getTime()<Date.now()-864e5}}}],on:{submit:async(s,{done:m,close:b})=>{await _.pms.warehouse.destination.outbound.ship({id:a.id,...s}).then(c=>{y.success("发货成功"),b(),d.value=c.status||2}).catch(c=>{y.error(c.message||"发货失败")}),m()}}})}function z(a){te.confirm("是否确认完成？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{await _.pms.warehouse.destination.outbound.complete({id:a.id}).then(t=>{y.success("操作成功"),d.value=t.status||3}).catch(t=>{y.error(t.message||"操作失败")})}).catch(()=>{})}return(a,t)=>{const s=l("cl-refresh-btn"),m=l("cl-add-btn"),b=l("cl-multi-delete-btn"),c=l("cl-flex1"),f=l("cl-search-key"),v=l("el-row"),G=l("el-tab-pane"),H=l("el-popover"),q=l("el-button"),r=l("el-table-column"),I=l("el-table"),K=l("cl-table"),O=l("cl-pagination"),W=l("el-tabs"),A=l("cl-form"),J=l("cl-crud"),E=ee("permission");return u(),w(J,{ref_key:"Crud",ref:B},{default:n(()=>[e(v,null,{default:n(()=>[e(s),e(m),e(b),e(c),e(f)]),_:1}),e(W,{modelValue:d.value,"onUpdate:modelValue":t[0]||(t[0]=o=>d.value=o),type:"border-card",onTabChange:L},{default:n(()=>[(u(!0),k(Z,null,X(C.value,o=>(u(),w(G,{key:o.value,label:`${o.label}(${o.count})`,name:o.value},null,8,["label","name"]))),128)),e(v,null,{default:n(()=>[e(K,{ref_key:"Table",ref:T,"row-key":"id",class:"table-row-pointer",onRowClick:R},{"column-trackingCost":n(({scope:o})=>[o.row.trackingCost>0?(u(),k("span",le,h(o.row.trackingCost),1)):(u(),k("span",se))]),"column-consignee":n(({scope:o})=>[e(H,{placement:"top-start",width:300,trigger:"hover",content:o.row.consignee},{reference:n(()=>[i("span",re,h(o.row.consignee),1)]),_:2},1032,["content"])]),"slot-btn-ship":n(({scope:o})=>[o.row.status===1?S((u(),w(q,{key:0,text:"",bg:"",type:"success",onClick:M(p=>j(o.row),["stop"])},{default:n(()=>[N(" 发货 ")]),_:2},1032,["onClick"])),[[E,g(_).pms.warehouse.destination.outbound.permission.ship]]):F("",!0)]),"slot-btn-complete":n(({scope:o})=>[o.row.status===2?S((u(),w(q,{key:0,text:"",bg:"",type:"success",onClick:M(p=>z(o.row),["stop"])},{default:n(()=>[N(" 完成 ")]),_:2},1032,["onClick"])),[[E,g(_).pms.warehouse.destination.outbound.permission.complete]]):F("",!0)]),"column-products":n(({scope:o})=>[e(I,{data:o.row.products,style:{width:"100%"},border:""},{default:n(()=>[e(r,{label:"产品名称",align:"center"},{default:n(()=>[e(r,{prop:"name",label:"中文名",align:"center"}),e(r,{prop:"nameEn",label:"英文名",align:"center"})]),_:1}),e(r,{prop:"cartonPo",label:"外箱PO#",width:"150",align:"center"}),e(r,{prop:"airway",label:"空运提单号",width:"150",align:"center"}),e(r,{prop:"carton",label:"箱号",width:"150",align:"center"},{default:n(p=>[i("span",null,"#"+h(p.row.carton),1)]),_:2},1024),e(r,{prop:"sku",label:"SKU",width:"150",align:"center"}),e(r,{prop:"upc",label:"UPC",width:"150",align:"center"}),e(r,{prop:"color",label:"颜色",width:"120",align:"center"},{default:n(p=>[i("span",null,h(g(ne)(g($),parseInt(p.row.color))),1)]),_:2},1024),e(r,{prop:"quantity",label:"订单数量",width:"100",align:"center"})]),_:2},1032,["data"]),i("div",ce,[i("div",ie,[i("div",null,[pe,i("span",ue,h(o.row.products.reduce((p,Q)=>p+Q.quantity,0)),1)])])])]),_:1},512)]),_:1}),e(v,null,{default:n(()=>[e(c),e(O)]),_:1})]),_:1},8,["modelValue"]),e(A,{ref_key:"ShipForm",ref:D},null,512)]),_:1},512)}}});export{we as default};
