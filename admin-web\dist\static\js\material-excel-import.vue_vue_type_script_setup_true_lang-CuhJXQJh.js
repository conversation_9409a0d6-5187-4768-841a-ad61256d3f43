import{e as z}from"./index-DkYL1aws.js";import{c as I,b as u,f as L,F as M,y as b,h as s,i as p,w as f,j as E,M as P,N as T,E as k,o as $}from"./.pnpm-hVqhwuVC.js";import{a as q}from"./index-C6cm1h61.js";const O={class:"import-error-dialog"},G=I({name:"undefined"}),W=I({...G,props:{disabled:{type:Boolean,default:!1}},emits:["success"],setup(N,{emit:B}){const x=N,F=B,{service:V}=q(),v=u(null),m=u(!1),n=u([]),h=u(!1);function j(){const l=v.value;l&&l.click()}function S(l){l&&(l.value="")}function R(l){const a=l.target,o=a.files;if(o&&o.length>0){const r=o[0],i=new FileReader;i.onload=g=>{var y;const c=new Uint8Array((y=g.target)==null?void 0:y.result),w=P(c,{type:"array"}),D=w.Sheets[w.SheetNames[0]],d=T.sheet_to_json(D,{header:1}).slice(1).filter(e=>e.length===2&&e[0]&&e[1]&&e[1]>0).map(e=>({code:e[0].toString().trim().replace(/[\r\n]/g,"").toUpperCase(),quantity:Number.parseFloat(e[1].toString().trim().replace(/[\r\n]/g,""))}));m.value=!0,V.pms.material.list({codeList:d.map(e=>e.code).join(",")}).then(e=>{e=e||[];const _=e.filter(t=>(t==null?void 0:t.id)&&t.id>0).map(t=>t),C=d.filter(t=>!_.find(U=>U.code===t.code));C.length>0&&C.forEach(t=>{n.value.push({row:d.indexOf(t)+2,message:`物料编码：${t.code} 不存在`})}),n.value.length===0&&_.length>0&&F("success",_,d),n.value.length>0&&(h.value=!0)}).catch(e=>{k.error({message:e.message||"读取物料信息错误"})}).finally(()=>{m.value=!1,S(a)})},i.readAsArrayBuffer(r)}}function A(){const l="导入模板.xlsx";fetch("/inbound_material_template.xlsx").then(o=>o.blob()).then(o=>{z(o,l)}).catch(()=>{k.error({message:"下载模板文件失败"})})}return(l,a)=>{const o=p("el-button"),r=p("el-table-column"),i=p("el-table"),g=p("cl-dialog");return $(),L(M,null,[b("div",null,[s(o,{disabled:x.disabled,class:"mb-10px mr-10px",size:"default",onClick:A},{default:f(()=>[E(" 下载Excel模板 ")]),_:1},8,["disabled"]),b("input",{ref_key:"fileInputRef",ref:v,type:"file",style:{display:"none"},accept:".xlsx, .xls",onChange:R},null,544),s(o,{disabled:x.disabled,size:"default",loading:m.value,type:"success",class:"mb-10px mr-10px",onClick:j},{default:f(()=>[E(" Excel导入 ")]),_:1},8,["disabled","loading"])]),s(g,{modelValue:h.value,"onUpdate:modelValue":a[0]||(a[0]=c=>h.value=c),"close-on-click-modal":!1,"close-on-press-escape":!1,controls:["close"],title:"导入错误信息",width:"500",onClose:a[1]||(a[1]=c=>n.value=[])},{default:f(()=>[b("div",O,[s(i,{data:n.value,style:{width:"100%"},"max-height":600,border:""},{default:f(()=>[s(r,{prop:"row",label:"行号",width:"100"}),s(r,{prop:"message",label:"错误信息"})]),_:1},8,["data"])])]),_:1},8,["modelValue"])],64)}}});export{W as _};
