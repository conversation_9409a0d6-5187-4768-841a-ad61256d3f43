import{s as g}from"./index-BtOcqcNl.js";import{c as d,k as i,l as h,n as V,e as _,b,E as x,q as M,i as k,m as w,v as c,x as C,o as E}from"./.pnpm-hVqhwuVC.js";const y=d({name:"select-supplier"}),A=d({...y,props:i({width:{default:"150px"},options:{default:()=>[]}},{modelValue:{},modelModifiers:{}}),emits:i(["update:modelValue","change"],["update:modelValue"]),setup(r,{emit:p}){const s=r,a=p,m=h(),l=V(r,"modelValue"),n=_({get:()=>{if(![0,"0"].includes(l.value))return l.value},set:e=>{l.value=e,a("update:modelValue",e)}}),t=b([]);s.options.length===0?g.pms.productionData.incoming.supplierList().then(e=>{e.forEach(o=>{o.label=o.name,o.value=o.id}),t.value=e}).catch(e=>{x.error(e.message||"获取用户列表失败")}):t.value=s.options;function f(e){if(e===void 0){a("change",e,t.value);return}const o=t.value.find(u=>u.id===e);a("change",o,t.value)}return(e,o)=>{const u=k("el-select-v2");return E(),M(u,w({modelValue:c(n),"onUpdate:modelValue":o[0]||(o[0]=v=>C(n)?n.value=v:null),filterable:""},c(m),{clearable:"",style:`width:${s.width}`,options:t.value,onChange:f}),null,16,["modelValue","style","options"])}}});export{A as _};
