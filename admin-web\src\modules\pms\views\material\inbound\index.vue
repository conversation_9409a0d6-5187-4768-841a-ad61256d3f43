<script lang="ts" name="pms-warehouse-inbound" setup>
import { useCrud, useForm, useSearch, useTable } from '@cool-vue-p/crud'
import { router, useCool } from '/@/cool'
import { computed, ref, watch, watchEffect } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import '/$/pms/static/css/index.scss'
import moment from 'moment'
import { useTableOps } from '/$/pms/hooks/table-ops'
import MaterialTableColumns from '/$/pms/components/material-table-columns.vue'
import { useStore } from '/$/base/store'
import Dayjs from 'dayjs'
import { cloneDeep, debounce } from 'lodash-es'
import AuditLog from '/src/modules/pms/views/material/AuditLog.vue'
import { V1_DISABLED } from '/$/pms/utils'
import { INBOUND_TYPE } from '/$/pms/views/material/js/constant.js'
import { getDictLabel } from '/$/pims/utils'
import MaterialInbound from '/$/pms/views/finance/components/MaterialInbound.vue'
import {useDict} from "/$/dict";
const { dict } = useDict()
const inbound_outbound_key_list = dict.get('inbound_outbound_key')

// --------- data ---------
const INBOUND_VALUE = -2

const status = ref(0)
const { service } = useCool()
const statusList = ref([
  { label: '草稿', value: 0, type: 'info', count: 0 },
  { label: '待审批', value: 4, type: 'info', count: 0 },
  // { label: '待处理', value: 1, type: 'info', count: 0 },
  // { label: '入库中', value: 2, type: 'info', count: 0 },
  { label: '已入库', value: 3, type: 'success', count: 0 },
  { label: '入库明细', value: INBOUND_VALUE },
])
const rowData = ref<any>({ id: 0 })

// ============ 配置操作按钮权限 ============
const opButtons = ref({
  'slot-btn-confirm': {
    width: 80,
    permission: service.pms.material.inbound.permission.confirm,
    show: computed(() => status.value === 0),
  },
  'slot-audit-log': {
    width: 120,
    permission: service.pms.material.inbound.permission.confirm,
    show: true,
  },
  'slot-btn-start': {
    width: 110,
    permission: service.pms.material.inbound.permission.start,
    show: computed(() => status.value === 1),
  },
  'slot-btn-edit': {
    width: 80,
    permission: service.pms.material.inbound.permission.update,
    show: computed(() => status.value === 0),
  },
  'slot-btn-delete': {
    width: 80,
    permission: service.pms.material.inbound.permission.delete,
    show: computed(() => status.value === 0),
  },
  'slot-btn-complete': {
    width: 120,
    permission: service.pms.material.inbound.permission.complete,
    show: computed(() => status.value === 2),
  },
  'slot-btn-revoke': {
    width: 110,
    permission: service.pms.material.inbound.permission.revoke,
    show: computed(() => status.value !== 0),
  },
  'slot-btn-print': {
    width: 80,
    permission: service.pms.material.inbound.permission.info,
    show: true,
  },
})

const { getOpWidth, checkOpButtonIsAvaliable, getOpIsHidden } = useTableOps(opButtons as any)
const opWidth = ref()
const opIsHidden = ref(false)

// 当status发生变化时，更新opColumnItem
watch(status, () => {
  opWidth.value = getOpWidth()
  opIsHidden.value = getOpIsHidden()
}, { immediate: true })
// ============ 配置操作按钮权限 ============

// cl-table 配置
const Table = useTable({
  columns: [
    { label: '#', prop: 'products', type: 'expand' },
    { label: '入库单号', prop: 'no', width: 220 },
    {
      label: '入库类型',
      prop: 'type',
      dict: INBOUND_TYPE,
    },
    // 采购单号
    { label: '关联单号', prop: 'orderNo', width: 220 },
    { label: '内部单号', prop: 'internalOrderNo', width: 220 },
    { label: '入库总数量', prop: 'totalQuantity',width: 120  },
    {
      label: '创建时间',
      prop: 'createTime',
      component: {
        name: 'cl-date-text',
        props: { format: 'YYYY-MM-DD HH:mm:ss' },
      },
    },
    {
      label: '入库凭证',
      prop: 'voucher',
      width: 120,
      component: { name: 'cl-image', props: { fit: 'cover', lazy: true, size: [50, 50] } },
    },
    {
      label: '单据时间',
      prop: 'inboundTime',
      component: {
        name: 'cl-date-text',
        props: { format: 'YYYY-MM-DD' },
      },
    },
    {
      label: '完成时间',
      prop: 'completeTime',
      component: {
        name: 'cl-date-text',
        props: { format: 'YYYY-MM-DD HH:mm:ss' },
      },
    },
    {
      label: '备注',
      prop: 'remark',
      width: 150,
      showOverflowTooltip: true,
    },
    {
      type: 'op',
      label: '操作',
      width: opWidth as any,
      hidden: opIsHidden,
      buttons: Object.keys(opButtons.value) as any,
    },
  ],
})

// cl-crud 配置
const Crud = useCrud(
  {
    service: service.pms.material.inbound,
    async onRefresh(params, { next, render }) {
      // 1 默认调用
      const { count, list, pagination } = await next(params)

      // 2 根据count设置tab
      statusList.value.forEach((item) => {
        item.count = count ? count[item.value] || 0 : 0
      })
      // eslint-disable-next-line array-callback-return
      // list.map((item: any) => {
      //   // eslint-disable-next-line array-callback-return
      //   item.products.map((product: any) => {
      //     product.address_arr = product.address_name ? product.address_name.split(',') : []
      //   })
      // })
      render(list, pagination)
    },
  },
  (app) => {
    app.refresh({ status })
  },
)

// --------------- functions ---------------
function handleStatusChange(tab: any) {
  // 切换tab时，刷新表格数据
  status.value = tab
  if (tab !== INBOUND_VALUE) {
    // 刷新表格数据
    Crud.value?.refresh()
  }
}

// 行点击展开
function onRowClick(row: any, column: any) {
  // 获取row的key
  if (
    column?.type === 'expand'
    || column?.type === 'op'
    || column?.property === 'voucher'
  ) {
    return
  }

  Table.value?.toggleRowExpansion(row)
}

/* function postConfirmForm(data: any, row: any) {
  // 返回Promise
  return new Promise((resolve, reject) => {
    service.pms.material.inbound
      .confirm({ ...data, id: row.id })
      .then((res) => {
        resolve(res)
      })
      .catch((err) => {
        reject(err)
      })
  })
} */

const InboundForm = useForm()
// 确认入库
function handleConfirm(row: any) {
  if (!row.id)
    return false
  if (row.status === 0) {
    InboundForm.value?.open({
      form: { ...row },
      title: '完善入库信息',
      width: '400px',
      dialog: { controls: ['close'] },
      items: [
        {
          label: '单据日期',
          prop: 'inboundTime',
          required: true,
          component: {
            name: 'el-date-picker',
            props: {
              'type': 'date',
              'value-format': 'YYYY-MM-DD',
              'format': 'YYYY-MM-DD',
              'clearable': true,
              // 不能超过今天
              'disabledDate': (time: any) => {
                return time.getTime() > Date.now()
              },
            },
          },
        },
        {
          label: '入库凭证',
          prop: 'voucher',
          required: true,
          component: {
            name: 'cl-upload',
            props: {
              multiple: true,
              limit: 5,
              accept: 'image/jpg,image/jpeg,image/png',
              text: '上传入库凭证',
              type: 'image',
              disabled: false,
              isPrivate: false,
            },
          },
        },
      ],
      on: {
        submit: debounce(async (data: any, { close, done }: any) => {
          service.pms.material.inbound.submit({ ...row, ...data, id: row.id }).then((res) => {
            if (Object.prototype.hasOwnProperty.call(res, 'status')) {
              ElMessage.success('提交成功')
              close()
              status.value = res?.status || 0
              Crud.value?.refresh()
            }
          }).catch((err) => {
            ElMessage.error(err.message || '提交失败')
          })
          done()
        }, 1000),
      },
    })
  }
}

// 完成入库
const isLoading = ref(false)
function handleComplete(row: any) {
  const message = `确定要完成入库吗？<br /><span text-red>注意，提交后将无法撤销，请谨慎操作！！！</span><br />`

  ElMessageBox.confirm(message, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
    dangerouslyUseHTMLString: true,
  })
    .then(() => {
      isLoading.value = true
      service.pms.material.inbound
        .complete({ id: row.id })
        .then((res) => {
          ElMessage.success('完成入库成功')
          status.value = res?.status || 0
          Crud.value?.refresh()
        })
        .catch((res) => {
          ElMessage.error(res.message)
        }).finally(() => {
          isLoading.value = false
        })
    })
    .catch(() => { })
}

function tableRowClassName() {
  return 'primary-row'
}

function handleAdd() {
  // 跳转到创建入库单页面
  router.push('/pms/material/inbound/add')
}

function handleEdit(id: number) {
  // 跳转到编辑入库单页面
  router.push(`/pms/material/inbound/add?id=${id}`)
}

function handleDeleteOrder(inboundId: number) {
  if (!inboundId)
    return false

  // 弹窗确认
  ElMessageBox.confirm('确认删除入库单吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(() => {
      // 删除入库单
      service.pms.material.inbound
        .delete({ ids: [inboundId] })
        .then(() => {
          // 提示成功消息
          ElMessage.success('入库单删除成功')
          Crud.value?.refresh()
        })
        .catch((err) => {
          // 提示错误消息
          ElMessage.error(err.message)
        })
    })
    .catch(() => {
      // 取消删除
    })
}

const tableExpandRowKeys = ref<number[]>([])
// watch  url 参数变化时，刷新表格数据
watchEffect(() => {
  const tabIndex = router.currentRoute.value.query.tab
  if (tabIndex) {
    status.value = Number.parseInt(tabIndex.toString())
    // 删除url中的tab参数
    router.replace({ query: { tab: undefined } })
  }
  // 显示展开的行
  const expandRowKey = router.currentRoute.value.query.expand
  if (expandRowKey) {
    // 根据key获取行数据
    tableExpandRowKeys.value = [Number.parseInt(expandRowKey.toString())]
    // 删除url中的expand参数
    router.replace({ query: { expand: undefined } })
  }
})

const Search = useSearch({
  items: [
    {
      label: '入库单号',
      prop: 'keyWord',
      props: {
        labelWidth: '120px',
      },
      component: {
        name: 'el-input',
        style: { width: '200px' },
        props: {
          clearable: true,
          onChange(keyword: string) {
            Crud.value?.refresh({ keyWord: keyword.trim(), page: 1 })
          },
        },
      },
    },
    {
      label: '入库类型',
      prop: 'type',
      props: { labelWidth: '80px' },
      component: {
        name: 'el-select',
        props: {
          style: 'width: 200px',
          clearable: true,
          onChange(type: number) {
            const params: any = { type, page: 1 }
            Crud.value?.refresh(params)
          },
        },
        options: INBOUND_TYPE,
      },
    },
    {
      label: '下单时间',
      prop: 'dateRange',
      props: {
        labelWidth: '80px',
      },
      component: {
        name: 'el-date-picker',
        props: {
          'type': 'daterange',
          'value-format': 'YYYY-MM-DD',
          'format': 'YYYY-MM-DD',
          'rangeSeparator': '至',
          'startPlaceholder': '开始日期',
          'endPlaceholder': '结束日期',
          'clearable': true,
          onChange(dateRange) {
            Crud.value?.refresh({ dateRange })
          },
        },
      },
    },
    {
      label: '关键字',
      prop: 'inbound_outbound_key',
      props: { labelWidth: '80px' },
      component: {
        name: 'el-select',
        props: {
          style: 'width: 200px',
          clearable: true,
          onChange(inbound_outbound_key: number) {
            const params: any = { inbound_outbound_key, page: 1 }
            Crud.value?.refresh(params)
          },
        },
        options: inbound_outbound_key_list,
      },
    },
  ],
})

//  审核记录
function handleAuditLog(row: any) {
  if (!row.id)
    return false

  rowData.value = cloneDeep(row)
}

function handleRevoke(row: any) {
  let message
    = '确定撤销入库单吗？<br /> 撤销后，该入库单将更新到草稿状态。<br />'

  if (row.status === 3 && row.deliveryNoteId <= 0) {
    message
      += '该入库单已完成入库，撤销时会扣除已入库的数量，请确保库存充足？<br />'
  } else if (row.status === 3 && row.deliveryNoteId > 0){
    message
      += '此入库单为送货单同步数据，撤销后，送货单将同时更改状态，并且删除入库单数据，再次提交请进入送货单列表菜单下修改！<br />'
    message
      += '该入库单已完成入库，撤销时会扣除已入库的数量，请确保库存充足？<br />'
  }
  if (row.status === 3 && row.deliveryNoteId <= 0) {
    ElMessageBox.confirm(message, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
      dangerouslyUseHTMLString: true,
    })
      .then(() => {
        service.pms.material.inbound
          .revoke({id: row.id})
          .then(() => {
            ElMessage.success('撤销入库单成功')
            // status.value = res?.status || 0
            Crud.value?.refresh()
          })
          .catch((res: any) => {
            ElMessage.error(res.message || '撤销入库单失败')
          })
      })
      .catch(() => {
      })
  } else if (row.status === 3 && row.deliveryNoteId > 0) {
    ElMessageBox.confirm(message, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
      dangerouslyUseHTMLString: true,
    })
      .then(() => {
        service.pms.material.inbound
          .revokeAndSync({id: row.id})
          .then(() => {
            ElMessage.success('撤销入库单并同步数据成功')
            Crud.value?.refresh()
          })
          .catch((res: any) => {
            ElMessage.error(res.message || '撤销入库单并同步数据失败')
          })
      })
      .catch((e) => {
        console.log('e========================',e)
      })
  }
}
// 打印入库单数据
const { user } = useStore()
function handlePrint(row: any) {
  const printObj: any = {}
  printObj.inboundTime = row.inboundTime ? Dayjs(row.inboundTime).format('YYYY/MM/DD') : ''
  printObj.date = Dayjs(new Date()).format('YYYY/MM/DD')
  printObj.user = user?.info?.name
  printObj.internalOrderNo = row.internalOrderNo || '' // 添加内部订单号
  printObj.totalQuantity = row.totalQuantity || ''
  if (row.products?.length > 0) {
    printObj.list = cloneDeep(row.products)
    printObj.list.forEach((item: any) => {
      item.warehouse = getDictLabel('warehouse_name', item.warehouseId)
    })
    const row0 = row.products[0]
    printObj.no = row.no
    printObj.po = row0.Po
    printObj.supplierName = row0.supplierName
    printObj.orderNo = row.orderNo
  }
  sessionStorage.setItem('printData', JSON.stringify([printObj]))
  // 获取域名
  if (row.type === 9) {
    window.open(`${window.location.origin}/printMaterialInboundReplenish.html`, '_blank')
  }else {
    window.open(`${window.location.origin}/printMaterialInbound.html`, '_blank')
  }
}
function isCanRevoke(row: any): boolean {
  // 状态必须是不是0
  if (row.status === 0 || row.status === 4)
    return false

  // 必须是7天内创建的
  return moment(row.createTime).add(60, 'days').isAfter(moment())
}

const isShowImagesPreview = ref(false)
const imagesPreviewList = ref<string[]>([])
function handleImagesPreview(imageString: string) {
  // 图片预览
  const images = imageString.split(',').map((e: string) => {
    return e.trim()
  })

  imagesPreviewList.value = images
  isShowImagesPreview.value = true
}

function handleImagesClosePreview() {
  imagesPreviewList.value = []
  isShowImagesPreview.value = false
}

function getMaterialColumns(row: any) {
  if (row.orderId && row.type === 1) {
    return [
      { label: '采购数量', prop: 'orderQuantity', align: 'center', width: 100, showOverflowTooltip: true },
      { label: '转单数量', prop: 'transfer', align: 'center', width: 100, showOverflowTooltip: true },
      { label: '已收数量', prop: 'receivedQuantity', align: 'center', width: 100, showOverflowTooltip: true },
      { label: '供应商', prop: 'supplierName', align: 'left', width: 300, showOverflowTooltip: true },
    ]
  }
  if (row.orderId && row.type === 3) {
    return [
      { label: 'Bom用量', prop: 'workOrderDetail.calcBomQuantity', align: 'center', width: 120, showOverflowTooltip: true },
      { label: '已领数量', prop: 'workOrderDetail.outboundQuantity', align: 'center', width: 120, showOverflowTooltip: true },
    ]
  }

  return []
}

function showAuditLog(row: any) {
  return row.isSubmit === 1 && (row.status === 0 || row.status === 4)
}
const { height } = useWindowSize()
const heightValue = computed(() => height.value - 240)
const heightVal = computed(() => height.value - 360)
</script>

<template>
  <div>
    <div v-if="status === INBOUND_VALUE" w-full :style="`height:${heightValue}px`">
      <!-- <div h="52px" /> -->
      <el-tabs v-model="status" type="border-card" @tab-change="handleStatusChange">
        <el-tab-pane v-for="row in statusList" :key="row.value" :label="row.value === INBOUND_VALUE ? `${row.label}` : `${row.label}(${row.count})`" :name="row.value" />
      </el-tabs>
      <MaterialInbound style="height: 100%;" :api="service.pms.material.inbound" />
    </div>
    <cl-crud v-if="status !== INBOUND_VALUE" ref="Crud">
      <el-row>
        <!-- 刷新按钮 -->
        <cl-refresh-btn />
        <!-- 创建入库单按钮 -->
        <el-button v-permission="service.pms.material.inbound.permission.add" text bg type="success" @click="handleAdd">
          创建物料入库单
        </el-button>
        <cl-flex1 />
        <!-- 关键字搜索 -->
        <cl-search ref="Search" />
      </el-row>

      <el-tabs v-model="status" type="border-card" @tab-change="handleStatusChange">
        <el-tab-pane v-for="row in statusList" :key="row.value" :label="row.value === INBOUND_VALUE ? `${row.label}` : `${row.label}(${row.count})`" :name="row.value" />
        <el-row>
          <!-- 数据表格 -->
          <cl-table
            ref="Table" row-key="id" :expand-row-keys="tableExpandRowKeys"
            class="table-row-pointer"
            :auto-height="false" :style="`height:${heightVal}px`"
            @row-click="onRowClick"
          >
            <!-- 关联单号 -->
            <template #column-orderNo="{ scope }">
              <span v-if="scope.row.type === 3">{{ scope.row.orderSn || '' }}</span>
              <span v-else>{{ scope.row.orderNo || '' }}</span>
            </template>

            <!-- 打印单据 -->
            <template #slot-btn-print="{ scope }">
              <el-button
                v-if="checkOpButtonIsAvaliable('slot-btn-print')" text bg type="primary"
                @click.stop="handlePrint(scope.row)"
              >
                打印
              </el-button>
            </template>

            <template #slot-btn-revoke="{ scope }">
              <el-button
                v-if="V1_DISABLED(scope.row.createTime) && checkOpButtonIsAvaliable('slot-btn-revoke') && isCanRevoke(scope.row)" text bg
                type="danger" @click.stop="handleRevoke(scope.row)"
              >
                撤销入库
              </el-button>
            </template>

            <!-- 审核记录 -->
            <template #slot-audit-log="{ scope }">
              <el-button
                v-if="showAuditLog(scope.row) && checkOpButtonIsAvaliable('slot-audit-log')" text bg type="warning"
                @click.stop="handleAuditLog(scope.row)"
              >
                审核记录
              </el-button>
            </template>

            <!-- 确认入库按钮 -->
            <template #slot-btn-confirm="{ scope }">
              <el-button
                v-if="checkOpButtonIsAvaliable('slot-btn-confirm')" text bg type="success"
                @click.stop="handleConfirm(scope.row)"
              >
                提交
              </el-button>
            </template>

            <!-- 开始入库 -->
            <template #slot-btn-start="{ scope }">
              <el-button
                v-if="checkOpButtonIsAvaliable('slot-btn-start')" text bg type="success"
                @click.stop="handleConfirm(scope.row)"
              >
                开始入库
              </el-button>
            </template>

            <!-- 编辑按钮 -->
            <template #slot-btn-edit="{ scope }">
              <el-button
                v-if="checkOpButtonIsAvaliable('slot-btn-edit')" text bg type="primary"
                @click.stop="handleEdit(scope.row.id)"
              >
                编辑
              </el-button>
            </template>

            <!-- 删除按钮 -->
            <template #slot-btn-delete="{ scope }">
              <el-button
                v-if="checkOpButtonIsAvaliable('slot-btn-delete')" text bg type="danger"
                @click.stop="handleDeleteOrder(scope.row.id)"
              >
                删除
              </el-button>
            </template>

            <!-- 完成入库按钮 -->
            <template #slot-btn-complete="{ scope }">
              <el-button
                v-if="checkOpButtonIsAvaliable('slot-btn-complete')" :loading="isLoading" text bg type="success"
                @click.stop="handleComplete(scope.row)"
              >
                完成入库
              </el-button>
            </template>

            <!-- voucher -->
            <template #column-voucher="{ scope }">
              <!-- <cl-image v-if="scope.row.voucher !== ''" :src="scope.row.voucher" fit="cover" lazy :size="[50, 50]" /> -->
              <!-- 如果不为空则显示点击查看，否则显示无图片 -->
              <el-button
                v-if="
                  scope.row.voucher && scope.row.voucher.split(',').length > 0
                " text type="primary" @click.stop="handleImagesPreview(scope.row.voucher)"
              >
                点击查看
              </el-button>
              <span v-else>无数据</span>
            </template>

            <template #column-products="{ scope }">
              <el-table :data="scope.row.products" style="width: 100%" border :row-class-name="tableRowClassName">
                <!-- PO -->
                <!-- 如果是不是订单入库，则不显示PO -->
                <el-table-column v-if="scope.row.type === 1" prop="Po" label="PO" width="250" show-overflow-tooltip />
                <el-table-column v-if="scope.row.type === 3" prop="workOrderDetail.workOrderNo" label="工单号" width="200" show-overflow-tooltip />

                <el-table-column prop="quantity" label="入库数量" align="center" width="100" show-overflow-tooltip />
                <el-table-column v-for="(column, index) in getMaterialColumns(scope.row)" :key="index" v-bind="column" />
                <el-table-column prop="warehouseId" label="仓位" align="center" width="120">
                  <template #default="{ row }">
                    <span>{{ getDictLabel('warehouse_name', row.warehouseId) }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="inbound_outbound_key" label="关键字" align="center" width="120">
                  <template #default="{ row }">
                    <span>{{ getDictLabel('inbound_outbound_key', row.inbound_outbound_key) }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="address" label="位置" align="center" width="120" show-overflow-tooltip />
                <MaterialTableColumns auto-width />
                <el-table-column prop="unit" label="单位" align="center" width="70" show-overflow-tooltip />
              </el-table>

              <!-- 显示汇总 -->
              <div class="table-summary">
                <div class="table-summary-container">
                  <!-- 标题加粗 -->
                  <span class="cl-table__expand-footer-title">数量总计：</span>
                  <span>{{ scope.row.totalQuantity }}</span>
                </div>
              </div>
            </template>
          </cl-table>
        </el-row>

        <el-row>
          <cl-flex1 />
          <!-- 分页控件 -->
          <cl-pagination />
        </el-row>
      </el-tabs>
      <cl-form ref="InboundForm" />

      <div class="outbound-images-preview">
        <el-image-viewer
          v-if="isShowImagesPreview" :url-list="imagesPreviewList" teleported
          @close="handleImagesClosePreview"
        />
      </div>
      <AuditLog v-model="rowData.id" />
    </cl-crud>
  </div>
</template>

<style media="print" lang="scss">
@page {
  size: portrait;
  /* 设置打印纸张为横向 */
  margin: 0 5px;
  /* 设置页边距 */
}

@media print {
  html {
    background-color: #ffffff;
    height: auto;
    margin: 0;
  }

  body {
    border: solid 1px #ffffff;
    margin: 0;
  }
}
</style>

<style scoped lang="scss">
table {
  border-collapse: collapse;
}

table,
th,
td {
  border: 1px solid black;
}
</style>
