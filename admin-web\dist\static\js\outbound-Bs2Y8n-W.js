import{c as Ce,b as y,e as $,z as ot,A as rt,q as k,w as a,h as l,y as v,i as g,f as N,s as ke,F as we,G as ut,H as st,B as P,j as m,t as V,v as x,Y as U,W as R,V as it,E as f,T as K,o as p,a0 as ct}from"./.pnpm-hVqhwuVC.js";import{g as pt,i as ne,j as A,e as xe}from"./index-DkYL1aws.js";import{u as dt}from"./table-ops-CrFIfhgA.js";/* empty css              */import{a as mt}from"./index-C6cm1h61.js";const ft={key:1},ht={key:0},bt={key:1},gt={key:0},vt={key:1},yt=v("span",{style:{color:"var(--el-color-danger)"}},"*",-1),_t=v("span",{style:{color:"var(--el-color-danger)"}},"*",-1),kt=v("span",{style:{color:"var(--el-color-danger)"}},"*",-1),wt=v("span",{style:{color:"var(--el-color-danger)"}},"*",-1),xt=v("span",{style:{color:"var(--el-color-danger)"}},"*",-1),Ct={style:{display:"flex","align-items":"center"}},It={style:{display:"flex","align-items":"center"}},Pt={style:{display:"flex",gap:"8px"}},Vt={key:0},St={key:1},Qt={class:"outbound-images-preview"},Dt=Ce({name:"undefined"}),Bt=Ce({...Dt,setup(qt){const Y="pack-form-data",{dict:oe}=pt(),{service:C}=mt(),G=oe.get("color")||[],_=y(0),M=y(!1),E=y(),H=y([]),Ie=y([]),W=y(!1),J=y(),re=y(0),c=y([]),B=y([]),ue=y("1"),S=y(!1),j=y(!1),X=y([{label:"备货完成",value:0,type:"success",count:0},{label:"打包中",value:1,type:"warning",count:0},{label:"打包完成",value:2,type:"success",count:0},{label:"交货完成",value:3,type:"success",count:0}]),se=y({"slot-btn-pack":{width:110,permission:C.pms.warehouse.source.outbound.permission.pack,show:$(()=>_.value===0)},"slot-btn-export-order":{width:120,permission:C.pms.sale.order.permission.export,show:$(()=>_.value===0)},"slot-btn-pack-finish":{width:110,permission:C.pms.warehouse.source.outbound.permission.finish,show:$(()=>_.value===1)},"slot-btn-repack":{width:110,permission:C.pms.warehouse.source.outbound.permission.finish,show:$(()=>_.value===2)},"slot-btn-ship":{width:80,permission:C.pms.warehouse.source.outbound.permission.ship,show:$(()=>_.value===2)},"slot-btn-export-delivery-receipt":{width:80,permission:C.pms.warehouse.source.outbound.permission.export,show:$(()=>_.value>=2)},"slot-btn-export-packing-slip":{width:110,permission:C.pms.warehouse.source.outbound.permission.export,show:$(()=>_.value>=2)},"slot-btn-export-pack":{width:110,permission:C.pms.warehouse.source.outbound.permission.export,show:$(()=>_.value>=2)},"slot-btn-revoke":{width:110,permission:C.pms.warehouse.source.outbound.permission.revoke,show:$(()=>_.value===1||_.value===2)}}),{getOpWidth:Pe,checkOpButtonIsAvaliable:T,getOpIsHidden:Ve}=dt(se),ie=y(),ce=y(!1);ot(_,()=>{ie.value=Pe(),ce.value=Ve()},{immediate:!0});const pe=ne.useTable({columns:[{label:"#",prop:"products",type:"expand"},{label:"出库单号",prop:"osn",width:250},{label:"关联的订单",prop:"order.orderSn"},{label:"单品总计数量",prop:"totalQuantity"},{label:"状态",prop:"status",dict:X},{label:"出库照片",prop:"images"},{label:"创建时间",prop:"createTime",component:{name:"cl-date-text",props:{format:"YYYY-MM-DD HH:mm:ss"}}},{label:"发货时间",prop:"shipTime",component:{name:"cl-date-text",props:{format:"YYYY-MM-DD HH:mm:ss"}}},{type:"op",label:"操作",width:ie,hidden:ce,buttons:Object.keys(se.value)}]}),O=ne.useCrud({service:C.pms.warehouse.source.outbound,async onRefresh(n,{next:e,render:u}){const{count:s,list:h,pagination:b}=await e(n);X.value.forEach(w=>{w.count=s[w.value]||0}),u(h,b)}},n=>{n.refresh({status:_})});function Se(n){_.value=n,ct(()=>{var e;(e=O.value)==null||e.refresh()})}function Qe(n,e){var u;(e==null?void 0:e.type)==="expand"||(e==null?void 0:e.type)==="op"||(e==null?void 0:e.property)==="images"||(u=pe.value)==null||u.toggleRowExpansion(n)}function De(n){const e=n==null?void 0:n.orderId,u={type:0};u.orderId=e;const s={url:"/export",method:"POST",data:u,responseType:"blob"};C.pms.sale.order.request(s).then(h=>{const b="出货单",w=`出货单-${n==null?void 0:n.order.orderSn}.xlsx`;xe(h,w)&&f.success(`${b}导出成功`)}).catch(h=>{f.error(h.message||"导出失败")})}function qe(n){K.confirm("确认开始打包吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{S.value=!0,C.pms.warehouse.source.outbound.pack({id:n.id}).then(()=>{f.success("设置开始打包成功"),_.value=1,S.value=!1}).catch(()=>{S.value=!1})}).catch(()=>{S.value=!1})}function de(n){if(E.value=n,H.value=n.products.map(e=>({...e})),B.value=n.products.map(e=>({productId:e.productId,sku:e.sku,caseQuantity:e.caseQuantity,caseGrossWeight:e.caseGrossWeight,unit:e.unit})),c.value=[],n.packData&&n.packData.length>0)c.value=n.packData.map(e=>({...e,index:new Date().getTime()+Math.floor(Math.random()*1e3),quantity:e.quantity||e.piece||0,productId:e.productId||(e.product?e.product.id:null),cartonQuantity:e.cartonQuantity||1,palletNumber:e.palletNumber||"",grossWeight:e.grossWeight||null,netWeight:e.netWeight||null}));else{const e=`${Y}-${n.id}`,u=localStorage.getItem(e);if(u){const s=JSON.parse(u);s&&s.length>0&&(c.value=s.map(h=>h))}}M.value=!0}async function me(n){if(!J.value)return!1;await J.value.validate(e=>{if(e&&(W.value=!0),c.value.length===0){f.error({message:"请添加装箱数据"});return}if(c.value.find(i=>i.productId===null||i.productId===void 0)){f.error({message:"请选择产品"});return}if(c.value.find(i=>i.palletNumber===null||i.palletNumber===void 0||i.palletNumber==="")){f.error({message:"请填写正确的托盘编号"});return}if(c.value.find(i=>i.cartonQuantity===null||i.cartonQuantity===void 0)){f.error({message:"请填写正确的箱数"});return}if(c.value.find(i=>i.quantity===null||i.quantity===void 0||i.quantity===0)){f.error({message:"请填写正确的数量"});return}const w=c.value.find(i=>i.carton===null||i.carton===void 0||i.carton===0);if(w){f.error({message:"请填写正确的箱号信息"});return}const d=c.value.map(i=>{const{...r}=i;return r});if(W.value=!w,e&&!w){let i="请确认填写的箱号正确，提交后无法修改!";if(H.value.every(I=>I.packQuantity===I.piece)||(i="出货数量与订单数量不符，请确认无误后提交，提交后无法修改!"),!n){localStorage.setItem(`${Y}-${E.value.id}`,JSON.stringify(d)),f.success("打包信息保存成功");return}K.confirm(i,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{j.value=!0,await C.pms.warehouse.source.outbound.finish({id:E.value.id,packData:d}).then(()=>{var I;localStorage.removeItem(`${Y}-${E.value.id}`),f.success("提交打包信息成功"),_.value=2,M.value=!1,(I=O==null?void 0:O.value)==null||I.refresh(),j.value=!1}).catch(I=>{f.error(I.message||"提交打包信息失败"),j.value=!1})}).catch(()=>{})}})}function $e(n){return/^\d+(?:\.\d{0,4})?$/.test(n)?n:n.slice(0,-1)}const fe=ne.useForm();function Te(n){var e;(e=fe.value)==null||e.open({title:"填写发货信息",dialog:{controls:["close"]},items:[{label:"送货方式",prop:"carrier",required:!0,component:{name:"el-select",props:{placeholder:"请选择",clearable:!0},options:oe.get("carrier")}},{label:"品名",prop:"category",required:!0,component:{name:"el-input"}},{label:"产品",prop:"product",required:!0,component:{name:"el-input"}},{label:"外箱PO#",prop:"cartonPo",required:!0,component:{name:"el-input"}},{label:"车牌号",prop:"truckPlate",required:!1,component:{name:"el-input"}},{label:"司机联系方式",prop:"driversContact",required:!1,component:{name:"el-input"}},{label:"运费",prop:"deliveryFee",required:!1,component:{name:"el-input",props:{type:"number",formatter:$e}}},{label:"备注",prop:"remarks",required:!1,component:{name:"el-input",props:{row:2,type:"textarea",autosize:{minRows:2,maxRows:6}}}},{label:"出库照片",prop:"images",required:!0,component:{name:"cl-upload",props:{multiple:!0,limit:30,accept:"image/jpg,image/jpeg,image/png",text:"上传出库照片",type:"image",disabled:!1,isPrivate:!1}}}],on:{submit:async(u,{done:s,close:h})=>{C.pms.warehouse.source.outbound.ship({id:n.id,...u}).then(()=>{f.success("发货成功"),h(),_.value=3}).catch(b=>{f.error(b.message||"发货失败")}),s()}}})}function Z(n,e){C.pms.warehouse.source.outbound.request({url:"/export",data:{id:n==null?void 0:n.id,type:e},method:"POST",responseType:"blob"}).then(u=>{var h,b,w,d,i,r;let s="导出文件";switch(e){case 1:s=(h=n.order)!=null&&h.orderSn?`货物交接单-${(b=n.order)==null?void 0:b.orderSn}`:"货物交接单";break;case 2:s=(w=n.order)!=null&&w.orderSn?`装箱数据-${(d=n.order)==null?void 0:d.orderSn}`:"装箱数据";break;case 3:s=(i=n.order)!=null&&i.orderSn?`Packing Slip-${(r=n.order)==null?void 0:r.orderSn}`:"Packing Slip";break}xe(u,`${s}.xlsx`)&&f.success("导出成功")}).catch(u=>{f.error(u.message||"导出失败")})}const ee=$(()=>{const e=(c.value||[]).map(u=>u.carton);return e.length===0?0:Math.max(...e)});function Ne(){c.value.push({index:new Date().getTime()+Math.floor(Math.random()*1e3),productId:null,quantity:null,carton:ee.value?ee.value+1:1,cartonQuantity:1,palletNumber:"",grossWeight:null})}function Ue(n){c.value=c.value.filter(e=>e.index!==n),c.value.length>0?(he(),f.success("已删除数据并重新排序箱号")):f.success("已删除数据")}function We(n){const u=(n.packData||[]).map(s=>s.palletNumber);return Array.from(new Set(u)).length}function Be(n){const u=(n.packData||[]).map(s=>s.carton);return Math.max(...u)}function Fe(n){K.confirm("确定撤销出库单？撤回后将清空打包数据，并更新为备货完成的状态！","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{S.value=!0,C.pms.warehouse.source.outbound.revoke({id:n.id}).then(()=>{S.value=!1,_.value=0,f.success("撤销成功")}).catch(e=>{S.value=!1,f.error(e.message||"撤销失败")})}).catch(()=>{})}function Me(n){const e=c.value||[],u=B.value.find(s=>s.productId===n.productId);u&&u.unit===0&&e.forEach(s=>{s.index===n.index&&(s.grossWeight=u.caseGrossWeight||null,s.quantity=u.caseQuantity||null)})}const te=y();function Ee(n){var u;const e=B.value.find(s=>s.productId===n);e&&e.unit===0&&((u=te.value)==null||u.setForm("defaultQuantity",e.caseQuantity||1))}function Oe(){var n;(n=te.value)==null||n.open({title:"快捷填充",dialog:{controls:["close"]},op:{saveButtonText:"填充"},items:[{label:"打包产品",prop:"packProductId",required:!0,component:{name:"el-select",options:B.value.map(e=>({label:e.sku,value:e.productId})),props:{filterable:!0,onChange:Ee}}},{label:"打包箱数",prop:"packCarton",required:!0,component:{name:"el-input-number",props:{min:1,precision:0}}},{label:"起始箱号",prop:"startCarton",value:ee.value+1,component:{name:"el-input-number",props:{min:1,precision:0}}},{label:"默认数量",prop:"defaultQuantity",component:{name:"el-input-number",props:{min:1,precision:0}}},{label:"托盘编号",prop:"palletNumber",component:{name:"el-input"}}],on:{submit:async(e,{done:u,close:s})=>{var r,I;const{startCarton:h,packProductId:b,packCarton:w,defaultQuantity:d,palletNumber:i}=e;if(b<=0){f.error("请选择打包产品");return}if(w<=0){f.error("请输入打包箱数");return}for(let F=0;F<w;F++)c.value.push({index:new Date().getTime()+Math.floor(Math.random()*1e3),productId:b,quantity:d||((r=B.value.find(L=>L.productId===b))==null?void 0:r.caseQuantity)||null,grossWeight:((I=B.value.find(L=>L.productId===b))==null?void 0:I.caseGrossWeight)||null,carton:h+F,cartonQuantity:1,palletNumber:i||null});s(),u()}}})}function Le(){const n=E.value;K.confirm("确定清空打包数据？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{if(c.value=[],n.id>0){const e=`${Y}-${n.id}`;localStorage.removeItem(e)}f.success("清空成功")}).catch(()=>{})}const ae=y(!1),le=y([]);function ze(n){const e=n.split(",").map(u=>u.trim());le.value=e,ae.value=!0}function Re(){le.value=[],ae.value=!1}rt(()=>{H.value.forEach(n=>{n.packQuantity=c.value.filter(e=>e.productId===n.productId).reduce((e,u)=>e+u.quantity*n.unitQuantity,0)})});function Ke(n){K.confirm("确认要重新编辑打包信息吗？这将允许修改已完成的打包数据。","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{S.value=!0,f.success("已切换到重新打包模式"),de(n),S.value=!1}).catch(()=>{S.value=!1})}function Ye(n){const e=c.value.find(h=>h.index===n);if(!e)return;const u={index:new Date().getTime()+Math.floor(Math.random()*1e3),productId:null,quantity:null,carton:(e.carton||0)+1,cartonQuantity:1,palletNumber:e.palletNumber||"",grossWeight:null,netWeight:null},s=c.value.findIndex(h=>h.index===n);if(s!==-1){c.value.splice(s+1,0,u);for(let h=s+2;h<c.value.length;h++){const b=c.value[h];b&&b.carton!==void 0&&b.carton!==null&&(b.carton+=1)}f.success("已在当前行后插入新数据")}else c.value.push(u),f.success("已添加新数据到末尾")}function he(){c.value.sort((n,e)=>(n.carton||0)-(e.carton||0)),c.value.forEach((n,e)=>{n.carton=e+1})}function Ge(){he(),f.success("箱号重新排序完成")}return(n,e)=>{const u=g("cl-refresh-btn"),s=g("cl-flex1"),h=g("cl-search-key"),b=g("el-row"),w=g("el-tab-pane"),d=g("el-button"),i=g("el-tag"),r=g("el-table-column"),I=g("el-table"),F=g("el-collapse-item"),L=g("el-collapse"),He=g("cl-table"),je=g("cl-pagination"),be=g("el-tabs"),ge=g("cl-form"),Ae=g("el-divider"),Je=g("el-option"),Xe=g("el-select"),z=g("el-input-number"),Ze=g("el-input"),et=g("el-form"),tt=g("cl-dialog"),at=g("el-image-viewer"),lt=g("cl-crud"),nt=st("loading");return p(),k(lt,{ref_key:"Crud",ref:O},{default:a(()=>[l(b,null,{default:a(()=>[l(u),l(s),l(h,{placeholder:"请输入出库单号或关联订单号"})]),_:1}),l(be,{modelValue:_.value,"onUpdate:modelValue":e[1]||(e[1]=t=>_.value=t),type:"border-card",onTabChange:Se},{default:a(()=>[(p(!0),N(we,null,ke(X.value,t=>(p(),k(w,{key:t.value,label:`${t.label}(${t.count})`,name:t.value},null,8,["label","name"]))),128)),l(b,null,{default:a(()=>[ut((p(),k(He,{ref_key:"Table",ref:pe,"row-key":"id",class:"table-row-pointer",onRowClick:Qe},{"column-images":a(({scope:t})=>[t.row.images&&t.row.images.split(",").length>0?(p(),k(d,{key:0,text:"",type:"primary",onClick:U(o=>ze(t.row.images),["stop"])},{default:a(()=>[m(" 点击查看 ")]),_:2},1032,["onClick"])):(p(),N("span",ft,"无数据"))]),"slot-btn-revoke":a(({scope:t})=>[x(T)("slot-btn-revoke")?(p(),k(d,{key:0,text:"",bg:"",type:"danger",onClick:o=>Fe(t.row)},{default:a(()=>[m(" 撤销出库 ")]),_:2},1032,["onClick"])):P("",!0)]),"slot-btn-pack":a(({scope:t})=>[x(T)("slot-btn-pack")?(p(),k(d,{key:0,text:"",bg:"",type:"success",onClick:U(o=>qe(t.row),["stop"])},{default:a(()=>[m(" 开始打包 ")]),_:2},1032,["onClick"])):P("",!0)]),"slot-btn-export-order":a(({scope:t})=>[x(T)("slot-btn-export-order")?(p(),k(d,{key:0,text:"",bg:"",type:"primary",onClick:U(o=>De(t.row),["stop"])},{default:a(()=>[m(" 导出出货单 ")]),_:2},1032,["onClick"])):P("",!0)]),"slot-btn-pack-finish":a(({scope:t})=>[x(T)("slot-btn-pack-finish")?(p(),k(d,{key:0,text:"",bg:"",type:"success",onClick:U(o=>de(t.row),["stop"])},{default:a(()=>[m(" 打包完成 ")]),_:2},1032,["onClick"])):P("",!0)]),"slot-btn-repack":a(({scope:t})=>[x(T)("slot-btn-repack")?(p(),k(d,{key:0,text:"",bg:"",type:"warning",onClick:U(o=>Ke(t.row),["stop"])},{default:a(()=>[m(" 重新打包 ")]),_:2},1032,["onClick"])):P("",!0)]),"slot-btn-ship":a(({scope:t})=>[x(T)("slot-btn-ship")?(p(),k(d,{key:0,text:"",bg:"",type:"success",onClick:U(o=>Te(t.row),["stop"])},{default:a(()=>[m(" 开始发货 ")]),_:2},1032,["onClick"])):P("",!0)]),"slot-btn-export-delivery-receipt":a(({scope:t})=>[x(T)("slot-btn-export-delivery-receipt")?(p(),k(d,{key:0,text:"",bg:"",type:"warning",onClick:U(o=>Z(t.row,1),["stop"])},{default:a(()=>[m(" 交接单 ")]),_:2},1032,["onClick"])):P("",!0)]),"slot-btn-export-packing-slip":a(({scope:t})=>[x(T)("slot-btn-export-packing-slip")?(p(),k(d,{key:0,text:"",bg:"",type:"danger",onClick:U(o=>Z(t.row,3),["stop"])},{default:a(()=>[m(" 装箱清单 ")]),_:2},1032,["onClick"])):P("",!0)]),"slot-btn-export-pack":a(({scope:t})=>[x(T)("slot-btn-export-pack")?(p(),k(d,{key:0,text:"",bg:"",type:"success",onClick:U(o=>Z(t.row,2),["stop"])},{default:a(()=>[m(" 装箱数据 ")]),_:2},1032,["onClick"])):P("",!0)]),"column-products":a(({scope:t})=>[l(L,{modelValue:ue.value,"onUpdate:modelValue":e[0]||(e[0]=o=>ue.value=o),class:"outbound-collapse"},{default:a(()=>[l(F,{title:"产品信息",name:"1"},{default:a(()=>[l(I,{data:t.row.products,style:{width:"100%"},border:""},{default:a(()=>[l(r,{label:"产品名称",align:"center"},{default:a(()=>[l(r,{prop:"name",label:"单位",width:"100",align:"center"},{default:a(o=>[o.row.unit===0?(p(),k(i,{key:0,type:"info"},{default:a(()=>[m(" 单 品 ")]),_:1})):P("",!0),o.row.unit===1?(p(),k(i,{key:1,type:"warning"},{default:a(()=>[m(" 展示盒 ")]),_:1})):P("",!0),o.row.unit===2?(p(),k(i,{key:2,type:"success"},{default:a(()=>[m(" 箱 装 ")]),_:1})):P("",!0)]),_:2},1024),l(r,{prop:"name",label:"中文名",align:"center",width:"250"}),l(r,{prop:"nameEn",label:"英文名",align:"center",width:"250"})]),_:2},1024),l(r,{prop:"sku",label:"SKU",width:"130",align:"center"}),l(r,{prop:"upc",label:"UPC",width:"130",align:"center"}),l(r,{prop:"color",label:"颜色",width:"130",align:"center"},{default:a(o=>[v("span",null,V(x(A)(x(G),parseInt(o.row.color))),1)]),_:2},1024),l(r,{prop:"quantity",label:"订单数量",width:"100",align:"center"}),l(r,{label:"包装单位信息",align:"center",width:"300"},{default:a(()=>[l(r,{prop:"pieceProduct.sku",label:"SKU",width:"130",align:"center"}),l(r,{prop:"pieceProduct.upc",label:"UPC",width:"130",align:"center"}),l(r,{prop:"unitQuantity",label:"单位数量",width:"130",align:"center"},{default:a(o=>[o.row.unit>0?(p(),N("span",ht,V(o.row.unitQuantity),1)):(p(),N("span",bt," - "))]),_:2},1024),l(r,{prop:"piece",label:"订单数量",width:"100",align:"center"}),l(r,{prop:"shippedPiece",label:"装箱数量",width:"100",align:"center"}),l(r,{prop:"color",label:"颜色",width:"130",align:"center"},{default:a(o=>[v("span",null,V(x(A)(x(G),parseInt(o.row.pieceProduct.color))),1)]),_:2},1024)]),_:2},1024),l(r,{label:"库存状态",align:"center"},{default:a(()=>[l(r,{prop:"pieceProduct.stock.inStock",label:"可用",width:"100",align:"center"},{default:a(o=>{var Q,D,q,ve,ye,_e;return[v("span",null,V((((q=(D=(Q=o.row)==null?void 0:Q.pieceProduct)==null?void 0:D.stock)==null?void 0:q.inStock)||0)-(((_e=(ye=(ve=o.row)==null?void 0:ve.pieceProduct)==null?void 0:ye.stock)==null?void 0:_e.freezeStock)||0)),1)]}),_:2},1024),l(r,{prop:"pieceProduct.stock.freezeStock",label:"冻结",width:"100",align:"center"},{default:a(o=>{var Q,D,q;return[v("span",null,V(((q=(D=(Q=o.row)==null?void 0:Q.pieceProduct)==null?void 0:D.stock)==null?void 0:q.freezeStock)||0),1)]}),_:2},1024),l(r,{prop:"inProductionQuantity",label:"在产",width:"100",align:"center"},{default:a(o=>{var Q,D,q;return[v("span",null,V(((q=(D=(Q=o.row)==null?void 0:Q.pieceProduct)==null?void 0:D.stock)==null?void 0:q.productionStock)||0),1)]}),_:2},1024),l(r,{prop:"waitInboundQuantity",label:"待入库",width:"100",align:"center"},{default:a(o=>{var Q,D,q;return[v("span",null,V(((q=(D=(Q=o.row)==null?void 0:Q.pieceProduct)==null?void 0:D.stock)==null?void 0:q.waitInboundStock)||0),1)]}),_:2},1024)]),_:2},1024)]),_:2},1032,["data"])]),_:2},1024),t.row.status>1?(p(),k(F,{key:0,title:`装箱信息 - 托盘数：${We(t.row)} - 箱数：${Be(t.row)}`,name:"2"},{default:a(()=>[l(I,{data:t.row.packData,style:{width:"100%"},border:""},{default:a(()=>[l(r,{prop:"product.sku",label:"SKU",align:"center"}),l(r,{prop:"carton",label:"箱号",align:"center"},{default:a(o=>[o.row.carton>0?(p(),N("span",gt,"#"+V(o.row.carton),1)):(p(),N("span",vt," - "))]),_:2},1024),l(r,{prop:"palletNumber",label:"托盘编号",align:"center"}),l(r,{prop:"piece",label:"装箱数量",align:"center"}),l(r,{prop:"cartonQuantity",label:"箱数",align:"center"})]),_:2},1032,["data"])]),_:2},1032,["title"])):P("",!0)]),_:2},1032,["modelValue"])]),_:1})),[[nt,S.value]])]),_:1}),l(b,null,{default:a(()=>[l(s),l(je)]),_:1})]),_:1},8,["modelValue"]),l(ge,{ref_key:"ShipForm",ref:fe},null,512),l(tt,{modelValue:M.value,"onUpdate:modelValue":e[6]||(e[6]=t=>M.value=t),width:"70%",controls:["close"],title:"填写打包信息"},{footer:a(()=>[l(d,{type:"success",loading:j.value,onClick:e[3]||(e[3]=t=>me(!0))},{default:a(()=>[m(" 提交打包信息 ")]),_:1},8,["loading"]),l(d,{type:"primary",onClick:e[4]||(e[4]=t=>me(!1))},{default:a(()=>[m(" 保存打包信息 ")]),_:1}),l(d,{onClick:e[5]||(e[5]=t=>M.value=!1)},{default:a(()=>[m(" 取 消 ")]),_:1})]),default:a(()=>[l(be,{modelValue:re.value,"onUpdate:modelValue":e[2]||(e[2]=t=>re.value=t),"tab-position":"right"},{default:a(()=>[l(w,{label:"打包信息",name:0},{default:a(()=>[l(b,null,{default:a(()=>[l(d,{type:"primary",style:{margin:"10px 0 0 0"},onClick:Oe},{default:a(()=>[m(" 快捷填充 ")]),_:1}),l(d,{disabled:c.value.length===0,type:"danger",style:{margin:"10px 0 0 10px"},onClick:Le},{default:a(()=>[m(" 一键清空 ")]),_:1},8,["disabled"]),l(d,{disabled:c.value.length<=1,type:"warning",style:{margin:"10px 0 0 10px"},onClick:Ge},{default:a(()=>[m(" 重新排序箱号 ")]),_:1},8,["disabled"])]),_:1}),l(Ae,{style:{margin:"10px 0 0 0"}}),l(et,{ref_key:"packForm",ref:J,model:Ie.value,"label-width":"100px",size:"large","status-icon":""},{default:a(()=>[l(I,{data:c.value,height:"600"},{default:a(()=>[l(r,{prop:"sku",label:"*SKU",width:"200"},{header:a(()=>[yt,m(" SKU ")]),default:a(t=>[v("div",{style:{display:"flex","align-items":"center"},class:R(W.value&&!(t.row.productId>0)?"sku-select-error":"")},[l(Xe,{modelValue:t.row.productId,"onUpdate:modelValue":o=>t.row.productId=o,prop:"productId",filterable:"","reserve-keyword":"",placeholder:"请选择产品",onChange:o=>Me(t.row)},{default:a(()=>[(p(!0),N(we,null,ke(B.value,o=>(p(),k(Je,{key:o.productId,label:o.sku,value:o.productId},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])],2)]),_:1}),l(r,{prop:"carton",label:"*箱号"},{header:a(()=>[_t,m(" 箱号 ")]),default:a(t=>[v("div",{style:{display:"flex","align-items":"center"},class:R(W.value&&!(t.row.carton>0)?"carton-input-error":"")},[l(z,{modelValue:t.row.carton,"onUpdate:modelValue":o=>t.row.carton=o,prop:"carton",min:0,placeholder:"箱号"},null,8,["modelValue","onUpdate:modelValue"])],2)]),_:1}),l(r,{prop:"palletNumber",label:"*托盘编号"},{header:a(()=>[kt,m(" 托盘编号 ")]),default:a(t=>[v("div",{style:{display:"flex","align-items":"center"},class:R(W.value&&t.row.palletNumber===""?"pallet-input-error":"")},[l(Ze,{modelValue:t.row.palletNumber,"onUpdate:modelValue":o=>t.row.palletNumber=o,prop:"palletNumber",placeholder:"托盘编号"},null,8,["modelValue","onUpdate:modelValue"])],2)]),_:1}),l(r,{prop:"quantity",label:"*数量"},{header:a(()=>[wt,m(" 数量 ")]),default:a(t=>[v("div",{style:{display:"flex","align-items":"center"},class:R(W.value&&!(t.row.quantity>0)?"quantity-input-error":"")},[l(z,{modelValue:t.row.quantity,"onUpdate:modelValue":o=>t.row.quantity=o,prop:"quantity",min:1,placeholder:"数量"},null,8,["modelValue","onUpdate:modelValue"])],2)]),_:1}),l(r,{prop:"cartonQuantity",label:"*箱数"},{header:a(()=>[xt,m(" 箱数 ")]),default:a(t=>[v("div",{style:{display:"flex","align-items":"center"},class:R(W.value&&!(t.row.cartonQuantity!==null||t.row.cartonQuantity!==void 0)?"carton-quantity-input-error":"")},[l(z,{modelValue:t.row.cartonQuantity,"onUpdate:modelValue":o=>t.row.cartonQuantity=o,prop:"cartonQuantity",min:0,placeholder:"箱数"},null,8,["modelValue","onUpdate:modelValue"])],2)]),_:1}),l(r,{prop:"netWeight",label:"净重"},{default:a(t=>[v("div",Ct,[l(z,{modelValue:t.row.netWeight,"onUpdate:modelValue":o=>t.row.netWeight=o,precision:4,prop:"netWeight",min:0,placeholder:"净重(KG)"},null,8,["modelValue","onUpdate:modelValue"])])]),_:1}),l(r,{prop:"grossWeight",label:"毛重(KG)"},{default:a(t=>[v("div",It,[l(z,{modelValue:t.row.grossWeight,"onUpdate:modelValue":o=>t.row.grossWeight=o,precision:4,prop:"grossWeight",min:0,placeholder:"毛重"},null,8,["modelValue","onUpdate:modelValue"])])]),_:1}),l(r,{label:"操作",width:"180"},{default:a(t=>[v("div",Pt,[l(d,{type:"primary",size:"small",onClick:o=>Ye(t.row.index)},{default:a(()=>[m(" 插入 ")]),_:2},1032,["onClick"]),l(d,{type:"danger",size:"small",onClick:o=>Ue(t.row.index)},{default:a(()=>[m(" 删除 ")]),_:2},1032,["onClick"])])]),_:1})]),_:1},8,["data"]),l(d,{style:{width:"100%"},class:"btn-data-add",onClick:Ne},{default:a(()=>[m(" + 选择产品 ")]),_:1})]),_:1},8,["model"])]),_:1}),l(w,{label:"订单信息",name:1},{default:a(()=>[l(I,{data:H.value,style:{width:"100%"},height:"600",stripe:"",border:""},{default:a(()=>[l(r,{prop:"sku",label:"SKU",align:"center"}),l(r,{prop:"quantity",label:"订单数量",width:"130",align:"center"}),l(r,{prop:"color",label:"颜色",align:"center",width:"140"},{default:a(t=>[v("span",null,V(x(A)(x(G),parseInt(t.row.color))),1)]),_:1}),l(r,{label:"包装单位信息",align:"center",width:"300"},{default:a(()=>[l(r,{prop:"pieceProduct.sku",label:"SKU",width:"130",align:"center"}),l(r,{prop:"unitQuantity",label:"单位数量",width:"100",align:"center"},{default:a(t=>[t.row.unit>0?(p(),N("span",Vt,V(t.row.unitQuantity),1)):(p(),N("span",St," - "))]),_:1}),l(r,{prop:"piece",label:"订单数量",width:"100",align:"center"}),l(r,{prop:"packQuantity",label:"装箱数量",width:"100",align:"center"},{default:a(t=>[v("span",{style:it({color:t.row.packQuantity===t.row.piece?"var(--el-color-success)":"var(--el-color-danger)"})},V(t.row.packQuantity),5)]),_:1}),l(r,{prop:"color",label:"颜色",width:"140",align:"center"},{default:a(t=>[v("span",null,V(x(A)(x(G),parseInt(t.row.pieceProduct.color))),1)]),_:1})]),_:1})]),_:1},8,["data"])]),_:1})]),_:1},8,["modelValue"])]),_:1},8,["modelValue"]),l(ge,{ref_key:"QuickForm",ref:te},null,512),v("div",Qt,[ae.value?(p(),k(at,{key:0,"url-list":le.value,teleported:"",onClose:Re},null,8,["url-list"])):P("",!0)])]),_:1},512)}}});export{Bt as default};
