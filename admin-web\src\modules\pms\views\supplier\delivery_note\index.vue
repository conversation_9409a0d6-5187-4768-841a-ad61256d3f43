<script lang="ts" name="pms-bill-payment" setup>
import { useCrud, useSearch, useTable, useUpsert } from '@cool-vue-p/crud'
import Dayjs from 'dayjs'
import { ElMessage, ElMessageBox } from 'element-plus'
import { cloneDeep } from 'lodash-es'
import { computed, ref, watch } from 'vue'
import SelectDict from '/$/base/components/select/select-dict.vue'
import { useStore } from '/$/base/store'
import { useDict } from '/$/dict'
import { getDictLabel } from '/$/pims/utils'
import { useTableOps } from '/$/pms/hooks/table-ops'
import '/$/pms/static/css/index.scss'
import { router, useCool } from '/@/cool'

const { dict } = useDict()
const inbound_outbound_key_list = dict.get('inbound_outbound_key')
const { service } = useCool()
const statusList = ref([
  { label: '草稿', value: 0, count: 0 },
  { label: '已提交', value: 1, count: 0 },
  { label: '已确认', value: 2, count: 0 },
])
const status = ref(0)
const { user } = useStore()
const isAdmin = ref(false)
const supplier_id = ref(0)
const voucher = ref('')
// 检查用户角色并获取供应商信息
async function checkUserRole() {
  user.info = user.info || {}
  const user_id = user?.info?.id

  try {
    const res = await service.pms.supplier_account.request({
      url: '/getUserRole',
      method: 'POST',
      data: {
        user_id,
      },
    })
    const roleData = []
    res && res.forEach((row) => {
      roleData.push(row.name)
    })
    //  用户的角色是供应商，则获取此供应商信息，否则获取全部信息
    if (roleData.includes('供应商')) {
      await fetchSupplierInfo()
      isAdmin.value = false
    }
    else {
      isAdmin.value = true
    }
  }
  catch (error) {
    console.error('查询角色信息失败:', error)
    ElMessage.error('查询角色信息失败！')
    // 默认设置为管理员模式
    isAdmin.value = true
  }
}

// 获取供应商信息
async function fetchSupplierInfo() {
  try {
    const res = await service.pms.supplier_account.request({
      url: '/getUserBindSupplier',
      method: 'GET',
      params: {
        user_id: user?.info?.id,
      },
    })
    supplier_id.value = res.supplier_id // 供应商ID
  }
  catch (e) {
    ElMessage.error('获取供应商信息失败')
  }
}

// ============ 配置操作按钮权限 ============
const opButtons = ref({
  'slot-btn-confirm': {
    width: 80,
    permission: service.pms.delivery_note.permission.submit,
    show: computed(() => status.value === 0),
  },
  'slot-btn-edit': {
    width: 80,
    permission: service.pms.delivery_note.permission.update,
    show: computed(() => status.value === 0),
  },
  'slot-btn-delete': {
    width: 80,
    permission: service.pms.delivery_note.permission.delete,
    show: computed(() => status.value === 0),
  },
  'slot-btn-revoke': {
    width: 80,
    permission: service.pms.delivery_note.permission.revoke,
    show: computed(() => status.value !== 0),
  },
  'slot-btn-success': {
    width: 80,
    permission: service.pms.delivery_note.permission.revoke,
    show: computed(() => status.value === 1),
  },
  'slot-btn-print': {
    width: 80,
    permission: service.pms.delivery_note.permission.page,
    show: true,
  },
})

const { getOpWidth, checkOpButtonIsAvaliable, getOpIsHidden } = useTableOps(
  opButtons as any,
)
const opWidth = ref()
const opIsHidden = ref(false)

// 当status发生变化时，更新opColumnItem
watch(
  status,
  () => {
    opWidth.value = getOpWidth()
    opIsHidden.value = getOpIsHidden()
  },
  { immediate: true },
)
// ============ 配置操作按钮权限 ============

// cl-upsert 配置
const Upsert = useUpsert({
  props: {
    class: 'delivery-note-form',
    labelWidth: '120px',
  },
  items: [],
})

// cl-table 配置
const Table = useTable({
  columns: [
    {
      label: '#',
      prop: 'products',
      type: 'expand',
      width: 50,
    },
    // { label: "ID", prop: "id", width: 60 },
    { label: '创建时间', prop: 'createTime', width: 220 },
    { label: '送货单号', prop: 'no', width: 220 },
    { label: 'PO', prop: 'po', width: 220 },
    { label: '订单号', prop: 'orderNo', width: 220 },
    { label: '送货总数', prop: 'totalQuantity', width: 220 },
    { label: '供应商名称', prop: 'supplier_name' },
    { label: '内部单号', prop: 'internalOrderNo' },
    { label: '入库类型', prop: 'inboundType', formatter: (row: any) => {
      return (
        row.inboundType === 1 ? '采购入库' : row.inboundType === 9 ? '补退货' : '未知'
      )
    } },
    {
      label: '送货单',
      prop: 'voucher',
      width: 220,
      component: {
        name: 'cl-image',
        props: { fit: 'cover', lazy: true, size: [50, 50] },
      },
    },
    { label: '备注', prop: 'remark' },
    {
      type: 'op',
      label: '操作',
      width: opWidth as any,
      hidden: opIsHidden,
      buttons: Object.keys(opButtons.value) as any,
    },
  ],
})

// cl-crud 配置
const Crud = useCrud({
  service: service.pms.delivery_note,
  async onRefresh(params, { next, render }) {
    // 1 默认调用
    const { count, list, pagination } = await next(params)
    // 2 根据count设置tab
    statusList.value.forEach((item) => {
      item.count = count[item.value] || 0
    })
    render(list, pagination)
  },
})

// 统一刷新方法
function refreshList(extraParams = {}) {
  const params: any = {
    status: status.value,
    ...(!isAdmin.value ? { supplier_id: supplier_id.value } : {}),
    ...extraParams,
  }
  Crud.value?.refresh(params)
}

const Search = useSearch({
  items: [
    {
      label: '关键字',
      prop: 'keyWord',
      props: {
        labelWidth: '120px',
      },
      component: {
        name: 'el-input',
        props: {
          clearable: true,
          style: 'width: 250px',
          placeholder: '请输入po/供应商订单号/送货单号',
          onChange(keyword: string) {
            refreshList({ keyWord: keyword.trim(), page: 1 })
          },
        },
      },
    },
  ],
})
function handleAdd() {
  // 跳转到创建入库单页面
  router.push('/pms/delivery_note/add')
}

function handleStatusChange(tab: number) {
  // 切换tab时，刷新表格数据
  status.value = tab
  checkUserRole().then(() => {
    refreshList()
  })
}
const tableExpandRowKeys = ref<number[]>([])
// watch  url 参数变化时，刷新表格数据
watch(
  () => [router.currentRoute.value.query.tab, router.currentRoute.value.query.expand],
  ([tab, expand]) => {
    checkUserRole().then(() => {
      if (tab !== undefined && tab !== null) {
        status.value = Number.parseInt(tab.toString())
        refreshList()
        router.replace({ query: { ...router.currentRoute.value.query, tab: undefined } })
      }
      else {
        refreshList()
      }
      if (expand !== undefined && expand !== null) {
        tableExpandRowKeys.value = [Number.parseInt(expand.toString())]
        router.replace({ query: { ...router.currentRoute.value.query, expand: undefined } })
      }
    })
  },
  { immediate: true },
)

// 行点击展开
function onRowClick(row: any, column: any) {
  // 获取row的key
  if (column?.type === 'expand' || column?.type === 'op')
    return

  Table.value?.toggleRowExpansion(row)
}

// 1. 定义 loading 状态
const loading = ref({
  delete: {} as Record<number, boolean>,
  confirm: {} as Record<number, boolean>,
  revoke: {} as Record<number, boolean>,
  complete: {} as Record<number, boolean>,
  dialogConfirm: false,
})

// 删除送货单
function handleDeleteOrder(id: number) {
  if (!id)
    return false

  // 弹窗确认
  ElMessageBox.confirm('确认删除送货单吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    loading.value.delete[id] = true
    // 删除入库单
    service.pms.delivery_note
      .delete({ ids: [id] })
      .then(() => {
        // 提示成功消息
        ElMessage.success('送货单删除成功')
        Crud.value?.refresh()
      })
      .catch((err) => {
        // 提示错误消息
        ElMessage.error(err.message)
      })
      .finally(() => {
        loading.value.delete[id] = false
      })
  })
}

function handleEditOrder(id: number) {
  if (!id)
    return false
  router.push(`/pms/delivery_note/add?id=${id}`)
}

// ============ 新增：提交对话框相关 ============
const confirmDialogVisible = ref(false)
const confirmDialogMaterials = ref<any[]>([])
const confirmDialogInputs = ref<any[]>([])

const delivery_note_id = ref(0)
const materialsInput = ref<any[]>([])
function handleConfirm(row: any) {
  if (!row) {
    ElMessage.error('未找到该送货单数据')
    return
  }

  // 重置上传文件状态
  voucher.value = ''

  delivery_note_id.value = row.id
  voucher.value = row.voucher
  confirmDialogMaterials.value = (row.products || []).map((item: any) => ({
    ...item,
    warehouseId: item.warehouseId || '',
    inbound_outbound_key: item.inbound_outbound_key || '',
    // address始终为数组，兼容字符串和数组
    address: Array.isArray(item.address)
      ? item.address
      : (item.address ? item.address.split(',') : []),
    id: item.id || '',
    address_arr: item.contract?.material?.address_name && item.contract.material.address_name !== ''
      ? item.contract.material.address_name.split(',')
      : [],
  }))
  confirmDialogVisible.value = true
}

// 提交送货单
function handleConfirmDialogSubmit() {
  // 验证是否已上传送货单文件
  if (!voucher.value || voucher.value.trim() === '') {
    ElMessage.error('请先上传送货单文件')
    return
  }

  loading.value.dialogConfirm = true
  materialsInput.value = (confirmDialogMaterials.value || []).map((item: any) => {
    if (Array.isArray(item.address)) {
      item.address = item.address.join(',')
    }
    return {
      warehouseId: item.warehouseId || 0,
      inbound_outbound_key: item.inbound_outbound_key || 0,
      address: item.address,
      id: item.id || 0,
    }
  })

  service.pms.delivery_note
    .request({
      url: '/submit',
      method: 'POST',
      data: {
        id: delivery_note_id.value,
        materials: materialsInput.value,
        voucher: voucher.value,
      },
    })
    .then((_res) => {
      ElMessage.success('提交成功')
      status.value = 2 // 更新状态为已确认
      confirmDialogVisible.value = false
      refreshList()
    })
    .catch((e) => {
      ElMessage.error(e.message || '提交送货单失败，请检查物料信息是否完整')
      confirmDialogVisible.value = false
    })
    .finally(() => {
      loading.value.dialogConfirm = false
      loading.value.confirm[delivery_note_id.value] = false
    })
}
function handleConfirmDialogCancel() {
  confirmDialogVisible.value = false
  // 重置提交按钮的加载状态
  loading.value.confirm[delivery_note_id.value] = false
}

// 处理对话框关闭事件（包括点击X按钮或点击外部空白处）
function handleDialogClose() {
  // 重置提交按钮的加载状态
  loading.value.confirm[delivery_note_id.value] = false
}

// 撤销送货单
function handleRevoke(row: any) {
  const delivery_note = {
    id: row.id,
    no: row.no,
    po: row.po,
    order_id: row.orderId,
    total_quantity: row.totalQuantity,
    supplier_id: row.supplier_id,
    supplier_order_no: row.supplier_order_no,
    voucher: row.voucher,
    remark: row.remark,
    status: row.status,
  }
  // 弹窗确认
  ElMessageBox.confirm('撤销后物料入库的相关数据也会删除并且会重置内部订单号，确认撤销送货单吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    loading.value.revoke[row.id] = true
    service.pms.delivery_note
      .request({
        url: '/revoke',
        method: 'POST',
        data: {
          delivery_note,
        },
      })
      .then((_res) => {
        ElMessage.success('撤销成功')
        status.value = 0
        refreshList()
      })
      .catch((_e) => {
        ElMessage.error('撤销送货单失败')
      })
      .finally(() => {
        loading.value.revoke[row.id] = false
      })
  })
}
// 确认采购单
function handleComplete(id: number) {
  // 弹窗确认
  ElMessageBox.confirm('确认完成送货单,并同步入库数据吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    loading.value.complete[id] = true
    service.pms.delivery_note
      .request({
        url: '/complete',
        method: 'POST',
        data: {
          id,
        },
      })
      .then((_res) => {
        ElMessage.success('确认成功')
        status.value = 2
        refreshList()
      })
      .catch((_e) => {
        ElMessage.error('确认失败')
      })
      .finally(() => {
        loading.value.complete[id] = false
      })
  })
}

// 打印送货单
function handlePrint(row: any) {
  const printObj: any = {}
  printObj.inboundTime = row.inboundTime ? Dayjs(row.inboundTime).format('YYYY/MM/DD') : ''
  printObj.date = Dayjs(new Date()).format('YYYY/MM/DD')
  printObj.user = user?.info?.name
  printObj.supplier_name = row.supplier_name
  printObj.supplierName = row.supplier_name // 补退货打印页面使用的字段名
  printObj.po = row.po || '' // 确保PO字段有值
  printObj.orderNo = row.orderNo || '' // 确保订单号有值
  printObj.no = row.no || '' // 确保NO字段有值
  printObj.supplier_order_no = row.supplier_order_no
  printObj.totalQuantity = row.totalQuantity
  printObj.voucher = row.voucher
  printObj.internalOrderNo = row.internalOrderNo || '' // 添加内部订单号

  if (row.products?.length > 0) {
    printObj.list = cloneDeep(row.products)
    printObj.list.forEach((item: any) => {
      console.log('item.warehouseId', item.warehouseId)
      item.warehouse = getDictLabel('warehouse_name', item.warehouseId)
    })
  }

  sessionStorage.setItem('printData', JSON.stringify([printObj]))

  // 根据入库类型选择不同的打印页面
  let printPageUrl = ''
  if (row.inboundType === 9) {
    // 补退货入库使用专门的打印页面
    printPageUrl = `${window.location.origin}/printMaterialInboundReplenish.html`
  }
  else {
    // 采购入库使用原有的打印页面
    printPageUrl = `${window.location.origin}/printDeliveNote.html`
  }

  window.open(printPageUrl, '_blank')
}

// 处理图片上传成功
function handleUploadSuccess(data: any) {
  voucher.value = data.url || data
}
</script>

<template>
  <cl-crud ref="Crud">
    <el-row>
      <!-- 刷新按钮 -->
      <cl-refresh-btn />
      <!-- 创建入库单按钮 -->
      <el-button v-permission="service.pms.delivery_note.permission.add" text bg type="success" @click="handleAdd">
        创建送货单
      </el-button>
      <cl-flex1 />
      <!-- 关键字搜索 -->
      <cl-search ref="Search" />
    </el-row>

    <el-tabs
      v-model="status"
      type="border-card"
      @tab-change="handleStatusChange"
    >
      <el-tab-pane
        v-for="row in statusList"
        :key="row.value"
        :label="`${row.label}(${row.count})`"
        :name="row.value"
      />
      <el-row>
        <!-- 数据表格 -->
        <cl-table
          ref="Table"
          row-key="id"
          :expand-row-keys="tableExpandRowKeys"
          class="table-row-pointer"
          @row-click="onRowClick"
        >
          <!-- 编辑按钮 -->
          <template #slot-btn-edit="{ scope }">
            <el-button
              v-if="checkOpButtonIsAvaliable('slot-btn-edit')"
              text
              bg
              type="primary"
              @click.stop="handleEditOrder(scope.row.id)"
            >
              编辑
            </el-button>
          </template>

          <!-- 删除按钮 -->
          <template #slot-btn-delete="{ scope }">
            <el-button
              v-if="checkOpButtonIsAvaliable('slot-btn-delete')"
              text
              bg
              type="danger"
              :loading="loading.delete[scope.row.id]"
              @click.stop="handleDeleteOrder(scope.row.id)"
            >
              删除
            </el-button>
          </template>
          <!-- 提交按钮 -->
          <template #slot-btn-confirm="{ scope }">
            <el-button
              v-if="checkOpButtonIsAvaliable('slot-btn-confirm')"
              text
              bg
              type="success"
              :loading="loading.confirm[scope.row.id]"
              @click.stop="loading.confirm[scope.row.id] = true;handleConfirm(scope.row)"
            >
              提交
            </el-button>
          </template>
          <!-- 撤销按钮 -->
          <template #slot-btn-revoke="{ scope }">
            <el-button
              v-if="checkOpButtonIsAvaliable('slot-btn-revoke')"
              text
              bg
              type="success"
              :loading="loading.revoke[scope.row.id]"
              @click.stop="handleRevoke(scope.row)"
            >
              撤销
            </el-button>
          </template>
          <!-- 确认 -->
          <template #slot-btn-success="{ scope }">
            <el-button
              v-if="checkOpButtonIsAvaliable('slot-btn-success')"
              text
              bg
              type="warning"
              :loading="loading.complete[scope.row.id]"
              @click.stop="handleComplete(scope.row.id)"
            >
              确认
            </el-button>
          </template>
          <!-- 打印 -->
          <template #slot-btn-print="{ scope }">
            <el-button
              v-if="checkOpButtonIsAvaliable('slot-btn-print')"
              text
              bg
              type="info"
              :loading="loading.complete[scope.row.id]"
              @click.stop="handlePrint(scope.row)"
            >
              打印
            </el-button>
          </template>
          <template #column-products="{ scope }">
            <el-table :data="scope.row.products" style="width: 100%" border>
              <!--              <el-table-column prop="id" label="ID" min-width="60" align="center" /> -->
              <el-table-column
                prop="quantity"
                label="送货数量"
                min-width="80"
                align="center"
              />

              <!-- 采购入库显示的列 -->
              <template v-if="scope.row.inboundType === 1">
                <el-table-column label="采购数量" min-width="120" align="center">
                  <template #default="productScope">
                    <span>{{ productScope.row.contract?.quantity || 0 }}</span>
                  </template>
                </el-table-column>

                <el-table-column label="已收数量" min-width="120" align="center">
                  <template #default="productScope">
                    <span>{{ productScope.row.contract?.receivedQuantity || 0 }}</span>
                  </template>
                </el-table-column>

                <el-table-column label="供应商" width="220" align="center">
                  <template #default="productScope">
                    <span>{{ productScope.row.contract?.supplier?.name || scope.row.supplier_name }}</span>
                  </template>
                </el-table-column>
              </template>

              <!-- 补退货入库显示的列 -->
              <template v-else-if="scope.row.inboundType === 9">
                <el-table-column label="退货数量" min-width="100" align="center">
                  <template #default="productScope">
                    <span>{{ productScope.row.expectedInbound || 0 }}</span>
                  </template>
                </el-table-column>

                <el-table-column label="已补货数量" min-width="100" align="center">
                  <template #default="productScope">
                    <span>{{ productScope.row.receivedQty || 0 }}</span>
                  </template>
                </el-table-column>

                <el-table-column label="剩余可补数量" min-width="100" align="center">
                  <template #default="productScope">
                    <span>{{ (productScope.row.expectedInbound || 0) - (productScope.row.receivedQty || 0) }}</span>
                  </template>
                </el-table-column>

                <el-table-column label="供应商" width="220" align="center">
                  <template #default="productScope">
                    <span>{{ scope.row.supplier_name }}</span>
                  </template>
                </el-table-column>
              </template>

              <el-table-column label="仓位" width="180" align="center">
                <template #default="scope">
                  <span>{{
                    getDictLabel("warehouse_name", scope.row.warehouseId)
                  }}</span>
                </template>
              </el-table-column>

              <el-table-column label="关键字" width="180" align="center">
                <template #default="scope">
                  <span>{{
                    getDictLabel(
                      "inbound_outbound_key",
                      scope.row.inbound_outbound_key,
                    )
                  }}</span>
                </template>
              </el-table-column>
              <el-table-column
                prop="address"
                label="位置"
                align="center"
                min-width="120"
                show-overflow-tooltip
              />
              <el-table-column label="物料名称" min-width="180" align="center">
                <template #default="productScope">
                  <span>{{ productScope.row.contract?.material?.name || productScope.row.name }}</span>
                </template>
              </el-table-column>
              <el-table-column label="物料编码" min-width="130" align="center">
                <template #default="productScope">
                  <span>{{ productScope.row.contract?.material?.code || productScope.row.code }}</span>
                </template>
              </el-table-column>
              <el-table-column label="型号" align="center" width="220">
                <template #default="productScope">
                  <span>{{ productScope.row.contract?.material?.model || productScope.row.model }}</span>
                </template>
              </el-table-column>
              <el-table-column label="尺寸" min-width="180" align="center">
                <template #default="productScope">
                  <span>{{ productScope.row.contract?.material?.size || productScope.row.size }}</span>
                </template>
              </el-table-column>
              <el-table-column label="单位" min-width="80" align="center">
                <template #default="productScope">
                  <span>{{ productScope.row.contract?.material?.unit || productScope.row.unit }}</span>
                </template>
              </el-table-column>

              <el-table-column
                prop="remark"
                label="备注"
                min-width="120"
                align="center"
              />
            </el-table>
          </template>
        </cl-table>
      </el-row>
    </el-tabs>

    <el-row>
      <cl-flex1 />
      <!-- 分页控件 -->
      <cl-pagination />
    </el-row>
    <!-- 新增、编辑 -->
    <cl-upsert ref="Upsert" />
    <!-- 新增：提交对话框 -->
    <el-dialog
      v-model="confirmDialogVisible"
      title="提交前确认物料信息"
      width="60%"
      @close="handleDialogClose"
    >
      <div style="margin-bottom: 20px">
        <div style="margin-bottom: 8px">
          <span style="color: red; margin-right: 5px">*</span>
          <span style="font-weight: 500">请上传送货单：</span>
          <span v-if="!voucher" style="color: #f56c6c; font-size: 12px; margin-left: 8px">
            （必填项，请先上传文件再提交）
          </span>
          <span v-else style="color: #67c23a; font-size: 12px; margin-left: 8px">
            ✓ 文件已上传
          </span>
        </div>
        <cl-upload
          v-model="voucher"
          type="image"
          :limit="1"
          :is-private="false"
          :multiple="false"
          @success="handleUploadSuccess"
        />
      </div>
      <el-table :data="confirmDialogMaterials" border style="margin-bottom: 20px">
        <el-table-column label="物料名称" width="200">
          <template #default="scope">
            <span>{{ scope.row.contract?.material?.name || scope.row.name }}</span>
          </template>
        </el-table-column>
        <el-table-column label="物料编码" width="180">
          <template #default="scope">
            <span>{{ scope.row.contract?.material?.code || scope.row.code }}</span>
          </template>
        </el-table-column>
        <el-table-column label="型号" width="400">
          <template #default="scope">
            <span>{{ scope.row.contract?.material?.model || scope.row.model }}</span>
          </template>
        </el-table-column>
        <el-table-column label="送货数量" prop="quantity" width="120" />
        <el-table-column
          label="仓位"
          align="center"
          width="220"
        >
          <template #default="scope">
            <SelectDict
              v-model="scope.row.warehouseId"
              code="warehouse_name"
              size="small"
              width="150px"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="关键字"
          align="center"
          width="150"
        >
          <template #default="scope">
            <SelectDict
              v-model="scope.row.inbound_outbound_key"
              code="inbound_outbound_key"
              size="small"
              width="100px"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="位置"
          align="left"
          width="220"
          show-overflow-tooltip
        >
          <template #default="scope">
            <div flex="~ items-center">
              <el-select
                v-model="scope.row.address"
                filterable
                multiple
                size="small"
                style="width: 220px"
                placeholder="请选择位置"
              >
                <el-option
                  v-for="(item, index) in scope.row.address_arr"
                  :key="index"
                  :label="item"
                  :value="item"
                />
              </el-select>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <template #footer>
        <el-button @click="handleConfirmDialogCancel">
          取消
        </el-button>
        <el-button type="primary" :loading="loading.dialogConfirm" @click="handleConfirmDialogSubmit">
          确定
        </el-button>
      </template>
    </el-dialog>
  </cl-crud>
</template>

<style lang="scss">
.delivery-note-form {
  .cl-form__items {
    .el-row {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      .full-line {
        grid-column: 1 / -1;
      }
    }

    // 让input-number的提示语靠左显示
    .el-input-number {
      :deep(.el-input__wrapper) {
        padding-left: 11px;
        input {
          text-align: left;
        }
      }
    }
  }
}
</style>
