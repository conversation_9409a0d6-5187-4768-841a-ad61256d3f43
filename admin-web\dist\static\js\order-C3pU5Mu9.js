import{c as Z,b as v,e as k,z as Ce,A as Te,ae as Se,f as E,B as p,q as i,V as K,h as r,w as a,F as G,s as J,i as c,v as d,G as Ve,H as Be,j as s,t as g,Y as C,o as l,T as L,E as y}from"./.pnpm-hVqhwuVC.js";import{i as W,r as T,e as Ee}from"./index-DkYL1aws.js";import{a as De}from"./index-C6cm1h61.js";import{u as Oe}from"./table-ops-CrFIfhgA.js";import{_ as Ye}from"./material-table-columns.vue_vue_type_script_setup_true_lang-BFX8eStE.js";import{n as S,V as $e}from"./index-CBanFtSc.js";import{_ as X}from"./PurchaseOrder.vue_vue_type_script_setup_true_name_ProductOutbound_lang-DgzK2KBX.js";import{_ as Ne}from"./_plugin-vue_export-helper-DlAUqK2U.js";const Re={key:1,height:"800px","w-full":""},Ie=Z({name:"pms-purchase-order"}),Le=Z({...Ie,setup(We){const{service:m}=De(),n=v(0),Y=v(!1),M=v([]),ee=v(10),D=-2,$=v([{label:"草稿",value:0,count:0},{label:"待审核",value:1,count:0},{label:"已提交",value:2,count:0},{label:"入库中",value:3,count:0},{label:"已完成",value:4,count:0},{label:"采购需求明细",value:D}]),A=v({"slot-btn-edit":{width:80,show:k(()=>n.value===0),permission:m.pms.production.purchase.order.permission.add},"slot-btn-confirm":{width:80,show:k(()=>n.value===0),permission:m.pms.production.purchase.order.permission.submit},"slot-btn-delete":{width:80,show:k(()=>n.value===0),permission:m.pms.production.purchase.order.permission.delete},"slot-audit-log":{width:110,show:k(()=>n.value>0&&n.value<=2),permission:m.pms.production.purchase.order.permission.auditLog},"slot-btn-export-summary":{width:110,show:!0,permission:m.pms.production.purchase.order.permission.exportSummary},"slot-btn-revoke":{width:80,show:k(()=>n.value===1),permission:m.pms.production.purchase.order.permission.revoke}}),{getOpWidth:te,checkOpButtonIsAvaliable:x,getOpIsHidden:oe}=Oe(A),q=v(),H=v(!1);Ce(n,()=>{q.value=te(),H.value=oe()},{immediate:!0});const z=v([]),P=W.useTable({columns:[{type:"expand",prop:"summary"},{prop:"orderNo",label:"采购单号",minWidth:110,showOverflowTooltip:!0},{prop:"orderSn",label:"内部订单号"},{prop:"total",label:"采购总数量"},{prop:"createTime",label:"创建时间"},{type:"op",label:"操作",width:q,hidden:H,buttons:Object.keys(A.value)}]}),w=W.useCrud({service:m.pms.production.purchase.order,async onRefresh(o,{next:e,render:u}){const{count:O,list:N,pagination:b}=await e(o);$.value.forEach(V=>{V.count=O[V.value]||0}),u(N,b)}},o=>{o.refresh({status:n,size:ee.value})}),ae=W.useSearch({items:[{label:"下单时间",prop:"dateRange",props:{labelWidth:"80px"},component:{name:"el-date-picker",props:{type:"daterange","value-format":"YYYY-MM-DD",format:"YYYY-MM-DD",rangeSeparator:"至",startPlaceholder:"开始日期",endPlaceholder:"结束日期",clearable:!0,onChange(o){var e;(e=w.value)==null||e.refresh({dateRange:o})}}}},{label:"采购单 / 内部单号",prop:"keyword",props:{labelWidth:"160px"},component:{name:"el-input",props:{placeholder:"输入采购单号/内部订单号搜索",clearable:!1,onChange(o){var e;(e=w.value)==null||e.refresh({keyWord:o.trim(),page:1})}}}}]});function re(){T.push("/pms/production/purchase/order/add")}function F(o){var e;o>=0&&(n.value=o),(e=w.value)==null||e.refresh()}function le(o,e){var u;(e==null?void 0:e.type)==="expand"||(e==null?void 0:e.type)==="op"||(u=P.value)==null||u.toggleRowExpansion(o)}function Q(o){if(o.value===D)return o.label;const e=o.label,u=o.count;return`${e}(${u})`}function ne(o){T.push(`/pms/production/purchase/order/add?id=${o}&step=1&is_edit=1`)}function se(o){if(!o)return!1;L.confirm("确认提交订单审核吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{m.pms.production.purchase.order.submit({id:o}).then(e=>{var u;y.success("订单提交成功"),n.value=(e==null?void 0:e.status)||1,(u=w.value)==null||u.refresh()}).catch(e=>{y.error(e.message)})}).catch(()=>{})}function ue(o){if(!o)return!1;L.confirm("确认删除订单吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{m.pms.production.purchase.order.delete({ids:[o]}).then(()=>{var e;y.success("订单删除成功"),(e=w.value)==null||e.refresh()}).catch(e=>{y.error(e.message)})}).catch(()=>{})}function ie(o){const e=o.id;if(!e)return!1;m.pms.production.purchase.order.auditLog({id:e}).then(u=>{M.value=u,Y.value=!0}).catch(u=>{y.error(u.message||"获取审核记录失败")})}function ce(o){const{row:e}=o;return e.hasWarning?"c-red":""}function pe(o){const e=o.id;if(!e)return!1;m.pms.production.purchase.order.request({url:"/exportSummary",method:"get",params:{id:e},responseType:"blob"}).then(u=>{Ee(u)&&y.success("导出成功")}).catch(()=>{y.error("导出失败")})}function de(o){if(!o)return!1;L.confirm("确认撤销订单吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{m.pms.production.purchase.order.revoke({id:o}).then(()=>{var e;y.success("订单撤销成功"),n.value=0,(e=w.value)==null||e.refresh()}).catch(e=>{y.error(e.message)})}).catch(()=>{})}Te(()=>{const o=T.currentRoute.value.query.expand;o&&(z.value=[Number.parseInt(o.toString())],T.replace({query:{expand:void 0}}));const e=T.currentRoute.value.query.status;e&&(n.value=Number.parseInt(e.toString()),T.replace({query:{status:void 0}}))});const{height:U}=Se(),me=k(()=>U.value-240),he=k(()=>U.value-360);return(o,e)=>{const u=c("el-tab-pane"),O=c("el-tabs"),N=c("cl-refresh-btn"),b=c("el-button"),V=c("cl-flex1"),fe=c("cl-search"),R=c("el-row"),_e=c("cl-svg"),_=c("el-table-column"),j=c("el-table"),be=c("cl-table"),ve=c("cl-pagination"),B=c("el-text"),ye=c("cl-date-text"),ge=c("el-dialog"),we=c("cl-crud"),ke=Be("permission");return l(),E("div",null,[n.value===D?(l(),E("div",{key:0,"w-full":"",style:K(`height:${me.value}px`)},[r(O,{modelValue:n.value,"onUpdate:modelValue":e[0]||(e[0]=t=>n.value=t),type:"border-card",onTabChange:F},{default:a(()=>[(l(!0),E(G,null,J($.value,t=>(l(),i(u,{key:t.value,label:Q(t),name:t.value,class:"hidden_el-tab-pane"},null,8,["label","name"]))),128))]),_:1},8,["modelValue"]),r(X,{"show-price":!1,style:{height:"100%"},api:d(m).pms.production.purchase.order},null,8,["api"])],4)):p("",!0),n.value!==D?(l(),i(we,{key:1,ref_key:"Crud",ref:w},{default:a(()=>[r(R,null,{default:a(()=>[r(N),Ve((l(),i(b,{type:"primary",onClick:re},{default:a(()=>[s(" 新建采购需求 ")]),_:1})),[[ke,d(m).pms.production.purchase.order.permission.add]]),r(V),r(fe,{ref_key:"Search",ref:ae},null,512)]),_:1}),r(O,{modelValue:n.value,"onUpdate:modelValue":e[1]||(e[1]=t=>n.value=t),type:"border-card",onTabChange:F},{default:a(()=>[(l(!0),E(G,null,J($.value,t=>(l(),i(u,{key:t.value,label:Q(t),name:t.value},null,8,["label","name"]))),128)),r(R,null,{default:a(()=>[n.value!==5?(l(),i(be,{key:0,ref_key:"Table",ref:P,"row-key":"id","expand-row-keys":z.value,class:"table-row-pointer","auto-height":!1,style:K(`height:${he.value}px`),"row-class-name":ce,onRowClick:le},{"slot-btn-confirm":a(({scope:t})=>[d(x)("slot-btn-confirm")?(l(),i(b,{key:0,text:"",bg:"",type:"success",onClick:C(f=>se(t.row.id),["stop"])},{default:a(()=>[s(" 提交 ")]),_:2},1032,["onClick"])):p("",!0)]),"slot-btn-edit":a(({scope:t})=>[d(x)("slot-btn-edit")?(l(),i(b,{key:0,text:"",bg:"",type:"primary",onClick:C(f=>ne(t.row.id),["stop"])},{default:a(()=>[s(" 编辑 ")]),_:2},1032,["onClick"])):p("",!0)]),"slot-btn-delete":a(({scope:t})=>[d(x)("slot-btn-delete")?(l(),i(b,{key:0,bg:"",text:"",type:"danger",onClick:C(f=>ue(t.row.id),["stop"])},{default:a(()=>[s(" 删除 ")]),_:2},1032,["onClick"])):p("",!0)]),"slot-audit-log":a(({scope:t})=>[d(x)("slot-audit-log")?(l(),i(b,{key:0,text:"",bg:"",type:"warning",onClick:C(f=>ie(t.row),["stop"])},{default:a(()=>[s(" 审核记录 ")]),_:2},1032,["onClick"])):p("",!0)]),"slot-btn-export-summary":a(({scope:t})=>[d(x)("slot-btn-export-summary")?(l(),i(b,{key:0,text:"",bg:"",type:"primary",onClick:C(f=>pe(t.row),["stop"])},{default:a(()=>[s(" 导出汇总 ")]),_:2},1032,["onClick"])):p("",!0)]),"slot-btn-revoke":a(({scope:t})=>[d($e)(t.row.createTime)&&d(x)("slot-btn-revoke")?(l(),i(b,{key:0,text:"",bg:"",type:"danger",onClick:C(f=>de(t.row.id),["stop"])},{default:a(()=>[s(" 撤销 ")]),_:2},1032,["onClick"])):p("",!0)]),"column-orderNo":a(({scope:t})=>[t.row.hasWarning?(l(),i(_e,{key:0,name:"carbonWarningAltFilled"})):p("",!0),s(" "+g(t.row.orderNo||"-"),1)]),"column-orderSn":a(({scope:t})=>[s(g(t.row.orderSn||"-"),1)]),"column-total":a(({scope:t})=>{var f,h;return[s(g(d(S)(((h=(f=t.row)==null?void 0:f.summary)==null?void 0:h.reduce((I,xe)=>I+xe.total,0))||0)),1)]}),"column-summary":a(({scope:t})=>{var f;return[r(j,{data:(f=t.row.summary)==null?void 0:f.sort((h,I)=>I.total-h.total),stripe:"",border:""},{default:a(()=>[r(Ye),r(_,{prop:"purchaseTotal",label:"需求数量",align:"center",width:"120"},{default:a(({row:h})=>[s(g(d(S)(h.purchaseTotal)),1)]),_:1}),r(_,{prop:"prepareQuantity",label:"生产备料",align:"center",width:"120"},{default:a(({row:h})=>[s(g(d(S)(h.prepareQuantity)),1)]),_:1}),r(_,{prop:"deductInventory",label:"库存抵扣",align:"center",width:"120"},{default:a(({row:h})=>[s(g(d(S)(h.deductInventory)),1)]),_:1}),r(_,{prop:"total",label:"实际下单",align:"center",width:"150"},{default:a(({row:h})=>[s(g(d(S)(h.total)),1)]),_:1}),r(_,{prop:"inboundQuantity",label:"入库数量",align:"center",width:"150"},{default:a(({row:h})=>[s(g(d(S)(h.inboundQuantity)),1)]),_:1}),r(_,{prop:"unit",label:"单位",align:"center",width:"100"})]),_:2},1032,["data"])]}),_:1},8,["expand-row-keys","style"])):(l(),E("div",Re,[r(X,{"show-price":!1})]))]),_:1}),n.value!==5?(l(),i(R,{key:0},{default:a(()=>[r(V),r(ve)]),_:1})):p("",!0)]),_:1},8,["modelValue"]),r(ge,{modelValue:Y.value,"onUpdate:modelValue":e[2]||(e[2]=t=>Y.value=t),title:"审核日志",width:"70%","close-on-click-modal":!1},{default:a(()=>[r(j,{data:M.value,stripe:"",border:""},{default:a(()=>[r(_,{prop:"operatorName",label:"操作人",align:"center",width:"100"}),r(_,{prop:"operatorNote",label:"审核细节","show-overflow-tooltip":""}),r(_,{prop:"type",label:"审核类型",align:"center",width:"100"},{default:a(({row:t})=>[t.type===0?(l(),i(B,{key:0,type:"primary"},{default:a(()=>[s(" 提交审核 ")]),_:1})):p("",!0),t.type===1?(l(),i(B,{key:1,type:"success"},{default:a(()=>[s(" 审核通过 ")]),_:1})):p("",!0),t.type===2?(l(),i(B,{key:2,type:"danger"},{default:a(()=>[s(" 驳回审核 ")]),_:1})):p("",!0),t.type===3?(l(),i(B,{key:3,type:"danger"},{default:a(()=>[s(" 作废审核 ")]),_:1})):p("",!0),t.type===4?(l(),i(B,{key:4,type:"danger"},{default:a(()=>[s(" 撤销审核 ")]),_:1})):p("",!0)]),_:1}),r(_,{prop:"createTime",label:"创建时间",align:"center",width:"180"},{default:a(({row:t})=>[r(ye,{"model-value":t.createTime,format:"YYYY-MM-DD HH:mm:ss"},null,8,["model-value"])]),_:1})]),_:1},8,["data"])]),_:1},8,["modelValue"])]),_:1},512)):p("",!0)])}}}),Ue=Ne(Le,[["__scopeId","data-v-464deccc"]]);export{Ue as default};
