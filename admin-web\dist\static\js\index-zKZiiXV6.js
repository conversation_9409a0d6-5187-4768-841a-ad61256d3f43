import{c as ae,b as v,e as R,z as ee,q as g,w as a,h as o,G as Ee,i as m,H as Re,v as C,j as T,f as N,s as te,F as z,B as U,y as u,t as _,Y as S,E as y,o as f,T as P,K as le,U as $e}from"./.pnpm-hVqhwuVC.js";import{g as ze,c as Me,i as M,r as V}from"./index-BtOcqcNl.js";import{_ as ne}from"./select-dict.vue_vue_type_script_setup_true_name_select-dict_lang-C2uy3Nel.js";import{g as Q}from"./index-Dw7jsygE.js";import{u as Ye}from"./table-ops-mcGHjph4.js";/* empty css              */import{a as Ae}from"./index-D95m1iJL.js";const Pe={style:{"margin-bottom":"20px"}},Qe={style:{"margin-bottom":"8px"}},We=u("span",{style:{color:"red","margin-right":"5px"}},"*",-1),je=u("span",{style:{"font-weight":"500"}},"请上传送货单：",-1),Le={key:0,style:{color:"#f56c6c","font-size":"12px","margin-left":"8px"}},He={key:1,style:{color:"#67c23a","font-size":"12px","margin-left":"8px"}},Fe={flex:"~ items-center"},Ge=ae({name:"pms-bill-payment"}),at=ae({...Ge,setup(Ke){const{dict:oe}=ze();oe.get("inbound_outbound_key");const{service:h}=Ae(),W=v([{label:"草稿",value:0,count:0},{label:"已提交",value:1,count:0},{label:"已确认",value:2,count:0}]),b=v(0),{user:w}=Me(),$=v(!1),j=v(0),k=v("");async function L(){var t;w.info=w.info||{};const e=(t=w==null?void 0:w.info)==null?void 0:t.id;try{const s=await h.pms.supplier_account.request({url:"/getUserRole",method:"POST",data:{user_id:e}}),d=[];s&&s.forEach(x=>{d.push(x.name)}),d.includes("供应商")?(await re(),$.value=!1):$.value=!0}catch(s){console.error("查询角色信息失败:",s),y.error("查询角色信息失败！"),$.value=!0}}async function re(){var e;try{const t=await h.pms.supplier_account.request({url:"/getUserBindSupplier",method:"GET",params:{user_id:(e=w==null?void 0:w.info)==null?void 0:e.id}});j.value=t.supplier_id}catch{y.error("获取供应商信息失败")}}const H=v({"slot-btn-confirm":{width:80,permission:h.pms.delivery_note.permission.submit,show:R(()=>b.value===0)},"slot-btn-edit":{width:80,permission:h.pms.delivery_note.permission.update,show:R(()=>b.value===0)},"slot-btn-delete":{width:80,permission:h.pms.delivery_note.permission.delete,show:R(()=>b.value===0)},"slot-btn-revoke":{width:80,permission:h.pms.delivery_note.permission.revoke,show:R(()=>b.value!==0)},"slot-btn-success":{width:80,permission:h.pms.delivery_note.permission.revoke,show:R(()=>b.value===1)},"slot-btn-print":{width:80,permission:h.pms.delivery_note.permission.page,show:!0}}),{getOpWidth:ie,checkOpButtonIsAvaliable:O,getOpIsHidden:se}=Ye(H),F=v(),G=v(!1);ee(b,()=>{F.value=ie(),G.value=se()},{immediate:!0});const ue=M.useUpsert({props:{class:"delivery-note-form",labelWidth:"120px"},items:[]}),K=M.useTable({columns:[{label:"#",prop:"products",type:"expand",width:50},{label:"创建时间",prop:"createTime",width:220},{label:"送货单号",prop:"no",width:220},{label:"PO",prop:"po",width:220},{label:"订单号",prop:"orderNo",width:220},{label:"送货总数",prop:"totalQuantity",width:220},{label:"供应商名称",prop:"supplier_name"},{label:"内部单号",prop:"internalOrderNo"},{label:"入库类型",prop:"inboundType",formatter:e=>e.inboundType===1?"采购入库":e.inboundType===9?"补退货":"未知"},{label:"送货单",prop:"voucher",width:220,component:{name:"cl-image",props:{fit:"cover",lazy:!0,size:[50,50]}}},{label:"备注",prop:"remark"},{type:"op",label:"操作",width:F,hidden:G,buttons:Object.keys(H.value)}]}),Y=M.useCrud({service:h.pms.delivery_note,async onRefresh(e,{next:t,render:s}){const{count:d,list:x,pagination:D}=await t(e);W.value.forEach(B=>{B.count=d[B.value]||0}),s(x,D)}});function I(e={}){var s;const t={status:b.value,...$.value?{}:{supplier_id:j.value},...e};(s=Y.value)==null||s.refresh(t)}const de=M.useSearch({items:[{label:"关键字",prop:"keyWord",props:{labelWidth:"120px"},component:{name:"el-input",props:{clearable:!0,style:"width: 250px",placeholder:"请输入po/供应商订单号/送货单号",onChange(e){I({keyWord:e.trim(),page:1})}}}}]});function ce(){V.push("/pms/delivery_note/add")}function pe(e){b.value=e,L().then(()=>{I()})}const J=v([]);ee(()=>[V.currentRoute.value.query.tab,V.currentRoute.value.query.expand],([e,t])=>{L().then(()=>{e!=null?(b.value=Number.parseInt(e.toString()),I(),V.replace({query:{...V.currentRoute.value.query,tab:void 0}})):I(),t!=null&&(J.value=[Number.parseInt(t.toString())],V.replace({query:{...V.currentRoute.value.query,expand:void 0}}))})},{immediate:!0});function me(e,t){var s;(t==null?void 0:t.type)==="expand"||(t==null?void 0:t.type)==="op"||(s=K.value)==null||s.toggleRowExpansion(e)}const c=v({delete:{},confirm:{},revoke:{},complete:{},dialogConfirm:!1});function _e(e){if(!e)return!1;P.confirm("确认删除送货单吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{c.value.delete[e]=!0,h.pms.delivery_note.delete({ids:[e]}).then(()=>{var t;y.success("送货单删除成功"),(t=Y.value)==null||t.refresh()}).catch(t=>{y.error(t.message)}).finally(()=>{c.value.delete[e]=!1})})}function fe(e){if(!e)return!1;V.push(`/pms/delivery_note/add?id=${e}`)}const q=v(!1),A=v([]);v([]);const E=v(0),X=v([]);function ve(e){if(!e){y.error("未找到该送货单数据");return}k.value="",E.value=e.id,k.value=e.voucher,A.value=(e.products||[]).map(t=>{var s,d;return{...t,warehouseId:t.warehouseId||"",inbound_outbound_key:t.inbound_outbound_key||"",address:Array.isArray(t.address)?t.address:t.address?t.address.split(","):[],id:t.id||"",address_arr:(d=(s=t.contract)==null?void 0:s.material)!=null&&d.address_name&&t.contract.material.address_name!==""?t.contract.material.address_name.split(","):[]}}),q.value=!0}function he(){if(!k.value||k.value.trim()===""){y.error("请先上传送货单文件");return}c.value.dialogConfirm=!0,X.value=(A.value||[]).map(e=>(Array.isArray(e.address)&&(e.address=e.address.join(",")),{warehouseId:e.warehouseId||0,inbound_outbound_key:e.inbound_outbound_key||0,address:e.address,id:e.id||0})),h.pms.delivery_note.request({url:"/submit",method:"POST",data:{id:E.value,materials:X.value,voucher:k.value}}).then(e=>{y.success("提交成功"),b.value=2,q.value=!1,I()}).catch(e=>{y.error(e.message||"提交送货单失败，请检查物料信息是否完整"),q.value=!1}).finally(()=>{c.value.dialogConfirm=!1,c.value.confirm[E.value]=!1})}function be(){q.value=!1,c.value.confirm[E.value]=!1}function ye(){c.value.confirm[E.value]=!1}function we(e){const t={id:e.id,no:e.no,po:e.po,order_id:e.orderId,total_quantity:e.totalQuantity,supplier_id:e.supplier_id,supplier_order_no:e.supplier_order_no,voucher:e.voucher,remark:e.remark,status:e.status};P.confirm("撤销后物料入库的相关数据也会删除并且会重置内部订单号，确认撤销送货单吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{c.value.revoke[e.id]=!0,h.pms.delivery_note.request({url:"/revoke",method:"POST",data:{delivery_note:t}}).then(s=>{y.success("撤销成功"),b.value=0,I()}).catch(s=>{y.error("撤销送货单失败")}).finally(()=>{c.value.revoke[e.id]=!1})})}function ge(e){P.confirm("确认完成送货单,并同步入库数据吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{c.value.complete[e]=!0,h.pms.delivery_note.request({url:"/complete",method:"POST",data:{id:e}}).then(t=>{y.success("确认成功"),b.value=2,I()}).catch(t=>{y.error("确认失败")}).finally(()=>{c.value.complete[e]=!1})})}function ke(e){var d,x;const t={};t.inboundTime=e.inboundTime?le(e.inboundTime).format("YYYY/MM/DD"):"",t.date=le(new Date).format("YYYY/MM/DD"),t.user=(d=w==null?void 0:w.info)==null?void 0:d.name,t.supplier_name=e.supplier_name,t.supplierName=e.supplier_name,t.po=e.po||"",t.orderNo=e.orderNo||"",t.no=e.no||"",t.supplier_order_no=e.supplier_order_no,t.totalQuantity=e.totalQuantity,t.voucher=e.voucher,t.internalOrderNo=e.internalOrderNo||"",((x=e.products)==null?void 0:x.length)>0&&(t.list=$e(e.products),t.list.forEach(D=>{console.log("item.warehouseId",D.warehouseId),D.warehouse=Q("warehouse_name",D.warehouseId)})),sessionStorage.setItem("printData",JSON.stringify([t]));let s="";e.inboundType===9?s=`${window.location.origin}/printMaterialInboundReplenish.html`:s=`${window.location.origin}/printDeliveNote.html`,window.open(s,"_blank")}function xe(e){k.value=e.url||e}return(e,t)=>{const s=m("cl-refresh-btn"),d=m("el-button"),x=m("cl-flex1"),D=m("cl-search"),B=m("el-row"),Ce=m("el-tab-pane"),i=m("el-table-column"),Z=m("el-table"),Te=m("cl-table"),Ve=m("el-tabs"),Ie=m("cl-pagination"),De=m("cl-upsert"),Ue=m("cl-upload"),Oe=m("el-option"),qe=m("el-select"),Be=m("el-dialog"),Ne=m("cl-crud"),Se=Re("permission");return f(),g(Ne,{ref_key:"Crud",ref:Y},{default:a(()=>[o(B,null,{default:a(()=>[o(s),Ee((f(),g(d,{text:"",bg:"",type:"success",onClick:ce},{default:a(()=>[T(" 创建送货单 ")]),_:1})),[[Se,C(h).pms.delivery_note.permission.add]]),o(x),o(D,{ref_key:"Search",ref:de},null,512)]),_:1}),o(Ve,{modelValue:b.value,"onUpdate:modelValue":t[0]||(t[0]=n=>b.value=n),type:"border-card",onTabChange:pe},{default:a(()=>[(f(!0),N(z,null,te(W.value,n=>(f(),g(Ce,{key:n.value,label:`${n.label}(${n.count})`,name:n.value},null,8,["label","name"]))),128)),o(B,null,{default:a(()=>[o(Te,{ref_key:"Table",ref:K,"row-key":"id","expand-row-keys":J.value,class:"table-row-pointer",onRowClick:me},{"slot-btn-edit":a(({scope:n})=>[C(O)("slot-btn-edit")?(f(),g(d,{key:0,text:"",bg:"",type:"primary",onClick:S(l=>fe(n.row.id),["stop"])},{default:a(()=>[T(" 编辑 ")]),_:2},1032,["onClick"])):U("",!0)]),"slot-btn-delete":a(({scope:n})=>[C(O)("slot-btn-delete")?(f(),g(d,{key:0,text:"",bg:"",type:"danger",loading:c.value.delete[n.row.id],onClick:S(l=>_e(n.row.id),["stop"])},{default:a(()=>[T(" 删除 ")]),_:2},1032,["loading","onClick"])):U("",!0)]),"slot-btn-confirm":a(({scope:n})=>[C(O)("slot-btn-confirm")?(f(),g(d,{key:0,text:"",bg:"",type:"success",loading:c.value.confirm[n.row.id],onClick:S(l=>{c.value.confirm[n.row.id]=!0,ve(n.row)},["stop"])},{default:a(()=>[T(" 提交 ")]),_:2},1032,["loading","onClick"])):U("",!0)]),"slot-btn-revoke":a(({scope:n})=>[C(O)("slot-btn-revoke")?(f(),g(d,{key:0,text:"",bg:"",type:"success",loading:c.value.revoke[n.row.id],onClick:S(l=>we(n.row),["stop"])},{default:a(()=>[T(" 撤销 ")]),_:2},1032,["loading","onClick"])):U("",!0)]),"slot-btn-success":a(({scope:n})=>[C(O)("slot-btn-success")?(f(),g(d,{key:0,text:"",bg:"",type:"warning",loading:c.value.complete[n.row.id],onClick:S(l=>ge(n.row.id),["stop"])},{default:a(()=>[T(" 确认 ")]),_:2},1032,["loading","onClick"])):U("",!0)]),"slot-btn-print":a(({scope:n})=>[C(O)("slot-btn-print")?(f(),g(d,{key:0,text:"",bg:"",type:"info",loading:c.value.complete[n.row.id],onClick:S(l=>ke(n.row),["stop"])},{default:a(()=>[T(" 打印 ")]),_:2},1032,["loading","onClick"])):U("",!0)]),"column-products":a(({scope:n})=>[o(Z,{data:n.row.products,style:{width:"100%"},border:""},{default:a(()=>[o(i,{prop:"quantity",label:"送货数量","min-width":"80",align:"center"}),n.row.inboundType===1?(f(),N(z,{key:0},[o(i,{label:"采购数量","min-width":"120",align:"center"},{default:a(l=>{var r;return[u("span",null,_(((r=l.row.contract)==null?void 0:r.quantity)||0),1)]}),_:1}),o(i,{label:"已收数量","min-width":"120",align:"center"},{default:a(l=>{var r;return[u("span",null,_(((r=l.row.contract)==null?void 0:r.receivedQuantity)||0),1)]}),_:1}),o(i,{label:"供应商",width:"220",align:"center"},{default:a(l=>{var r,p;return[u("span",null,_(((p=(r=l.row.contract)==null?void 0:r.supplier)==null?void 0:p.name)||n.row.supplier_name),1)]}),_:2},1024)],64)):n.row.inboundType===9?(f(),N(z,{key:1},[o(i,{label:"退货数量","min-width":"100",align:"center"},{default:a(l=>[u("span",null,_(l.row.expectedInbound||0),1)]),_:1}),o(i,{label:"已补货数量","min-width":"100",align:"center"},{default:a(l=>[u("span",null,_(l.row.receivedQty||0),1)]),_:1}),o(i,{label:"剩余可补数量","min-width":"100",align:"center"},{default:a(l=>[u("span",null,_((l.row.expectedInbound||0)-(l.row.receivedQty||0)),1)]),_:1}),o(i,{label:"供应商",width:"220",align:"center"},{default:a(l=>[u("span",null,_(n.row.supplier_name),1)]),_:2},1024)],64)):U("",!0),o(i,{label:"仓位",width:"180",align:"center"},{default:a(l=>[u("span",null,_(C(Q)("warehouse_name",l.row.warehouseId)),1)]),_:2},1024),o(i,{label:"关键字",width:"180",align:"center"},{default:a(l=>[u("span",null,_(C(Q)("inbound_outbound_key",l.row.inbound_outbound_key)),1)]),_:2},1024),o(i,{prop:"address",label:"位置",align:"center","min-width":"120","show-overflow-tooltip":""}),o(i,{label:"物料名称","min-width":"180",align:"center"},{default:a(l=>{var r,p;return[u("span",null,_(((p=(r=l.row.contract)==null?void 0:r.material)==null?void 0:p.name)||l.row.name),1)]}),_:1}),o(i,{label:"物料编码","min-width":"130",align:"center"},{default:a(l=>{var r,p;return[u("span",null,_(((p=(r=l.row.contract)==null?void 0:r.material)==null?void 0:p.code)||l.row.code),1)]}),_:1}),o(i,{label:"型号",align:"center",width:"220"},{default:a(l=>{var r,p;return[u("span",null,_(((p=(r=l.row.contract)==null?void 0:r.material)==null?void 0:p.model)||l.row.model),1)]}),_:1}),o(i,{label:"尺寸","min-width":"180",align:"center"},{default:a(l=>{var r,p;return[u("span",null,_(((p=(r=l.row.contract)==null?void 0:r.material)==null?void 0:p.size)||l.row.size),1)]}),_:1}),o(i,{label:"单位","min-width":"80",align:"center"},{default:a(l=>{var r,p;return[u("span",null,_(((p=(r=l.row.contract)==null?void 0:r.material)==null?void 0:p.unit)||l.row.unit),1)]}),_:1}),o(i,{prop:"remark",label:"备注","min-width":"120",align:"center"})]),_:2},1032,["data"])]),_:1},8,["expand-row-keys"])]),_:1})]),_:1},8,["modelValue"]),o(B,null,{default:a(()=>[o(x),o(Ie)]),_:1}),o(De,{ref_key:"Upsert",ref:ue},null,512),o(Be,{modelValue:q.value,"onUpdate:modelValue":t[2]||(t[2]=n=>q.value=n),title:"提交前确认物料信息",width:"60%",onClose:ye},{footer:a(()=>[o(d,{onClick:be},{default:a(()=>[T(" 取消 ")]),_:1}),o(d,{type:"primary",loading:c.value.dialogConfirm,onClick:he},{default:a(()=>[T(" 确定 ")]),_:1},8,["loading"])]),default:a(()=>[u("div",Pe,[u("div",Qe,[We,je,k.value?(f(),N("span",He," ✓ 文件已上传 ")):(f(),N("span",Le," （必填项，请先上传文件再提交） "))]),o(Ue,{modelValue:k.value,"onUpdate:modelValue":t[1]||(t[1]=n=>k.value=n),type:"image",limit:1,"is-private":!1,multiple:!1,onSuccess:xe},null,8,["modelValue"])]),o(Z,{data:A.value,border:"",style:{"margin-bottom":"20px"}},{default:a(()=>[o(i,{label:"物料名称",width:"200"},{default:a(n=>{var l,r;return[u("span",null,_(((r=(l=n.row.contract)==null?void 0:l.material)==null?void 0:r.name)||n.row.name),1)]}),_:1}),o(i,{label:"物料编码",width:"180"},{default:a(n=>{var l,r;return[u("span",null,_(((r=(l=n.row.contract)==null?void 0:l.material)==null?void 0:r.code)||n.row.code),1)]}),_:1}),o(i,{label:"型号",width:"400"},{default:a(n=>{var l,r;return[u("span",null,_(((r=(l=n.row.contract)==null?void 0:l.material)==null?void 0:r.model)||n.row.model),1)]}),_:1}),o(i,{label:"送货数量",prop:"quantity",width:"120"}),o(i,{label:"仓位",align:"center",width:"220"},{default:a(n=>[o(ne,{modelValue:n.row.warehouseId,"onUpdate:modelValue":l=>n.row.warehouseId=l,code:"warehouse_name",size:"small",width:"150px"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),o(i,{label:"关键字",align:"center",width:"150"},{default:a(n=>[o(ne,{modelValue:n.row.inbound_outbound_key,"onUpdate:modelValue":l=>n.row.inbound_outbound_key=l,code:"inbound_outbound_key",size:"small",width:"100px"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),o(i,{label:"位置",align:"left",width:"220","show-overflow-tooltip":""},{default:a(n=>[u("div",Fe,[o(qe,{modelValue:n.row.address,"onUpdate:modelValue":l=>n.row.address=l,filterable:"",multiple:"",size:"small",style:{width:"220px"},placeholder:"请选择位置"},{default:a(()=>[(f(!0),N(z,null,te(n.row.address_arr,(l,r)=>(f(),g(Oe,{key:r,label:l,value:l},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])])]),_:1})]),_:1},8,["data"])]),_:1},8,["modelValue"])]),_:1},512)}}});export{at as default};
