import{i as V,h as C}from"./index-DkYL1aws.js";import{a as b}from"./index-C6cm1h61.js";import{c as d,b as a,z as w,i as l,f as N,o as g,h as c,y as B,w as T}from"./.pnpm-hVqhwuVC.js";import{_ as E}from"./_plugin-vue_export-helper-DlAUqK2U.js";const U={class:"cl-menu-check"},z={class:"cl-menu-check__scroller"},A=d({name:"cl-menu-check"}),I=d({...A,props:{modelValue:{type:Array,default:()=>[]}},emits:["update:modelValue"],setup(u,{emit:i}){const m=u,p=i,{service:_}=b(),o=a(),r=a(),n=a("");async function f(){return _.base.sys.menu.list().then(e=>{r.value=C(e)})}function h(e,t){return e?t.name.includes(e):!0}function k(e,{checkedKeys:t,halfCheckedKeys:s}){p("update:modelValue",[...t,...s])}return w(n,e=>{o.value.filter(e)}),V.useUpsert({async onOpened(){await f(),o.value.setCheckedKeys((m.modelValue||[]).filter(e=>{var t;return(t=o.value.getNode(e))==null?void 0:t.isLeaf}))}}),(e,t)=>{const s=l("el-input"),v=l("el-tree"),y=l("el-scrollbar");return g(),N("div",U,[c(s,{modelValue:n.value,"onUpdate:modelValue":t[0]||(t[0]=x=>n.value=x),placeholder:"输入关键字进行过滤"},null,8,["modelValue"]),B("div",z,[c(y,{"max-height":"700px"},{default:T(()=>[c(v,{ref_key:"Tree",ref:o,"node-key":"id","show-checkbox":"",data:r.value,props:{label:"name",children:"children"},"filter-node-method":h,onCheck:k},null,8,["data"])]),_:1})])])}}}),q=E(I,[["__scopeId","data-v-2fd0d5ef"]]);export{q as default};
