import{g as Me,i as G,s as h,e as he}from"./index-DkYL1aws.js";import{c as ve,b as d,aa as S,A as Fe,a0 as be,S as Oe,E as W,q as P,w as p,h as n,y as Ie,G as X,i as c,H as Se,v as Q,j as Z,f as ee,s as te,F as oe,M as Ue,N as Re,o as w}from"./.pnpm-hVqhwuVC.js";import{u as Le}from"./table-ops-CrFIfhgA.js";const Te=ve({name:"undefined"}),je=ve({...Te,setup($e){var _e,fe;const U=d([]),R=d([]),ae=d([]),L=d([]),{dict:le}=Me(),re=d([]),D=d(!0),T=le.get("color"),Y=le.get("product_floor");async function ye(){try{const e=await h.pms.product.request({url:"/getAllProduct",method:"GET"});re.value=e==null?void 0:e.map(t=>({group_id:t.groupId,value:t.id,sku:t.sku,label:`${t.sku} ${t.name}`}))}catch(e){console.error(e)}}ye();const b=G.useCrud({service:h.pms.DailyProductionReport}),x=d([S().startOf("month").format("YYYY-MM-DD"),S().endOf("month").format("YYYY-MM-DD")]),C=d(""),z=d(""),K=d(0),$=G.useSearch({items:[{label:"作业人员",prop:"people_name",props:{labelWidth:"80px"},component:{name:"el-input",props:{style:"width: 120px",placeholder:"请输入作业人员",clearable:!0,onChange(e){var t;D.value||(t=b.value)==null||t.refresh({people_name:e.trim(),page:1})}}}},{label:"SKU",prop:"keyWord",props:{labelWidth:"80px"},component:{name:"el-input",props:{clearable:!0,onChange(e){var t;D.value||(C.value=e.trim(),(t=b.value)==null||t.refresh({keyWord:e.trim(),dateRange:x.value,page:1}))}}}},{label:"生产时间",prop:"dateRange",props:{labelWidth:"100px"},component:{name:"el-date-picker",props:{type:"daterange","value-format":"YYYY-MM-DD",format:"YYYY-MM-DD",rangeSeparator:"至",startPlaceholder:"开始日期",endPlaceholder:"结束日期",clearable:!0,onChange(e){var t;D.value||(x.value=e,(t=b.value)==null||t.refresh({keyWord:C.value,dateRange:e,page:1}))}}}},{label:"生产车间",prop:"workshop_id",props:{labelWidth:"80px"},component:{name:"el-select",props:{style:"width: 160px",clearable:!0,filterable:!0,onChange(e){var t;(t=b.value)==null||t.refresh({workshop_id:e,page:1})}},options:Y}},{label:"订单号",prop:"orderId",props:{labelWidth:"80px"},component:{name:"el-select",props:{style:"width: 200px",clearable:!0,filterable:!0,onChange(e){var t;D.value||(z.value=e,(t=b.value)==null||t.refresh({keyWord:C.value,dateRange:x.value,orderId:e,page:1}))}},options:ae}},{prop:"abnormal",label:"是否只显示异常",props:{labelWidth:"120px"},component:{name:"el-switch",props:{clearable:!0,activeValue:1,inactiveValue:0,onChange(e){var t;D.value||(K.value=e,(t=b.value)==null||t.refresh({abnormal:e}))}}}}]});Fe(()=>{We()}),be(()=>{var e;D.value=!0,(e=$.value)==null||e.setForm("dateRange",x.value),be(()=>{D.value=!1})}),Oe(()=>{var e;(e=b.value)==null||e.refresh({dateRange:x.value,page:1})}),(_e=T.value)!=null&&_e.find(e=>e.value===0)||(fe=T.value)==null||fe.unshift({label:"无",value:0});const ne=d({edit:{width:80,permission:h.pms.DailyProductionReport.permission.update,show:!0},delete:{width:80,permission:h.pms.DailyProductionReport.permission.delete,show:!0}}),{getOpWidth:ge,getOpIsHidden:ke}=Le(ne),we=d(ge()),xe=d(ke());async function We(){try{const e=await h.pms.production.schedule.request({url:"/list",method:"POST"});U.value=e,R.value=e,ae.value=e==null?void 0:e.map(t=>({value:t.id,label:t.sn}))}catch(e){console.error(e)}}const M=[{label:"临时1（试产）",value:1,name:"-",nameEn:"-",type:"info"},{label:"临时2（首次量产）",value:2,name:"-",nameEn:"-",type:"warning"},{label:"正式（量产）",value:3,name:"-",nameEn:"-",type:"success"}],F=[{label:"加工段",value:1,name:"-",nameEn:"-",type:"info"},{label:"组装段",value:2,name:"-",nameEn:"-",type:"info"},{label:"老化段",value:3,name:"-",nameEn:"-",type:"info"},{label:"包装段",value:4,name:"-",nameEn:"-",type:"info"},{label:"加工段一",value:5,name:"-",nameEn:"-",type:"info"},{label:"加工段二",value:6,name:"-",nameEn:"-",type:"info"},{label:"加工段三",value:7,name:"-",nameEn:"-",type:"info"},{label:"芯子配件加工段一",value:8,name:"-",nameEn:"-",type:"info"},{label:"芯子配件加工段二",value:9,name:"-",nameEn:"-",type:"info"},{label:"芯子配件组装段一",value:10,name:"-",nameEn:"-",type:"info"},{label:"芯子配件组装段二",value:11,name:"-",nameEn:"-",type:"info"},{label:"芯子配件组装段三",value:12,name:"-",nameEn:"-",type:"info"},{label:"芯子配件组装段四",value:13,name:"-",nameEn:"-",type:"info"},{label:"芯子配件组装段五",value:14,name:"-",nameEn:"-",type:"info"},{label:"芯子配件包装段",value:15,name:"-",nameEn:"-",type:"info"}],H=G.useUpsert({props:{class:"report-form",labelWidth:"160px"},items:[{label:"日期",prop:"produced_date",required:!0,component:{name:"el-date-picker",props:{type:"date",placeholder:"选择日期","value-format":"YYYY-MM-DD",format:"YYYY-MM-DD",clearable:!0,disabledDate:e=>e.getTime()>Date.now()}}},{label:"生产订单",prop:"order_id",required:!0,component:{name:"slot-order-select"}},{label:"机型",prop:"product_id",required:!0,component:{name:"slot-product-select"}},{label:"生产车间",prop:"workshop_id",required:!0,component:{name:"slot-workshop-select"}},{label:"颜色",prop:"color",required:!1,component:{name:"slot-color-select"}},{label:"订单数量",prop:"quantity",required:!0,component:{name:"slot-input-quantity"}},{label:"生产阶段",prop:"production_stages",required:!0,component:{name:"el-select",props:{filterable:!0},options:M}},{label:"人数",prop:"number_of_people",required:!0,component:{name:"slot-input-number-people"}},{label:"工时",prop:"man_hour",required:!0,component:{name:"slot-input-man-hour"}},{label:"工段",prop:"workshop_section",required:!0,component:{name:"el-select",props:{filterable:!0},options:F}},{label:"日产量",prop:"daily_output",required:!0,component:{name:"slot-input-daily_output"}},{label:"备注",prop:"re_mark",required:!0,component:{name:"el-input"}}],async onOpened(e){(e==null?void 0:e.id)!=null&&(await ue(e.order_id),pe(e.product_id),ce(e.product_id))},onSubmit(e,{done:t,close:r,next:i}){var m;const s={produced_date:e.produced_date,order_id:e.order_id,product_id:e.product_id,quantity:e.quantity,production_stages:e.production_stages,number_of_people:e.number_of_people,man_hour:e.man_hour,workshop_section:e.workshop_section,daily_output:e.daily_output,workshop_id:e.workshop_id,sku:(m=L.value.find(u=>u.id===e.product_id))==null?void 0:m.sku,re_mark:e.re_mark,id:e.id};i({id:e.id,requestData:s,done:t}),r()}}),De=G.useTable({columns:[{type:"selection"},{label:"生产日期",prop:"produced_date",minWidth:60,component:{name:"cl-date-text",props:{format:"YYYY-MM-DD"}}},{label:"订单",prop:"sn",minWidth:60},{label:"订单数量(PCS)",prop:"quantity",minWidth:60},{label:"剩余生产数量(PCS)",prop:"except_quantity",minWidth:70},{label:"机型",prop:"sku",minWidth:100,formatter(e){return`${e.sku}   ${e.product.name}`}},{label:"生产车间",prop:"workshop_id",minWidth:65,formatter(e){var t;return((t=Y.value.find(r=>r.id===Number.parseInt(e.workshop_id)))==null?void 0:t.label)||"-"}},{label:"线别",prop:"production_line",minWidth:70},{label:"作业人员",prop:"busywork_group",minWidth:70},{label:"规格型号(长宽高)",prop:"",minWidth:80,formatter(e){return`${e.product.length} * ${e.product.width} * ${e.product.height}cm`}},{label:"工段",prop:"workshop_section",minWidth:40,formatter:e=>{var t;return((t=F.find(r=>r.value===Number.parseInt(e.workshop_section)))==null?void 0:t.label)||"-"}},{label:"颜色",prop:"product.color",minWidth:80,formatter:e=>{var t,r;return((r=(t=T.value)==null?void 0:t.find(i=>i.value===Number.parseInt(e.product.color)))==null?void 0:r.label)||"-"}},{label:"生产阶段",prop:"production_stages",minWidth:70,formatter:e=>{var t;return((t=M.find(r=>r.value===Number.parseInt(e.production_stages)))==null?void 0:t.label)||"-"}},{label:"生产标准",align:"center",children:[{label:"标准人均产能(PCS/H)",prop:"standard_capacity",minWidth:70,formatter:e=>e.standard_capacity===0?"-":e.standard_capacity.toFixed(2)},{label:"标准累计工时(H)",minWidth:70,formatter:e=>{const t=e.standard_capacity===0?-9999:e.daily_output/e.standard_capacity;return t===-9999?"-":t.toFixed(2)}}]},{label:"实际生产数据",align:"center",children:[{label:"人数(人)",prop:"number_of_people",minWidth:50},{label:"人均工时(H)",prop:"average_working_hours",minWidth:60,formatter(e){return e.average_working_hours.toFixed(2)}},{label:"累计工时(H)",prop:"man_hour",minWidth:60,formatter(e){return e.man_hour.toFixed(2)}},{label:"人均产能(pcs/h)",prop:"average_capacity",minWidth:60,formatter(e){return e.average_capacity.toFixed(2)}},{label:"当日产能(pcs/h)",prop:"daily_output",minWidth:60,formatter(e){return e.daily_output.toFixed(2)}}]},{label:"制造成本(H)",align:"center",children:[{label:"单机成本",align:"center",children:[{label:"标准",prop:"standard_unit_cost",minWidth:40,formatter(e){return e.standard_unit_cost>0?e.standard_unit_cost.toFixed(2):"-"}},{label:"实际",prop:"actual_unit_cost",minWidth:40,formatter(e){return e.actual_unit_cost>0?e.actual_unit_cost.toFixed(2):"-"}}]},{label:"成本合计",align:"center",children:[{label:"标准",prop:"total_standard_cost",minWidth:40,formatter(e){return e.total_standard_cost>0?e.total_standard_cost.toFixed(2):"-"}},{label:"实际",prop:"total_actual_cost",minWidth:40,formatter(e){return e.total_actual_cost>0?e.total_actual_cost.toFixed(2):"-"}}]},{label:"成本差异",prop:"cost_difference",minWidth:60,formatter(e){return e.total_standard_cost>0?e.cost_difference.toFixed(2):"-"}}]},{label:"异常工时(H)",prop:"abnormal_working_hours",minWidth:60,formatter(e){return e.abnormal_working_hours>0?e.abnormal_working_hours.toFixed(2):"0"}},{label:"工时差异(标准-(实际-异常)，H)",prop:"time_variance",minWidth:80,formatter(e){return e.time_variance===0?"0":e.time_variance.toFixed(2)}},{label:"备注",prop:"re_mark",minWidth:60},{label:"产能描述",prop:"description",minWidth:100,showOverflowTooltip:!0},{type:"op",label:"操作",width:we,hidden:xe,buttons:Object.keys(ne.value)}]});async function Ye(e){e=e.trim(),e||(R.value=U.value),R.value=U.value.filter(t=>t.sn.toLowerCase().includes(e.toLowerCase()))}function Ne({row:e}){return e.time_variance!==void 0&&e.time_variance<=-9999?"variance-warning-row":""}async function ue(e){try{L.value=await h.pms.production.schedule.request({url:"/getProductListByOrderId",method:"POST",data:{order_id:e}})}catch(t){console.error(t)}}function ie(e){return/^\d+(?:\.\d{0,2})?$/.test(e)?e:e.slice(0,-1)}function se(e){return/^\d+$/.test(e)?e:e.slice(0,-1)}function Ve(){var s,m;const e=(s=$.value)==null?void 0:s.getForm("people_name"),t=(m=$.value)==null?void 0:m.getForm("workshop_id");let r={};if(x.value&&x.value.length===2){const[u,y]=x.value,N=S(u).startOf("month").format("YYYY-MM-DD"),O=S(y).endOf("month").format("YYYY-MM-DD");r={sku:C.value,orderId:z.value,abnormal:K.value,start:N,end:O,people_name:e,workshop_id:t}}else r={sku:C.value,orderId:z.value,abnormal:K.value,people_name:e,workshop_id:t};const i={url:"/export",method:"GET",responseType:"blob",params:r};h.pms.DailyProductionReport.request(i).then(u=>{var y;he(u)&&W.success("导出成功"),(y=b.value)==null||y.refresh()}).catch(u=>{W.error(u.message||"导出失败")})}function pe(e){var i,s;const t=L.value.find(m=>m.id===e),r=((i=T.value.find(m=>m.id===(t==null?void 0:t.color)))==null?void 0:i.label)||"-";(s=H.value)==null||s.setForm("color",r)}const de=d(0);function ce(e){var r;const t=(r=H.value)==null?void 0:r.getForm("order_id");h.pms.production.schedule.request({url:"/getOrderProductQuantity",method:"POST",data:{order_id:t,product_id:e}}).then(i=>{var s;(s=H.value)==null||s.setForm("quantity",i.quantity),de.value=i.quantity}).catch(i=>{W.error(i.message||"查询失败")})}const me=d(null),_=d(!1);async function qe(e){const t=e.target,r=t.files,i={};F&&F.length>0&&F.forEach(u=>{i[u.label]=u.value});const s={};M&&M.length>0&&M.forEach(u=>{s[u.label]=u.value});const m={};if(Y.value&&Y.value.length>0&&Y.value.forEach(u=>{m[u.label]=u.id}),r&&r.length>0){_.value=!0;const u=r[0],y=new FileReader;y.onload=N=>{var A,j,q;const O=new Uint8Array((A=N.target)==null?void 0:A.result),B=Ue(O,{type:"array"}),J=B.Sheets[B.SheetNames[0]],g=Re.sheet_to_json(J,{header:1}),f=[void 0,null,"","undefined","null","NaN"],V=["","produced_date","order_id","name","workshop_id","workshop_section","sku","production_stages","number_of_people","man_hour","daily_output","re_mark"],I=[];if(g&&g.length>0){for(let l=5;l<g.length;l++){const a=g[l],o={};for(let k=1;k<a.length;k++){const E=V[k];typeof a[k]=="string"&&(a[k]=a[k].trim()),o[E]=a[k],E==="produced_date"&&o.produced_date&&(o.produced_date=S(o.produced_date).format("YYYY-MM-DD")),E==="workshop_section"&&(o.workshop_section=i[o.workshop_section]?i[o.workshop_section]:0),E==="workshop_id"&&(o.workshop_id=m[o.workshop_id]?m[o.workshop_id]:0),E==="production_stages"&&(o.production_stages=s[o.production_stages]?s[o.production_stages]:0),!(E==="order_id"&&(o.order_id=(j=U.value.find(Ce=>Ce.sn.toLowerCase().includes(o.order_id.toLowerCase())))==null?void 0:j.id,o.order_id===void 0||o.order_id===0))&&(o.number_of_people=Number.parseInt(o.number_of_people),o.man_hour=Number.parseFloat(o.man_hour),o.daily_output=Number.parseInt(o.daily_output),o.quantity=Number.parseInt(o.quantity))}if(o.product_id=(q=re.value.find(k=>k.sku===o.sku))==null?void 0:q.value,f.includes(o.daily_output)||Number.isNaN(o.daily_output)){v(t),_.value=!1;break}if(f.includes(o.man_hour)||Number.isNaN(o.man_hour)||o.man_hour===0){v(t),_.value=!1;break}if(f.includes(o.number_of_people)||Number.isNaN(o.number_of_people)||o.number_of_people===0){v(t),_.value=!1;break}if(f.includes(o.produced_date)){v(t),_.value=!1;break}if(f.includes(o.workshop_section)||Number.isNaN(o.workshop_section)||o.workshop_section===0){v(t),_.value=!1;break}if(f.includes(o.workshop_id)||Number.isNaN(o.workshop_id)||o.workshop_id===0){v(t),_.value=!1;break}if(f.includes(o.production_stages)||Number.isNaN(o.production_stages)||o.production_stages===0){v(t),_.value=!1;break}if(f.includes(o.sku)||o.sku===""){v(t),_.value=!1;break}I.push(o)}I.length>0?h.pms.DailyProductionReport.importDailyProductionData({dailyProductionData:I}).then(()=>{var l;(l=b.value)==null||l.refresh(),W.success("导入成功")}).catch(l=>{W.error(l.message||"导入失败")}).finally(()=>{_.value=!1}):(_.value=!1,W.error("导入有效数据为空")),v(t)}},y.readAsArrayBuffer(u)}else _.value=!1,W.error("请选择文件")}function v(e){e&&(e.value="")}function Ee(){const e="生产日报表_模板.xlsx";fetch("/daily_production.xlsx").then(r=>r.blob()).then(r=>{he(r,e)}).catch(()=>{W.error({message:"下载模板文件失败"})})}function Pe(){const e=me.value;e&&e.click()}return(e,t)=>{const r=c("cl-refresh-btn"),i=c("cl-add-btn"),s=c("el-button"),m=c("cl-multi-delete-btn"),u=c("cl-flex1"),y=c("cl-search"),N=c("cl-row"),O=c("cl-table"),B=c("cl-pagination"),J=c("el-row"),g=c("el-option"),f=c("el-select"),V=c("el-input"),I=c("el-input-number"),A=c("cl-upsert"),j=c("cl-crud"),q=Se("permission");return w(),P(j,{ref_key:"Crud",ref:b},{default:p(()=>[n(N,null,{default:p(()=>[n(r),n(i),Ie("input",{ref_key:"fileInputRef",ref:me,type:"file",style:{display:"none"},accept:".xlsx, .xls",onChange:qe},null,544),X((w(),P(s,{size:"default",loading:_.value,type:"warning",class:"mb-10px mr-10px",ml:"20px",onClick:Pe},{default:p(()=>[Z(" Excel导入 ")]),_:1},8,["loading"])),[[q,Q(h).pms.DailyProductionReport.permission.importDailyProductionData]]),X((w(),P(s,{type:"info",class:"mb-10px mr-10px",size:"default",onClick:Ee},{default:p(()=>[Z(" 下载Excel模板 ")]),_:1})),[[q,Q(h).pms.DailyProductionReport.permission.importDailyProductionData]]),n(s,{type:"success",onClick:Ve},{default:p(()=>[Z(" 导出 ")]),_:1}),X(n(m,null,null,512),[[q,Q(h).pms.DailyProductionReport.permission.delete]]),n(u),n(y,{ref_key:"Search",ref:$},null,512)]),_:1}),n(N,null,{default:p(()=>[n(O,{ref_key:"Table",ref:De,"auto-height":!1,"row-class-name":Ne},null,512)]),_:1}),n(J,null,{default:p(()=>[n(u),n(B)]),_:1}),n(A,{ref_key:"Upsert",ref:H},{"slot-order-select":p(({scope:l})=>[n(f,{modelValue:l.order_id,"onUpdate:modelValue":a=>l.order_id=a,filterable:"",remote:"",placeholder:"选择生产订单","remote-method":Ye,onChange:a=>{ue(a),l.product_id=void 0,l.quantity=void 0}},{default:p(()=>[(w(!0),ee(oe,null,te(R.value,a=>(w(),P(g,{key:a.id,label:a.sn,value:a.id},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])]),"slot-product-select":p(({scope:l})=>[n(f,{modelValue:l.product_id,"onUpdate:modelValue":a=>l.product_id=a,filterable:"",remote:"",placeholder:"选择产品",onChange:t[0]||(t[0]=a=>{pe(a),ce(a)})},{default:p(()=>[(w(!0),ee(oe,null,te(L.value,a=>(w(),P(g,{key:a.id,label:`${a.sku} ${a.name}`,value:a.id},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),"slot-workshop-select":p(({scope:l})=>[n(f,{modelValue:l.workshop_id,"onUpdate:modelValue":a=>l.workshop_id=a,placeholder:"选择生产车间"},{default:p(()=>[n(g,{key:"0",label:"请选择车间",value:0}),(w(!0),ee(oe,null,te(Q(Y),a=>(w(),P(g,{key:a.id,label:a.name,value:a.id},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),"slot-color-select":p(({scope:l})=>[n(V,{modelValue:l.color,"onUpdate:modelValue":a=>l.color=a,placeholder:"产品颜色",disabled:!0},null,8,["modelValue","onUpdate:modelValue"])]),"slot-input-quantity":p(({scope:l})=>[n(V,{modelValue:l.quantity,"onUpdate:modelValue":a=>l.quantity=a,type:"number",placeholder:"请输入产品数量",formatter:se,step:"1",disabled:""},null,8,["modelValue","onUpdate:modelValue"])]),"slot-input-number-people":p(({scope:l})=>[n(V,{modelValue:l.number_of_people,"onUpdate:modelValue":a=>l.number_of_people=a,type:"number",placeholder:"请输入人数",formatter:se,step:"1"},null,8,["modelValue","onUpdate:modelValue"])]),"slot-input-man-hour":p(({scope:l})=>[n(V,{modelValue:l.man_hour,"onUpdate:modelValue":a=>l.man_hour=a,type:"number",placeholder:"请输入工时",formatter:ie,step:.01},null,8,["modelValue","onUpdate:modelValue"])]),"slot-input-daily_output":p(({scope:l})=>[n(I,{modelValue:l.daily_output,"onUpdate:modelValue":a=>l.daily_output=a,type:"number",max:de.value,placeholder:"请输入日产量",formatter:ie,step:.01},null,8,["modelValue","onUpdate:modelValue","max"])]),_:1},512)]),_:1},512)}}});export{je as default};
