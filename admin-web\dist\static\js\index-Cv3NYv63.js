import{c as y,at as p,e as o,au as _,$ as h,as as g,i as s,f as z,o as w,h as r,w as n,y as v,V as c}from"./.pnpm-hVqhwuVC.js";import{p as u}from"./index-DkYL1aws.js";import{_ as S}from"./_plugin-vue_export-helper-DlAUqK2U.js";const B=y({name:"ClImage",components:{PictureFilled:p},props:{modelValue:[String,Array],src:[String,Array],size:{type:[Number,Array],default:100},lazy:Boolean,fit:{type:String,default:"cover"},justify:{type:String,default:"center"}},setup(e){const i=o(()=>{const t=e.modelValue||e.src;return _(t)?t:h(t)?(t||"").split(",").filter(Boolean):[]}),a=o(()=>{const[t,l]=g(e.size)?[e.size,e.size]:e.size;return{h:u(t),w:u(l)}});return{urls:i,style:a}}}),C={class:"image-slot"};function V(e,i,a,t,l,$){const d=s("PictureFilled"),f=s("el-icon"),m=s("el-image");return w(),z("div",{class:"cl-image",style:c({justifyContent:e.justify,height:e.style.h})},[r(m,{src:e.urls[0],fit:e.fit,lazy:e.lazy,"preview-src-list":e.urls,style:c({height:e.style.h,width:e.style.w}),"preview-teleported":""},{error:n(()=>[v("div",C,[r(f,{size:20},{default:n(()=>[r(d)]),_:1})])]),_:1},8,["src","fit","lazy","preview-src-list","style"])],4)}const b=S(B,[["render",V],["__scopeId","data-v-ba881a01"]]);export{b as default};
