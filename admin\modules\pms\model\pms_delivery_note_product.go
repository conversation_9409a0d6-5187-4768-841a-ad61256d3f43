package model

import (
	"github.com/imhuso/lookah-erp/admin/yc"
)

const TableNameDeliveryNoteProduct = "pms_delivery_note_product"

// PmsDeliveryNoteProductOutput mapped from table <pms_delivery_note_product>
type PmsDeliveryNoteProductOutput struct {
	ID                 int64   `json:"id"` // ID
	InboundId          int64   `json:"inboundId"`
	ContractId         int64   `json:"contractId"`
	MaterialId         int64   `json:"materialId"`
	Quantity           float64 `json:"quantity"`
	WarehouseId        int64   `json:"warehouseId"`
	Address            string  `json:"address"` // 地址
	InboundOutboundKey int     `json:"inbound_outbound_key"`
	Remark             string  `json:"remark"`
	*PmsMaterialOutput `orm:"with:id=material_id"`
	Po                 string               `json:"po" gorm:"-"`
	Contract           *PmsPurchaseContract `json:"contract" orm:"with:id=contract_id"`
}

// PmsDeliveryNoteProduct mapped from table <pms_delivery_note_product>
type PmsDeliveryNoteProduct struct {
	ID int64 `json:"id"       gorm:"column:id;type:bigint(20);not null;primary_key;auto_increment;comment:ID;"` // ID
	// 送货单ID
	InboundId int64 `json:"inboundId" gorm:"column:inbound_id;type:bigint(20);not null;default:0;comment:入库单ID;"`
	// 采购单合同ID
	ContractId int64 `json:"contractId" gorm:"column:contract_id;type:bigint(20);not null;default:0;comment:contractId;"`
	// 物料ID
	MaterialId int64 `json:"materialId" gorm:"column:material_id;type:bigint(20);not null;default:0;comment:物料ID;"`
	// 入库数量
	Quantity           float64 `json:"quantity" gorm:"column:quantity;type:decimal(14,4);not null;default:0.0000;comment:入库数量;"`
	WarehouseId        int64   `json:"warehouseId" gorm:"column:warehouse_id;type:bigint(20);not null;default:0;comment:仓库ID;"`
	Address            string  `json:"address"      gorm:"column:address;type:varchar(1000);not null;default:'';comment:地址;"` // 地址
	InboundOutboundKey int     `json:"inbound_outbound_key" gorm:"column:inbound_outbound_key;type:int(10);not null;default:0;comment:关键字ID;"`
	Remark             string  `json:"remark" gorm:"column:remark;type:varchar(500);not null;default:'';comment:备注;"`
	Po                 string  `json:"po" gorm:"-"`
}

// GroupName 返回分组名
func (m *PmsDeliveryNoteProduct) GroupName() string {
	return ""
}

// TableName PmsDeliveryNoteProduct's table name
func (*PmsDeliveryNoteProduct) TableName() string {
	return TableNameDeliveryNoteProduct
}

// NewDeliveryNoteProduct 创建一个新的PmsDeliveryNote
func NewDeliveryNoteProduct() *PmsDeliveryNoteProduct {
	return &PmsDeliveryNoteProduct{}
}

// init 创建表
func init() {
	_ = yc.CreateTable(NewDeliveryNoteProduct())
}
