import{U as le}from"./user-avatar-DTjmVWm6.js";import{s as p,i as O,e as te}from"./index-DkYL1aws.js";import{_ as ae}from"./select-supplier.vue_vue_type_script_setup_true_name_select-supplier_lang-DJAbRp-s.js";import{_ as oe}from"./select-material.vue_vue_type_script_setup_true_name_select-material_lang-BraZDHOH.js";import{c as F,k as J,n as re,b as c,A as ne,f as Z,h as e,w as r,i,v as n,x as ie,a_ as se,j as U,E as I,o as v,y as G,af as ue,ag as de,K as pe,G as x,q as k,B as Q,H as W,U as me,T as ce}from"./.pnpm-hVqhwuVC.js";import{_ as fe}from"./select-product.vue_vue_type_script_setup_true_name_select-product_lang-CnfS9ER-.js";import{_ as K}from"./select-dict.vue_vue_type_script_setup_true_name_select-dict_lang-uUMI05Bz.js";import{_ as _e}from"./select-dept.vue_vue_type_script_setup_true_name_select-dept_lang-BjcPl6nA.js";import{_ as ge}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{g as A,b as be,a as X}from"./index-BFVs8cCE.js";import"./index.vue_vue_type_script_setup_true_name_cl-avatar_lang-Dwbve7L9.js";import"./text-overflow-tooltip.vue_vue_type_script_setup_true_name_text-overflow-tooltip_lang-D_kIBTxD.js";const ve=w=>(ue("data-v-dd18f621"),w=w(),de(),w),ye=ve(()=>G("span",{"font-size-20px":"",style:{"margin-left":"5px"}},"%",-1)),we=F({name:"add-process-abnormality"}),Ve=F({...we,props:J({rowData:{default:()=>({})},refresh:{type:Function,default:()=>{}}},{modelValue:{type:Boolean},modelModifiers:{}}),emits:J(["update:rowData"],["update:modelValue"]),setup(w,{emit:H}){const f=w,_=H,m=re(w,"modelValue"),V=c(!1),T=c(),t=c({}),N=c(["1"]),M=c({supplierId:[{required:!0,message:"请输入供应商",trigger:"blur"}],code:[{required:!0,message:"请选择物料名称",trigger:"blur"}],discoveryDate:[{required:!0,message:"请选择日期",trigger:"blur"}],defectiveRate:[{required:!0,message:"请输入不良率",trigger:"blur"}],abnormalType:[{required:!0,message:"请输入异常分类",trigger:"blur"}],abnormalDescription:[{required:!0,message:"请输入异常描述",trigger:"blur"}],abnormalPicture:[{required:!0,message:"请上传异常图片",trigger:"blur"}],responsibleUnit:[{required:!0,message:"请输入责任单位",trigger:"blur"}],responsiblePerson:[{required:!0,message:"请输入责任人",trigger:"blur"}],modelId:[{required:!0,message:"请输入机型",trigger:"blur"}],floor:[{required:!0,message:"请输入楼层",trigger:"blur"}],line:[{required:!0,message:"请选择线别",trigger:"blur"}]});async function E(){var l;if(await((l=T.value)==null?void 0:l.validate()))try{V.value=!0,f.rowData.id&&f.rowData.id>0?await p.pms.productionData.processAbnormality.update(t.value):await p.pms.productionData.processAbnormality.add(t.value),I.success("保存成功"),m.value=!1,f.refresh()}catch(u){I.error(u.message||"保存失败"),console.error(u)}finally{V.value=!1}}function B(){m.value=!1,_("update:rowData",{})}async function Y(){f.rowData.id&&p.pms.productionData.processAbnormality.info({id:f.rowData.id}).then(a=>{t.value=a}).catch(a=>{console.error(a),I.error(a.message||"获取数据失败")})}function S(a){if(a===void 0){t.value.materialId=0,t.value.materialName="";return}t.value.materialName=a.label,t.value.materialId=a.id}function P(a){if(a===void 0){t.value.model="";return}t.value.model=a.label}function z(a){if(a===void 0){t.value.supplierName="",t.value.supplierShortName="";return}t.value.supplierName=a.label,t.value.supplierShortName=a.shortName}return ne(()=>{m.value&&Y()}),(a,l)=>{const u=i("el-date-picker"),s=i("el-form-item"),d=i("el-col"),g=i("el-row"),C=i("el-input-number"),L=i("cl-upload"),j=i("el-input"),h=i("el-card"),D=i("el-collapse-item"),q=i("cl-editor-wang"),y=i("el-collapse"),$=i("el-button"),R=i("el-drawer");return v(),Z("div",null,[e(R,{modelValue:m.value,"onUpdate:modelValue":l[18]||(l[18]=o=>m.value=o),title:f.rowData.id?"编辑":"新增","close-on-press-escape":!1,"close-on-click-modal":!1,size:"50%",onClose:B},{footer:r(()=>[e($,{onClick:B},{default:r(()=>[U(" 取消 ")]),_:1}),e($,{type:"success",loading:n(V),onClick:E},{default:r(()=>[U(" 确定 ")]),_:1},8,["loading"])]),default:r(()=>[e(y,{modelValue:n(N),"onUpdate:modelValue":l[17]||(l[17]=o=>ie(N)?N.value=o:null)},{default:r(()=>[e(D,{title:"基本信息",name:"1"},{default:r(()=>[e(h,null,{default:r(()=>[e(n(se),{ref_key:"FormRef",ref:T,model:n(t),"label-width":"100px",rules:n(M)},{default:r(()=>[e(g,{gutter:20},{default:r(()=>[e(d,{span:12},{default:r(()=>[e(s,{label:"日期",prop:"discoveryDate"},{default:r(()=>[e(u,{modelValue:n(t).discoveryDate,"onUpdate:modelValue":l[0]||(l[0]=o=>n(t).discoveryDate=o),"disabled-date":o=>o.getTime()>Date.now()-864e5,style:{width:"100%"},type:"date",format:"YYYY-MM-DD",placeholder:"请选择日期"},null,8,["modelValue","disabled-date"])]),_:1})]),_:1}),e(d,{span:12},{default:r(()=>[e(s,{label:"机型",prop:"modelId"},{default:r(()=>[e(fe,{modelValue:n(t).modelId,"onUpdate:modelValue":l[1]||(l[1]=o=>n(t).modelId=o),width:"100%",onChange:P},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(g,{gutter:20},{default:r(()=>[e(d,{span:12},{default:r(()=>[e(s,{label:"供应商",prop:"supplierId"},{default:r(()=>[e(ae,{modelValue:n(t).supplierId,"onUpdate:modelValue":l[2]||(l[2]=o=>n(t).supplierId=o),width:"100%",onChange:z},null,8,["modelValue"])]),_:1})]),_:1}),e(d,{span:12},{default:r(()=>[e(s,{label:"物料名称",prop:"code"},{default:r(()=>[e(oe,{modelValue:n(t).code,"onUpdate:modelValue":l[3]||(l[3]=o=>n(t).code=o),width:"100%",onChange:S},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(g,{gutter:20},{default:r(()=>[e(d,{span:8},{default:r(()=>[e(s,{label:"不良率",prop:"defectiveRate"},{default:r(()=>[e(C,{modelValue:n(t).defectiveRate,"onUpdate:modelValue":l[4]||(l[4]=o=>n(t).defectiveRate=o),style:{width:"90%"},controls:!1,min:0,max:100,precision:2},null,8,["modelValue"]),ye]),_:1})]),_:1}),e(d,{span:8},{default:r(()=>[e(s,{label:"楼层",prop:"floor"},{default:r(()=>[e(K,{modelValue:n(t).floor,"onUpdate:modelValue":l[5]||(l[5]=o=>n(t).floor=o),code:"product_floor",width:"100%",onChange:l[6]||(l[6]=()=>{n(t).line=void 0})},null,8,["modelValue"])]),_:1})]),_:1}),e(d,{span:8},{default:r(()=>[e(s,{label:"线别",prop:"line"},{default:r(()=>[e(K,{modelValue:n(t).line,"onUpdate:modelValue":l[7]||(l[7]=o=>n(t).line=o),code:n(t).floor,width:"100%","product-floor":""},null,8,["modelValue","code"])]),_:1})]),_:1})]),_:1}),e(g,{gutter:20},{default:r(()=>[e(d,{span:8},{default:r(()=>[e(s,{label:"异常分类",prop:"abnormalType"},{default:r(()=>[e(K,{modelValue:n(t).abnormalType,"onUpdate:modelValue":l[8]||(l[8]=o=>n(t).abnormalType=o),code:"pms_process_abnormal",width:"100%"},null,8,["modelValue"])]),_:1})]),_:1}),e(_e,{modelValue:n(t).responsibleUnit,"onUpdate:modelValue":l[9]||(l[9]=o=>n(t).responsibleUnit=o),departmentId:n(t).responsiblePerson,"onUpdate:departmentId":l[10]||(l[10]=o=>n(t).responsiblePerson=o),width:"100%"},null,8,["modelValue","departmentId"])]),_:1}),e(s,{label:"异常图片",prop:"abnormalPicture",required:""},{default:r(()=>[e(L,{modelValue:n(t).abnormalPicture,"onUpdate:modelValue":l[11]||(l[11]=o=>n(t).abnormalPicture=o),type:"image",multiple:!0,limit:1,"is-private":!1},null,8,["modelValue"])]),_:1}),e(s,{label:"异常描述",prop:"abnormalDescription",required:""},{default:r(()=>[e(j,{modelValue:n(t).abnormalDescription,"onUpdate:modelValue":l[12]||(l[12]=o=>n(t).abnormalDescription=o),"w-full":"",type:"textarea",rows:2},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1})]),_:1}),e(D,{title:"临时措施",name:"2"},{default:r(()=>[e(h,null,{default:r(()=>[e(q,{modelValue:n(t).temporaryMeasures,"onUpdate:modelValue":l[13]||(l[13]=o=>n(t).temporaryMeasures=o),height:"150px"},null,8,["modelValue"])]),_:1})]),_:1}),e(D,{title:"原因分析",name:"3"},{default:r(()=>[e(h,null,{default:r(()=>[e(q,{modelValue:n(t).causeAnalysis,"onUpdate:modelValue":l[14]||(l[14]=o=>n(t).causeAnalysis=o),height:"150px"},null,8,["modelValue"])]),_:1})]),_:1}),e(D,{title:"改善方案",name:"4"},{default:r(()=>[e(h,null,{default:r(()=>[e(q,{modelValue:n(t).improvementPlan,"onUpdate:modelValue":l[15]||(l[15]=o=>n(t).improvementPlan=o),height:"150px"},null,8,["modelValue"])]),_:1})]),_:1}),e(D,{title:"完成状况",name:"5"},{default:r(()=>[e(h,null,{default:r(()=>[e(q,{modelValue:n(t).completionStatus,"onUpdate:modelValue":l[16]||(l[16]=o=>n(t).completionStatus=o),height:"150px"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1},8,["modelValue"])]),_:1},8,["modelValue","title"])])}}}),he=ge(Ve,[["__scopeId","data-v-dd18f621"]]),De={"w-full":"",flex:"~ justify-center"},xe=F({name:"undefined"}),Fe=F({...xe,setup(w){const H=c({}),f=c(!1),_=O.useCrud({service:p.pms.productionData.processAbnormality,async onRefresh(a,{next:l,done:u,render:s}){const{list:d,pagination:g}=await l(a);u(),s(d,g)}},a=>{a.refresh()}),m=c(!1),V=c({}),T=c(null),t=c(!1);function N(){const a=T.value;a&&a.click()}const M=O.useTable({columns:[{label:"供应商",prop:"supplierName",align:"center",fixed:"left",showOverflowTooltip:!0},{label:"物料名称",prop:"materialName",width:300,align:"center"},{label:"日期",prop:"discoveryDate",width:160,align:"center",sortable:"desc",formatter(a){return a.discoveryDate?pe(a.discoveryDate).format("YYYY-MM-DD"):""}},{label:"楼层",prop:"floor",width:80,align:"center",formatter(a){return A("product_floor",a.floor)}},{label:"线别",prop:"line",width:80,align:"center",formatter(a){return be("product_floor",a.floor,a.line)}},{label:"异常分类",prop:"abnormalType",align:"center",width:160,formatter(a){return A("pms_process_abnormal",a.abnormalType)}},{label:"异常描叙",prop:"abnormalDescription",align:"center",width:160},{label:"不良率",prop:"defectiveRate",width:100,align:"center",sortable:"desc",formatter(a){return`${a.defectiveRate}%`}},{label:"责任单位",prop:"responsibleUnit",align:"center",width:160,formatter(a){return A("dept",a.responsibleUnit)}},{label:"责任人",prop:"responsiblePerson",align:"center",width:160,formatter(a){return A("user",a.responsiblePerson)}},{label:"创建人",prop:"creator",align:"center",width:100},{label:"创建时间",prop:"createTime",align:"center",sortable:"desc",width:160},{type:"op",label:"操作",width:160,buttons:["slot-edit-btn","slot-del-btn"]}]});function E(){m.value=!0}async function B(a){V.value=me(a),m.value=!0}async function Y(a){ce.confirm("确定删除吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{var l;try{f.value=!0,await p.pms.productionData.processAbnormality.delete({ids:[a.id]}),(l=_==null?void 0:_.value)==null||l.refresh()}catch(u){console.error(u)}finally{f.value=!1}})}async function S(a){var s;const l=a.target,u=l.files;if(u&&u.length>0){t.value=!0;const d=u[0],g=new FormData;g.append("incomingFile",d);try{await p.pms.productionData.incoming.import(g),(s=_==null?void 0:_.value)==null||s.refresh(),I.success("导入成功")}catch(C){I.error(C.message||"导入失败"),console.error(C)}finally{t.value=!1,P(l)}}else t.value=!1,P(l),I.error("请选择文件")}function P(a){a&&(a.value="")}function z(){const a="制程异常导入模板.xlsx";fetch("/processAbnormality.xlsx").then(u=>u.blob()).then(u=>{te(u,a)}).catch(()=>{I.error({message:"下载模板文件失败"})})}return(a,l)=>{var R;const u=i("cl-refresh-btn"),s=i("el-button"),d=i("cl-flex1"),g=i("el-date-picker"),C=i("cl-search-key"),L=i("el-row"),j=i("cl-table"),h=i("cl-pagination"),D=i("cl-row"),q=i("cl-crud"),y=W("permission"),$=W("loading");return v(),Z("div",null,[x((v(),k(q,{ref_key:"Crud",ref:_},{default:r(()=>[e(L,null,{default:r(()=>[e(u),x((v(),k(s,{type:"primary",onClick:E},{default:r(()=>[U(" 新增 ")]),_:1})),[[y,n(p).pms.productionData.incoming.add]]),G("input",{ref_key:"fileInputRef",ref:T,type:"file",style:{display:"none"},accept:".xlsx, .xls",onChange:S},null,544),x((v(),k(s,{size:"default",loading:t.value,type:"success",class:"mb-10px mr-10px",ml:"20px",onClick:N},{default:r(()=>[U(" Excel导入 ")]),_:1},8,["loading"])),[[y,n(p).pms.productionData.incoming.import]]),x((v(),k(s,{class:"mb-10px mr-10px",size:"default",onClick:z},{default:r(()=>[U(" 下载Excel模板 ")]),_:1})),[[y,n(p).pms.productionData.incoming.import]]),e(d),Q("",!0),x(e(C,{placeholder:"请输入供应商名称或物料名称或编号",width:"600px"},null,512),[[y,n(p).pms.productionData.incoming.page]])]),_:1}),e(j,{ref_key:"Table",ref:M,"auto-height":!1,"min-height":"200"},{"column-creator":r(({scope:o})=>{var b;return[G("div",De,[e(le,{user:(b=o==null?void 0:o.row)==null?void 0:b.creator,"show-name":!1},null,8,["user"])])]}),"slot-edit-btn":r(({scope:o})=>{var b;return[x((v(),k(s,{type:"primary",text:"",onClick:ee=>B(o.row)},{default:r(()=>[U(" 编辑 ")]),_:2},1032,["onClick"])),[[y,{and:[n(p).pms.productionData.incoming.update,n(X)===((b=o.row)==null?void 0:b.creatorId)]}]])]}),"slot-del-btn":r(({scope:o})=>{var b;return[x((v(),k(s,{type:"danger",text:"",onClick:ee=>Y(o.row)},{default:r(()=>[U(" 删除 ")]),_:2},1032,["onClick"])),[[y,{and:[n(p).pms.productionData.incoming.delete,n(X)===((b=o.row)==null?void 0:b.creatorId)]}]])]}),_:1},512),e(D,{mt:"10px"},{default:r(()=>[e(d),e(h)]),_:1})]),_:1})),[[$,f.value]]),m.value?(v(),k(he,{key:0,modelValue:m.value,"onUpdate:modelValue":l[1]||(l[1]=o=>m.value=o),"row-data":V.value,"onUpdate:rowData":l[2]||(l[2]=o=>V.value=o),refresh:(R=n(_))==null?void 0:R.refresh},null,8,["modelValue","row-data","refresh"])):Q("",!0)])}}});export{Fe as default};
