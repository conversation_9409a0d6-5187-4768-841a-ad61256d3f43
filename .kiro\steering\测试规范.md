---
inclusion: fileMatch
fileMatchPattern: "**/*{test,spec}*"
---

# 测试规范

## 测试策略

### 测试金字塔
1. **单元测试 (70%)**: 测试单个函数或方法
2. **集成测试 (20%)**: 测试模块间的交互
3. **端到端测试 (10%)**: 测试完整的用户流程

### 测试覆盖率要求
- **后端代码覆盖率**: ≥ 70%
- **前端组件覆盖率**: ≥ 60%
- **关键业务逻辑**: ≥ 90%
- **API 接口覆盖率**: 100%

## 后端测试规范

### Go 单元测试
```go
// internal/service/user_test.go
package service_test

import (
    "context"
    "testing"
    
    "github.com/gogf/gf/v2/test/gtest"
    "github.com/imhuso/lookah-erp/admin/internal/service"
    "github.com/imhuso/lookah-erp/admin/internal/model"
)

func TestUserService_Create(t *testing.T) {
    gtest.C(t, func(t *gtest.T) {
        ctx := context.Background()
        
        // 准备测试数据
        input := &model.UserCreateInput{
            Username: "testuser",
            Email:    "<EMAIL>",
            Password: "password123",
            Status:   1,
        }
        
        // 执行测试
        id, err := service.User().Create(ctx, input)
        
        // 断言结果
        t.AssertNil(err)
        t.AssertGT(id, 0)
        
        // 验证数据是否正确创建
        user, err := service.User().GetById(ctx, id)
        t.AssertNil(err)
        t.AssertEQ(user.Username, input.Username)
        t.AssertEQ(user.Email, input.Email)
        t.AssertEQ(user.Status, input.Status)
        
        // 清理测试数据
        err = service.User().Delete(ctx, id)
        t.AssertNil(err)
    })
}

func TestUserService_List(t *testing.T) {
    gtest.C(t, func(t *gtest.T) {
        ctx := context.Background()
        
        // 准备测试数据
        testUsers := []*model.UserCreateInput{
            {Username: "user1", Email: "<EMAIL>", Password: "pass123", Status: 1},
            {Username: "user2", Email: "<EMAIL>", Password: "pass123", Status: 0},
        }
        
        var createdIds []uint64
        for _, user := range testUsers {
            id, err := service.User().Create(ctx, user)
            t.AssertNil(err)
            createdIds = append(createdIds, id)
        }
        
        // 测试列表查询
        result, err := service.User().List(ctx, &model.UserListInput{
            Page: 1,
            Size: 10,
        })
        t.AssertNil(err)
        t.AssertGE(len(result.List), 2)
        t.AssertGE(result.Total, 2)
        
        // 测试状态筛选
        result, err = service.User().List(ctx, &model.UserListInput{
            Page:   1,
            Size:   10,
            Status: 1,
        })
        t.AssertNil(err)
        
        // 清理测试数据
        for _, id := range createdIds {
            service.User().Delete(ctx, id)
        }
    })
}

// 测试错误情况
func TestUserService_CreateWithInvalidData(t *testing.T) {
    gtest.C(t, func(t *gtest.T) {
        ctx := context.Background()
        
        // 测试空用户名
        _, err := service.User().Create(ctx, &model.UserCreateInput{
            Username: "",
            Email:    "<EMAIL>",
            Password: "password123",
        })
        t.AssertNE(err, nil)
        
        // 测试无效邮箱
        _, err = service.User().Create(ctx, &model.UserCreateInput{
            Username: "testuser",
            Email:    "invalid-email",
            Password: "password123",
        })
        t.AssertNE(err, nil)
    })
}
```

### 数据库测试
```go
// internal/dao/user_test.go
package dao_test

import (
    "context"
    "testing"
    
    "github.com/gogf/gf/v2/test/gtest"
    "github.com/imhuso/lookah-erp/admin/internal/dao"
    "github.com/imhuso/lookah-erp/admin/internal/model/do"
)

func TestUserDao_Insert(t *testing.T) {
    gtest.C(t, func(t *gtest.T) {
        ctx := context.Background()
        
        // 插入测试数据
        result, err := dao.User.Ctx(ctx).Data(do.User{
            Username: "testuser",
            Email:    "<EMAIL>",
            Password: "hashedpassword",
            Status:   1,
        }).Insert()
        
        t.AssertNil(err)
        
        id, err := result.LastInsertId()
        t.AssertNil(err)
        t.AssertGT(id, 0)
        
        // 验证数据
        var user *entity.User
        err = dao.User.Ctx(ctx).Where("id = ?", id).Scan(&user)
        t.AssertNil(err)
        t.AssertEQ(user.Username, "testuser")
        
        // 清理数据
        dao.User.Ctx(ctx).Where("id = ?", id).Delete()
    })
}
```

### API 测试
```go
// internal/controller/user_test.go
package controller_test

import (
    "testing"
    
    "github.com/gogf/gf/v2/frame/g"
    "github.com/gogf/gf/v2/net/ghttp"
    "github.com/gogf/gf/v2/test/gtest"
)

func TestUserController_List(t *testing.T) {
    gtest.C(t, func(t *gtest.T) {
        // 创建测试服务器
        s := g.Server("test")
        s.Group("/api/v1", func(group *ghttp.RouterGroup) {
            group.Bind(controller.User)
        })
        s.SetDumpRouterMap(false)
        s.Start()
        defer s.Shutdown()
        
        // 发送测试请求
        client := g.Client()
        client.SetPrefix(fmt.Sprintf("http://127.0.0.1:%d", s.GetListenedPort()))
        
        // 测试获取用户列表
        response := client.GetContent(ctx, "/api/v1/users?page=1&size=10")
        t.AssertNE(response, "")
        
        // 解析响应
        var result struct {
            Code int `json:"code"`
            Data struct {
                List  []interface{} `json:"list"`
                Total int          `json:"total"`
            } `json:"data"`
        }
        
        err := json.Unmarshal([]byte(response), &result)
        t.AssertNil(err)
        t.AssertEQ(result.Code, 0)
    })
}
```

## 前端测试规范

### Vue 组件测试
```typescript
// src/modules/system/views/__tests__/user-list.spec.ts
import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { ElTable, ElButton } from 'element-plus'
import UserList from '../user-list.vue'
import { UserApi } from '../../services/user-api'

// Mock API
vi.mock('../../services/user-api', () => ({
  UserApi: {
    list: vi.fn(),
    delete: vi.fn(),
  }
}))

describe('UserList', () => {
  let wrapper: any
  
  beforeEach(() => {
    // Mock API 响应
    vi.mocked(UserApi.list).mockResolvedValue({
      list: [
        { id: 1, username: 'user1', email: '<EMAIL>', status: 1 },
        { id: 2, username: 'user2', email: '<EMAIL>', status: 0 },
      ],
      total: 2
    })
    
    wrapper = mount(UserList, {
      global: {
        components: {
          ElTable,
          ElButton,
        }
      }
    })
  })
  
  it('should render user list correctly', async () => {
    await wrapper.vm.$nextTick()
    
    // 检查表格是否渲染
    expect(wrapper.find('.el-table').exists()).toBe(true)
    
    // 检查数据是否正确显示
    const rows = wrapper.findAll('.el-table__row')
    expect(rows).toHaveLength(2)
    
    // 检查用户名是否正确显示
    expect(wrapper.text()).toContain('user1')
    expect(wrapper.text()).toContain('user2')
  })
  
  it('should handle search correctly', async () => {
    const searchInput = wrapper.find('input[placeholder="请输入用户名"]')
    await searchInput.setValue('user1')
    
    const searchButton = wrapper.find('.search-btn')
    await searchButton.trigger('click')
    
    // 验证 API 是否被正确调用
    expect(UserApi.list).toHaveBeenCalledWith({
      page: 1,
      size: 20,
      username: 'user1'
    })
  })
  
  it('should handle delete user', async () => {
    vi.mocked(UserApi.delete).mockResolvedValue(undefined)
    
    // 模拟确认删除
    vi.spyOn(window, 'confirm').mockReturnValue(true)
    
    const deleteButton = wrapper.find('.delete-btn')
    await deleteButton.trigger('click')
    
    expect(UserApi.delete).toHaveBeenCalledWith(1)
  })
  
  it('should handle pagination', async () => {
    const pagination = wrapper.findComponent({ name: 'ElPagination' })
    
    // 模拟翻页
    await pagination.vm.$emit('current-change', 2)
    
    expect(UserApi.list).toHaveBeenCalledWith({
      page: 2,
      size: 20
    })
  })
})
```

### 组合式函数测试
```typescript
// src/composables/__tests__/use-table.spec.ts
import { describe, it, expect } from 'vitest'
import { useTable } from '../use-table'

describe('useTable', () => {
  it('should initialize with default values', () => {
    const { tableData, loading, pagination } = useTable()
    
    expect(tableData.value).toEqual([])
    expect(loading.value).toBe(false)
    expect(pagination.value).toEqual({
      page: 1,
      size: 20,
      total: 0
    })
  })
  
  it('should update pagination correctly', () => {
    const { pagination, updatePagination } = useTable()
    
    updatePagination({ page: 2, total: 100 })
    
    expect(pagination.value.page).toBe(2)
    expect(pagination.value.total).toBe(100)
  })
})
```

### API 服务测试
```typescript
// src/services/__tests__/user-api.spec.ts
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { UserApi } from '../user-api'
import { http } from '../../utils/http'

// Mock http 模块
vi.mock('../../utils/http', () => ({
  http: {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    delete: vi.fn(),
  }
}))

describe('UserApi', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })
  
  it('should call list API correctly', async () => {
    const mockResponse = {
      list: [{ id: 1, username: 'test' }],
      total: 1
    }
    
    vi.mocked(http.get).mockResolvedValue(mockResponse)
    
    const params = { page: 1, size: 20, username: 'test' }
    const result = await UserApi.list(params)
    
    expect(http.get).toHaveBeenCalledWith('/api/v1/users', { params })
    expect(result).toEqual(mockResponse)
  })
  
  it('should call create API correctly', async () => {
    const mockResponse = { id: 1 }
    const userData = {
      username: 'newuser',
      email: '<EMAIL>',
      password: 'password123',
      status: 1
    }
    
    vi.mocked(http.post).mockResolvedValue(mockResponse)
    
    const result = await UserApi.create(userData)
    
    expect(http.post).toHaveBeenCalledWith('/api/v1/users', userData)
    expect(result).toEqual(mockResponse)
  })
})
```

## 测试工具配置

### Vitest 配置
```typescript
// vitest.config.ts
import { defineConfig } from 'vitest/config'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['./src/test/setup.ts'],
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        'src/test/',
        '**/*.d.ts',
        '**/*.config.*',
      ],
      thresholds: {
        global: {
          branches: 60,
          functions: 60,
          lines: 60,
          statements: 60
        }
      }
    }
  },
  resolve: {
    alias: {
      '/@': resolve(__dirname, 'src'),
    }
  }
})
```

### 测试环境配置
```typescript
// src/test/setup.ts
import { config } from '@vue/test-utils'
import ElementPlus from 'element-plus'

// 全局配置 Vue Test Utils
config.global.plugins = [ElementPlus]

// Mock localStorage
Object.defineProperty(window, 'localStorage', {
  value: {
    getItem: vi.fn(),
    setItem: vi.fn(),
    removeItem: vi.fn(),
    clear: vi.fn(),
  },
  writable: true,
})

// Mock window.confirm
Object.defineProperty(window, 'confirm', {
  value: vi.fn(() => true),
  writable: true,
})
```

## 测试数据管理

### 测试数据工厂
```go
// internal/test/factory/user.go
package factory

import (
    "github.com/imhuso/lookah-erp/admin/internal/model"
    "github.com/imhuso/lookah-erp/admin/internal/model/entity"
)

// UserFactory 用户测试数据工厂
type UserFactory struct{}

func NewUserFactory() *UserFactory {
    return &UserFactory{}
}

// CreateUser 创建测试用户
func (f *UserFactory) CreateUser(overrides ...*entity.User) *entity.User {
    user := &entity.User{
        Username: "testuser",
        Email:    "<EMAIL>",
        Password: "hashedpassword",
        RealName: "Test User",
        Phone:    "13800138000",
        Status:   1,
    }
    
    // 应用覆盖参数
    for _, override := range overrides {
        if override.Username != "" {
            user.Username = override.Username
        }
        if override.Email != "" {
            user.Email = override.Email
        }
        // ... 其他字段
    }
    
    return user
}

// CreateUserInput 创建用户输入数据
func (f *UserFactory) CreateUserInput(overrides ...*model.UserCreateInput) *model.UserCreateInput {
    input := &model.UserCreateInput{
        Username: "testuser",
        Email:    "<EMAIL>",
        Password: "password123",
        Status:   1,
    }
    
    for _, override := range overrides {
        if override.Username != "" {
            input.Username = override.Username
        }
        // ... 其他字段
    }
    
    return input
}
```

### 前端测试数据
```typescript
// src/test/fixtures/user.ts
export const mockUsers = [
  {
    id: 1,
    username: 'admin',
    email: '<EMAIL>',
    realName: '管理员',
    status: 1,
    createdAt: '2024-01-01T00:00:00Z'
  },
  {
    id: 2,
    username: 'user1',
    email: '<EMAIL>',
    realName: '用户1',
    status: 1,
    createdAt: '2024-01-02T00:00:00Z'
  }
]

export const createMockUser = (overrides: Partial<typeof mockUsers[0]> = {}) => ({
  ...mockUsers[0],
  ...overrides
})
```

## 性能测试

### Go 基准测试
```go
// internal/service/user_benchmark_test.go
package service_test

import (
    "context"
    "testing"
    
    "github.com/imhuso/lookah-erp/admin/internal/service"
    "github.com/imhuso/lookah-erp/admin/internal/model"
)

func BenchmarkUserService_List(b *testing.B) {
    ctx := context.Background()
    input := &model.UserListInput{
        Page: 1,
        Size: 20,
    }
    
    b.ResetTimer()
    for i := 0; i < b.N; i++ {
        _, err := service.User().List(ctx, input)
        if err != nil {
            b.Fatal(err)
        }
    }
}

func BenchmarkUserService_GetById(b *testing.B) {
    ctx := context.Background()
    
    b.ResetTimer()
    for i := 0; i < b.N; i++ {
        _, err := service.User().GetById(ctx, 1)
        if err != nil {
            b.Fatal(err)
        }
    }
}
```

## 测试执行规范

### 测试命令
```bash
# 后端测试
cd admin

# 运行所有测试
go test ./...

# 运行特定包的测试
go test ./internal/service

# 运行测试并生成覆盖率报告
go test -cover ./...
go test -coverprofile=coverage.out ./...
go tool cover -html=coverage.out

# 运行基准测试
go test -bench=. ./...

# 前端测试
cd admin-web

# 运行所有测试
npm run test

# 运行测试并生成覆盖率报告
npm run test:coverage

# 运行特定测试文件
npm run test user-list.spec.ts

# 监听模式运行测试
npm run test:watch
```

### CI/CD 集成
```yaml
# .github/workflows/test.yml
name: Test

on: [push, pull_request]

jobs:
  backend-test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-go@v3
        with:
          go-version: '1.21'
      
      - name: Run tests
        run: |
          cd admin
          go test -v -cover ./...
  
  frontend-test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      
      - name: Install dependencies
        run: |
          cd admin-web
          npm ci
      
      - name: Run tests
        run: |
          cd admin-web
          npm run test:coverage
```