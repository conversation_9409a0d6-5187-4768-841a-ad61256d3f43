import{k as oe,g as ae,s as K}from"./index-DkYL1aws.js";import{c as D,b as h,A as R,f as $,y as _,q,B as z,h as e,i as n,w as t,J as se,j as v,t as d,v as P,m as ne,E as H,o as r,af as W,ag as X,z as re,e as G,F as J,s as ie}from"./.pnpm-hVqhwuVC.js";import{_ as Y}from"./material-drawing.vue_vue_type_script_setup_true_name_pms-material-drawing_lang-Dep4YP4I.js";import{u as ue,_ as ce}from"./bom-DOuAfwoo.js";import{_ as Z}from"./material-table-columns.vue_vue_type_script_setup_true_lang-BFX8eStE.js";import{_ as ee}from"./_plugin-vue_export-helper-DlAUqK2U.js";const L=m=>(W("data-v-51e8ab27"),m=m(),X(),m),de={mt2:"","w-full":""},me=L(()=>_("div",{class:"cell-item"}," 产品SKU ",-1)),pe=L(()=>_("div",{class:"cell-item"}," 产品名称 ",-1)),_e=L(()=>_("div",{class:"cell-item"}," 产品颜色 ",-1)),fe=L(()=>_("div",{class:"cell-item"}," BOM版本 ",-1)),ve=L(()=>_("div",{class:"cell-item"}," BOM最后新时间 ",-1)),he={flex:"~ items-center justify-between"},be=D({name:"undefined"}),ye=D({...be,props:{bomId:{type:Number,required:!1},bom:{type:Object,required:!1},showOnlyMyMaterial:{type:Boolean,required:!1}},setup(m){const i=m,{user:p}=oe(),w=h({}),u=h(),a=h([]),M=h(!0),S=h(!1),y=h(),B=h(!1),{getAllUsedColors:x,getBomMaterialCellStyle:b,getBomMaterialRowStyle:N}=ue(w);function C(f){const c=B.value;f>0&&K.pms.bom.GetBomById({id:f,mine:c}).then(o=>{var g;u.value=o,a.value=((g=u.value)==null?void 0:g.materials)||[],w.value[o.id]=o==null?void 0:o.changeLog}).catch(o=>{H.error(o.message||"获取BOM数据失败")})}function T(){B.value=!B.value}function U(f){y.value=f,S.value=!0}const{dict:A}=ae(),E=A.get("color");return R(()=>{var f,c,o,g;if(i.bomId)C(i.bomId);else if(i.bom){u.value=i.bom;const s=(f=i.bom)==null?void 0:f.id,O=B.value,I=((c=p.info)==null?void 0:c.id)||0;a.value=((o=u.value)==null?void 0:o.materials.filter(V=>O?V.bindUserId===I:!0))||[],w.value[s]=(g=u.value)==null?void 0:g.changeLog}}),(f,c)=>{var Q;const o=n("el-descriptions-item"),g=n("el-descriptions"),s=n("el-button"),O=n("cl-flex1"),I=n("el-table-column"),V=n("el-table"),te=n("cl-dialog");return r(),$("div",null,[_("div",de,[e(g,{class:"margin-top",column:3,size:"small",border:""},{default:t(()=>[e(o,null,{label:t(()=>[me]),default:t(()=>{var l,k;return[v(" "+d(((k=(l=u.value)==null?void 0:l.product)==null?void 0:k.sku)||"-"),1)]}),_:1}),e(o,null,{label:t(()=>[pe]),default:t(()=>{var l,k;return[v(" "+d(((k=(l=u.value)==null?void 0:l.product)==null?void 0:k.name)||"-"),1)]}),_:1}),e(o,null,{label:t(()=>[_e]),default:t(()=>{var l,k;return[v(" "+d(((k=(l=P(E))==null?void 0:l.find(le=>{var j,F;return le.value===Number.parseInt((F=(j=u.value)==null?void 0:j.product)==null?void 0:F.color)}))==null?void 0:k.label)||"-"),1)]}),_:1}),e(o,null,{label:t(()=>[fe]),default:t(()=>{var l;return[v(" v"+d(((l=u.value)==null?void 0:l.version)||"-"),1)]}),_:1}),se(f.$slots,"default",{},void 0,!0),e(o,null,{label:t(()=>[ve]),default:t(()=>{var l;return[v(" "+d((l=u.value)==null?void 0:l.lastUpdateTime),1)]}),_:1})]),_:3})]),_("div",he,[_("div",null,[i.bomId?(r(),q(s,{key:0,size:"small",onClick:c[0]||(c[0]=l=>C(i.bomId))},{default:t(()=>[v(" 刷新 ")]),_:1})):z("",!0),i.showOnlyMyMaterial===!1?(r(),q(s,{key:1,size:"small",type:B.value?"success":"default",onClick:T},{default:t(()=>[v(d(B.value?"显示全部物料":"仅显示与我相关的物料"),1)]),_:1},8,["type"])):z("",!0)]),e(O),_("div",null,[e(ce,{modelValue:M.value,"onUpdate:modelValue":c[1]||(c[1]=l=>M.value=l),flex:"~ justify-end",colors:P(x)()},null,8,["modelValue","colors"])])]),a.value?(r(),q(V,ne({key:0,data:a.value,"max-height":"400px","row-style":M.value?P(N):()=>{},"cell-style":P(b),border:"",size:"small",style:{width:"100%"},class:"bom-material-table"},f.$attrs),{default:t(()=>[e(Z,{"auto-width":""}),e(I,{prop:"unit",label:"单位",width:"80",align:"center"}),e(I,{prop:"quantity",label:"用量",width:"80",align:"center"}),e(I,{label:"图纸",width:"100",align:"center"},{default:t(({row:l})=>[e(s,{type:"primary",text:"",bg:"",size:"small",onClick:k=>U(l)},{default:t(()=>[v(" 查看图纸 ")]),_:2},1032,["onClick"])]),_:1})]),_:1},16,["data","row-style","cell-style"])):z("",!0),e(te,{modelValue:S.value,"onUpdate:modelValue":c[2]||(c[2]=l=>S.value=l),controls:["close"],title:`图纸管理 - ${(Q=y.value)==null?void 0:Q.code}`,width:"60%",height:"400","close-on-click-modal":!1},{default:t(()=>[e(Y,{material:y.value,readonly:""},null,8,["material"])]),_:1},8,["modelValue","title"])])}}}),ge=ee(ye,[["__scopeId","data-v-51e8ab27"]]),we=D({name:"undefined"}),ke=D({...we,props:{summary:{type:Array,required:!1}},setup(m){const i=m,p=h(i.summary);return re(()=>i.summary,w=>{p.value=w}),(w,u)=>{const a=n("el-table-column"),M=n("el-table");return r(),q(M,{data:P(p),stripe:"",border:"",style:{width:"100%"}},{default:t(()=>[e(a,{prop:"code",label:"物料编码",width:"150",align:"left","show-overflow-tooltip":""}),e(a,{prop:"name",label:"名称",align:"left","show-overflow-tooltip":""}),e(a,{prop:"model",label:"型号","show-overflow-tooltip":""}),e(a,{prop:"purchaseTotal",label:"需求数量",align:"center",width:"100","show-overflow-tooltip":""}),e(a,{prop:"deductInventory",label:"抵扣数量",align:"center",width:"100","show-overflow-tooltip":""}),e(a,{prop:"prepareQuantity",label:"备货数量",align:"center",width:"100","show-overflow-tooltip":""}),e(a,{prop:"total",label:"采购数量",align:"center",width:"100","show-overflow-tooltip":""})]),_:1},8,["data"])}}}),$e=m=>(W("data-v-d185df23"),m=m(),X(),m),Be={key:0,"max-h800px":"","overflow-y-scroll":"",py2:""},xe={flex:"~ justify-between items-center","w-full":""},Ie={key:0},Me={key:0},Se={key:1},Ce={"p-l-2":"","p-t-2":"","text-green-6":"","font-600":""},Oe={key:1},Ve=$e(()=>_("p",{my2:"","text-green-6":"","font-600":""}," 采购需求物料列表 ",-1)),qe=D({name:"undefined"}),ze=D({...qe,props:{orderId:{type:Number,required:!0},production:{type:Boolean,required:!1}},setup(m){const i=m,p=h(),w=h(),u=h(!1),a=G(()=>i.production);function M(x){x&&K.pms.purchase.order.auditData({orderId:x,isProduction:a.value}).then(b=>{p.value=b}).catch(b=>{H.error(b.message||"获取采购订单信息失败")})}const S=G(()=>{var x,b;return(b=(x=p.value)==null?void 0:x.objects)==null?void 0:b.some(N=>N.generateType===1)}),y=h(!1);function B(){y.value=!y.value}return R(()=>{M(i.orderId)}),(x,b)=>{var c,o,g;const N=n("el-button"),C=n("el-descriptions-item"),T=n("el-descriptions"),U=n("el-table-column"),A=n("el-table"),E=n("el-empty"),f=n("cl-dialog");return r(),$(J,null,[p.value?(r(),$("div",Be,[e(T,{size:"small",title:`${a.value?"采购需求":"采购订单"}信息`,column:3,border:"",class:"purchase-order-info"},{title:t(()=>[_("div",xe,[_("div",null,d(`${a.value?"采购需求":"采购订单"}信息`),1),S.value?(r(),q(N,{key:0,type:y.value?"primary":"warning",onClick:B},{default:t(()=>[v(d(y.value?"查看BOM数据":"查看汇总数据"),1)]),_:1},8,["type"])):z("",!0)])]),default:t(()=>[e(C,{label:"订单号",align:"center"},{default:t(()=>[v(d(p.value.orderNo),1)]),_:1}),e(C,{label:"创建时间",align:"center"},{default:t(()=>[v(d(p.value.createTime),1)]),_:1})]),_:1},8,["title"]),S.value?(r(),$("div",Ie,[y.value?(r(),$("div",Me,[e(ke,{mt1:"","max-height":"500",summary:p.value.summary},null,8,["summary"])])):z("",!0),y.value?z("",!0):(r(),$("div",Se,[(r(!0),$(J,null,ie((o=(c=p.value)==null?void 0:c.objects)==null?void 0:o.filter(s=>s.generateType===1),s=>{var O,I,V;return r(),$("div",{key:s.generateId,class:"mt2 b-1px b-gray-400 b-dashed"},[_("p",Ce,d((O=s.bom.product)==null?void 0:O.name)+" - "+d((V=(I=s.bom)==null?void 0:I.product)==null?void 0:V.sku)+" * "+d(s.generateQuantity),1),e(ge,{bom:s.bom,"max-height":300},{default:t(()=>[e(C,{label:"采购套数"},{default:t(()=>[v(d(s.generateQuantity),1)]),_:2},1024)]),_:2},1032,["bom"])])}),128))]))])):(r(),$("div",Oe,[Ve,e(A,{data:p.value.objects,"max-height":"600",stripe:"",border:"",size:"small"},{default:t(()=>[e(Z,{"auto-width":""}),e(U,{prop:"generateQuantity",label:"数量",align:"center",width:"100","show-overflow-tooltip":""})]),_:1},8,["data"])]))])):(r(),q(E,{key:1,description:"暂未获取到采购信息"})),e(f,{modelValue:u.value,"onUpdate:modelValue":b[0]||(b[0]=s=>u.value=s),controls:["close"],title:`图纸管理 - ${(g=w.value)==null?void 0:g.code}`,width:"60%",height:"400","close-on-click-modal":!1},{default:t(()=>[e(Y,{material:w.value,readonly:""},null,8,["material"])]),_:1},8,["modelValue","title"])],64)}}}),Ae=ee(ze,[["__scopeId","data-v-d185df23"]]);export{ge as B,Ae as P};
