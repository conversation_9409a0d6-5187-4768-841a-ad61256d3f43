import{s as ue,i as X,d as de}from"./index-DkYL1aws.js";import{c as N,b as g,A as ce,f as _,h as e,y as U,i as o,w as t,j as m,t as y,E as h,o as d,q as k,B as p,U as pe}from"./.pnpm-hVqhwuVC.js";import{a as me}from"./index-C6cm1h61.js";import{B as _e,P as Z}from"./purchase-order-info-BOLLQIIm.js";import{n as fe}from"./index-CBanFtSc.js";import ve from"./AuditView-DX2XA32i.js";import{_ as be}from"./AuditMaterialOutboundView.vue_vue_type_script_setup_true_name_AuditMaterialOutboundView_lang-BnWJm06M.js";import{_ as we}from"./AuditLogTable.vue_vue_type_script_setup_true_name_AuditLogTable_lang-I6kBwh-b.js";import"./material-drawing.vue_vue_type_script_setup_true_name_pms-material-drawing_lang-Dep4YP4I.js";/* empty css              */import"./bom-DOuAfwoo.js";import"./material-table-columns.vue_vue_type_script_setup_true_lang-BFX8eStE.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-BFVs8cCE.js";import"./constant-C2dsBPRR.js";const he={mt2:""},ge={mt2:""},ye=N({name:"undefined"}),ke=N({...ye,props:{orderId:{type:Number,required:!0}},setup(E){const V=E,c=g();function x(n){if(!n)return h.error("订单数据异常");ue.pms.purchase.order.info({id:n}).then(u=>{c.value=u}).catch(u=>{h.error(u.message||"获取采购订单信息失败")})}function r(){return c.value?c.value.contracts.reduce((n,u)=>n+u.quantity,0):0}function f(){if(!c.value)return 0;const n=c.value.contracts.reduce((u,v)=>u+v.subtotal,0);return fe(n)}return ce(()=>{x(V.orderId)}),(n,u)=>{var I;const v=o("el-descriptions-item"),P=o("el-descriptions"),s=o("el-table-column"),$=o("cl-date-text"),q=o("el-table");return d(),_("div",he,[e(P,{size:"small",title:"采购订单清单",column:2,border:""},{default:t(()=>[e(v,{label:"订单号",align:"center"},{default:t(()=>{var a;return[m(y(((a=c.value)==null?void 0:a.orderNo)||"-"),1)]}),_:1}),e(v,{label:"创建时间",align:"center"},{default:t(()=>{var a;return[m(y(((a=c.value)==null?void 0:a.createTime)||"-"),1)]}),_:1}),e(v,{label:"总采购数量",align:"center"},{default:t(()=>[m(y(r()),1)]),_:1}),e(v,{label:"总采购金额",align:"center"},{default:t(()=>[m(y(f()),1)]),_:1})]),_:1}),U("div",ge,[e(q,{data:(I=c.value)==null?void 0:I.contracts,border:"",size:"small","max-height":"702"},{default:t(()=>[e(s,{prop:"material.code",label:"物料编码",align:"left",width:"150","show-overflow-tooltip":""}),e(s,{prop:"material.name",label:"物料名称",align:"left",width:"150","show-overflow-tooltip":""}),e(s,{prop:"material.model",label:"物料型号",align:"left","min-width":"150","show-overflow-tooltip":""}),e(s,{prop:"material.size",label:"物料尺寸",align:"left",width:"150","show-overflow-tooltip":""}),e(s,{prop:"material.material",label:"物料材质",align:"left",width:"100","show-overflow-tooltip":""}),e(s,{prop:"material.process",label:"物料工艺",align:"left",width:"100","show-overflow-tooltip":""}),e(s,{prop:"material.coverColor",label:"物料颜色",align:"left",width:"100","show-overflow-tooltip":""}),e(s,{prop:"unitPrice",label:"含税单价",align:"center",width:"90","show-overflow-tooltip":""}),e(s,{prop:"quantity",label:"采购数量",align:"center",width:"100","show-overflow-tooltip":""}),e(s,{prop:"material.unit",label:"单位",align:"center",width:"70","show-overflow-tooltip":""}),e(s,{prop:"subtotal",label:"小记",align:"center",width:"90","show-overflow-tooltip":""},{default:t(({row:a})=>[m(y(a.subtotal.toFixed(2)),1)]),_:1}),e(s,{prop:"taxRate",label:"税率",align:"center",width:"60","show-overflow-tooltip":""},{default:t(({row:a})=>[m(y(a.taxRate)+"% ",1)]),_:1}),e(s,{prop:"supplierName",label:"供应商",align:"center",width:"140","show-overflow-tooltip":""}),e(s,{prop:"deliveryDate",label:"交货日期",align:"center",width:"100","show-overflow-tooltip":""},{default:t(({row:a})=>[e($,{"model-value":a.deliveryDate,format:"YYYY-MM-DD"},null,8,["model-value"])]),_:1})]),_:1},8,["data"])])])}}}),Ve={pt3:""},xe={key:0},Ie={key:0},Ce={key:1},De={key:2},Te={key:3},Ne={key:4},Pe={key:1},$e={key:0},qe={flex:"~ justify-end","mt-2":""},Be=N({name:"pms-product-group"}),Je=N({...Be,setup(E){const{service:V}=me(),c=g(!1),x=g(!1),r=g(),f=g({dataId:0}),n=g(0),u=g({result:void 0,remark:""}),v=[{label:"人员",value:0,type:"success"},{label:"部门",value:1,type:"warning"},{label:"角色",value:2,type:"danger"}],P=X.useTable({columns:[{label:"审批名称",prop:"process.name",showOverflowTooltip:!0},{label:"关联单号",prop:"no",width:220},{label:"类型",prop:"category",width:160},{label:"审批节点",prop:"nodeName"},{label:"审批对象类型",prop:"nodeType",dict:v},{label:"当前审批对象",prop:"displayName"},{label:"提交人",prop:"audit.operatorName"},{label:"提交时间",prop:"audit.createTime",width:180},{label:"状态",width:180,prop:"status",dict:[{label:"等待他人处理",value:0,type:"danger"},{label:"等待处理",value:1,type:"warning"},{label:"已处理",value:2,type:"success"}]},{type:"op",hidden:!de(V.pms.audit.processor.node.permission.deal),width:100,buttons:["slot-btn-process"]}]}),s=X.useCrud({service:V.pms.audit.processor.node},a=>{a.refresh()});function $(a){const{id:l}=a;if(!l)return h.warning("当前数据不存在");c.value=!0,r.value=a,f.value=pe(a)}function q(){var C;const a=(C=r.value)==null?void 0:C.id;if(!a)return h.warning("当前数据不存在");const{result:l,remark:D}=u.value;if(l!=="0"&&l!=="1")return h.warning("请选择审批结果");if(!D)return h.warning("请输入备注");x.value=!0,V.pms.audit.processor.node.deal({id:a,result:l,remark:D}).then(()=>{var b;(b=s.value)==null||b.refresh(),h.success("处理成功"),I()}).catch(b=>{h.error(b.message||"处理失败")}).finally(()=>{x.value=!1})}function I(){c.value=!1,r.value=void 0,n.value=0,u.value={result:void 0,remark:""},f.value={dataId:0}}return(a,l)=>{const D=o("cl-refresh-btn"),C=o("cl-flex1"),b=o("el-row"),T=o("el-button"),ee=o("cl-table"),te=o("cl-pagination"),B=o("el-step"),le=o("el-steps"),z=o("el-tab-pane"),oe=o("el-tabs"),A=o("el-radio"),ae=o("el-radio-group"),M=o("el-form-item"),re=o("el-input"),ne=o("el-form"),se=o("cl-dialog"),ie=o("cl-crud");return d(),k(ie,{ref_key:"Crud",ref:s},{default:t(()=>[e(b,null,{default:t(()=>[e(D),e(C)]),_:1}),e(b,null,{default:t(()=>[e(ee,{ref_key:"Table",ref:P},{"slot-btn-process":t(({scope:w})=>[w.row.audit?(d(),k(T,{key:0,type:"success",disabled:w.row.status!==1,text:"",bg:"",onClick:O=>$(w.row)},{default:t(()=>[m(" 处理 ")]),_:2},1032,["disabled","onClick"])):p("",!0)]),_:1},512)]),_:1}),e(b,null,{default:t(()=>[e(C),e(te)]),_:1}),e(se,{modelValue:c.value,"onUpdate:modelValue":l[7]||(l[7]=w=>c.value=w),width:"75%","lock-scroll":"",title:"处理审批","min-w1000px":"",controls:["close"],"close-on-click-modal":!1,"close-on-press-escape":!1,onClose:I},{default:t(()=>{var w,O,R,S,Y,j,F,L,Q,G,H,J,K,W;return[e(le,{active:n.value,"finish-status":"success","align-center":""},{default:t(()=>[e(B,{title:"核对数据",description:"请确认是否正确"}),e(B,{title:"处理审批",description:"请处理审批"},{default:t(()=>[m(" 处理 ")]),_:1}),e(B,{title:"完成",description:"处理完成"})]),_:1},8,["active"]),U("div",Ve,[n.value===0?(d(),_("div",xe,[((O=(w=r.value)==null?void 0:w.process)==null?void 0:O.key)==="bom"?(d(),_("div",Ie,[e(_e,{"bom-id":(R=r.value)==null?void 0:R.dataId},null,8,["bom-id"])])):p("",!0),((Y=(S=r.value)==null?void 0:S.process)==null?void 0:Y.key)==="productionPurchaseOrder"?(d(),_("div",Ce,[e(Z,{"order-id":(j=r.value)==null?void 0:j.dataId,production:""},null,8,["order-id"])])):p("",!0),((L=(F=r.value)==null?void 0:F.process)==null?void 0:L.key)==="purchase"?(d(),_("div",De,[e(oe,{"tab-position":"right",stretch:""},{default:t(()=>[e(z,{label:"BOM信息"},{default:t(()=>{var i;return[e(Z,{"order-id":(i=r.value)==null?void 0:i.dataId},null,8,["order-id"])]}),_:1}),e(z,{label:"订单信息"},{default:t(()=>{var i;return[e(ke,{"order-id":(i=r.value)==null?void 0:i.dataId},null,8,["order-id"])]}),_:1})]),_:1})])):p("",!0),((G=(Q=r.value)==null?void 0:Q.process)==null?void 0:G.key)==="materialInbound"?(d(),_("div",Te,[e(ve,{modelValue:f.value.dataId,"onUpdate:modelValue":l[0]||(l[0]=i=>f.value.dataId=i)},null,8,["modelValue"])])):p("",!0),((J=(H=r.value)==null?void 0:H.process)==null?void 0:J.key)==="materialOutbound"?(d(),_("div",Ne,[e(be,{modelValue:f.value.dataId,"onUpdate:modelValue":l[1]||(l[1]=i=>f.value.dataId=i)},null,8,["modelValue"])])):p("",!0)])):p("",!0),n.value===1?(d(),_("div",Pe,[e(ne,{model:u.value},{default:t(()=>[e(M,{label:"审批结果",prop:"result",required:"",rules:[{required:!0,message:"请选择审批结果",trigger:"change"}]},{default:t(()=>[e(ae,{modelValue:u.value.result,"onUpdate:modelValue":l[2]||(l[2]=i=>u.value.result=i)},{default:t(()=>[e(A,{label:"1"},{default:t(()=>[m(" 通过 ")]),_:1}),e(A,{label:"0"},{default:t(()=>[m(" 驳回 ")]),_:1})]),_:1},8,["modelValue"])]),_:1}),e(M,{label:"备注",prop:"remark",required:"",rules:[{required:!0,message:"请输入备注",trigger:"blur"}]},{default:t(()=>[e(re,{modelValue:u.value.remark,"onUpdate:modelValue":l[3]||(l[3]=i=>u.value.remark=i),type:"textarea",autosize:{minRows:1,maxRows:3},maxlength:"200","show-word-limit":""},null,8,["modelValue"])]),_:1})]),_:1},8,["model"]),(K=r.value)!=null&&K.process&&["materialOutbound","materialInbound"].includes(r.value.process.key)?(d(),_("div",$e,[(W=r.value)!=null&&W.dataId?(d(),k(we,{key:0,modelValue:r.value.dataId,"onUpdate:modelValue":l[4]||(l[4]=i=>r.value.dataId=i)},null,8,["modelValue"])):p("",!0)])):p("",!0)])):p("",!0)]),U("div",qe,[n.value===1?(d(),k(T,{key:0,type:"primary",onClick:l[5]||(l[5]=i=>n.value--)},{default:t(()=>[m(" 上一步 ")]),_:1})):p("",!0),n.value===0?(d(),k(T,{key:1,type:"success",onClick:l[6]||(l[6]=i=>n.value++)},{default:t(()=>[m(" 下一步 ")]),_:1})):p("",!0),n.value===1?(d(),k(T,{key:2,type:"success",loading:x.value,onClick:q},{default:t(()=>[m(" 提交 ")]),_:1},8,["loading"])):p("",!0)])]}),_:1},8,["modelValue"])]),_:1},512)}}});export{Je as default};
