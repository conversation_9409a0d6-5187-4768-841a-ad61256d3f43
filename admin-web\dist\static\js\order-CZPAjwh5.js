import{c as ne,b as _,e as O,z as ae,A as Be,q as u,w as l,h as n,G as z,B as b,i as h,H as $e,v as m,j as w,f as Y,s as We,F as Ne,y as f,t as k,Y as M,E as v,o as i,T as re}from"./.pnpm-hVqhwuVC.js";import{g as Ee,i as K,j as le,e as U}from"./index-BtOcqcNl.js";import{u as Re}from"./table-ops-mcGHjph4.js";/* empty css              */import{a as oe}from"./index-D95m1iJL.js";import{statusList as Le}from"./dict-D_Vssv3j.js";const qe={key:0},Ve={key:1},Ae={key:0},ze={key:1},Ke={key:1},Ue={key:0},je={key:1},He={class:"cl-table__expand-footer"},Qe={class:"table-summary-container"},Ge=f("span",{class:"cl-table__expand-footer-title"},"订单总数：",-1),Je=f("span",{class:"cl-table__expand-footer-title"},"实际发货：",-1),Xe=ne({name:"sale-orders"}),nt=ne({...Xe,setup(Ze){const{service:p}=oe(),{router:F}=oe(),{dict:j}=Ee(),H=j.get("color"),se=j.get("carrier"),S=_(-1),c=_(),Q=_([]),E=_(Le),ie=_([{label:"空运",value:0,type:"success"},{label:"海运",value:1,type:"primary"}]),G=_({"slot-btn-confirm":{width:80,permission:p.pms.sale.order.permission.confirm,show:O(()=>c.value===0)},"slot-btn-edit":{width:80,permission:p.pms.sale.order.permission.update,show:O(()=>c.value===0)},"slot-btn-delete":{width:80,permission:p.pms.sale.order.permission.delete,show:O(()=>c.value===0)},"slot-btn-export":{width:120,permission:p.pms.sale.order.permission.export},"slot-btn-arrived":{width:90,permission:p.pms.sale.order.permission.arrived,show:O(()=>c.value===21)},"slot-btn-download-attachment":{width:140,permission:p.pms.sale.order.permission.download,show:O(()=>c.value===11)},"slot-btn-download-signature":{width:110,permission:p.pms.sale.order.permission.downloadSign,show:O(()=>c.value===33)}}),{getOpWidth:de,checkOpButtonIsAvaliable:T,getOpIsHidden:ce}=Re(G),R=_(),pe=_(!1);ae(c,()=>{R.value=de(),pe.value=ce()},{immediate:!0});const J=K.useTable({defaultSort:{prop:"createTime",order:"descending"},columns:[{type:"selection",width:50},{label:"#",prop:"products",type:"expand",width:50},{label:"订单号",prop:"orderSn",width:180},{label:"状态",prop:"status",width:120,dict:E},{label:"类型",prop:"type",width:120},{label:"单品数量合计",prop:"total",width:120},{label:"下单日期",prop:"createTime",width:180,sortable:!0},{label:"要求出货日期",prop:"requiredShipDate",width:150,sortable:!0,sortBy:"required_ship_date",component:{name:"cl-date-text",props:{format:"YYYY-MM-DD"}}},{label:"实际发货日期",prop:"shipDate",width:150,component:{name:"cl-date-text",props:{format:"YYYY-MM-DD"}}},{label:"出货点",prop:"sourcePoint.name",width:150,showOverflowTooltip:!0},{label:"收货点",prop:"destinationPoint.name",width:150,showOverflowTooltip:!0},{label:"货代",prop:"forwarder.name",width:200,showOverflowTooltip:!0},{label:"发货信息",children:[{label:"入仓号",prop:"receiptNumber",width:120,showOverflowTooltip:!0},{label:"送货方式",prop:"carrier",width:120,showOverflowTooltip:!0},{label:"车牌号",prop:"dataWarehouse.truckPlate",width:120,showOverflowTooltip:!0},{label:"司机电话",prop:"dataWarehouse.driversContact",width:120,showOverflowTooltip:!0},{label:"送货费",prop:"dataWarehouse.deliveryFee",width:120,showOverflowTooltip:!0},{label:"备注",width:200,prop:"dataWarehouse.remarks",showOverflowTooltip:!0}]},{label:"货运信息",children:[{label:"货运方式",prop:"shipBy",width:120},{label:"货运参考号",prop:"dataForwarder.forwarderRef",width:120},{label:"空运提单号",prop:"dataForwarder.airway",width:150},{label:"是否报关",prop:"customsDeclaration",width:120},{label:"开船/起飞日期",prop:"departDate",width:120},{label:"预计到港日期",prop:"arrivalDate",width:120},{label:"货物计费重量",prop:"cargoWeight",width:120,formatter(t){var e,o;return(e=t.dataForwarder)!=null&&e.cargoWeight?`${(o=t.dataForwarder)==null?void 0:o.cargoWeight}KG`:""}},{label:"空运/海运单价",prop:"dataForwarder.shippingRate",width:120}]},{label:"清关信息",children:[{label:"卡车提货日期",prop:"pickupDate",width:120,align:"center"},{label:"预计派送日期",prop:"etd",width:120,align:"center"},{label:"派送地址",prop:"dataClearance.deliverTo",width:150,align:"center"},{label:"卡车追踪号",prop:"dataClearance.pro",width:150,align:"center"},{label:"卡车追踪网站",prop:"dataClearance.trackingWebsite",width:150,align:"center"},{label:"签收日期",prop:"delDate",width:120,align:"center",component:{name:"cl-date-text",props:{format:"YYYY-MM-DD"}}},{label:"当地清关费",prop:"dataClearance.customsClearanceFee",width:100,align:"center"},{label:"关税",prop:"dataClearance.tax",width:100,align:"center"},{label:"卡车派送费",prop:"dataClearance.truckingCharge",width:100,align:"center"},{label:"仓储费",prop:"dataClearance.storageFee",width:100,align:"center"},{label:"托盘费",prop:"dataClearance.palletFee",width:100,align:"center"},{label:"查验费",prop:"dataClearance.inspectionFee",width:100,align:"center"},{label:"港口费",prop:"dataClearance.terminalFee",width:100,align:"center"},{label:"通讯费",prop:"dataClearance.messageFee",width:100,align:"center"},{label:"周末卡车费",prop:"dataClearance.weekendTruckingFee",width:100,align:"center"},{label:"卡车等待费",prop:"dataClearance.truckingChargeFee",width:100,align:"center"},{label:"机场转运费",prop:"dataClearance.airportTransferFee",width:100,align:"center"},{label:"操作费",prop:"dataClearance.handlingFee",width:100,align:"center"}]},{type:"op",label:"操作",width:R,hidden:O(()=>R.value===0),buttons:Object.keys(G.value)}],contextMenu:["refresh","order-desc","order-asc"]}),W=K.useCrud({service:p.pms.sale.order,async onRefresh(t,{next:e,render:o}){const{count:s,list:g,pagination:C}=await e(t);E.value.forEach(D=>{D.count=s[D.value]||0}),o(g,C)}},t=>{t.refresh({status:c})});function ue({row:t}){var e,o,s,g;if(c.value===0||c.value===1||c.value===2)return(((o=(e=t.pieceProduct)==null?void 0:e.stock)==null?void 0:o.inStock)||0)-(((g=(s=t.pieceProduct)==null?void 0:s.stock)==null?void 0:g.freezeStock)||0)>=t.piece?"success-row":"danger-row"}function he(t){const e=se.value.find(o=>o.value===t);return e==null?void 0:e.label}function me(){F.push("/pms/sale/order/add")}function we(t,e){var o;(e==null?void 0:e.type)==="expand"||(e==null?void 0:e.type)==="op"||(o=J.value)==null||o.toggleRowExpansion(t)}function be(t){return t&&t.length>50?`${t.substring(0,50)}...`:t}function fe(t){if(!t)return!1;p.pms.sale.order.confirm({orderId:t}).then(e=>{v.success("订单提交成功！已转交给产线处理。"),c.value=(e==null?void 0:e.status)||0}).catch(e=>{v.error(e.message)})}function ge(t){var e;t>=0?c.value=t:t===-1&&(c.value=null),S.value=t,(e=W.value)==null||e.refresh()}function ve(t){if(!t)return!1;re.confirm("确认删除订单吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{p.pms.sale.order.delete({ids:[t]}).then(()=>{var e;v.success("订单删除成功"),(e=W.value)==null||e.refresh()}).catch(e=>{v.error(e.message)})}).catch(()=>{})}function _e(t){if(!t)return!1;F.push(`/pms/sale/order/add?orderId=${t}`)}const P=_([]);function X(t){return P.value.includes(t)}const I=_([]);function ke(t){return I.value.includes(t.id)}const B=_([]);function ye(t){return B.value.includes(t.id)}function L(t,e){const o=e==null?void 0:e.id,s={type:t};t===0&&(s.orderId=o,B.value.push(o));const g={url:"/export",method:"POST",data:s,responseType:"blob"};P.value.push(t),p.pms.sale.order.request(g).then(C=>{const D=["出货单","订单汇总表","出货计划汇总表"][t],y=[`出货单-${e==null?void 0:e.orderSn}.xlsx`,"订单汇总表.xlsx","出货计划汇总表.xlsx"][t];U(C,y)&&v.success(`${D}导出成功`);const $=P.value.findIndex(x=>x===t);if($>-1&&P.value.splice($,1),t===0){const x=B.value.findIndex(q=>q===o);x>-1&&B.value.splice(x,1)}}).catch(C=>{v.error(C.message||"导出失败");const D=P.value.findIndex(y=>y===t);if(D>-1&&P.value.splice(D,1),t===0){const y=B.value.findIndex($=>$===o);y>-1&&B.value.splice(y,1)}})}function xe(t){const e=t.id;if(!e)return!1;I.value.push(e),p.pms.sale.order.request({url:"/download",data:{orderId:e},method:"POST",responseType:"blob"}).then(o=>{U(o,`订单-${t.orderSn}-货运附件.zip`)&&v.success("附件下载成功");const s=I.value.findIndex(g=>g===e);s>-1&&I.value.splice(s,1)}).catch(o=>{v.error(o.message);const s=I.value.findIndex(g=>g===e);s>-1&&I.value.splice(s,1)})}function Ce(t){const e=t.id;if(!e)return!1;p.pms.sale.order.request({url:"/downloadSign",data:{orderId:e},method:"POST",responseType:"blob"}).then(o=>{const s=o.type.split("/")[1]||"jpeg";U(o,`签收单-${t.orderSn}.${s}`)&&v.success("签收单下载成功")}).catch(o=>{v.error(o.message)})}function De(t){const e=t.id;if(!e)return!1;re.confirm("确定设置订单已送达？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{p.pms.sale.order.arrived({orderId:e}).then(o=>{v.success("设置订单已送达成功");const s=o==null?void 0:o.status;s>=0&&(S.value=s,s.value=s)}).catch(o=>{v.error(o.message)})}).catch(()=>{})}ae(c,()=>{c.value===void 0||c.value===null?S.value=-1:S.value=c.value}),Be(()=>{const t=F.currentRoute.value.query.tab;if(t)return c.value=Number.parseInt(t.toString()),S.value=Number.parseInt(t.toString()),F.replace({query:{tab:void 0}});const e=F.currentRoute.value.query.expand;e&&(Q.value=[Number.parseInt(e.toString())],F.replace({query:{expand:void 0}}))});const Ye=K.useSearch({items:[{label:"下单时间",prop:"dateRange",props:{labelWidth:"80px"},component:{name:"el-date-picker",props:{type:"daterange","value-format":"YYYY-MM-DD",format:"YYYY-MM-DD",rangeSeparator:"至",startPlaceholder:"开始日期",endPlaceholder:"结束日期",clearable:!0,onChange(t){var e;(e=W.value)==null||e.refresh({dateRange:t})}}}},{label:"订单号",prop:"keyword",props:{labelWidth:"60px"},component:{name:"el-input",props:{placeholder:"输入订单号搜索",clearable:!1,onChange(t){var e;(e=W.value)==null||e.refresh({keyWord:t.trim(),page:1})}}}}]});function Se(t){const e=ie.value.find(o=>o.value===t);return(e==null?void 0:e.label)||""}function Te(t){const e=t.label,o=t.count;return t.value>=0?`${e}(${o})`:e}return(t,e)=>{const o=h("cl-refresh-btn"),s=h("el-button"),g=h("cl-multi-delete-btn"),C=h("cl-flex1"),D=h("cl-search"),y=h("el-row"),$=h("el-tab-pane"),x=h("cl-date-text"),q=h("el-popover"),V=h("el-tag"),d=h("el-table-column"),Oe=h("el-table"),Me=h("cl-table"),Fe=h("cl-pagination"),Pe=h("el-tabs"),Ie=h("cl-crud"),A=$e("permission");return i(),u(Ie,{ref_key:"Crud",ref:W},{default:l(()=>[n(y,null,{default:l(()=>[n(o),z((i(),u(s,{type:"primary",onClick:me},{default:l(()=>[w(" 新建订单 ")]),_:1})),[[A,m(p).pms.sale.order.permission.add]]),z((i(),u(s,{loading:X(2),type:"success",onClick:e[0]||(e[0]=a=>L(2,null))},{default:l(()=>[w(" 导出出货计划汇总表 ")]),_:1},8,["loading"])),[[A,m(p).pms.sale.order.permission.export]]),z((i(),u(s,{type:"warning",loading:X(1),onClick:e[1]||(e[1]=a=>L(1,null))},{default:l(()=>[w(" 导出订单汇总表 ")]),_:1},8,["loading"])),[[A,m(p).pms.sale.order.permission.export]]),c.value===0?(i(),u(g,{key:0})):b("",!0),n(C),n(D,{ref_key:"Search",ref:Ye},null,512)]),_:1}),n(Pe,{modelValue:S.value,"onUpdate:modelValue":e[2]||(e[2]=a=>S.value=a),type:"border-card",onTabChange:ge},{default:l(()=>[(i(!0),Y(Ne,null,We(E.value,a=>(i(),u($,{key:a.value,label:Te(a),name:a.value},null,8,["label","name"]))),128)),n(y,null,{default:l(()=>[n(Me,{ref_key:"Table",ref:J,"row-key":"id","expand-row-keys":Q.value,class:"table-row-pointer",onRowClick:we},{"slot-btn-edit":l(({scope:a})=>[m(T)("slot-btn-edit")?(i(),u(s,{key:0,text:"",bg:"",type:"primary",onClick:M(r=>_e(a.row.id),["stop"])},{default:l(()=>[w(" 编辑 ")]),_:2},1032,["onClick"])):b("",!0)]),"slot-btn-confirm":l(({scope:a})=>[m(T)("slot-btn-confirm")?(i(),u(s,{key:0,text:"",bg:"",type:"success",onClick:M(r=>fe(a.row.id),["stop"])},{default:l(()=>[w(" 提交 ")]),_:2},1032,["onClick"])):b("",!0)]),"slot-btn-delete":l(({scope:a})=>[m(T)("slot-btn-delete")?(i(),u(s,{key:0,text:"",bg:"",type:"danger",onClick:M(r=>ve(a.row.id),["stop"])},{default:l(()=>[w(" 删除 ")]),_:2},1032,["onClick"])):b("",!0)]),"slot-btn-export":l(({scope:a})=>[m(T)("slot-btn-export")?(i(),u(s,{key:0,text:"",bg:"",type:"primary",loading:ye(a.row),onClick:M(r=>L(0,a.row),["stop"])},{default:l(()=>[w(" 导出出货单 ")]),_:2},1032,["loading","onClick"])):b("",!0)]),"slot-btn-download-attachment":l(({scope:a})=>[m(T)("slot-btn-download-attachment")?(i(),u(s,{key:0,text:"",bg:"",type:"warning",loading:ke(a.row),onClick:M(r=>xe(a.row),["stop"])},{default:l(()=>[w(" 下载货运附件 ")]),_:2},1032,["loading","onClick"])):b("",!0)]),"slot-btn-download-signature":l(({scope:a})=>[m(T)("slot-btn-download-signature")?(i(),u(s,{key:0,text:"",bg:"",type:"primary",onClick:M(r=>Ce(a.row),["stop"])},{default:l(()=>[w(" 下载签收单 ")]),_:2},1032,["onClick"])):b("",!0)]),"slot-btn-arrived":l(({scope:a})=>[m(T)("slot-btn-arrived")&&a.row.type===1?(i(),u(s,{key:0,text:"",bg:"",type:"success",onClick:M(r=>De(a.row),["stop"])},{default:l(()=>[w(" 已送达 ")]),_:2},1032,["onClick"])):b("",!0)]),"column-type":l(({scope:a})=>[a.row.type===1?(i(),Y("span",qe,"直派")):b("",!0),a.row.type===0?(i(),Y("span",Ve,"送仓")):b("",!0)]),"column-carrier":l(({scope:a})=>{var r;return[f("span",null,k(he((r=a.row.dataWarehouse)==null?void 0:r.carrier)),1)]}),"column-shipBy":l(({scope:a})=>{var r;return[f("span",null,k(Se((r=a.row.dataForwarder)==null?void 0:r.shipBy)),1)]}),"column-customsDeclaration":l(({scope:a})=>{var r;return[a.row.dataForwarder!==null?(i(),Y("span",Ae,k(((r=a.row.dataForwarder)==null?void 0:r.customsDeclaration)===1?"是":"否"),1)):(i(),Y("span",ze))]}),"column-departDate":l(({scope:a})=>{var r;return[n(x,{"model-value":(r=a.row.dataForwarder)==null?void 0:r.departDate,format:"YYYY-MM-DD"},null,8,["model-value"])]}),"column-arrivalDate":l(({scope:a})=>{var r;return[n(x,{"model-value":(r=a.row.dataForwarder)==null?void 0:r.arrivalDate,format:"YYYY-MM-DD"},null,8,["model-value"])]}),"column-pickupDate":l(({scope:a})=>{var r;return[n(x,{"model-value":(r=a.row.dataClearance)==null?void 0:r.pickupDate,format:"YYYY-MM-DD"},null,8,["model-value"])]}),"column-etd":l(({scope:a})=>{var r;return[n(x,{"model-value":(r=a.row.dataClearance)==null?void 0:r.etd,format:"YYYY-MM-DD"},null,8,["model-value"])]}),"column-delDate":l(({scope:a})=>{var r;return[n(x,{"model-value":(r=a.row.dataClearance)==null?void 0:r.delDate,format:"YYYY-MM-DD"},null,8,["model-value"])]}),"column-remarks":l(({scope:a})=>{var r;return[((r=a.row.dataWarehouse)==null?void 0:r.remarks.length)>50?(i(),u(q,{key:0,placement:"top-start",width:200,trigger:"hover",content:a.row.remarks},{reference:l(()=>[f("span",null,k(be(a.row.dataWarehouse)),1)]),_:2},1032,["content"])):(i(),Y("span",Ke,k(a.row.dataWarehouse),1))]}),"column-products":l(({scope:a})=>[n(Oe,{data:a.row.products,style:{width:"100%"},border:"","row-class-name":ue},{default:l(()=>[n(d,{label:"产品名称",align:"center"},{default:l(()=>[n(d,{prop:"name",label:"单位",width:"100",align:"center"},{default:l(r=>[r.row.unit===0?(i(),u(V,{key:0,type:"info"},{default:l(()=>[w(" 单 品 ")]),_:1})):b("",!0),r.row.unit===1?(i(),u(V,{key:1,type:"warning"},{default:l(()=>[w(" 展示盒 ")]),_:1})):b("",!0),r.row.unit===2?(i(),u(V,{key:2,type:"success"},{default:l(()=>[w(" 箱 装 ")]),_:1})):b("",!0)]),_:2},1024),n(d,{prop:"name",label:"中文名",align:"center",width:"300"}),n(d,{prop:"nameEn",label:"英文名",align:"center",width:"300"})]),_:2},1024),n(d,{prop:"sku",label:"SKU",width:"150",align:"center"}),n(d,{prop:"upc",label:"UPC",width:"150",align:"center"}),n(d,{prop:"color",label:"颜色",width:"120",align:"center"},{default:l(r=>[f("span",null,k(m(le)(m(H),Number.parseInt(r.row.color))),1)]),_:2},1024),n(d,{prop:"quantity",label:"订单数量",width:"100",align:"center"}),n(d,{prop:"csCode",label:"客编号",width:"100",align:"center"}),n(d,{label:"包装单位信息",align:"center",width:"300"},{default:l(()=>[n(d,{prop:"pieceProduct.sku",label:"SKU",width:"130",align:"center"}),n(d,{prop:"pieceProduct.upc",label:"UPC",width:"130",align:"center"}),n(d,{prop:"unitQuantity",label:"单位数量",width:"130",align:"center"},{default:l(r=>[r.row.unit>0?(i(),Y("span",Ue,k(r.row.unitQuantity),1)):(i(),Y("span",je," - "))]),_:2},1024),n(d,{prop:"piece",label:"订单数量",width:"100",align:"center"}),n(d,{prop:"shippedPiece",label:"实际发货",width:"100",align:"center"}),n(d,{prop:"color",label:"颜色",width:"120",align:"center"},{default:l(r=>[f("span",null,k(m(le)(m(H),Number.parseInt(r.row.pieceProduct.color))),1)]),_:2},1024)]),_:2},1024),n(d,{label:"库存状态",align:"center"},{default:l(()=>[n(d,{prop:"pieceProduct.stock.availableQuantity",label:"可用",width:"100",align:"center"},{default:l(r=>{var N,Z,ee,te;return[w(k((((Z=(N=r.row.pieceProduct)==null?void 0:N.stock)==null?void 0:Z.inStock)||0)-(((te=(ee=r.row.pieceProduct)==null?void 0:ee.stock)==null?void 0:te.freezeStock)||0)),1)]}),_:2},1024),n(d,{prop:"pieceProduct.stock.freezeStock",label:"冻结",width:"100",align:"center"}),n(d,{prop:"pieceProduct.stock.productionStock",label:"在产",width:"100",align:"center"}),n(d,{prop:"pieceProduct.stock.waitInboundStock",label:"待入库",width:"100",align:"center"})]),_:2},1024)]),_:2},1032,["data"]),f("div",He,[f("div",Qe,[f("div",null,[Ge,f("span",null,k(a.row.total),1)]),f("div",null,[Je,f("span",null,k(a.row.products.reduce((r,N)=>r+N.shippedPiece,0)),1)])])])]),_:1},8,["expand-row-keys"])]),_:1}),n(y,null,{default:l(()=>[n(C),n(Fe)]),_:1})]),_:1},8,["modelValue"])]),_:1},512)}}});export{nt as default};
