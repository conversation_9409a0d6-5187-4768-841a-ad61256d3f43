import{c as ee,b as h,e as k,z as Se,A as $e,ae as Ie,f as T,B as b,q as p,V as Z,h as l,w as a,F as P,s as H,i as d,v as m,y as g,G as Ve,H as Oe,j as v,t as S,Y as x,Z as J,o as r,m as Ne,aT as Ee,T as U,E as C,U as Le,af as Re,ag as Pe}from"./.pnpm-hVqhwuVC.js";import{g as <PERSON>,i as I,r as B,c as Ue}from"./index-DkYL1aws.js";import{a as qe}from"./index-C6cm1h61.js";/* empty css              */import{u as Ae}from"./table-ops-CrFIfhgA.js";import{_ as We}from"./material-table-columns.vue_vue_type_script_setup_true_lang-BFX8eStE.js";import{_ as Qe}from"./AuditLog.vue_vue_type_script_setup_true_name_AuditLog_lang-d1Cc9i9a.js";import{V as je}from"./index-CBanFtSc.js";import{INBOUND_TYPE as ze}from"./constant-C2dsBPRR.js";import{g as X}from"./index-BFVs8cCE.js";import{_ as Fe}from"./OutsourceInbound.vue_vue_type_script_setup_true_name_MaterialInbound_lang-CSLhKnR1.js";import{_ as Ke}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./AuditLogTable.vue_vue_type_script_setup_true_name_AuditLogTable_lang-I6kBwh-b.js";const Ge=$=>(Re("data-v-660db9a7"),$=$(),Pe(),$),Ze={key:1},Je={class:"table-summary"},Xe={class:"table-summary-container"},et=Ge(()=>g("span",{class:"cl-table__expand-footer-title"},"数量总计：",-1)),tt={class:"outbound-images-preview"},ot=ee({name:"pms-warehouse-inbound"}),at=ee({...ot,setup($){const{dict:te}=He(),oe=te.get("inbound_outbound_key"),Y=-2,s=h(0),{service:c}=qe(),V=h([{label:"草稿",value:0,type:"info",count:0},{label:"已入库",value:3,type:"success",count:0},{label:"入库明细",value:Y}]),O=h({id:0}),q=h({"slot-btn-confirm":{width:80,permission:c.pms.material.inbound.permission.confirm,show:k(()=>s.value===0)},"slot-audit-log":{width:120,permission:c.pms.material.inbound.permission.confirm,show:!0},"slot-btn-start":{width:110,permission:c.pms.material.inbound.permission.start,show:k(()=>s.value===1)},"slot-btn-edit":{width:80,permission:c.pms.material.inbound.permission.update,show:k(()=>s.value===0)},"slot-btn-delete":{width:80,permission:c.pms.material.inbound.permission.delete,show:k(()=>s.value===0)},"slot-btn-complete":{width:120,permission:c.pms.material.inbound.permission.complete,show:k(()=>s.value===2)},"slot-btn-revoke":{width:110,permission:c.pms.material.inbound.permission.revoke,show:k(()=>s.value!==0)}}),{getOpWidth:ae,checkOpButtonIsAvaliable:D,getOpIsHidden:ne}=Ae(q),A=h(),W=h(!1);Se(s,()=>{A.value=ae(),W.value=ne()},{immediate:!0});const Q=I.useTable({columns:[{label:"#",prop:"products",type:"expand"},{label:"入库单号",prop:"no",width:220},{label:"入库类型",prop:"type",dict:ze,formatter:e=>"委外入库"},{label:"关联单号",prop:"orderNo",width:220},{label:"入库总数量",prop:"totalQuantity"},{label:"创建时间",prop:"createTime",component:{name:"cl-date-text",props:{format:"YYYY-MM-DD HH:mm:ss"}}},{label:"入库凭证",prop:"voucher",component:{name:"cl-image",props:{fit:"cover",lazy:!0,size:[50,50]}}},{label:"单据时间",prop:"inboundTime",component:{name:"cl-date-text",props:{format:"YYYY-MM-DD"}}},{label:"完成时间",prop:"completeTime",component:{name:"cl-date-text",props:{format:"YYYY-MM-DD HH:mm:ss"}}},{label:"备注",prop:"remark",width:150,showOverflowTooltip:!0},{type:"op",label:"操作",width:A,hidden:W,buttons:Object.keys(q.value)}]}),_=I.useCrud({service:c.pms.outsource.inbound,async onRefresh(e,{next:o,render:n}){const{count:f,list:M,pagination:i}=await o(e);V.value.forEach(y=>{y.count=f&&f[y.value]||0}),n(M,i)}},e=>{e.refresh({status:s})});function j(e){var o;s.value=e,e!==Y&&((o=_.value)==null||o.refresh())}function le(e,o){var n;(o==null?void 0:o.type)==="expand"||(o==null?void 0:o.type)==="op"||(o==null?void 0:o.property)==="voucher"||(n=Q.value)==null||n.toggleRowExpansion(e)}const z=I.useForm();function F(e){var o;if(!e.id)return!1;e.status===0&&((o=z.value)==null||o.open({form:{...e},title:"完善入库信息",width:"400px",dialog:{controls:["close"]},items:[{label:"单据日期",prop:"inboundTime",required:!0,component:{name:"el-date-picker",props:{type:"date","value-format":"YYYY-MM-DD",format:"YYYY-MM-DD",clearable:!0,disabledDate:n=>n.getTime()>Date.now()}}},{label:"入库凭证",prop:"voucher",required:!0,component:{name:"cl-upload",props:{multiple:!0,limit:5,accept:"image/jpg,image/jpeg,image/png",text:"上传入库凭证",type:"image",disabled:!1,isPrivate:!1}}}],on:{submit:Ee(async(n,{close:f,done:M})=>{c.pms.outsource.inbound.submit({...e,...n,id:e.id}).then(i=>{var y;Object.prototype.hasOwnProperty.call(i,"status")&&(C.success("提交成功"),f(),s.value=(i==null?void 0:i.status)||0,(y=_.value)==null||y.refresh())}).catch(i=>{C.error(i.message||"提交失败")}),M()},1e3)}}))}const N=h(!1);function re(e){U.confirm("确定要完成入库吗？<br /><span text-red>注意，提交后将无法撤销，请谨慎操作！！！</span><br />","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning",dangerouslyUseHTMLString:!0}).then(()=>{N.value=!0,c.pms.material.inbound.complete({id:e.id}).then(n=>{var f;C.success("完成入库成功"),s.value=(n==null?void 0:n.status)||0,(f=_.value)==null||f.refresh()}).catch(n=>{C.error(n.message)}).finally(()=>{N.value=!1})}).catch(()=>{})}function se(){return"primary-row"}function ie(){B.push("/pms/outsource/inbound/add")}function ue(e){B.push(`/pms/outsource/inbound/add?id=${e}`)}function pe(e){if(!e)return!1;U.confirm("确认删除入库单吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{c.pms.outsource.inbound.delete({ids:[e]}).then(()=>{var o;C.success("入库单删除成功"),(o=_.value)==null||o.refresh()}).catch(o=>{C.error(o.message)})}).catch(()=>{})}const K=h([]);$e(()=>{const e=B.currentRoute.value.query.tab;e&&(s.value=Number.parseInt(e.toString()),B.replace({query:{tab:void 0}}));const o=B.currentRoute.value.query.expand;o&&(K.value=[Number.parseInt(o.toString())],B.replace({query:{expand:void 0}}))});const ce=I.useSearch({items:[{label:"入库单号",prop:"keyWord",props:{labelWidth:"120px"},component:{name:"el-input",style:{width:"200px"},props:{clearable:!0,onChange(e){var o;(o=_.value)==null||o.refresh({keyWord:e.trim(),page:1})}}}},{label:"下单时间",prop:"dateRange",props:{labelWidth:"80px"},component:{name:"el-date-picker",props:{type:"daterange","value-format":"YYYY-MM-DD",format:"YYYY-MM-DD",rangeSeparator:"至",startPlaceholder:"开始日期",endPlaceholder:"结束日期",clearable:!0,onChange(e){var o;(o=_.value)==null||o.refresh({dateRange:e})}}}},{label:"关键字",prop:"inbound_outbound_key",props:{labelWidth:"80px"},component:{name:"el-select",props:{style:"width: 200px",clearable:!0,onChange(e){var n;const o={inbound_outbound_key:e,page:1};(n=_.value)==null||n.refresh(o)}},options:oe}}]});function de(e){if(!e.id)return!1;O.value=Le(e)}function me(e){U.confirm("确定撤销入库单吗？<br /> 撤销后，该入库单将更新到草稿状态。<br />","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning",dangerouslyUseHTMLString:!0}).then(()=>{c.pms.outsource.inbound.revoke({id:e.id}).then(()=>{var n;C.success("撤销入库单成功"),(n=_.value)==null||n.refresh()}).catch(n=>{C.error(n.message||"撤销入库单失败")})}).catch(()=>{})}Ue();function be(e){return e.status===0?!1:J(e.createTime).add(160,"days").isAfter(J())}const E=h(!1),L=h([]);function fe(e){const o=e.split(",").map(n=>n.trim());L.value=o,E.value=!0}function he(){L.value=[],E.value=!1}function _e(e){return[{label:"Bom用量",prop:"workOrderDetail.calcBomQuantity",align:"center",width:120,showOverflowTooltip:!0}]}function ve(e){return e.isSubmit===1&&(e.status===0||e.status===4)}const{height:G}=Ie(),ge=k(()=>G.value-240),ye=k(()=>G.value-360);return(e,o)=>{const n=d("el-tab-pane"),f=d("el-tabs"),M=d("cl-refresh-btn"),i=d("el-button"),y=d("cl-flex1"),we=d("cl-search"),R=d("el-row"),w=d("el-table-column"),ke=d("el-table"),xe=d("cl-table"),Ce=d("cl-pagination"),De=d("cl-form"),Ye=d("el-image-viewer"),Te=d("cl-crud"),Be=Oe("permission");return r(),T("div",null,[s.value===Y?(r(),T("div",{key:0,"w-full":"",style:Z(`height:${ge.value}px`)},[l(f,{modelValue:s.value,"onUpdate:modelValue":o[0]||(o[0]=t=>s.value=t),type:"border-card",onTabChange:j},{default:a(()=>[(r(!0),T(P,null,H(V.value,t=>(r(),p(n,{key:t.value,label:t.value===Y?`${t.label}`:`${t.label}(${t.count})`,name:t.value},null,8,["label","name"]))),128))]),_:1},8,["modelValue"]),l(Fe,{style:{height:"100%"},api:m(c).pms.outsource.inbound},null,8,["api"])],4)):b("",!0),s.value!==Y?(r(),p(Te,{key:1,ref_key:"Crud",ref:_},{default:a(()=>[l(R,null,{default:a(()=>[l(M),Ve((r(),p(i,{text:"",bg:"",type:"success",onClick:ie},{default:a(()=>[v(" 创建委外入库单 ")]),_:1})),[[Be,m(c).pms.material.inbound.permission.add]]),l(y),l(we,{ref_key:"Search",ref:ce},null,512)]),_:1}),l(f,{modelValue:s.value,"onUpdate:modelValue":o[1]||(o[1]=t=>s.value=t),type:"border-card",onTabChange:j},{default:a(()=>[(r(!0),T(P,null,H(V.value,t=>(r(),p(n,{key:t.value,label:t.value===Y?`${t.label}`:`${t.label}(${t.count})`,name:t.value},null,8,["label","name"]))),128)),l(R,null,{default:a(()=>[l(xe,{ref_key:"Table",ref:Q,"row-key":"id","expand-row-keys":K.value,class:"table-row-pointer","auto-height":!1,style:Z(`height:${ye.value}px`),onRowClick:le},{"column-orderNo":a(({scope:t})=>[g("span",null,S(t.row.orderSn||""),1)]),"slot-btn-revoke":a(({scope:t})=>[m(je)(t.row.createTime)&&m(D)("slot-btn-revoke")&&be(t.row)?(r(),p(i,{key:0,text:"",bg:"",type:"danger",onClick:x(u=>me(t.row),["stop"])},{default:a(()=>[v(" 撤销入库 ")]),_:2},1032,["onClick"])):b("",!0)]),"slot-audit-log":a(({scope:t})=>[ve(t.row)&&m(D)("slot-audit-log")?(r(),p(i,{key:0,text:"",bg:"",type:"warning",onClick:x(u=>de(t.row),["stop"])},{default:a(()=>[v(" 审核记录 ")]),_:2},1032,["onClick"])):b("",!0)]),"slot-btn-confirm":a(({scope:t})=>[m(D)("slot-btn-confirm")?(r(),p(i,{key:0,text:"",bg:"",type:"success",onClick:x(u=>F(t.row),["stop"])},{default:a(()=>[v(" 提交 ")]),_:2},1032,["onClick"])):b("",!0)]),"slot-btn-start":a(({scope:t})=>[m(D)("slot-btn-start")?(r(),p(i,{key:0,text:"",bg:"",type:"success",onClick:x(u=>F(t.row),["stop"])},{default:a(()=>[v(" 开始入库 ")]),_:2},1032,["onClick"])):b("",!0)]),"slot-btn-edit":a(({scope:t})=>[m(D)("slot-btn-edit")?(r(),p(i,{key:0,text:"",bg:"",type:"primary",onClick:x(u=>ue(t.row.id),["stop"])},{default:a(()=>[v(" 编辑 ")]),_:2},1032,["onClick"])):b("",!0)]),"slot-btn-delete":a(({scope:t})=>[m(D)("slot-btn-delete")?(r(),p(i,{key:0,text:"",bg:"",type:"danger",onClick:x(u=>pe(t.row.id),["stop"])},{default:a(()=>[v(" 删除 ")]),_:2},1032,["onClick"])):b("",!0)]),"slot-btn-complete":a(({scope:t})=>[m(D)("slot-btn-complete")?(r(),p(i,{key:0,loading:N.value,text:"",bg:"",type:"success",onClick:x(u=>re(t.row),["stop"])},{default:a(()=>[v(" 完成入库 ")]),_:2},1032,["loading","onClick"])):b("",!0)]),"column-voucher":a(({scope:t})=>[t.row.voucher&&t.row.voucher.split(",").length>0?(r(),p(i,{key:0,text:"",type:"primary",onClick:x(u=>fe(t.row.voucher),["stop"])},{default:a(()=>[v(" 点击查看 ")]),_:2},1032,["onClick"])):(r(),T("span",Ze,"无数据"))]),"column-products":a(({scope:t})=>[l(ke,{data:t.row.products,style:{width:"100%"},border:"","row-class-name":se},{default:a(()=>[t.row.type===3?(r(),p(w,{key:0,prop:"workOrderDetail.workOrderNo",label:"工单号",width:"200","show-overflow-tooltip":""})):b("",!0),l(w,{prop:"quantity",label:"入库数量",align:"center",width:"100","show-overflow-tooltip":""}),(r(!0),T(P,null,H(_e(t.row),(u,Me)=>(r(),p(w,Ne({key:Me,ref_for:!0},u),null,16))),128)),l(w,{prop:"workOrderDetail.receivedQuantity",label:"已收数量",align:"center",width:"100","show-overflow-tooltip":""}),l(w,{label:"剩余数量",align:"center",width:"120"},{default:a(({row:u})=>[g("span",null,S(u.workOrderDetail.calcBomQuantity-u.workOrderDetail.receivedQuantity),1)]),_:1}),l(w,{prop:"inbound_outbound_key",label:"关键字",align:"center",width:"120"},{default:a(u=>[g("span",null,S(m(X)("inbound_outbound_key",u.row.inbound_outbound_key)),1)]),_:1}),l(w,{prop:"warehouseId",label:"仓位",align:"center",width:"120"},{default:a(({row:u})=>[g("span",null,S(m(X)("warehouse_name",u.warehouseId)),1)]),_:1}),l(We,{"auto-width":""}),l(w,{prop:"unit",label:"单位",align:"center",width:"70","show-overflow-tooltip":""})]),_:2},1032,["data"]),g("div",Je,[g("div",Xe,[et,g("span",null,S(t.row.totalQuantity),1)])])]),_:1},8,["expand-row-keys","style"])]),_:1}),l(R,null,{default:a(()=>[l(y),l(Ce)]),_:1})]),_:1},8,["modelValue"]),l(De,{ref_key:"InboundForm",ref:z},null,512),g("div",tt,[E.value?(r(),p(Ye,{key:0,"url-list":L.value,teleported:"",onClose:he},null,8,["url-list"])):b("",!0)]),l(Qe,{modelValue:O.value.id,"onUpdate:modelValue":o[2]||(o[2]=t=>O.value.id=t)},null,8,["modelValue"])]),_:1},512)):b("",!0)])}}}),_t=Ke(at,[["__scopeId","data-v-660db9a7"]]);export{_t as default};
