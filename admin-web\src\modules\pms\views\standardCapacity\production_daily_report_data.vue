<script lang="ts" name="pms-material-testing" setup>
import { useCrud, useSearch, useTable, useUpsert } from '@cool-vue-p/crud'
import dayjs from 'dayjs'
import { ElMessage } from 'element-plus'
import moment from 'moment'
import { computed, ref, watchEffect } from 'vue'
import * as XLSX from 'xlsx'
import { useDict } from '/$/dict'
import { useTableOps } from '/$/pms/hooks/table-ops'
import { useCool } from '/@/cool'
import { downloadBlob } from '/@/cool/utils'

const { dict } = useDict()
const orderList = ref<any[]>([])
// const materialOptions = ref<any[]>([])
// const materialList = ref<any[]>([])
const productList = ref<any[]>([])
const productOptions = ref<any[]>([])
const { service } = useCool()
const orderSearchList = ref<any[]>([])
// 获取产品列表
async function getAllProductList() {
  try {
    const res = await service.pms.product.request({
      url: '/getAllProduct',
      method: 'GET',
    })
    productOptions.value = res
    productList.value = res?.map((e: any) => {
      return {
        group_id: e.groupId,
        value: e.id,
        sku: e.sku,
        label: `${e.sku} ${e.name}`,
      }
    })
  }
  catch (e: any) {
    console.error(e)
  }
}
const WorkshopSectionOptions = [
  {
    label: '加工段',
    value: 1,
    name: '-',
    nameEn: '-',
    type: 'info',
  },
  {
    label: '组装段',
    value: 2,
    name: '-',
    nameEn: '-',
    type: 'info',
  },
  {
    label: '老化段',
    value: 3,
    name: '-',
    nameEn: '-',
    type: 'info',
  },
  {
    label: '包装段',
    value: 4,
    name: '-',
    nameEn: '-',
    type: 'info',
  },
  {
    label: '加工段一',
    value: 5,
    name: '-',
    nameEn: '-',
    type: 'info',
  },
  {
    label: '加工段二',
    value: 6,
    name: '-',
    nameEn: '-',
    type: 'info',
  },
  {
    label: '加工段三',
    value: 7,
    name: '-',
    nameEn: '-',
    type: 'info',
  },
  {
    label: '芯子配件加工段一',
    value: 8,
    name: '-',
    nameEn: '-',
    type: 'info',
  },
  {
    label: '芯子配件加工段二',
    value: 9,
    name: '-',
    nameEn: '-',
    type: 'info',
  },
  {
    label: '芯子配件组装段一',
    value: 10,
    name: '-',
    nameEn: '-',
    type: 'info',
  },
  {
    label: '芯子配件组装段二',
    value: 11,
    name: '-',
    nameEn: '-',
    type: 'info',
  },
  {
    label: '芯子配件组装段三',
    value: 12,
    name: '-',
    nameEn: '-',
    type: 'info',
  },
  {
    label: '芯子配件组装段四',
    value: 13,
    name: '-',
    nameEn: '-',
    type: 'info',
  },
  {
    label: '芯子配件组装段五',
    value: 14,
    name: '-',
    nameEn: '-',
    type: 'info',
  },
  {
    label: '芯子配件包装段',
    value: 15,
    name: '-',
    nameEn: '-',
    type: 'info',
  },
]
const ProductionStagesOptions = [
  {
    label: '临时1（试产）',
    value: 1,
    name: '-',
    nameEn: '-',
    type: 'info',
  },
  {
    label: '临时2（首次量产）',
    value: 2,
    name: '-',
    nameEn: '-',
    type: 'warning',
  },
  {
    label: '正式（量产）',
    value: 3,
    name: '-',
    nameEn: '-',
    type: 'success',
  },
]
const colorList = dict.get('color')
const workshopList = dict.get('product_floor')

// cl-crud 配置
const Crud = useCrud(
  {
    service: service.pms.daily_report_data,
  },
  (app) => {
    app.refresh()
  },
)
// 获取生产订单列表
async function getOrder() {
  try {
    const res = await service.pms.production.schedule.request({
      url: '/list',
      method: 'POST',
    })
    orderList.value = res
    orderSearchList.value = res?.map((e: any) => {
      return {
        value: e.id,
        label: e.sn,
      }
    })
  }
  catch (e: any) {
    console.error(e)
  }
}

watchEffect(() => {
  // getMaterial()
  getOrder()
  getAllProductList()
})

const Search = useSearch({
  items: [
    {
      label: '下单时间',
      prop: 'dateRange',
      props: {
        labelWidth: '80px',
      },
      component: {
        name: 'el-date-picker',
        props: {
          'type': 'daterange',
          'value-format': 'YYYY-MM-DD',
          'format': 'YYYY-MM-DD',
          'rangeSeparator': '至',
          'startPlaceholder': '开始日期',
          'endPlaceholder': '结束日期',
          'clearable': true,
          onChange(dateRange) {
            Crud.value?.refresh({ dateRange, page: 1 })
          },
        },
      },
    },
    {
      label: '工段',
      prop: 'workshop_section',
      props: { labelWidth: '60px' },
      component: {
        name: 'el-select',
        props: {
          style: 'width: 150px',
          clearable: true,
          filterable: true,
          onChange(workshop_section) {
            Crud.value?.refresh({ workshop_section, page: 1 })
          },
        },
        options: WorkshopSectionOptions,
      },
    },
    {
      label: '生产阶段',
      prop: 'production_stages',
      props: { labelWidth: '80px' },
      component: {
        name: 'el-select',
        props: {
          style: 'width: 150px',
          clearable: true,
          filterable: true,
          onChange(production_stages) {
            Crud.value?.refresh({ production_stages, page: 1 })
          },
        },
        options: ProductionStagesOptions,
      },
    },
    {
      label: '订单号',
      prop: 'order_id',
      props: { labelWidth: '80px' },
      component: {
        name: 'el-select',
        props: {
          style: 'width: 150px',
          clearable: true,
          filterable: true,
          onChange(order_id) {
            Crud.value?.refresh({ order_id, page: 1 })
          },
        },
        options: orderSearchList,
      },
    },
    {
      label: '生产车间',
      prop: 'workshop_id',
      props: { labelWidth: '80px' },
      component: {
        name: 'el-select',
        props: {
          style: 'width: 120px',
          clearable: true,
          filterable: true,
          onChange(workshop_id) {
            Crud.value?.refresh({ workshop_id, page: 1 })
          },
        },
        options: workshopList,
      },
    },
    {
      label: 'sku/作业人员',
      prop: 'keyWord',
      props: {
        labelWidth: '100px',
      },
      component: {
        name: 'el-input',
        props: {
          style: 'width: 160px',
          placeholder: '请输入sku或作业人员',
          clearable: true,
          onChange(keyword: string) {
            Crud.value?.refresh({ keyWord: keyword.trim(), page: 1 })
          },
        },
      },
    },
  ],
})
const opButtons = ref({
  edit: {
    width: 80,
    permission: service.pms.material_test.permission.update,
    show: true,
  },
  delete: {
    width: 80,
    permission: service.pms.material_test.permission.delete,
    show: true,
  },
})
const { getOpWidth, checkOpButtonIsAvaliable, getOpIsHidden } = useTableOps(opButtons as any)
const opWidth = ref(getOpWidth())
const opIsHidden = ref(getOpIsHidden())

// cl-upsert 配置
const Upsert = useUpsert({
  props: {
    labelWidth: '120px',
    class: 'production_daily_report_data-form',
  },
  items: [
    {
      label: '生产日期',
      prop: 'produced_date',
      required: true,
      component: {
        name: 'el-date-picker',
        props: {
          'type': 'date',
          'placeholder': '选择日期',
          'value-format': 'YYYY-MM-DD',
          'format': 'YYYY-MM-DD',
          'clearable': true,
          // 不能超过今天
          'disabledDate': (time: any) => {
            return time.getTime() > Date.now()
          },
        },
      },
    },
    {
      label: '订单号',
      prop: 'order_id',
      required: true,
      component: {
        options: orderSearchList,
        name: 'el-select',
        props: {
          clearable: true,
          filterable: true,
        },
      },
    },
    {
      label: '机型',
      prop: 'product_id',
      required: true,
      component: {
        options: productList,
        name: 'el-select',
        props: {
          clearable: true,
          filterable: true,
        },
      },
    },
    {
      label: '生产车间',
      prop: 'workshop_id',
      required: true,
      component: {
        options: workshopList,
        name: 'el-select',
        props: {
          clearable: true,
          filterable: true,
        },
      },
    },
    {
      label: '产线',
      prop: 'production_line',
      required: true,
      component: {
        name: 'el-input',
      },
    },
    {
      label: '作业人员',
      prop: 'busywork_group',
      required: true,
      component: {
        name: 'el-input',
      },
    },
    {
      label: '工段',
      prop: 'workshop_section',
      required: true,
      component: {
        options: WorkshopSectionOptions,
        name: 'el-select',
        props: {
          clearable: true,
          filterable: true,
        },
      },
    },
    {
      label: '生产阶段',
      prop: 'production_stages',
      required: true,
      component: {
        options: ProductionStagesOptions,
        name: 'el-select',
        props: {
          clearable: true,
          filterable: true,
        },
      },
    },
    {
      label: '工时',
      prop: 'man_hour',
      required: true,
      component: { name: 'el-input-number', props: { min: 0.01 } },
    },
    {
      label: '当日产能',
      prop: 'daily_output',
      required: true,
      component: { name: 'el-input-number', props: { min: 0.01 } },
    },
    {
      label: '备注',
      prop: 'remark',
      required: false,
      component: {
        name: 'el-input',
      },
    },
    {
      label: '人数',
      prop: 'number_of_people',
      required: true,
      component: {
        name: 'el-input-number',
        props: {
          disabled: true,
        },
      },
    },
  ],
  async onClose(action: string, done: Function) {
    done()
  },
})

// cl-table 配置
const Table = useTable({
  columns: [
    { type: 'selection' },
    {
      label: '日期',
      prop: 'produced_date',
      minWidth: 80,
      align: 'center',
      sortable: 'desc',
      formatter(row) {
        if (!row.produced_date) {
          return ''
        }
        return dayjs(row.produced_date).format('YYYY-MM-DD')
      },
    },
    {
      label: '订单号',
      prop: 'order_id',
      minWidth: 60,
      formatter(row) {
        const info = orderList.value.find(e => e.id === row.order_id)
        return info ? info.sn : ''
      },
    },
    {
      label: '订单数量(PCS)',
      prop: 'quantity',
      minWidth: 60,
    },
    {
      label: '剩余生产数量(PCS)',
      prop: 'except_quantity',
      minWidth: 60,
    },
    {
      label: '机型',
      prop: 'product_id',
      minWidth: 100,
      formatter(row) {
        const info = productOptions.value.find(e => e.id === row.product_id)
        return info ? info.name : ''
      },
    },
    {
      label: 'SKU',
      prop: 'sku',
      minWidth: 100,
      formatter(row) {
        const info = productOptions.value.find(e => e.id === row.product_id)
        return info ? info.sku : ''
      },
    },
    {
      label: '颜色',
      prop: 'color',
      minWidth: 100,
      formatter: (row: any) => {
        if (!productOptions.value || !Array.isArray(productOptions.value))
          return ''
        const info = productOptions.value.find(e => e.id === row.product_id)
        if (!info || !colorList.value || !Array.isArray(colorList.value))
          return '-'
        return (
          colorList.value.find(
            e => e.value === Number.parseInt(info.color),
          )?.label || '-'
        )
      },
    },
    {
      label: '生产车间',
      prop: 'workshop_id',
      minWidth: 60,
      formatter: (row: any) => {
        if (!workshopList.value || !Array.isArray(workshopList.value))
          return ''
        return workshopList.value.find(item => item.id === row.workshop_id)?.label || ''
      },
    },
    {
      label: '线别',
      prop: 'production_line',
      minWidth: 60,
    },
    {
      label: '工位',
      prop: 'station',
      minWidth: 60,
    },
    {
      label: '作业人员',
      prop: 'busywork_group',
      minWidth: 60,
    },
    {
      label: '工段',
      prop: 'workshop_section',
      minWidth: 60,
      formatter: (row: any) => {
        if (!WorkshopSectionOptions || !Array.isArray(WorkshopSectionOptions))
          return ''
        return WorkshopSectionOptions.find(item => item.value === row.workshop_section)?.label || ''
      },
    },
    {
      label: '生产阶段',
      prop: 'production_stages',
      minWidth: 60,
      formatter: (row: any) => {
        if (!ProductionStagesOptions || !Array.isArray(ProductionStagesOptions))
          return ''
        return ProductionStagesOptions.find(item => item.value === row.production_stages)?.label || ''
      },
    },
    {
      label: '实际生产数据',
      align: 'center',
      children: [
        {
          label: '人数(人)',
          prop: 'number_of_people',
          minWidth: 60,
        },
        {
          label: '合计人数(人)',
          prop: 'total_number_of_people',
          minWidth: 60,
        },
        {
          label: '累计工时(H)',
          prop: 'man_hour',
          minWidth: 60,
        },
        {
          label: '合计累计工时(H)',
          prop: 'total_man_hour',
          minWidth: 60,
        },

        {
          label: '当日产能(pcs)',
          prop: 'daily_output',
          minWidth: 60,
        },
        {
          label: '合计当日产能(pcs)',
          prop: 'total_daily_output',
          minWidth: 60,
        },
        {
          label: '人均产能（pcs/h)',
          prop: 'average_capacity',
          minWidth: 60,
        },
        {
          label: '备注',
          prop: 'remark',
          minWidth: 60,
        },
      ],
    },

    {
      type: 'op',
      label: '操作',
      width: opWidth as any,
      hidden: opIsHidden,
      buttons: Object.keys(opButtons.value) as any,
    },
  ],
})

function handleExport() {
  let keyWord = Search.value?.getForm('keyWord')
  let order_id = Search.value?.getForm('order_id')
  let dateRange = Search.value?.getForm('dateRange')
  let workshop_section = Search.value?.getForm('workshop_section')
  let production_stages = Search.value?.getForm('production_stages')
  let workshop_id = Search.value?.getForm('workshop_id')
  const params = {
    url: '/export',
    method: 'GET',
    responseType: 'blob',
    params: {
      workshop_section,
      production_stages,
      dateRange,
      keyWord,
      order_id,
      workshop_id,
    },
  }
  service.pms.daily_report_data
    .request(params)
    .then((res: any) => {
      if (downloadBlob(res))
        ElMessage.success('导出成功')
      Crud.value?.refresh()
    })
    .catch((err: any) => {
      // 提示错误消息
      ElMessage.error(err.message || '导出失败')
    })
}

function downloadExcelTemplate() {
  const fileName = '生产日报数据_模板.xlsx'
  const filePath = '/daily_report_data.xlsx'

  // 发起下载请求
  fetch(filePath)
    .then(response => response.blob())
    .then((blob) => {
      // 保存文件
      downloadBlob(blob, fileName)
    })
    .catch(() => {
      ElMessage.error({
        message: '下载模板文件失败',
      })
    })
}

// 导入文件
const fileInputRef = ref<HTMLInputElement | null>(null)
const isLoading = ref(false)

function openFileInput() {
  const fileInput = fileInputRef.value
  if (fileInput)
    fileInput.click()
}
// 处理文件输入框的change事件
async function handleFileInputChange(event: Event) {
  const fileInput = event.target as HTMLInputElement
  const files = fileInput.files
  const WorkshopSectionMap: any = {}
  if (WorkshopSectionOptions && WorkshopSectionOptions.length > 0) {
    WorkshopSectionOptions.forEach((item: any) => {
      WorkshopSectionMap[item.label] = item.value
    })
  }
  const ProductionStagesOptionsMap: any = {}
  if (ProductionStagesOptions && ProductionStagesOptions.length > 0) {
    ProductionStagesOptions.forEach((item: any) => {
      ProductionStagesOptionsMap[item.label] = item.value
    })
  }

  const workshopListMap: any = {}
  if (workshopList.value && workshopList.value.length > 0) {
    workshopList.value.forEach((item: any) => {
      workshopListMap[item.label] = item.id
    })
  }
  if (files && files.length > 0) {
    isLoading.value = true
    const file = files[0]
    const reader = new FileReader()

    reader.onload = (e: ProgressEvent<FileReader>) => {
      const data = new Uint8Array(e.target?.result as ArrayBuffer)
      const workbook = XLSX.read(data, { type: 'array' })

      // 这里可以根据需要处理读取到的Excel数据
      const worksheet = workbook.Sheets[workbook.SheetNames[0]]
      fillMergedCells(worksheet) // 先还原合并单元格
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1, defval: '' }) as any[][]
      // 定义非法值
      const illegalValue = [undefined, null, '', 'undefined', 'null', 'NaN']
      // 定义列
      const columns: string[] = [
        '',
        'produced_date',
        'order_id',
        'name',
        'sku',
        'workshop_id',
        'production_line',
        'busywork_group',
        'workshop_section',
        'production_stages',
        'station',
        'daily_output',
        'man_hour',
        'remark',
      ]
      const result: any = []
      if (jsonData && jsonData.length > 0) {
        for (let i = 3; i < jsonData.length; i++) {
          const row = (jsonData[i] as string[]).slice(0, 14)
          const cell: any = {}
          for (let j = 1; j < row.length; j++) {
            const columnName = columns[j]
            if (typeof row[j] === 'string') {
              row[j] = row[j].trim()
            }
            cell[columnName] = row[j]
            if (columnName === 'produced_date' && cell.produced_date) {
              cell.produced_date = moment(cell.produced_date).format('YYYY-MM-DD')
            }

            if (columnName === 'workshop_section') {
              cell.workshop_section = WorkshopSectionMap[cell.workshop_section]
                ? WorkshopSectionMap[cell.workshop_section]
                : 0
            }
            if (columnName === 'workshop_id') {
              cell.workshop_id = workshopListMap[cell.workshop_id]
                ? workshopListMap[cell.workshop_id]
                : 0
            }
            if (columnName === 'production_stages') {
              cell.production_stages = ProductionStagesOptionsMap[
                cell.production_stages
              ]
                ? ProductionStagesOptionsMap[cell.production_stages]
                : 0
            }
            if (columnName === 'order_id') {
              cell.order_id = orderList.value.find(item =>
                item.sn.toLowerCase().includes(cell.order_id.toLowerCase()),
              )?.id
              if (cell.order_id === undefined || cell.order_id === 0) {
                continue
              }
            }
            // cell.number_of_people = Number.parseInt(cell.number_of_people)
            cell.man_hour = Number.parseFloat(cell.man_hour)
            cell.daily_output = Number.parseFloat(cell.daily_output)
            cell.number_of_people = 1 // 人数固定为1
          }
          cell.product_id = productList.value.find(
            item => item.sku === cell.sku,
          )?.value

          if (
            illegalValue.includes(cell.daily_output)
            || Number.isNaN(cell.daily_output)
          ) {
            clearInput(fileInput)
            isLoading.value = false
            break
          }
          if (
            illegalValue.includes(cell.man_hour)
            || Number.isNaN(cell.man_hour)
            || cell.man_hour === 0
          ) {
            clearInput(fileInput)
            isLoading.value = false
            break
          }
          if (illegalValue.includes(cell.produced_date)) {
            clearInput(fileInput)
            isLoading.value = false
            break
          }
          if (
            illegalValue.includes(cell.workshop_section)
            || Number.isNaN(cell.workshop_section)
            || cell.workshop_section === 0
          ) {
            clearInput(fileInput)
            isLoading.value = false
            break
          }

          if (
            illegalValue.includes(cell.workshop_id)
            || Number.isNaN(cell.workshop_id)
            || cell.workshop_id === 0
          ) {
            clearInput(fileInput)
            isLoading.value = false
            break
          }

          if (
            illegalValue.includes(cell.production_stages)
            || Number.isNaN(cell.production_stages)
            || cell.production_stages === 0
          ) {
            clearInput(fileInput)
            isLoading.value = false
            break
          }
          if (illegalValue.includes(cell.sku) || cell.sku === '') {
            clearInput(fileInput)
            isLoading.value = false
            break
          }
          if (illegalValue.includes(cell.station) || cell.station === '') {
            clearInput(fileInput)
            isLoading.value = false
            break
          }
          result.push(cell)
        }
        if (result.length > 0) {
          service.pms.daily_report_data.importDailyReportData({
            daily_report_data: result,
          })
            .then(() => {
              Crud.value?.refresh()
              ElMessage.success(`导入成功`)
            })
            .catch((e: any) => {
              ElMessage.error(e.message || '导入失败')
            })
            .finally(() => {
              isLoading.value = false
            })
        }
        else {
          isLoading.value = false
          ElMessage.error('导入有效数据为空')
        }
        clearInput(fileInput)
      }
    }
    reader.readAsArrayBuffer(file)
  }
  else {
    isLoading.value = false
    ElMessage.error('请选择文件')
  }
}

function clearInput(fileInput: { value: string }) {
  // 清空文件输入的值
  if (fileInput)
    fileInput.value = ''
}

// 还原合并单元格的值
function fillMergedCells(worksheet: XLSX.WorkSheet) {
  const merges = worksheet['!merges'] || []
  merges.forEach((merge: any) => {
    const start = XLSX.utils.encode_cell({ r: merge.s.r, c: merge.s.c })
    const value = worksheet[start]?.v
    for (let R = merge.s.r; R <= merge.e.r; ++R) {
      for (let C = merge.s.c; C <= merge.e.c; ++C) {
        const cell = XLSX.utils.encode_cell({ r: R, c: C })
        if (!worksheet[cell])
          worksheet[cell] = { t: 's', v: value }
      }
    }
  })
}

const mergeColumns = [
  'produced_date',
  'order_id',
  'quantity',
  'except_quantity',
  'product_id',
  'sku',
  'color',
  'workshop_id',
  'production_line',
  'workshop_section',
  'production_stages',
  'total_number_of_people',
  'total_man_hour',
  'total_daily_output',
  'average_capacity',
]

function getMergeMap(data: any[]) {
  const mergeMap: Record<number, number> = {}
  let i = 0
  while (i < data.length) {
    const currentGroupId = data[i].group_id
    let count = 1
    // 统计连续相同 group_id 的行数
    for (let j = i + 1; j < data.length; j++) {
      if (data[j].group_id === currentGroupId) {
        count++
      }
      else {
        break
      }
    }
    if (count > 1) {
      mergeMap[i] = count
    }
    i += count
  }
  return mergeMap
}

const mergeMap = computed(() => getMergeMap(Table.value?.data || []))

function tableSpanMethod({ row, column, rowIndex, columnIndex }) {
  const colProp = column.property
  if (!mergeColumns.includes(colProp))
    return [1, 1]
  const map = mergeMap.value
  if (map[rowIndex]) {
    return [map[rowIndex], 1]
  }
  for (const start in map) {
    const startIdx = Number(start)
    const span = map[start]
    if (rowIndex > startIdx && rowIndex < startIdx + span) {
      return [0, 0]
    }
  }
  return [1, 1]
}
</script>

<template>
  <cl-crud ref="Crud">
    <el-row>
      <!--      <cl-add-btn /> -->
      <cl-refresh-btn />
      <input ref="fileInputRef" type="file" style="display: none" accept=".xlsx, .xls" @change="handleFileInputChange">
      <el-button v-permission="service.pms.daily_report_data.permission.importDailyReportData" size="default" :loading="isLoading" type="warning" class="mb-10px mr-10px" ml="20px" @click="openFileInput">
        Excel导入
      </el-button>
      <!-- 下载excel模板 -->
      <el-button v-permission="service.pms.daily_report_data.permission.importDailyReportData" type="info" class="mb-10px mr-10px" size="default" @click="downloadExcelTemplate">
        下载Excel模板
      </el-button>
      <cl-multi-delete-btn
        v-permission="service.pms.daily_report_data.permission.delete"
      />
      <cl-flex1 />
      <cl-search ref="Search" />
      <el-button type="success" @click="handleExport" style="margin-left: 20px">
        导出
      </el-button>
    </el-row>

    <el-row>
      <cl-table ref="Table" :auto-height="false" row-key="id" :span-method="tableSpanMethod" />
    </el-row>

    <el-row>
      <cl-flex1 />
      <cl-pagination />
    </el-row>

    <cl-upsert ref="Upsert" />
  </cl-crud>
</template>

<style lang="scss">
.production_daily_report_data-form {
  .cl-form__items {
    .el-row {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      .full-line {
        grid-column: 1 / -1;
      }
    }

    // 让input-number的提示语靠左显示
    .el-input-number {
      :deep(.el-input__wrapper) {
        padding-left: 11px;
        input {
          text-align: left;
        }
      }
    }
  }
}

.cell .holiday {
  position: absolute;
  width: 6px;
  height: 6px;
  background: var(--el-color-danger);
  border-radius: 50%;
  bottom: 0px;
  left: 50%;
  transform: translateX(-50%);
}

// 统一表格背景色
.el-table th,
.el-table__header-wrapper th {
  background: #f5f7fa !important;
  color: #333;
}

.el-table tr,
.el-table__body-wrapper tr {
  background: #fff;
}

.el-table .el-table__footer-wrapper tr,
.el-table__footer-wrapper td {
  background: #f0f2f5 !important;
  color: #333;
}

// 鼠标悬浮高亮
.el-table__body tr:hover > td {
  background: #e6f7ff !important;
}
</style>
