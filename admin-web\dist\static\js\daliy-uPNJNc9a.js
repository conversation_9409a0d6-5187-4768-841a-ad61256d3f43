import{d as ae,c as W,k as z,X as te,b as _,n as se,K as C,e as le,A as ne,f as D,h as f,w as c,y as S,t as re,i as w,F as ie,s as ce,J as A,v as H,B as L,j as x,E as v,U as ue,T as O,o as g,q as J,Y as de}from"./.pnpm-hVqhwuVC.js";import"./index-DkYL1aws.js";import{a as fe}from"./index-C6cm1h61.js";import pe from"./daliy-form-Dwwh99Sq.js";import{u as me,c as I}from"./index-BFVs8cCE.js";import{_ as he}from"./_plugin-vue_export-helper-DlAUqK2U.js";const ve=ae("pimsStoreServive",()=>{const p={};function R(m,h){p[m]=h}function r(m){return p[m]}async function k(){Object.keys(p).forEach(async m=>{const h=p[m];h&&typeof h=="function"&&await h()})}return{setDispatchMap:R,getDispatchMap:r,refresh:k}}),ye=ve(),_e={flex:"~ col","w-full":""},we={"h-40px":"","p-l-10px":"","font-size-16px":"","font-bold":"","line-height-30px":""},ge={class:"middle"},ke={key:1,"h-40px":"","p-b-10px":"","p-l-100px":"","pt-10px":"",flex:"~ justify-end"},Ne={key:0},Te=W({name:"daliy"}),be=W({...Te,props:z({id:{},openType:{default:""}},{modelValue:{type:Boolean},modelModifiers:{}}),emits:z(["handleData","update:id","refresh"],["update:modelValue"]),setup(p,{emit:R}){const r=p,k=R,m=te(),h=ye.getDispatchMap("refreshTodoTable"),{service:N}=fe(),$=_(),M=se(p,"modelValue"),T=_(!1),b=_(!1),U=_(`${C().format("YYYY-MM-DD")} ${me.name}的工作日记`),y=_([0]);function F(){return{jobDescription:"",ccUserIds:[],jobDetail:"",workTimeRange:[],estimatedWorkNum:0,workitemId:void 0,filePath:[],remark:"",collapseName:"",workitemLogList:[]}}const V=F();V.collapseName=0,V.showCollapseName="工作A";const t=_([]);t.value.push(V);const q=le(()=>r.id?t==null?void 0:t.value.some(e=>e.isTemp===0):!0);function K(){const e=F(),a=t.value.length;y.value.push(a),e.collapseName=a,e.showCollapseName=`工作${I(a)}`,t.value.push(e)}function E(){t.value=[];const e=F();e.collapseName=0,e.showCollapseName="工作A",t.value=[e]}function X(e){if(t.value.length===1)return v.error("至少保留一个工作");t.value.splice(e,1),y.value.splice(e,1),t.value.forEach((a,s)=>{a.collapseName=s,a.showCollapseName=`工作${I(s)}`})}async function G(e){var a,s,n;try{T.value=!0;let o=await N.pims.daliyReport.info({id:e});o.workTimeRange=o.workTimeRange?JSON.parse(o.workTimeRange):[],((a=o.ccUserIds)==null?void 0:a.length)>0&&(o.ccUserIds=o.ccUserIds.split(",").map(i=>Number.parseInt(i))),o.workitemId&&(o.workitemId=Number.parseInt(o.workitemId)),((s=o.filePath)==null?void 0:s.length)>0&&(o.filePath=o.filePath.split(",")),k("handleData",o,i=>{o=i}),t.value[0]=o,t.value[0].collapseName=0,t.value[0].showCollapseName="工作A",U.value=`${C(o.createTime).format("YYYY-MM-DD")} ${(n=o==null?void 0:o.creator)==null?void 0:n.name}的工作日记`}catch(o){console.error(o)}finally{T.value=!1}}async function Y(e,a){var n;let s=0;if(((n=$.value)==null?void 0:n.length)>0)for(const o of $.value){try{await o.validate()}catch{y.value[s]=t.value[s].collapseName,v.error(`请检查${t.value[s].showCollapseName}是否填写完整`);return}s++}try{const o=ue(t.value),i=[],u=[];o.forEach(l=>{e==="submit"&&(l.isTemp=1),l.ccUserIds.length===0?l.ccUserIds="":l.ccUserIds=l.ccUserIds.join(","),l.filePath.length===0&&(l.filePath=""),l.id&&l.id>0?u.push(l):i.push(l)}),b.value=!0,e==="submit"?(await N.pims.daliyReport.submitReport({data:o}),E(),h(),v.success("提交成功")):(i.length>0&&await N.pims.daliyReport.add({data:i}),u.length>0&&await N.pims.daliyReport.update({data:u}),v.success(a||"保存成功")),B(),k("refresh")}catch(o){return console.error(o),v.error(o.messages||"保存失败")}finally{b.value=!1}}async function Q(){await O.confirm("日报提交后不能修改删除,您确定提交吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"})&&await Y("submit","提交成功")}async function Z(e){if(!e.row.id&&t.value.length>1){X(e.index);return}if(await O.confirm("确定删除吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}))try{T.value=!0,await N.pims.daliyReport.delete({ids:[e.row.id]}),v.success("删除成功")}catch(s){return console.error(s),v.error(s.messages||"删除失败")}finally{T.value=!1}}function ee(e){!e||e.length===0?t.value.forEach(a=>{const s=P(a);s&&(a.showCollapseName=s)}):t.value.forEach(a=>{const s=e.find(n=>n===a.collapseName);if(s)a.showCollapseName=`工作${I(s)}`;else{const n=P(a);n&&(a.showCollapseName=n)}})}function P(e){if(y.value.includes(e.collapseName))return`工作${I(e.collapseName)}`;if(e.workTimeRange&&e.workTimeRange.length>0&&e.jobDescription)return`${C(e.workTimeRange[0]).format("HH:mm")} ~ ${C(e.workTimeRange[1]).format("HH:mm")} ${e.jobDescription}`}function B(){M.value=!1,E(),r.id&&r.id>0&&k("update:id",void 0)}function oe(){return r.id?t!=null&&t.value.some(e=>e.isTemp===0)?"修改工作日记":"查看工作日记":"新建工作日记"}return ne(async()=>{r.id&&r.id>0&&await G(r.id)}),(e,a)=>{const s=w("DeleteFilled"),n=w("el-icon"),o=w("el-collapse-item"),i=w("el-collapse"),u=w("el-button"),l=w("el-drawer");return g(),D("div",null,[f(l,{modelValue:M.value,"onUpdate:modelValue":a[1]||(a[1]=d=>M.value=d),title:oe(),"append-to-body":"",size:"40%",onClose:B},{footer:c(()=>[H(m).footer?A(e.$slots,"footer",{key:0},void 0,!0):(g(),D("div",ke,[f(u,{"mr-10px":"",onClick:B},{default:c(()=>[x(" 关闭 ")]),_:1}),H(q)?(g(),D("div",Ne,[f(u,{type:"primary",onClick:K},{default:c(()=>[x(" 添加工作 ")]),_:1}),f(u,{type:"primary",loading:b.value,onClick:Y},{default:c(()=>[x(" 暂存草稿 ")]),_:1},8,["loading"]),f(u,{type:"success",loading:b.value,onClick:Q},{default:c(()=>[x(" 提交 ")]),_:1},8,["loading"])])):L("",!0)]))]),default:c(()=>[S("div",_e,[S("div",we,re(U.value),1),S("div",ge,[f(i,{modelValue:y.value,"onUpdate:modelValue":a[0]||(a[0]=d=>y.value=d),onChange:ee},{default:c(()=>[(g(!0),D(ie,null,ce(t.value,(d,j)=>(g(),J(o,{key:d.id||j,px:"10px",class:"del_relative",title:d.showCollapseName,name:j},{default:c(()=>[t.value.length>1?(g(),J(n,{key:0,size:"18",color:"#f56c6c",class:"delete",onClick:de(De=>Z({row:d,index:j}),["stop"])},{default:c(()=>[f(s)]),_:2},1032,["onClick"])):L("",!0),f(pe,{ref_for:!0,ref_key:"DetailFormRef",ref:$,form:d,"open-type":r.openType},{default:c(()=>[A(e.$slots,"default",{},void 0,!0)]),_:2},1032,["form","open-type"])]),_:2},1032,["title","name"]))),128))]),_:3},8,["modelValue"])])])]),_:3},8,["modelValue","title"])])}}}),Ce=he(be,[["__scopeId","data-v-800046de"]]),Ve=Object.freeze(Object.defineProperty({__proto__:null,default:Ce},Symbol.toStringTag,{value:"Module"}));export{Ce as D,Ve as d,ye as p};
