package service

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"net/url"
	"strings"
	"time"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/imhuso/lookah-erp/admin/modules/pms/model"
	"github.com/imhuso/lookah-erp/admin/yc"
	"github.com/xuri/excelize/v2"
)

type PmsDailyReportDataService struct {
	*yc.Service
}

// removeDuplicateFromCommaSeparatedString 去除逗号分隔字符串中的重复元素
func removeDuplicateFromCommaSeparatedString(str string) string {
	if str == "" {
		return ""
	}

	// 分割字符串
	parts := strings.Split(str, ",")

	// 使用map去重
	seen := make(map[string]bool)
	var result []string

	for _, part := range parts {
		// 去除前后空格
		trimmed := strings.TrimSpace(part)
		if trimmed != "" && !seen[trimmed] {
			seen[trimmed] = true
			result = append(result, trimmed)
		}
	}

	// 重新组合字符串
	return strings.Join(result, ",")
}

// GetEmployeeWorking 获取员工工时统计
func (s *PmsDailyReportDataService) GetEmployeeWorking(ctx context.Context, reqVo *model.QueryVo) (result model.PageResult[*model.FinancePaymentVo], err error) {
	//r := g.RequestFromCtx(ctx)
	//rMap := r.GetMap()

	fmt.Printf("reqVo=====================%+v\n", reqVo)
	return
}

// SyncReportData 同步生产日报数据
func SyncReportData(ctx context.Context, info *model.PmsDailyReportData) (err error) {
	dailyReportData := make([]*model.PmsDailyReportData, 0)
	// 查询生产日报信息
	m := yc.DBM(model.NewPmsDailyReportData()).Ctx(ctx).
		Where("produced_date", info.ProducedDate.Format("Y-m-d")).
		Where("order_id", info.OrderId).
		Where("product_id", info.ProductId).
		Where("workshop_id", info.WorkshopId).
		Where("production_stages", info.ProductionStages).
		Where("workshop_section", info.WorkshopSection)

	count, err := m.Clone().Count()
	if err != nil {
		return
	}
	if count <= 0 {
		// 如果符合条件的数据已经不存在，则删除生产日报列表数据
		_, err = yc.DBM(model.NewPmsDailyProductionReport()).Ctx(ctx).
			Where("produced_date", info.ProducedDate.Format("Y-m-d")).
			Where("order_id", info.OrderId).
			Where("product_id", info.ProductId).
			Where("workshop_id", info.WorkshopId).
			Where("production_stages", info.ProductionStages).
			Where("workshop_section", info.WorkshopSection).Delete()
		return
	}
	err = m.Scan(&dailyReportData)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return
	}
	var TotalNumberOfPeople int
	var TotalManHour float32
	var TotalDailyOutput float32

	busyworkGroup := ""
	productionLine := ""
	step := ""

	// 计算人员总数、累计工时、累计产能
	for _, item := range dailyReportData {
		TotalNumberOfPeople += item.NumberOfPeople //总人数
		TotalManHour += item.ManHour               // 累计工时
		TotalDailyOutput += item.DailyOutput       // 累计产能
		productionLine += step + item.ProductionLine
		busyworkGroup += step + item.BusyworkGroup
		step = ","
	}
	reportInfo := &model.PmsDailyProductionReport{}
	// 查询成本表
	err = yc.DBM(model.NewPmsDailyProductionReport()).Ctx(ctx).
		Where("produced_date", info.ProducedDate.Format("Y-m-d")).
		Where("order_id", info.OrderId).
		Where("product_id", info.ProductId).
		Where("workshop_id", info.WorkshopId).
		Where("production_stages", info.ProductionStages).
		Where("workshop_section", info.WorkshopSection).
		Scan(&reportInfo)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return err
	}
	flag := true
	if errors.Is(err, sql.ErrNoRows) {
		flag = false
	}
	// 成本表查询到数据
	if flag {
		// 重构成本表数据
		reportInfo.BusyworkGroup = removeDuplicateFromCommaSeparatedString(busyworkGroup)
		reportInfo.ProductionLine = removeDuplicateFromCommaSeparatedString(productionLine)
		reportInfo.NumberOfPeople = TotalNumberOfPeople
		reportInfo.ManHour = TotalManHour
		reportInfo.DailyOutput = TotalDailyOutput
		// 更新成本表
		_, err = yc.DBM(model.NewPmsDailyProductionReport()).Ctx(ctx).
			Where("id", reportInfo.ID).
			Data(reportInfo).
			Update()
	} else {
		reportInfo.BusyworkGroup = removeDuplicateFromCommaSeparatedString(busyworkGroup)
		reportInfo.ProductionLine = removeDuplicateFromCommaSeparatedString(productionLine)
		reportInfo.NumberOfPeople = TotalNumberOfPeople
		reportInfo.ManHour = TotalManHour
		reportInfo.DailyOutput = TotalDailyOutput
		reportInfo.Sku = info.Sku
		reportInfo.ProductId = info.ProductId
		reportInfo.Quantity = info.Quantity
		reportInfo.OrderId = info.OrderId
		reportInfo.ProducedDate = info.ProducedDate
		reportInfo.ProductionStages = info.ProductionStages
		reportInfo.WorkshopId = info.WorkshopId
		reportInfo.WorkshopSection = info.WorkshopSection
		reportInfo.ProductionStages = info.ProductionStages
		// 插入成本表
		_, err = yc.DBM(model.NewPmsDailyProductionReport()).Ctx(ctx).
			Where("id", reportInfo.ID).
			Data(reportInfo).
			Insert()
	}
	return
}

// ServiceDelete 删除数据
func (s *PmsDailyReportDataService) ServiceDelete(ctx context.Context, _ *yc.DeleteReq) (data interface{}, err error) {
	r := g.RequestFromCtx(ctx)
	rMap := r.GetMap()
	ids := gconv.Int64s(rMap["ids"])
	if len(ids) == 0 {
		return nil, gerror.New("数据有误")
	}
	// 查询分组信息
	infos := make([]*model.PmsDailyReportData, 0)
	err = yc.DBM(s.Model).Ctx(ctx).WhereIn("id", ids).Group("group_id").Scan(&infos)
	if err != nil {
		return nil, err
	}
	// 开启事务
	err = yc.DB(s.Model.GroupName()).Ctx(ctx).Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		//删除数据
		_, err = yc.DBM(s.Model).Ctx(ctx).WhereIn("id", ids).Delete()
		if err != nil {
			return err
		}

		// 同步更新成本表数据
		for _, info := range infos {
			// 重新计算数据表内统计信息
			// 查询分组信息
			dailyReportGroupData := make([]*model.PmsDailyReportData, 0)
			err = yc.DBM(s.Model).Ctx(ctx).Where("group_id", info.GroupId).Scan(&dailyReportGroupData)
			if err != nil {
				return err
			}
			var TotalNumberOfPeople int
			var TotalManHour float32
			var TotalDailyOutput float32
			// 计算统计信息
			for _, item := range dailyReportGroupData {
				TotalNumberOfPeople += item.NumberOfPeople
				TotalManHour += item.ManHour
				TotalDailyOutput += item.DailyOutput
			}
			// 完成更新
			for _, item := range dailyReportGroupData {
				item.TotalNumberOfPeople = TotalNumberOfPeople
				item.TotalManHour = TotalManHour
				item.TotalDailyOutput = TotalDailyOutput
				if item.TotalManHour > 0 {
					item.AverageCapacity = item.TotalDailyOutput / item.TotalManHour
				} else {
					item.AverageCapacity = 0
				}
				_, err = yc.DBM(s.Model).Ctx(ctx).Data(item).OmitEmptyData().Where("id", item.ID).Update()
				if err != nil {
					return err
				}
			}

			err = SyncReportData(ctx, info)
			if err != nil {
				return err
			}
		}

		return nil
	})
	return
}

// ServiceUpdate 更新数据
func (s *PmsDailyReportDataService) ServiceUpdate(ctx context.Context, _ *yc.UpdateReq) (data interface{}, err error) {
	r := g.RequestFromCtx(ctx)
	rMap := r.GetMap()
	var dailyReportData model.PmsDailyReportData
	err = gconv.Scan(rMap, &dailyReportData)
	if err != nil {
		return 0, err
	}
	// 查询是否有重复项冲突
	count, err := yc.DBM(s.Model).Ctx(ctx).
		WhereNot("group_id", dailyReportData.GroupId).
		Where("produced_date", dailyReportData.ProducedDate.Format("Y-m-d")).
		Where("order_id", dailyReportData.OrderId).
		Where("product_id", dailyReportData.ProductId).
		Where("workshop_id", dailyReportData.WorkshopId).
		Where("production_line", dailyReportData.ProductionLine).
		Where("production_stages", dailyReportData.ProductionStages).
		Where("workshop_section", dailyReportData.WorkshopSection).
		Count()
	if err != nil {
		return nil, err
	}
	if count > 0 {
		return nil, gerror.New("更新失败，发现重复项！")
	}

	// 查询原始数据
	originalDailyReportData := &model.PmsDailyReportData{}
	err = yc.DBM(s.Model).Ctx(ctx).Where("id", dailyReportData.ID).Scan(&originalDailyReportData)
	if err != nil {
		return nil, err
	}
	// 查询订单信息
	var orderInfo *model.PmsProductionScheduleProduct
	err = yc.DBM(model.NewPmsProductionScheduleProduct()).Ctx(ctx).
		Where("schedule_id", dailyReportData.OrderId).Scan(&orderInfo)
	if err != nil {
		return nil, err
	}

	// 查询产品信息
	var productInfo *model.PmsProduct
	err = yc.DBM(model.NewPmsProduct()).Ctx(ctx).
		Where("id", dailyReportData.ProductId).Scan(&productInfo)
	if err != nil {
		return nil, err
	}

	// 开启事务
	err = yc.DB(s.Model.GroupName()).Ctx(ctx).Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 更新工时、当日产能数据、作业人员、备注
		_, err = yc.DBM(s.Model).Ctx(ctx).Where("id", dailyReportData.ID).Data(g.Map{
			"man_hour":       dailyReportData.ManHour,
			"daily_output":   dailyReportData.DailyOutput,
			"busywork_group": dailyReportData.BusyworkGroup,
			"remark":         dailyReportData.Remark,
		}).Update()
		if err != nil {
			return err
		}

		// 查询相同分组的信息
		dailyReportDatas := make([]*model.PmsDailyReportData, 0)
		err = yc.DBM(s.Model).Ctx(ctx).Where("group_id", dailyReportData.GroupId).Scan(&dailyReportDatas)
		if err != nil {
			return err
		}
		var TotalNumberOfPeople int
		var TotalManHour float32
		var TotalDailyOutput float32
		// 需要更新同组的产品、订单、工序、生产阶段、产能、生产车间等数据
		for _, item := range dailyReportDatas {
			item.Sku = productInfo.Sku
			item.ProductId = dailyReportData.ProductId
			item.ProducedDate = dailyReportData.ProducedDate
			item.Quantity = orderInfo.Quantity
			item.OrderId = dailyReportData.OrderId
			item.WorkshopId = dailyReportData.WorkshopId
			item.ProductionLine = dailyReportData.ProductionLine
			item.ProductionStages = dailyReportData.ProductionStages
			item.WorkshopSection = dailyReportData.WorkshopSection
			TotalNumberOfPeople += item.NumberOfPeople
			TotalManHour += item.ManHour
			TotalDailyOutput += item.DailyOutput
		}
		// 完成更新
		for _, item := range dailyReportDatas {
			item.TotalNumberOfPeople = TotalNumberOfPeople
			item.TotalManHour = TotalManHour
			item.TotalDailyOutput = TotalDailyOutput
			if item.TotalManHour > 0 {
				item.AverageCapacity = item.TotalDailyOutput / item.TotalManHour
			} else {
				item.AverageCapacity = 0
			}
			_, err = yc.DBM(s.Model).Ctx(ctx).Data(item).OmitEmptyData().Where("id", item.ID).Update()
			if err != nil {
				return err
			}
		}

		// 查询当前数据
		currentDailyReportData := &model.PmsDailyReportData{}
		err = yc.DBM(s.Model).Ctx(ctx).Where("id", dailyReportData.ID).Scan(&currentDailyReportData)
		if err != nil {
			return err
		}
		// 同步原本关联的数据
		err = SyncReportData(ctx, originalDailyReportData)
		if err != nil {
			return err
		}

		// 同步当前关联数据
		err = SyncReportData(ctx, currentDailyReportData)
		if err != nil {
			return err
		}

		return nil
	})

	return
}

// ExportDailyReportData 导出产能数据
func (s *PmsDailyReportDataService) ExportDailyReportData(ctx context.Context, dateRange []string, order_id int64, keyWord string,
	workshop_section int, production_stages int, workshop_id int64) (fileName string, bytes []byte, err error) {
	m := yc.DBM(s.Model).Ctx(ctx)
	if len(dateRange) == 2 {
		t1 := gconv.GTime(dateRange[0], "Y-m-d")
		t2 := gconv.GTime(dateRange[1], "Y-m-d")
		if t1.Unix() < t2.Unix() {
			m = m.Where("produced_date between ? and ?", t1, t2)
		} else if t1.Unix() > t2.Unix() {
			m = m.Where("produced_date between ? and ?", t2, t1)
		} else if t1.Unix() == t2.Unix() {
			m = m.Where("produced_date", t1)
		}
	}
	if order_id > 0 {
		m = m.Where("order_id", order_id)
	}
	if keyWord != "" {
		m = m.Where("sku like ? or busywork_group like ?", "%"+keyWord+"%", "%"+keyWord+"%")
	}
	if workshop_section > 0 {
		m = m.Where("workshop_section", workshop_section)
	}
	if production_stages > 0 {
		m = m.Where("production_stages", production_stages)
	}

	if workshop_id > 0 {
		m = m.Where("workshop_id", workshop_id)
	}

	dailyReportData := make([]*model.PmsDailyReportDataOutput, 0)
	err = m.OrderDesc("group_id").Scan(&dailyReportData)
	if err != nil {
		return "", nil, err
	}

	if len(dailyReportData) == 0 {
		return "", nil, gerror.New("无数据可导出")
	}

	// 计算剩余生产数量
	// 查询已生产数量
	type ProductionInfo struct {
		OrderId         int64  `json:"order_id"`
		Sku             string `json:"sku"`
		WorkshopSection int    `json:"workshop_section"`
		DailyOutput     int    `json:"daily_output"`
	}
	productionInfos := make([]ProductionInfo, 0)
	err = yc.DBM(model.NewPmsDailyProductionReport()).Fields("order_id,sku,workshop_section,sum(daily_output) as daily_output").
		Group("order_id,sku,workshop_section").Scan(&productionInfos)
	if err != nil {
		return "", nil, err
	}
	for _, item := range dailyReportData {
		for _, info := range productionInfos {
			if item.OrderId == info.OrderId && item.Sku == info.Sku && item.WorkshopSection == info.WorkshopSection {
				item.ExceptQuantity = item.Quantity - info.DailyOutput
			}
		}
	}

	// 获取字典中所有的颜色
	colors, _ := NewPmsProductService().GetAllColor(ctx)
	// 获取生产车间
	workshopList, err := NewPmsProcessAbnormalityService().QueryDictByKey(ctx, "product_floor")
	mWorkshoplist := make(map[int64]string, 0)
	for _, item := range workshopList {
		mWorkshoplist[item.ID] = item.Name
	}
	ProductList, err := NewPmsProductService().GetAllProduct(ctx)
	idMap := make(map[int64]*model.PmsProduct)
	for _, item := range ProductList {
		idMap[item.ID] = item
	}

	f := excelize.NewFile()
	sheet := "Sheet1"
	f.SetSheetName("Sheet1", sheet)

	// 表头
	headers := []string{"日期", "订单号", "订单数量(PCS)", "剩余订单数量(PCS)", "机型", "SKU", "颜色", "生产车间", "线别", "作业人员", "工序", "生产阶段", "人数(人)", "合计人数(人)", "累计工时(H)", "合计累计工时(H)", "当日产能(PCS)", "合计当日产能(PCS)", "人均产能(PCS/H)", "备注"}
	for i, h := range headers {
		col, _ := excelize.ColumnNumberToName(i + 1)
		f.SetCellValue(sheet, col+"1", h)
	}

	// 设置表头加粗、居中
	headStyle, _ := f.NewStyle(&excelize.Style{
		Font:      &excelize.Font{Bold: true},
		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center"},
	})
	f.SetCellStyle(sheet, "A1", "T1", headStyle)

	// 设置内容居中
	contentStyle, _ := f.NewStyle(&excelize.Style{
		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center"},
	})

	row := 2

	groupMap := make(map[int][]*model.PmsDailyReportDataOutput)
	orderIds := make([]int64, 0)

	for _, d := range dailyReportData {
		groupMap[d.GroupId] = append(groupMap[d.GroupId], d)
		orderIds = append(orderIds, d.OrderId)
	}
	orderInfos := make([]*model.PmsProductionSchedule, 0)
	// 删除重复元素
	orderIds = RemoveDuplicateInt64(orderIds)
	err = yc.DBM(model.NewPmsProductionSchedule()).Ctx(ctx).WhereIn("id", orderIds).Scan(&orderInfos)
	orderMap := make(map[int64]*model.PmsProductionSchedule, 0)
	for _, item := range orderInfos {
		orderMap[item.ID] = item
	}

	for _, group := range groupMap {
		groupLen := len(group)
		startRow := row
		for _, d := range group {
			orderSn := "-"
			if item, ok := orderMap[d.OrderId]; ok {
				orderSn = item.Sn
			}

			// 转换工段值为对应的标签
			workshopSection := "-"
			if v, ok := WORKSHOP_SECTIONS[d.WorkshopSection]; ok {
				workshopSection = v
			}
			var product *model.PmsProduct
			if v, ok := idMap[d.ProductId]; ok {
				product = v
			} else {
				return "", nil, gerror.New("缺少产品信息！")
			}
			color_name := "-"
			color := product.Color
			if color > 0 {
				if c, ok := colors[color]; ok {
					color_name = c.Name
				} else {
					color_name = "-"
				}
			}
			product_name := fmt.Sprintf("%s", product.Name)

			f.SetCellValue(sheet, "A"+gconv.String(row), d.ProducedDate.Format("Y-m-d"))
			f.SetCellValue(sheet, "B"+gconv.String(row), orderSn)
			f.SetCellValue(sheet, "C"+gconv.String(row), d.Quantity)
			f.SetCellValue(sheet, "D"+gconv.String(row), d.ExceptQuantity) // 新增：剩余订单数量
			f.SetCellValue(sheet, "E"+gconv.String(row), product_name)
			f.SetCellValue(sheet, "F"+gconv.String(row), d.Sku)
			f.SetCellValue(sheet, "G"+gconv.String(row), color_name)
			workshop_name := "-"
			if v, ok := mWorkshoplist[d.WorkshopId]; ok {
				workshop_name = v
			}
			f.SetCellValue(sheet, "H"+gconv.String(row), workshop_name)
			f.SetCellValue(sheet, "I"+gconv.String(row), d.ProductionLine)
			f.SetCellValue(sheet, "J"+gconv.String(row), d.BusyworkGroup)
			f.SetCellValue(sheet, "K"+gconv.String(row), workshopSection)
			f.SetCellValue(sheet, "L"+gconv.String(row), GetProductionStageLabel(d.ProductionStages))
			f.SetCellValue(sheet, "M"+gconv.String(row), d.NumberOfPeople)
			f.SetCellValue(sheet, "N"+gconv.String(row), d.TotalNumberOfPeople)
			f.SetCellValue(sheet, "O"+gconv.String(row), d.ManHour)
			f.SetCellValue(sheet, "P"+gconv.String(row), d.TotalManHour)
			f.SetCellValue(sheet, "Q"+gconv.String(row), d.DailyOutput)
			f.SetCellValue(sheet, "R"+gconv.String(row), d.TotalDailyOutput)
			f.SetCellValue(sheet, "S"+gconv.String(row), d.AverageCapacity)
			f.SetCellValue(sheet, "T"+gconv.String(row), d.Remark)
			for colIdx := 1; colIdx <= len(headers); colIdx++ {
				col, _ := excelize.ColumnNumberToName(colIdx)
				f.SetCellStyle(sheet, col+gconv.String(row), col+gconv.String(row), contentStyle)
			}
			row++
		}
		endRow := row - 1
		// 合并前9列（A-I），包括新增的D列
		for i := 0; i < 9; i++ {
			col, _ := excelize.ColumnNumberToName(i + 1)
			if groupLen > 1 {
				f.MergeCell(sheet, col+gconv.String(startRow), col+gconv.String(endRow))
			}
		}
		// 合并指定的列：K、L、N、P、R、S（因为插入了D列，所有列都向后移动了一位）
		if groupLen > 1 {
			// K列 - 工序
			f.MergeCell(sheet, "K"+gconv.String(startRow), "K"+gconv.String(endRow))
			// L列 - 生产阶段
			f.MergeCell(sheet, "L"+gconv.String(startRow), "L"+gconv.String(endRow))
			// N列 - 合计人数
			f.MergeCell(sheet, "N"+gconv.String(startRow), "N"+gconv.String(endRow))
			// P列 - 合计累计工时
			f.MergeCell(sheet, "P"+gconv.String(startRow), "P"+gconv.String(endRow))
			// R列 - 合计当日产能
			f.MergeCell(sheet, "R"+gconv.String(startRow), "R"+gconv.String(endRow))
			// S列 - 人均产能
			f.MergeCell(sheet, "S"+gconv.String(startRow), "S"+gconv.String(endRow))
		}
	}

	// 自动列宽，E列（机型）宽度加大
	for i := 1; i <= len(headers); i++ {
		col, _ := excelize.ColumnNumberToName(i)
		if col == "E" { // E列现在是机型
			f.SetColWidth(sheet, col, col, 25)
		} else {
			f.SetColWidth(sheet, col, col, 15)
		}
	}

	// 导出为字节数组
	buffer, err := f.WriteToBuffer()
	if err != nil {
		return "", nil, err
	}
	fileName = fmt.Sprintf("每日生产数据表_%s.xlsx", time.Now().Format("20060102150405"))
	fileName = url.QueryEscape(fileName)
	return fileName, buffer.Bytes(), nil
}

// ImportDailyReportData 导入产能数据
func (s *PmsDailyReportDataService) ImportDailyReportData(ctx context.Context, req []*model.PmsDailyReportData) (data interface{}, err error) {
	numberOfPeoplemap := make(map[string]int)
	orderIds := make([]int64, 0)
	totalManHourMap := make(map[string]float32)
	totalDailyOutputMap := make(map[string]float32)
	for _, item := range req {
		// 检查重复数据
		count, err := yc.DBM(s.Model).Ctx(ctx).
			Where("produced_date", item.ProducedDate.Format("Y-m-d")).
			Where("order_id", item.OrderId).
			Where("sku", item.Sku).
			Where("workshop_id", item.WorkshopId).
			Where("production_line", item.ProductionLine).
			Where("production_stages", item.ProductionStages).
			Where("workshop_section", item.WorkshopSection).
			Count()
		if err != nil && !errors.Is(err, sql.ErrNoRows) {
			return nil, err
		}
		if count > 0 {
			return nil, gerror.New("发现重复数据！")
		}
		orderIds = append(orderIds, item.OrderId)
		key := fmt.Sprintf("%s_%d_%s_%d_%s_%d_%d", item.ProducedDate, item.OrderId, item.Sku, item.WorkshopId, item.ProductionLine, item.WorkshopSection, item.ProductionStages)
		if _, ok := totalDailyOutputMap[key]; ok {
			totalDailyOutputMap[key] += item.DailyOutput
		} else {
			totalDailyOutputMap[key] = item.DailyOutput
		}

		if _, ok := totalManHourMap[key]; ok {
			totalManHourMap[key] += item.ManHour
		} else {
			totalManHourMap[key] = item.ManHour
		}

		if _, ok := numberOfPeoplemap[key]; ok {
			numberOfPeoplemap[key]++
		} else {
			numberOfPeoplemap[key] = 1
		}
	}

	orderInfo := make([]*model.PmsProductionScheduleProduct, 0)
	// 查询订单数量
	if len(orderIds) <= 0 {
		return nil, gerror.New("没有订单数据，请检查导入文件")
	} else {
		queryOrderInfoStr := ""
		queryOrderInfoStrStep := ""
		for _, item := range req {
			queryOrderInfoStr += queryOrderInfoStrStep + fmt.Sprintf("product_id = %d and schedule_id = %d", item.ProductId, item.OrderId)
			queryOrderInfoStrStep = " OR "
		}
		err := yc.DBM(model.NewPmsProductionScheduleProduct()).As("s").Ctx(ctx).
			Where(queryOrderInfoStr).
			Scan(&orderInfo)

		if err != nil {
			return nil, err
		}
	}
	orderMap := make(map[int64]map[int64]int)
	for _, item := range orderInfo {
		// 先初始化内层map
		if _, exist := orderMap[item.ProductId]; !exist {
			orderMap[item.ProductId] = make(map[int64]int)
		}
		// 现在可以安全赋值
		orderMap[item.ProductId][item.ScheduleId] = item.Quantity
	}

	//查询最大组别数
	max_group_id, err := yc.DBM(s.Model).Ctx(ctx).Fields("max(group_id) as max_group_id").Value()
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return nil, err
	}
	maxValue := 0
	if errors.Is(err, sql.ErrNoRows) {
		maxValue = 0
	} else {
		maxValue = max_group_id.Int()
	}

	i := 0
	for _, item := range req {
		key := fmt.Sprintf("%s_%d_%s_%d_%s_%d_%d", item.ProducedDate, item.OrderId, item.Sku, item.WorkshopId, item.ProductionLine, item.WorkshopSection, item.ProductionStages)
		item.TotalNumberOfPeople = numberOfPeoplemap[key]
		item.TotalManHour = totalManHourMap[key]
		item.TotalDailyOutput = totalDailyOutputMap[key]
		//item.AverageCapacity = item.TotalDailyOutput / item.TotalManHour
		if item.TotalManHour > 0 {
			item.AverageCapacity = item.TotalDailyOutput / item.TotalManHour
		} else {
			item.AverageCapacity = 0
		}
		if _, ok1 := orderMap[item.ProductId]; ok1 {
			if v2, ok2 := orderMap[item.ProductId][item.OrderId]; ok2 {
				item.Quantity = v2
			}
		}

		i++
		if i == 1 {
			maxValue++
		}
		item.GroupId = maxValue
		// 计算完一组数据
		if i == item.TotalNumberOfPeople {
			i = 0
		}
	}
	var insertCount int64
	// 开启事务
	err = yc.DB(s.Model.GroupName()).Ctx(ctx).Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		res, err := yc.DBM(s.Model).Ctx(ctx).Data(req).Insert()
		if err != nil {
			return err
		}
		if res != nil {
			insertCount, err = res.RowsAffected()
		}
		if err != nil {
			return err
		}

		// 分组
		infoMap := make(map[int][]*model.PmsDailyReportData)
		for _, info := range req {
			if _, ok := infoMap[info.GroupId]; ok {
				infoMap[info.GroupId] = append(infoMap[info.GroupId], info)
			} else {
				infoMap[info.GroupId] = make([]*model.PmsDailyReportData, 0)
				infoMap[info.GroupId] = append(infoMap[info.GroupId], info)
			}
		}
		for _, item := range infoMap {
			if len(item) > 0 {
				reportInfo := &model.PmsDailyProductionReport{}
				reportInfo.ProducedDate = item[0].ProducedDate
				reportInfo.Sku = item[0].Sku
				reportInfo.OrderId = item[0].OrderId
				reportInfo.Quantity = item[0].Quantity
				reportInfo.ProductionStages = item[0].ProductionStages
				reportInfo.WorkshopId = item[0].WorkshopId
				reportInfo.WorkshopSection = item[0].WorkshopSection
				reportInfo.ProductId = item[0].ProductId
				reportInfo.ProductionStages = item[0].ProductionStages
				busyworkGroup := ""
				productionLine := ""
				step := ""
				for _, info := range item {
					reportInfo.NumberOfPeople += info.NumberOfPeople
					reportInfo.ManHour += info.ManHour
					reportInfo.DailyOutput += info.DailyOutput

					productionLine = info.ProductionLine
					busyworkGroup += step + info.BusyworkGroup
					step = ","
				}
				YetReportInfo := &model.PmsDailyProductionReport{}

				// 查询成本表内数据
				err := yc.DBM(model.NewPmsDailyProductionReport()).Ctx(ctx).
					Where("produced_date", reportInfo.ProducedDate.Format("Y-m-d")).
					Where("order_id", reportInfo.OrderId).
					Where("product_id", reportInfo.ProductId).
					Where("workshop_id", reportInfo.WorkshopId).
					Where("production_stages", reportInfo.ProductionStages).
					Where("workshop_section", reportInfo.WorkshopSection).
					Scan(&YetReportInfo)
				if err != nil && !errors.Is(err, sql.ErrNoRows) {
					return err
				}
				flag := true
				if errors.Is(err, sql.ErrNoRows) {
					flag = false
				}
				// 如果已存在数据
				if flag {
					if YetReportInfo.BusyworkGroup != "" || YetReportInfo.ProductionLine != "" {
						// 合并作业人员字符串并去重
						combinedBusyworkGroup := ""
						if YetReportInfo.BusyworkGroup != "" {
							combinedBusyworkGroup = YetReportInfo.BusyworkGroup + "," + busyworkGroup
						} else {
							combinedBusyworkGroup = busyworkGroup
						}
						reportInfo.BusyworkGroup = removeDuplicateFromCommaSeparatedString(combinedBusyworkGroup)

						combinedProductionLine := ""
						if YetReportInfo.ProductionLine != "" {
							combinedProductionLine = YetReportInfo.ProductionLine + "," + productionLine
						} else {
							combinedProductionLine = productionLine
						}
						reportInfo.ProductionLine = removeDuplicateFromCommaSeparatedString(combinedProductionLine)

						reportInfo.NumberOfPeople += YetReportInfo.NumberOfPeople
						reportInfo.ManHour += YetReportInfo.ManHour
						reportInfo.DailyOutput += YetReportInfo.DailyOutput
					} else {
						reportInfo.BusyworkGroup = removeDuplicateFromCommaSeparatedString(busyworkGroup)
						reportInfo.ProductionLine = removeDuplicateFromCommaSeparatedString(productionLine)
					}
					_, err = yc.DBM(model.NewPmsDailyProductionReport()).Ctx(ctx).
						Where("id", YetReportInfo.ID).OmitEmptyData().Data(reportInfo).Update()
					if err != nil {
						return err
					}
				} else {
					reportInfo.BusyworkGroup = busyworkGroup
					reportInfo.ProductionLine = productionLine
					_, err = yc.DBM(model.NewPmsDailyProductionReport()).Ctx(ctx).
						Data(reportInfo).Insert()
				}
				if err != nil {
					return err
				}
			}
		}

		return nil
	})
	return insertCount, err
}

// addDailyReportQueryConditions 添加查询条件
func addDailyReportQueryConditions(ctx g.Ctx, builder *gdb.WhereBuilder) *gdb.WhereBuilder {
	request := g.RequestFromCtx(ctx)
	// 判断是否传入dateRange
	dateRange := request.Get("dateRange").Array()
	if len(dateRange) > 0 {
		t1 := gconv.GTime(dateRange[0], "Y-m-d")
		t2 := gconv.GTime(dateRange[1], "Y-m-d")
		if t1.Unix() < t2.Unix() {
			builder = builder.Where("produced_date between ? and ?", t1, t2)
		} else if t1.Unix() > t2.Unix() {
			builder = builder.Where("produced_date between ? and ?", t2, t1)
		} else if t1.Unix() == t2.Unix() {
			builder = builder.Where("produced_date", t1)
		}
	}

	return builder
}

func NewPmsDailyReportDataService() *PmsDailyReportDataService {
	return &PmsDailyReportDataService{
		&yc.Service{
			Model: model.NewPmsDailyReportData(),
			PageQueryOp: &yc.QueryOp{
				FieldEQ:      []string{"order_id", "workshop_section", "production_stages", "workshop_id"},
				KeyWordField: []string{"sku", "busywork_group"},
				Where:        addDailyReportQueryConditions,
				OrderByHandle: func(ctx context.Context, m *gdb.Model) {
					m = m.Order("group_id desc")
				},
				ResultModify: func(ctx context.Context, data interface{}) (res interface{}, err error) {
					dailyReportDataOutput := make([]*model.PmsDailyReportDataOutput, 0)
					err = gconv.Structs(data, &dailyReportDataOutput)
					if err != nil {
						return nil, err
					}
					if len(dailyReportDataOutput) > 0 {
						// 计算剩余生产数量
						// 查询已生产数量
						type ProductionInfo struct {
							OrderId         int64  `json:"order_id"`
							Sku             string `json:"sku"`
							WorkshopSection int    `json:"workshop_section"`
							DailyOutput     int    `json:"daily_output"`
						}
						productionInfos := make([]ProductionInfo, 0)
						// 查询成本表数据
						err = yc.DBM(model.NewPmsDailyProductionReport()).Fields("order_id,sku,workshop_section,sum(daily_output) as daily_output").
							Group("order_id,sku,workshop_section").Scan(&productionInfos)
						if err != nil {
							return nil, err
						}
						for _, item := range dailyReportDataOutput {
							for _, info := range productionInfos {
								if item.OrderId == info.OrderId && item.Sku == info.Sku && item.WorkshopSection == info.WorkshopSection {
									item.ExceptQuantity = item.Quantity - info.DailyOutput
								}
							}
						}
					}
					return dailyReportDataOutput, nil
				},
			},
		},
	}
}
