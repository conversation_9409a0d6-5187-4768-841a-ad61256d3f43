import{c as ce,b as I,A as qe,r as Pe,U as Me,E as s,f as V,y,h as i,q as k,B as x,t as z,w as r,i as h,F as de,s as se,j as O,v as X,an as Se,ao as Ae,a0 as Ee,T as ue,o as v,af as Le,ag as Be}from"./.pnpm-hVqhwuVC.js";import{c as $e,s as w}from"./index-BtOcqcNl.js";import{_ as Ue}from"./OutboundTable.vue_vue_type_script_setup_true_name_OutboundTable_lang-D_iDFovR.js";import{a as Ne}from"./index-D95m1iJL.js";import{_ as De}from"./_plugin-vue_export-helper-DlAUqK2U.js";const Y=G=>(Le("data-v-9296da5a"),G=G(),Be(),G),Fe={class:"delivery-note-add"},Ge={class:"header-container"},Re={class:"form-section"},je=Y(()=>y("div",{class:"section-title"}," 基本信息 ",-1)),Je={key:0,style:{color:"#999","font-size":"12px","margin-top":"5px"}},Ke={key:1,style:{color:"#67c23a","font-size":"12px","margin-top":"5px"}},Ze={key:0,style:{color:"#999","font-size":"12px","margin-top":"5px"}},He={key:1,style:{color:"#67c23a","font-size":"12px","margin-top":"5px"}},We={class:"form-section"},Xe={class:"section-header"},Ye=Y(()=>y("div",{class:"section-title"}," 物料信息 ",-1)),et={key:0,class:"empty-material-tip"},tt=Y(()=>y("span",{class:"required-field"},"数量",-1)),at={class:"ellipsis-cell"},lt={class:"ellipsis-cell"},ot={class:"ellipsis-cell"},nt={class:"ellipsis-cell"},it={class:"ellipsis-cell"},rt={key:2,class:"add-material-button-container"},dt={class:"form-actions"},st={class:"mode-dialog-content"},ut={class:"dialog-icon"},ct={class:"dialog-message"},pt={class:"dialog-buttons"},mt=ce({name:"undefined"}),vt=ce({...mt,setup(G){const A=I([]),g=I([]),m=I([]),R=I(!1),Q=I(!1),T=I(0),{router:M}=Ne(),{user:q}=$e(),C=I(!1),L=I(!1);qe(()=>{me();const a=M.currentRoute.value.query.id;a&&(C.value=!0,pe(Number(a)))});function pe(a){console.log("fetchById 开始加载数据"),L.value=!0,w.pms.delivery_note.getDeliveryInfo({id:a}).then(async e=>{var d;console.log("fetchById 收到数据:",{inboundType:e.inboundType,po:e.po}),l.inboundType=e.inboundType||1,console.log("设置入库类型:",l.inboundType),l.id=e.id,l.supplierId=e.supplier_id,l.supplierOrderNo=e.supplier_order_no,l.inboundType===1?(console.log("采购入库，设置PO字段:",e.po),l.po=e.po):console.log("补退货入库，不设置PO字段"),l.voucher=e.voucher,l.remark=e.remark,l.orderId=e.orderId,l.no=e.no,l.inboundType===1&&e.po&&T.value&&await Ce(e.po);const t=((d=e.products)==null?void 0:d.map(u=>{var p,S,U,b,N,J,f,K,P,Z,D,H,F,n,_,ae,le,oe,ne;const o=Me(u);if(l.inboundType===1)return{quantity:o.quantity,contractId:o.contractId,materialId:o.materialId,createTime:o.createTime||"",status:o.status||0,remark:o.remark||"",expectedInbound:((p=o.contract)==null?void 0:p.expectedQuantity)||0,receivedQuantity:((S=o.contract)==null?void 0:S.receivedQuantity)||0,name:((b=(U=o.contract)==null?void 0:U.material)==null?void 0:b.name)||o.name,model:((J=(N=o.contract)==null?void 0:N.material)==null?void 0:J.model)||o.model,size:((K=(f=o.contract)==null?void 0:f.material)==null?void 0:K.size)||o.size,material:((Z=(P=o.contract)==null?void 0:P.material)==null?void 0:Z.material)||o.material,unit:((H=(D=o.contract)==null?void 0:D.material)==null?void 0:H.unit)||o.unit,process:((n=(F=o.contract)==null?void 0:F.material)==null?void 0:n.process)||o.process,code:((ae=(_=o.contract)==null?void 0:_.material)==null?void 0:ae.code)||o.code,coverColor:((oe=(le=o.contract)==null?void 0:le.material)==null?void 0:oe.coverColor)||o.coverColor||"",orderId:((ne=o.contract)==null?void 0:ne.purchaseId)||o.orderId};{const ie=o.returnOrderQuantity||o.expectedInbound||0,re=o.restockingQty||o.receivedQty||0;return{quantity:o.quantity,contractId:o.contractId||0,materialId:o.materialId,createTime:o.createTime||"",status:o.status||0,remark:o.remark||"",expectedInbound:ie,receivedQuantity:re,name:o.name||"",model:o.model||"",size:o.size||"",material:o.material||"",unit:o.unit||"",process:o.process||"",code:o.code||"",coverColor:o.coverColor||"",orderId:o.orderId||0,returnOrderQuantity:ie,restockingQty:re}}}))||[];if(l.inboundType===1){const o=[...t.map(p=>({id:p.materialId,code:p.code,name:p.name,model:p.model,size:p.size,material:p.material,unit:p.unit,process:p.process,coverColor:p.coverColor,contractId:p.contractId,expectedQuantity:p.expectedInbound,receivedQuantity:p.receivedQuantity,purchaseId:p.orderId}))];m.value.forEach(p=>{o.some(S=>S.id===p.id)||o.push(p)}),m.value=o}else m.value=t.map(u=>({id:u.materialId,code:u.code,name:u.name,model:u.model,size:u.size,material:u.material,unit:u.unit,process:u.process,coverColor:u.coverColor,returnOrderQuantity:u.returnOrderQuantity,restockingQty:u.restockingQty,orderId:u.orderId}));c.value=t}).catch(e=>{s.error({message:e.message||"获取送货单信息失败"})}).finally(()=>{L.value=!1})}async function me(){var e;q.info=q.info||{};const a=(e=q==null?void 0:q.info)==null?void 0:e.id;w.pms.supplier_account.request({url:"/getUserRole",method:"POST",data:{user_id:a}}).then(t=>{const d=[];t&&t.forEach(u=>{d.push(u.name)}),d.includes("供应商")?ve():(s.error("本页面只供供应商账号访问！"),M.push("/pms/delivery_note"))}).catch(t=>{s.error("查询角色信息失败！"),M.push("/pms/delivery_note")})}function ve(){var a;w.pms.supplier_account.request({url:"/getUserBindSupplier",method:"GET",params:{user_id:(a=q==null?void 0:q.info)==null?void 0:a.id}}).then(e=>{T.value=e.supplier_id,l.supplierId=T.value,fe(e.supplier_id)}).catch(e=>{M.push("/pms/delivery_note")})}function fe(a){if(l.inboundType!==1){console.log("非采购入库，跳过PO列表获取");return}console.log("采购入库，开始获取PO列表"),R.value=!0,w.pms.purchase.contract.request({url:"/getUnfinishedPo",method:"GET",params:{supplier_id:a}}).then(e=>{if(e){if(Array.isArray(e))A.value=e.map(t=>{if(typeof t=="string")return{po:t};if(t&&typeof t.po=="string")return t;if(t&&t.po_number)return{po:t.po_number};for(const d in t)if(typeof t[d]=="string"&&/^[A-Z0-9]+$/i.test(t[d]))return{po:t[d]};return{po:JSON.stringify(t)}});else if(typeof e=="object"){for(const t in e)if(Array.isArray(e[t])){A.value=e[t].map(d=>typeof d=="string"?{po:d}:d&&typeof d.po=="string"?d:{po:JSON.stringify(d)});break}}s.success(`已加载${A.value.length}个PO号`)}R.value=!1}).catch(e=>{s.error("获取PO列表失败"),R.value=!1})}const l=Pe({id:"",no:"",po:"",remark:"",voucher:"",orderId:"",supplierId:T.value,supplierOrderNo:"",inboundType:1}),c=I([]),B=I(!1),E=I(""),j=I(!1),ee=I(0);function ye(a){c.value.length>0&&c.value.splice(a,1)}function $(){const a={quantity:void 0,contractId:void 0,materialId:void 0,createTime:"",status:0,remark:"",expectedInbound:void 0,receivedQuantity:void 0,name:"",model:"",size:"",material:"",unit:"",process:"",coverColor:"",orderId:0,code:"",returnOrderQuantity:void 0,restockingQty:void 0};c.value.push(a)}function _e(a,e){return c.value.some(t=>t!==e&&t.materialId===a)}function he(a){if(l.inboundType===1){const e=Number(a.expectedInbound)||0;return Math.max(e,0)}else if(l.inboundType===9){const e=(a.returnOrderQuantity||0)-(a.restockingQty||0);return Math.max(e,0)}return 999999}function ge(a,e){const t=m.value.find(d=>d.id===a);t&&(e.name=t.name,e.model=t.model,e.size=t.size,e.material=t.material,e.unit=t.unit,e.process=t.process,e.code=t.code,e.coverColor=t.coverColor||"",l.inboundType===1?t.contractId&&(e.contractId=t.contractId,e.expectedInbound=t.expectedQuantity,e.receivedQuantity=t.receivedQuantity,e.orderId=t.purchaseId):l.inboundType===9&&(e.returnOrderQuantity=t.returnOrderQuantity,e.restockingQty=t.restockingQty||0,e.orderId=t.orderId))}function be(a){!a||!T.value||(Q.value=!0,w.pms.purchase.contract.request({url:"/getContractListByPoAndSupplierId",method:"GET",params:{po:a,supplier_id:T.value}}).then(e=>{e&&Array.isArray(e)?(g.value=e,m.value=e.map(t=>({id:t.materialId,code:t.material.code,name:t.material.name,model:t.material.model,size:t.material.size,material:t.material.material,unit:t.material.unit,process:t.material.process,coverColor:t.material.coverColor||"",contractId:t.id,expectedQuantity:t.expectedQuantity,receivedQuantity:t.receivedQuantity,purchaseId:t.purchaseId})),s.success(`已加载${m.value.length}个可选物料`)):(g.value=[],m.value=[],s.warning("未找到相关物料")),Q.value=!1}).catch(e=>{s.error("获取物料列表失败"),g.value=[],m.value=[],Q.value=!1}))}function te(a){if(B.value=!1,a==="auto")W(E.value);else if(C.value)xe(E.value);else{c.value=[];for(let e=0;e<10;e++)$();be(E.value)}}function Ie(){B.value=!1,l.po="",E.value=""}function ke(){if(l.inboundType===1&&!l.po){s.error("请选择送货单PO号");return}if(l.inboundType===9&&c.value.length===0){s.error("请选择退货出库单");return}const a=c.value.filter(t=>{if(!t.materialId)return!1;const d=Number(t.quantity);if(t.quantity===void 0||t.quantity===""||Number.isNaN(d)||d===0)return!1;if(l.inboundType===9){const u=(t.returnOrderQuantity||0)-(t.restockingQty||0);if(d>u)return s.error(`物料 ${t.name} 的补货数量不能超过剩余可补数量 ${u}`),!1}return!0});if(a.length===0){s.error("请至少选择一个物料并填写数量");return}a.length>0&&(l.inboundType===1||l.inboundType===9&&!l.orderId&&a[0].orderId)&&(l.orderId=String(a[0].orderId));const e={delivery_note:l,materials:a};C.value?w.pms.delivery_note.update(e).then(t=>{s.success("修改成功"),M.push("/pms/delivery_note")}).catch(t=>{s.error(t.message||"修改失败")}):w.pms.delivery_note.add(e).then(t=>{s.success("创建成功"),M.push("/pms/delivery_note")}).catch(t=>{s.error(t.message||"创建失败")})}function Qe(a){console.log("selectPo 被调用:",{val:a,inboundType:l.inboundType,isLoadingData:L.value,isEdit:C.value}),a?l.inboundType===1&&!L.value?(console.log("显示模式选择对话框"),E.value=a,B.value=!0):console.log("不显示对话框，原因:",{inboundType:l.inboundType,isLoadingData:L.value}):(g.value=[],C.value||(c.value=[]))}function W(a){!a||!T.value||(Q.value=!0,w.pms.purchase.contract.request({url:"/getContractListByPoAndSupplierId",method:"GET",params:{po:a,supplier_id:T.value}}).then(e=>{e&&Array.isArray(e)?(g.value=e,m.value=e.map(t=>({id:t.materialId,code:t.material.code,name:t.material.name,model:t.material.model,size:t.material.size,material:t.material.material,unit:t.material.unit,process:t.material.process,coverColor:t.material.coverColor||"",contractId:t.id,expectedQuantity:t.expectedQuantity,receivedQuantity:t.receivedQuantity,purchaseId:t.purchaseId})),Te(e),s.success(`已加载${g.value.length}个物料`)):(g.value=[],c.value=[],m.value=[],s.warning("未找到相关物料")),Q.value=!1}).catch(e=>{s.error("获取物料列表失败"),g.value=[],c.value=[],m.value=[],Q.value=!1}))}function Te(a){c.value=a.map(e=>({quantity:void 0,contractId:e.id,materialId:e.materialId,createTime:"",status:0,remark:"",expectedInbound:e.expectedQuantity||0,receivedQuantity:e.receivedQuantity||0,name:e.material.name,model:e.material.model,size:e.material.size,material:e.material.material,unit:e.material.unit,process:e.material.process,code:e.material.code,coverColor:e.material.coverColor||"",orderId:e.purchaseId}))}async function Ce(a){if(!(!a||!T.value)){Q.value=!0;try{const e=await w.pms.purchase.contract.request({url:"/getContractListByPoAndSupplierId",method:"GET",params:{po:a,supplier_id:T.value}});e&&Array.isArray(e)?(g.value=e,m.value=e.map(t=>({id:t.materialId,code:t.material.code,name:t.material.name,model:t.material.model,size:t.material.size,material:t.material.material,unit:t.material.unit,process:t.material.process,coverColor:t.material.coverColor||"",contractId:t.id,expectedQuantity:t.expectedQuantity,receivedQuantity:t.receivedQuantity,purchaseId:t.purchaseId}))):(g.value=[],m.value=[])}catch{g.value=[],m.value=[]}finally{Q.value=!1}}}async function xe(a){if(!(!a||!T.value)){Q.value=!0;try{const e=await w.pms.purchase.contract.request({url:"/getContractListByPoAndSupplierId",method:"GET",params:{po:a,supplier_id:T.value}});let t=[];e&&Array.isArray(e)?(g.value=e,t=e.map(d=>({id:d.materialId,code:d.material.code,name:d.material.name,model:d.material.model,size:d.material.size,material:d.material.material,unit:d.material.unit,process:d.material.process,coverColor:d.material.coverColor||"",contractId:d.id,expectedQuantity:d.expectedQuantity,receivedQuantity:d.receivedQuantity,purchaseId:d.purchaseId}))):g.value=[],m.value=t,c.value=[];for(let d=0;d<10;d++)$();t.length>0?s.success(`已切换到新PO，加载了${t.length}个可选物料，创建了10行空白行`):s.warning("新PO暂无物料，已创建10行空白行")}catch{g.value=[],m.value=[],c.value=[];for(let t=0;t<10;t++)$();s.error("获取新PO物料失败，已创建10行空白行")}finally{Q.value=!1}}}function Oe(a){if(a===l.inboundType)return;c.value.length>0&&s.warning("切换入库类型将清空当前物料列表和表单信息"),l.inboundType=a;const e=l.supplierId,t=l.id;l.no="",l.po="",l.remark="",l.voucher="",l.orderId="",l.supplierOrderNo="",l.supplierId=e,l.id=t,c.value.length=0,m.value.length=0,g.value.length=0,E.value="",ee.value++,Ee(()=>{a===1?s.info("已切换到采购入库模式，请选择PO号"):a===9&&s.info("已切换到补退货入库模式，请选择退货出库单")})}function ze(){j.value=!0}async function we(a){const e=a.row||a;if(!e||!e.products||e.products.length===0){s.error("该退货出库单没有物料");return}if(c.value.length>0)try{await ue.confirm("当前物料列表中存在已选数据，如果确定，将先清除列表中的数据，是否继续？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"})}catch{return}c.value=[],m.value=[],e.id&&(l.orderId=String(e.id)),m.value=e.products.map(t=>({id:t.materialId,code:t.code,name:t.name,model:t.model,size:t.size,material:t.material,unit:t.unit,process:t.process,coverColor:t.coverColor||"",returnOrderQuantity:t.quantity,restockingQty:t.restockingQty||0,orderId:e.id||0}));try{await ue.confirm("是否需要自动填充该退货单的全部物料？","提示",{confirmButtonText:"自动填充",cancelButtonText:"手动输入",type:"warning",showClose:!1}),Ve()}catch{for(let d=0;d<10;d++)$()}j.value=!1,s.success(`已加载退货出库单物料，共${e.products.length}个物料`)}function Ve(){c.value=m.value.map(a=>({quantity:0,contractId:void 0,materialId:a.id,createTime:"",status:0,remark:"",expectedInbound:void 0,receivedQuantity:void 0,name:a.name,model:a.model,size:a.size,material:a.material,unit:a.unit,process:a.process,coverColor:a.coverColor,orderId:a.orderId,code:a.code,returnOrderQuantity:a.returnOrderQuantity,restockingQty:a.restockingQty}))}return(a,e)=>{const t=h("el-radio"),d=h("el-radio-group"),u=h("el-form-item"),o=h("el-col"),p=h("el-row"),S=h("el-option"),U=h("el-select"),b=h("el-button"),N=h("el-input"),J=h("el-empty"),f=h("el-table-column"),K=h("el-input-number"),P=h("el-tooltip"),Z=h("el-table"),D=h("el-icon"),H=h("el-form"),F=h("el-dialog");return v(),V("div",Fe,[y("div",Ge,[y("h2",null,z(C.value?"编辑送货单":"添加送货单"),1)]),i(H,{"label-width":"120px",class:"delivery-form"},{default:r(()=>[y("div",Re,[je,i(p,{gutter:20},{default:r(()=>[i(o,{span:12},{default:r(()=>[i(u,{label:"入库类型",required:""},{default:r(()=>[i(d,{"model-value":l.inboundType,disabled:C.value,"onUpdate:modelValue":e[0]||(e[0]=n=>Oe(n))},{default:r(()=>[i(t,{value:1,label:"采购入库"}),i(t,{value:9,label:"补退货入库"})]),_:1},8,["model-value","disabled"])]),_:1})]),_:1})]),_:1}),l.inboundType===1?(v(),k(p,{key:0,gutter:20},{default:r(()=>[i(o,{span:12},{default:r(()=>[i(u,{label:"送货单PO号",required:""},{default:r(()=>[i(U,{modelValue:l.po,"onUpdate:modelValue":e[1]||(e[1]=n=>l.po=n),filterable:"",placeholder:"请选择送货单PO号",style:{width:"100%"},loading:R.value,clearable:"",onChange:Qe},{default:r(()=>[(v(!0),V(de,null,se(A.value,n=>(v(),k(S,{key:n.po,label:n.po,value:n.po},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading"]),A.value.length===0?(v(),V("div",Je," PO列表为空，请等待加载或刷新页面 ")):(v(),V("div",Ke," 共加载了 "+z(A.value.length)+" 个PO号 ",1))]),_:1})]),_:1})]),_:1})):x("",!0),l.inboundType===9?(v(),k(p,{key:1,gutter:20},{default:r(()=>[i(o,{span:12},{default:r(()=>[i(u,{label:"选择退货出库单",required:""},{default:r(()=>[i(b,{type:"warning",disabled:c.value.length>0,onClick:ze},{default:r(()=>[O(" 选择退货出库单 ")]),_:1},8,["disabled"]),c.value.length===0?(v(),V("div",Ze," 请选择退货出库单以加载物料信息 ")):(v(),V("div",He," 已加载 "+z(m.value.length)+" 个可选物料 ",1))]),_:1})]),_:1})]),_:1})):x("",!0),i(p,{gutter:20},{default:r(()=>[i(o,{span:12},{default:r(()=>[i(u,{label:"送货单备注"},{default:r(()=>[i(N,{modelValue:l.remark,"onUpdate:modelValue":e[2]||(e[2]=n=>l.remark=n),type:"textarea",placeholder:"请输入送货单备注",rows:2},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),y("div",We,[y("div",Xe,[Ye,l.po&&!C.value?(v(),k(b,{key:0,type:"primary",loading:Q.value,onClick:e[3]||(e[3]=()=>W(l.po))},{default:r(()=>[O(" 刷新物料列表 ")]),_:1},8,["loading"])):x("",!0),l.po&&C.value?(v(),k(b,{key:1,type:"warning",loading:Q.value,onClick:e[4]||(e[4]=()=>W(l.po))},{default:r(()=>[O(" 重新加载物料（将覆盖当前数据） ")]),_:1},8,["loading"])):x("",!0)]),c.value.length===0?(v(),V("div",et,[i(J,{description:C.value?"编辑模式：物料信息加载中...":"请先选择PO号以加载物料信息","image-size":100},null,8,["description"])])):(v(),k(Z,{key:`table-${l.inboundType}-${ee.value}`,data:c.value,border:"",size:"small",class:"compact-table"},{default:r(()=>[i(f,{label:"序号",type:"index",width:"70",align:"center"}),i(f,{label:"物料编号",prop:"materialId","min-width":"150"},{default:r(({row:n})=>[i(U,{modelValue:n.materialId,"onUpdate:modelValue":_=>n.materialId=_,filterable:"",placeholder:"请选择物料",size:"small",style:{width:"100%"},onChange:_=>ge(_,n)},{default:r(()=>[(v(!0),V(de,null,se(m.value,_=>(v(),k(S,{key:_.id,label:`${_.code}/${_.name}`,value:_.id,disabled:_e(_.id,n)},null,8,["label","value","disabled"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),i(f,{label:"数量",prop:"quantity","min-width":"120"},{header:r(()=>[tt]),default:r(({row:n})=>[i(K,{modelValue:n.quantity,"onUpdate:modelValue":_=>n.quantity=_,min:0,max:he(n),precision:2,style:{width:"100%"},placeholder:"请输入数量"},null,8,["modelValue","onUpdate:modelValue","max"])]),_:1}),i(f,{label:"备注",prop:"remark","min-width":"200"},{default:r(({row:n})=>[i(N,{modelValue:n.remark,"onUpdate:modelValue":_=>n.remark=_,placeholder:"请输入备注"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),i(f,{label:"合同ID",prop:"contractId","min-width":"50"}),l.inboundType===1?(v(),k(f,{key:0,label:"剩余入库数量",prop:"expectedInbound","min-width":"70"})):x("",!0),l.inboundType===1?(v(),k(f,{key:1,label:"已收数量",prop:"receivedQuantity","min-width":"60"})):x("",!0),l.inboundType===9?(v(),k(f,{key:2,label:"退货数量",prop:"returnOrderQuantity","min-width":"70"})):x("",!0),l.inboundType===9?(v(),k(f,{key:3,label:"已补货数量",prop:"restockingQty","min-width":"80"})):x("",!0),l.inboundType===9?(v(),k(f,{key:4,label:"剩余可补数量","min-width":"90"},{default:r(({row:n})=>[O(z((n.returnOrderQuantity||0)-(n.restockingQty||0)),1)]),_:1})):x("",!0),i(f,{label:"物料名称",prop:"name","min-width":"60"},{default:r(({row:n})=>[i(P,{content:n.name,placement:"top","show-after":500,enterable:!1},{default:r(()=>[y("div",at,z(n.name),1)]),_:2},1032,["content"])]),_:1}),i(f,{label:"物料型号",prop:"model","min-width":"60"},{default:r(({row:n})=>[i(P,{content:n.model,placement:"top","show-after":500,enterable:!1},{default:r(()=>[y("div",lt,z(n.model),1)]),_:2},1032,["content"])]),_:1}),i(f,{label:"物料尺寸",prop:"size","min-width":"80"},{default:r(({row:n})=>[i(P,{content:n.size,placement:"top","show-after":500,enterable:!1},{default:r(()=>[y("div",ot,z(n.size),1)]),_:2},1032,["content"])]),_:1}),i(f,{label:"物料材质",prop:"material","min-width":"80"},{default:r(({row:n})=>[i(P,{content:n.material,placement:"top","show-after":500,enterable:!1},{default:r(()=>[y("div",nt,z(n.material),1)]),_:2},1032,["content"])]),_:1}),i(f,{label:"物料单位",prop:"unit","min-width":"50"}),i(f,{label:"物料工艺",prop:"process","min-width":"80"},{default:r(({row:n})=>[i(P,{content:n.process,placement:"top","show-after":500,enterable:!1},{default:r(()=>[y("div",it,z(n.process),1)]),_:2},1032,["content"])]),_:1}),i(f,{label:"操作",width:"100",align:"center"},{default:r(({$index:n})=>[i(b,{type:"danger",text:"",onClick:_=>ye(n)},{default:r(()=>[O(" 删除 ")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),c.value.length>0?(v(),V("div",rt,[i(b,{type:"primary",class:"add-material-button",onClick:$},{default:r(()=>[i(D,null,{default:r(()=>[i(X(Se))]),_:1}),O(" 添加物料 ")]),_:1})])):x("",!0)]),y("div",dt,[i(b,{onClick:e[5]||(e[5]=n=>X(M).go(-1))},{default:r(()=>[O(" 取消 ")]),_:1}),i(b,{type:"primary",onClick:ke},{default:r(()=>[O(" 保存 ")]),_:1})])]),_:1}),l.inboundType===1?(v(),k(F,{key:0,modelValue:B.value,"onUpdate:modelValue":e[8]||(e[8]=n=>B.value=n),title:"提示",width:"400px","close-on-click-modal":!1,"close-on-press-escape":!1,"show-close":!1},{default:r(()=>[y("div",st,[y("div",ut,[i(D,{size:"24",color:"#E6A23C"},{default:r(()=>[i(X(Ae))]),_:1})]),y("div",ct,z(C.value?"是否需要自动填充该订单的全部物料（将覆盖当前物料列表）":"是否需要自动填充该订单的全部物料"),1),y("div",pt,[i(b,{onClick:Ie},{default:r(()=>[O(" 取消 ")]),_:1}),i(b,{onClick:e[6]||(e[6]=n=>te("manual"))},{default:r(()=>[O(" 手动输入 ")]),_:1}),i(b,{type:"primary",onClick:e[7]||(e[7]=n=>te("auto"))},{default:r(()=>[O(" 自动填充 ")]),_:1})])])]),_:1},8,["modelValue"])):x("",!0),i(F,{modelValue:j.value,"onUpdate:modelValue":e[9]||(e[9]=n=>j.value=n),title:"选择退货出库单",width:"80%","close-on-click-modal":!1},{default:r(()=>[i(Ue,{"outbound-type":1,"supplier-id":l.supplierId,onSelected:we},null,8,["supplier-id"])]),_:1},8,["modelValue"])])}}}),bt=De(vt,[["__scopeId","data-v-9296da5a"]]);export{bt as default};
