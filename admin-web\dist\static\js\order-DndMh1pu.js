import{c as R,b as w,e as B,z as te,q as y,w as o,h as p,i as m,f as C,s as W,F as j,B as x,v as q,Y as T,j as D,t as I,o as b,T as re,E as g}from"./.pnpm-hVqhwuVC.js";import{i as N,e as ae}from"./index-BtOcqcNl.js";import{a as ne}from"./index-D95m1iJL.js";/* empty css              */import{u as oe}from"./table-ops-mcGHjph4.js";const le={key:0},pe={key:1},ce=R({name:"pms-clearance-order"}),fe=R({...ce,setup(ie){const{service:h}=ne(),s=w(0),O=w([{label:"未开始",value:0,count:0},{label:"清关中",value:1,count:0},{label:"待完善",value:2,count:0},{label:"已完成",value:3,count:0}]),S=w({"slot-btn-download":{width:110,permission:h.pms.clearance.order.permission.download,show:!0},"slot-btn-confirm":{width:110,permission:h.pms.clearance.order.permission.confirm,show:B(()=>s.value===0)},"slot-btn-finish":{width:110,permission:h.pms.clearance.order.permission.finish,show:B(()=>s.value===1)},"slot-btn-information":{width:110,permission:h.pms.clearance.order.permission.information,show:B(()=>s.value===2)}}),{getOpWidth:z,checkOpButtonIsAvaliable:k,getOpIsHidden:A}=oe(S),M=w(),$=w(!1);te(s,()=>{M.value=z(),$.value=A()},{immediate:!0});const H=N.useTable({columns:[{label:"订单号",prop:"orderSn",width:250,align:"left"},{label:"创建时间",prop:"createTime",width:170},{label:"清关信息",children:[{label:"卡车提货日期",prop:"pickupDate",width:120,align:"center"},{label:"预计派送日期",prop:"etd",width:120,align:"center"},{label:"派送地址",prop:"order.dataClearance.deliverTo",width:150,align:"center"},{label:"卡车追踪号",prop:"order.dataClearance.pro",width:150,align:"center"},{label:"卡车追踪网站",prop:"order.dataClearance.trackingWebsite",width:150,align:"center"},{label:"签收日期",prop:"delDate",width:120,align:"center"}]},{label:"费用信息",children:[{label:"当地清关费",prop:"order.dataClearance.customsClearanceFee",width:150,align:"center"},{label:"关税",prop:"order.dataClearance.tax",width:150,align:"center"},{label:"卡车派送费",prop:"order.dataClearance.truckingCharge",width:150,align:"center"},{label:"仓储费",prop:"order.dataClearance.storageFee",width:150,align:"center"},{label:"其他杂费",children:[{label:"托盘费",prop:"order.dataClearance.palletFee",width:150,align:"center"},{label:"查验费",prop:"order.dataClearance.inspectionFee",width:150,align:"center"},{label:"港口费",prop:"order.dataClearance.terminalFee",width:150,align:"center"},{label:"通讯费",prop:"order.dataClearance.messageFee",width:150,align:"center"},{label:"周末卡车费",prop:"order.dataClearance.weekendTruckingFee",width:150,align:"center"},{label:"卡车等待费",prop:"order.dataClearance.truckingChargeFee",width:150,align:"center"},{label:"机场转运费",prop:"order.dataClearance.airportTransferFee",width:150,align:"center"},{label:"操作费",prop:"order.dataClearance.handlingFee",width:150,align:"center"}]}]},{type:"op",label:"操作",width:M,hidden:$,buttons:Object.keys(S.value)}]}),E=N.useCrud({service:h.pms.clearance.order,async onRefresh(e,{next:r,render:a}){const{count:f,list:i,pagination:n}=await r(e);O.value.forEach(_=>{_.count=f[_.value]||0}),a(i,n)}},e=>{e.refresh({status:s})});function L(e){var r;s.value=e,(r=E.value)==null||r.refresh()}async function P(e){const r=e.id;r&&await re.confirm("确认开始清关吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{await h.pms.clearance.order.confirm({id:r}).then(a=>{g.success("处理成功"),s.value=(a==null?void 0:a.status)||1}).catch(a=>{g.error(a.message||"处理失败")})})}function c(e){return/^(\d+)((?:\.\d{0,4})?)$/.test(e)?e:e.slice(0,-1)}const Y=w();async function U(e){var r;(r=Y.value)==null||r.open({title:"填写清关信息",dialog:{controls:["close"]},props:{labelWidth:"130px",class:"clearance-order-confirm-form"},items:[{label:"卡车提货日期",prop:"pickupDate",required:!0,component:{name:"el-date-picker",type:"date",props:{placeholder:"请选择卡车提货日期"}}},{label:"预计派送日期",prop:"etd",required:!0,component:{name:"el-date-picker",type:"date",props:{placeholder:"请选择预计派送日期"}}},{label:"派送地址",prop:"deliverTo",required:!0,component:{name:"el-input",props:{type:"textarea",autosize:{minRows:1,maxRows:3}}}}],on:{submit:async(a,{done:f,close:i})=>{await h.pms.clearance.order.finish({id:e.id,...a}).then(n=>{g.success("清关完成"),i(),s.value=(n==null?void 0:n.status)||0}).catch(n=>{g.error(n.message||"清关失败")}),f()}}})}async function G(e){var r;(r=Y.value)==null||r.open({title:"填写清关信息",dialog:{controls:["close"]},props:{labelWidth:"130px",class:"clearance-order-confirm-form"},items:[{label:"卡车追踪号",prop:"pro",required:!0,component:{name:"el-input"}},{label:"卡车追踪网站",prop:"trackingWebsite",required:!0,component:{name:"el-input"}},{label:"签收日期",prop:"delDate",required:!0,component:{name:"el-date-picker",type:"date",props:{placeholder:"请选择签收日期"}}},{label:"当地清关费",required:!0,prop:"customsClearanceFee",component:{name:"el-input",props:{type:"number",formatter:c}}},{label:"关税",prop:"tax",required:!0,component:{name:"el-input",props:{type:"number",formatter:c}}},{label:"卡车派送费",prop:"truckingCharge",required:!0,component:{name:"el-input",props:{type:"number",formatter:c}}},{label:"仓储费",prop:"storageFee",required:!0,component:{name:"el-input",props:{type:"number",formatter:c}}},{label:"托盘费",prop:"palletFee",required:!0,component:{name:"el-input",props:{type:"number",formatter:c}}},{label:"查验费",prop:"inspectionFee",required:!0,component:{name:"el-input",props:{type:"number",formatter:c}}},{label:"港口费",prop:"terminalFee",required:!0,component:{name:"el-input",props:{type:"number",formatter:c}}},{label:"通讯费",prop:"messageFee",required:!0,component:{name:"el-input",props:{type:"number",formatter:c}}},{label:"周末卡车费",prop:"weekendTruckingFee",required:!0,component:{name:"el-input",props:{type:"number",formatter:c}}},{label:"卡车等待费",prop:"truckingChargeFee",required:!0,component:{name:"el-input",props:{type:"number",formatter:c}}},{label:"机场转运费",prop:"airportTransferFee",required:!0,component:{name:"el-input",props:{type:"number",formatter:c}}},{label:"操作费",prop:"handlingFee",required:!0,component:{name:"el-input",props:{type:"number",formatter:c}}},{label:"签收单",prop:"signReceipt",required:!0,component:{name:"cl-upload",props:{accept:"image/jpg,image/jpeg,image/png",text:"上传签收单",type:"image",disabled:!1}}}],on:{submit:async(a,{done:f,close:i})=>{await h.pms.clearance.order.information({id:e.id,...a}).then(n=>{g.success("资料完善成功"),i(),s.value=(n==null?void 0:n.status)||0}).catch(n=>{g.error(n.message||"资料完善失败")}),f()}}})}function J(e){const r=e.id;if(!r)return!1;h.pms.clearance.order.request({url:"/download",data:{id:r},method:"POST",responseType:"blob"}).then(a=>{var i;const f=e!=null&&e.subOrder?e.subOrder.map(n=>n.orderSn).join("-"):(i=e==null?void 0:e.order)==null?void 0:i.orderSn;ae(a,`订单-${f}-货运附件.zip`)&&g.success("附件下载成功")}).catch(a=>{g.error(a.message)})}return(e,r)=>{const a=m("cl-refresh-btn"),f=m("cl-flex1"),i=m("el-row"),n=m("el-tab-pane"),_=m("cl-date-text"),v=m("el-button"),K=m("cl-table"),Q=m("cl-pagination"),X=m("el-tabs"),Z=m("cl-form"),ee=m("cl-crud");return b(),y(ee,{ref_key:"Crud",ref:E},{default:o(()=>[p(i,null,{default:o(()=>[p(a),p(f)]),_:1}),p(X,{modelValue:s.value,"onUpdate:modelValue":r[0]||(r[0]=t=>s.value=t),type:"border-card",onTabChange:L},{default:o(()=>[(b(!0),C(j,null,W(O.value,t=>(b(),y(n,{key:t.value,label:`${t.label}(${t.count})`,name:t.value},null,8,["label","name"]))),128)),p(i,null,{default:o(()=>[p(K,{ref_key:"Table",ref:H,class:"table-row-pointer"},{"column-orderSn":o(({scope:t})=>{var l,d,u,V;return[(l=t.row)!=null&&l.subOrder?(b(),C("div",le,[(b(!0),C(j,null,W((d=t.row)==null?void 0:d.subOrder,F=>(b(),C("p",{key:F.id},I(F==null?void 0:F.orderSn),1))),128))])):(b(),C("div",pe,I((V=(u=t.row)==null?void 0:u.order)==null?void 0:V.orderSn),1))]}),"column-pickupDate":o(({scope:t})=>{var l,d,u;return[p(_,{"model-value":(u=(d=(l=t.row)==null?void 0:l.order)==null?void 0:d.dataClearance)==null?void 0:u.pickupDate,format:"YYYY-MM-DD"},null,8,["model-value"])]}),"column-etd":o(({scope:t})=>{var l,d,u;return[p(_,{"model-value":(u=(d=(l=t.row)==null?void 0:l.order)==null?void 0:d.dataClearance)==null?void 0:u.etd,format:"YYYY-MM-DD"},null,8,["model-value"])]}),"column-delDate":o(({scope:t})=>{var l,d,u;return[p(_,{"model-value":(u=(d=(l=t.row)==null?void 0:l.order)==null?void 0:d.dataClearance)==null?void 0:u.delDate,format:"YYYY-MM-DD"},null,8,["model-value"])]}),"slot-btn-download":o(({scope:t})=>[q(k)("slot-btn-download")?(b(),y(v,{key:0,text:"",bg:"",type:"primary",onClick:T(l=>J(t.row),["stop"])},{default:o(()=>[D(" 下载附件 ")]),_:2},1032,["onClick"])):x("",!0)]),"slot-btn-confirm":o(({scope:t})=>[q(k)("slot-btn-confirm")?(b(),y(v,{key:0,text:"",bg:"",type:"success",onClick:T(l=>P(t.row),["stop"])},{default:o(()=>[D(" 开始清关 ")]),_:2},1032,["onClick"])):x("",!0)]),"slot-btn-finish":o(({scope:t})=>[q(k)("slot-btn-finish")?(b(),y(v,{key:0,text:"",bg:"",type:"success",onClick:T(l=>U(t.row),["stop"])},{default:o(()=>[D(" 清关完成 ")]),_:2},1032,["onClick"])):x("",!0)]),"slot-btn-information":o(({scope:t})=>[q(k)("slot-btn-information")?(b(),y(v,{key:0,text:"",bg:"",type:"success",onClick:T(l=>G(t.row),["stop"])},{default:o(()=>[D(" 完善资料 ")]),_:2},1032,["onClick"])):x("",!0)]),_:1},512)]),_:1}),p(i,null,{default:o(()=>[p(f),p(Q)]),_:1})]),_:1},8,["modelValue"]),p(Z,{ref_key:"FinishForm",ref:Y},null,512)]),_:1},512)}}});export{fe as default};
