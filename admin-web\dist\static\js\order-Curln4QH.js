import{c as X,b as D,e as B,z as ve,q as C,w as a,h as n,B as w,i as f,Y as O,j as S,f as g,s as U,F as z,y as k,t as _,v as W,o as d,T as V,E as b}from"./.pnpm-hVqhwuVC.js";import{i as Q}from"./index-DkYL1aws.js";import{a as ye}from"./index-C6cm1h61.js";/* empty css              */import{u as ke}from"./table-ops-CrFIfhgA.js";const xe={key:0,flex:"~ justify-between"},Ce={key:1},De={key:0},Se={key:1},Be={key:0},Te={key:0},Fe={class:"table-summary"},Oe={class:"table-summary-container"},Ne=k("span",{class:"cl-table__expand-footer-title"},"数量总计：",-1),We={key:0},qe={key:1},Pe=X({name:"undefined"}),Ee=X({...Pe,setup(Re){const{service:h}=ye(),c=D(0),I=D([{label:"收货中",value:0,count:0},{label:"已收货",value:1,count:0},{label:"发货中",value:2,count:0},{label:"资料上传",value:3,count:0},{label:"已完成",value:4,count:0}]),A=D([{label:"空运",value:0,type:"success"},{label:"海运",value:1,type:"primary"}]),j=D({"slot-btn-confirm":{width:110,permission:h.pms.freight.forwarder.order.permission.confirm,show:B(()=>c.value===0)},"slot-btn-prepare":{width:110,permission:h.pms.freight.forwarder.order.permission.prepare,show:B(()=>c.value===1)},"slot-btn-send":{width:110,permission:h.pms.freight.forwarder.order.permission.send,show:B(()=>c.value===2)},"slot-btn-upload":{width:110,permission:h.pms.freight.forwarder.order.permission.upload,show:B(()=>c.value===3)},"slot-btn-supplement":{width:110,permission:h.pms.freight.forwarder.order.permission.supplement,show:B(()=>c.value===4)},"slot-btn-revoke":{width:80,permission:h.pms.freight.forwarder.order.permission.revoke,show:B(()=>c.value>0)}}),{getOpWidth:Z,checkOpButtonIsAvaliable:N,getOpIsHidden:ee}=ke(j),H=D(),G=D(!1);ve(c,()=>{H.value=Z(),G.value=ee()},{immediate:!0});const $=Q.useTable({columns:[{type:"selection",hidden:B(()=>c.value!==0),width:50,align:"center",reserveSelection:!0,selectable:r=>!r.subOrders},{label:"#",prop:"products",type:"expand",width:50},{label:"订单号",prop:"orderSn",align:"left"},{label:"入仓号",prop:"receiptNumber",width:250,align:"left",showOverflowTooltip:!0},{label:"货运信息",children:[{label:"货运方式",prop:"shipBy",width:82},{label:"货运参考号",prop:"order.dataForwarder.forwarderRef",width:180},{label:"空运提单号",prop:"order.dataForwarder.airway",width:130},{label:"是否报关",prop:"customsDeclaration",width:82},{label:"开船/起飞日期",width:120,prop:"departDate"},{label:"预计到港日期",prop:"arrivalDate",width:120},{label:"货物计费重量",prop:"cargoWeight",width:120,formatter(r){var e,o,s;return(o=(e=r.order)==null?void 0:e.dataForwarder)!=null&&o.cargoWeight?`${(s=r.order.dataForwarder)==null?void 0:s.cargoWeight}kg`:""}},{label:"空运/海运单价",width:120,prop:"order.dataForwarder.shippingRate"},{label:"总运费",width:100,prop:"shippingFee"}]},{label:"创建时间",prop:"createTime",width:180},{align:"right",type:"op",label:"操作",width:H,hidden:G,buttons:Object.keys(j.value)}]}),L=Q.useCrud({service:h.pms.freight.forwarder.order,async onRefresh(r,{next:e,render:o}){const{count:s,list:u,pagination:i}=await e(r);I.value.forEach(R=>{R.count=s[R.value]||0}),o(u,i)}},r=>{r.refresh({status:c})});function te(r){var e;c.value=r,(e=L.value)==null||e.refresh()}function re(r,e){var o;(e==null?void 0:e.type)==="expand"||(e==null?void 0:e.type)==="op"||(e==null?void 0:e.type)==="selection"||(o=$.value)==null||o.toggleRowExpansion(r)}async function ae(r){await V.confirm("确认收到货物了吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{await h.pms.freight.forwarder.order.confirm({id:r.id}).then(e=>{b.success("确认收货成功"),c.value=(e==null?void 0:e.status)||0}).catch(e=>{b.error(e.message||"确认收货失败")})}).catch(()=>{})}async function oe(r){await V.confirm("确认开始发货吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{await h.pms.freight.forwarder.order.prepare({id:r.id}).then(e=>{b.success("开始发货成功"),c.value=(e==null?void 0:e.status)||0}).catch(e=>{b.error(e.message||"开始发货失败")})}).catch(()=>{})}function K(r){return/^(\d+)((?:\.\d{0,4})?)$/.test(r)?r:r.slice(0,-1)}const q=D();function le(r){var e;(e=q.value)==null||e.open({title:"填写发货信息",dialog:{controls:["close"]},props:{labelWidth:"130px"},items:[{label:"货运方式",prop:"shipBy",required:!0,component:{name:"el-select",options:A.value}},{label:"货运参考号",prop:"forwarderRef",required:!0,component:{name:"el-input"}},{label:"空运提单号",prop:"airway",required:!0,component:{name:"el-input"}},{label:"是否报关",prop:"customsDeclaration",value:0,required:!0,component:{name:"el-radio-group",options:[{label:"否",value:0},{label:"是",value:1}]}},{label:"开船/起飞日期",prop:"departDate",required:!0,component:{name:"el-date-picker",type:"date"}},{label:"到港/到达日期",prop:"arrivalDate",component:{name:"el-date-picker",type:"date",props:{disabledDate:o=>{var u;const s=(u=q.value)==null?void 0:u.form.departDate;if(s)return o.getTime()<s.getTime()}}}},{label:"货物计费重量",prop:"cargoWeight",required:!0,component:{name:"slot-cargoWeight"}},{label:"空运/海运单价",prop:"shippingRate",required:!0,component:{name:"el-input",props:{type:"number",formatter:K}}}],on:{submit:async(o,{done:s,close:u})=>{await h.pms.freight.forwarder.order.send({id:r.id,...o}).then(i=>{b.success("发货成功"),u(),c.value=(i==null?void 0:i.status)||0}).catch(i=>{b.error(i.message||"发货失败")}),s()}}})}const M=D();function ne(r){var e;(e=M.value)==null||e.open({title:"上传货运资料",dialog:{controls:["close"]},props:{labelWidth:"130px",class:"upload-form"},items:[{label:"货运提单",prop:"forwarderBillOfLading",required:!0,component:{name:"cl-upload",props:{accept:"image/*,.pdf,.doc,.docx,.xls,.xlsx",limitSize:20,text:"上传提单（包括空运主单，分单，舱单，水路单）",type:"file",multiple:!0,limit:4,disabled:!1,isPrivate:!0}}},{label:"国内报关资料",prop:"importPackingList",required:!0,component:{name:"cl-upload",props:{accept:"image/*,.pdf,.doc,.docx,.xls,.xlsx",limitSize:20,text:"上传报关资料（包括报关单，合同，商业发票，装箱单）",multiple:!0,limit:5,type:"file",disabled:!1,isPrivate:!0}}},{label:"品质承诺书",prop:"qualityCommitment",required:!0,component:{name:"cl-upload",props:{accept:"image/*,.pdf,.doc,.docx,.xls,.xlsx",limitSize:20,text:"上传品质承诺书",type:"file",disabled:!1,isPrivate:!0}}},{label:"国外清关资料",prop:"exportPackingList",component:{name:"cl-upload",props:{accept:"image/*,.pdf,.doc,.docx,.xls,.xlsx",limitSize:20,multiple:!0,limit:3,text:"上传清关资料（包括CI, Packing List）",type:"file",disabled:!1,isPrivate:!0}}}],on:{submit:async(o,{done:s,close:u})=>{await h.pms.freight.forwarder.order.upload({id:r.id,...o}).then(i=>{b.success("货运资料上传成功"),u(),c.value=(i==null?void 0:i.status)||0}).catch(i=>{b.error(i.message||"上传失败")}),s()}}})}const se=D();function ie(r){var e;(e=M.value)==null||e.open({title:"补填货运资料",dialog:{controls:["close"]},props:{labelWidth:"130px",class:"upload-form"},items:[{label:"到港/到达日期",prop:"arrivalDate",hidden:!r.isNeedCompleteArrivalDate,component:{name:"el-date-picker",type:"date",props:{disabledDate:o=>{var u;const s=(u=q.value)==null?void 0:u.form.departDate;if(s)return o.getTime()<s.getTime()}}}},{label:"国外清关资料",prop:"exportPackingList",hidden:!r.isNeedCompleteAttachment,component:{name:"cl-upload",props:{accept:"image/*,.pdf,.doc,.docx,.xls,.xlsx",limitSize:20,multiple:!0,limit:3,text:"上传清关资料（包括CI, Packing List）",type:"file",disabled:!1,isPrivate:!0}}}],on:{submit:async(o,{done:s,close:u})=>{await h.pms.freight.forwarder.order.supplement({id:r.id,...o}).then(()=>{var i;b.success("资料补填成功"),(i=L.value)==null||i.refresh(),u()}).catch(i=>{b.error(i.message||"资料补填失败")}),s()}}})}function de(r){let e="确定撤销货运单吗？<br /> 撤销后，该货运单将更新到收货中状态。<br /> ";r.status===4&&(e+='<b style="color: red">该出库单已完成，撤销时会删除未处理关联的清关单，确定撤销吗？</b><br />'),V.confirm(e,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning",dangerouslyUseHTMLString:!0}).then(async()=>{await h.pms.freight.forwarder.order.revoke({id:r.id}).then(o=>{b.success("撤销成功"),c.value=(o==null?void 0:o.status)||0}).catch(o=>{b.error(o.message||"撤销失败")})}).catch(()=>{})}function pe(r){const e=A.value.find(o=>o.value===r);return(e==null?void 0:e.label)||""}function ce(r){var i;const e=((i=r==null?void 0:r.order)==null?void 0:i.dataForwarder)||null;if(!e)return"";const o=(e==null?void 0:e.cargoWeight)||0,s=(e==null?void 0:e.shippingRate)||0;return(o*s).toFixed(4)}const P=B(()=>{var r;return((r=$.value)==null?void 0:r.getSelectionRows().map(e=>e.id))||[]});function ue(){if(P.value.length<2){b.warning("请至少选择两条订单");return}V.confirm(`确定要合并${P.value.length}条货运订单吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{await h.pms.freight.forwarder.order.merge({ids:P.value}).then(r=>{b.success("合并订单成功"),c.value=(r==null?void 0:r.status)||0}).catch(r=>{b.error(r.message||"合并订单失败")})}).catch(()=>{})}function me({row:r}){return r.subOrders?"danger-row":""}return(r,e)=>{const o=f("cl-refresh-btn"),s=f("el-button"),u=f("cl-flex1"),i=f("el-row"),R=f("el-tab-pane"),fe=f("el-tag"),J=f("cl-date-text"),x=f("el-table-column"),Y=f("el-table"),be=f("cl-table"),he=f("cl-pagination"),ge=f("el-tabs"),we=f("el-input"),E=f("cl-form"),_e=f("cl-crud");return d(),C(_e,{ref_key:"Crud",ref:L},{default:a(()=>[n(i,null,{default:a(()=>[n(o),c.value===0?(d(),C(s,{key:0,disabled:P.value.length<2,type:"danger",onClick:O(ue,["stop"])},{default:a(()=>[S(" 合并订单 ")]),_:1},8,["disabled"])):w("",!0),n(u)]),_:1}),n(ge,{modelValue:c.value,"onUpdate:modelValue":e[0]||(e[0]=t=>c.value=t),type:"border-card",onTabChange:te},{default:a(()=>[(d(!0),g(z,null,U(I.value,t=>(d(),C(R,{key:t.value,label:`${t.label}(${t.count})`,name:t.value},null,8,["label","name"]))),128)),n(i,null,{default:a(()=>[n(be,{ref_key:"Table",ref:$,class:"table-row-pointer","row-class-name":me,onRowClick:re},{"column-orderSn":a(({scope:t})=>{var l;return[t.row.subOrders?(d(),g("div",xe,[k("div",null,[(d(!0),g(z,null,U(t.row.subOrders,p=>{var v;return d(),g("p",{key:p.id},[k("span",null,_((v=p.order)==null?void 0:v.orderSn),1)])}),128))]),n(fe,{type:"danger",mr10px:""},{default:a(()=>[S(" 合 ")]),_:1})])):w("",!0),t.row.subOrders?w("",!0):(d(),g("span",Ce,_((l=t.row.order)==null?void 0:l.orderSn),1))]}),"column-receiptNumber":a(({scope:t})=>{var l,p,v;return[t.row.subOrders?(d(),g("div",De,[(d(!0),g(z,null,U(t.row.subOrders,m=>{var y,T,F;return d(),g("p",{key:m.id},[k("span",null,_(((y=m.order)==null?void 0:y.receiptNumber)||((F=(T=m.order)==null?void 0:T.dataWarehouse)==null?void 0:F.ref)),1)])}),128))])):w("",!0),t.row.subOrders?w("",!0):(d(),g("span",Se,_(((l=t.row.order)==null?void 0:l.receiptNumber)||((v=(p=t.row.order)==null?void 0:p.dataWarehouse)==null?void 0:v.ref)),1))]}),"column-shipBy":a(({scope:t})=>{var l,p;return[k("span",null,_(pe((p=(l=t.row.order)==null?void 0:l.dataForwarder)==null?void 0:p.shipBy)),1)]}),"column-customsDeclaration":a(({scope:t})=>{var l,p,v,m;return[(p=(l=t.row)==null?void 0:l.order)!=null&&p.dataForwarder?(d(),g("span",Be,_(((m=(v=t.row.order)==null?void 0:v.dataForwarder)==null?void 0:m.customsDeclaration)===1?"是":"否"),1)):w("",!0)]}),"column-departDate":a(({scope:t})=>{var l,p;return[n(J,{"model-value":(p=(l=t.row.order)==null?void 0:l.dataForwarder)==null?void 0:p.departDate,format:"YYYY-MM-DD"},null,8,["model-value"])]}),"column-arrivalDate":a(({scope:t})=>{var l,p;return[n(J,{"model-value":(p=(l=t.row.order)==null?void 0:l.dataForwarder)==null?void 0:p.arrivalDate,format:"YYYY-MM-DD"},null,8,["model-value"])]}),"column-shippingFee":a(({scope:t})=>[k("span",null,_(ce(t.row)),1)]),"slot-btn-confirm":a(({scope:t})=>[W(N)("slot-btn-confirm")?(d(),C(s,{key:0,text:"",bg:"",type:"success",onClick:O(l=>ae(t.row),["stop"])},{default:a(()=>[S(" 确认收货 ")]),_:2},1032,["onClick"])):w("",!0)]),"slot-btn-prepare":a(({scope:t})=>[W(N)("slot-btn-prepare")?(d(),C(s,{key:0,text:"",bg:"",type:"success",onClick:O(l=>oe(t.row),["stop"])},{default:a(()=>[S(" 开始发货 ")]),_:2},1032,["onClick"])):w("",!0)]),"slot-btn-send":a(({scope:t})=>[W(N)("slot-btn-send")?(d(),C(s,{key:0,text:"",bg:"",type:"success",onClick:O(l=>le(t.row),["stop"])},{default:a(()=>[S(" 确认发货 ")]),_:2},1032,["onClick"])):w("",!0)]),"slot-btn-upload":a(({scope:t})=>[W(N)("slot-btn-upload")?(d(),C(s,{key:0,text:"",bg:"",type:"success",onClick:O(l=>ne(t.row),["stop"])},{default:a(()=>[S(" 上传资料 ")]),_:2},1032,["onClick"])):w("",!0)]),"slot-btn-supplement":a(({scope:t})=>[W(N)("slot-btn-supplement")&&(t.row.isNeedCompleteAttachment||t.row.isNeedCompleteArrivalDate)?(d(),C(s,{key:0,text:"",bg:"",type:"success",onClick:O(l=>ie(t.row),["stop"])},{default:a(()=>[S(" 补填资料 ")]),_:2},1032,["onClick"])):w("",!0)]),"slot-btn-revoke":a(({scope:t})=>[W(N)("slot-btn-revoke")?(d(),C(s,{key:0,text:"",bg:"",type:"danger",onClick:O(l=>de(t.row),["stop"])},{default:a(()=>[S(" 撤销 ")]),_:2},1032,["onClick"])):w("",!0)]),"column-products":a(({scope:t})=>{var l,p,v;return[t.row.subOrders?(d(),g("div",Te,[n(Y,{data:t.row.subOrders,style:{width:"100%"},stripe:"",border:""},{default:a(()=>[n(x,{type:"expand"},{default:a(m=>[n(Y,{data:m.row.order.packData,style:{width:"100%"},border:""},{default:a(()=>[n(x,{label:"名称型号",prop:"product.sku",align:"center"}),n(x,{label:"数量",prop:"piece",align:"center"}),n(x,{label:"箱号",prop:"carton",align:"center"},{default:a(y=>[k("span",null,"#"+_(y.row.carton),1)]),_:2},1024)]),_:2},1032,["data"])]),_:2},1024),n(x,{label:"订单号",prop:"order.orderSn","show-overflow-tooltip":""}),n(x,{label:"入仓号",prop:"order.receiptNumber"},{default:a(m=>{var y,T,F;return[k("span",null,_(((y=m.row.order)==null?void 0:y.receiptNumber)||((F=(T=m.row.order)==null?void 0:T.dataWarehouse)==null?void 0:F.ref)),1)]}),_:2},1024),n(x)]),_:2},1032,["data"])])):(d(),C(Y,{key:1,data:t.row.order.packData,style:{width:"100%"},border:""},{default:a(()=>[n(x,{label:"名称型号",prop:"product.sku",align:"center"}),n(x,{label:"数量",prop:"piece",align:"center"}),n(x,{label:"箱号",prop:"carton",align:"center"},{default:a(m=>[k("span",null,"#"+_(m.row.carton),1)]),_:2},1024)]),_:2},1032,["data"])),k("div",Fe,[k("div",Oe,[Ne,t.row.subOrders?(d(),g("span",qe,_(((v=t.row.subOrders)==null?void 0:v.reduce((m,y)=>m+y.order.packData.reduce((T,F)=>T+F.piece,0),0))||0),1)):(d(),g("span",We,_(((p=(l=t.row.order)==null?void 0:l.packData)==null?void 0:p.reduce((m,y)=>m+y.piece,0))||0),1))])])]}),_:1},512)]),_:1}),n(i,null,{default:a(()=>[n(u),n(he)]),_:1})]),_:1},8,["modelValue"]),n(E,{ref_key:"SendForm",ref:q},{"slot-cargoWeight":a(({scope:t})=>[n(we,{modelValue:t.cargoWeight,"onUpdate:modelValue":l=>t.cargoWeight=l,type:"number",placeholder:"请输空运/海运单价",formatter:K},{append:a(()=>[S(" KG ")]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:1},512),n(E,{ref_key:"UploadForm",ref:M},null,512),n(E,{ref_key:"SupplementForm",ref:se},null,512)]),_:1},512)}}});export{Ee as default};
