import{c as W,b as y,e as D,z as U,A as ye,q as c,w as o,h as l,y as k,G as ge,i as p,H as ke,v as _,j as m,f as S,s as xe,F as Ce,B as b,t as C,Y as T,Z as Q,o as s,E as w,T as V}from"./.pnpm-hVqhwuVC.js";import{g as Te,i as E,r as B,j as K}from"./index-BtOcqcNl.js";import{a as Be}from"./index-D95m1iJL.js";/* empty css              */import{u as Pe}from"./table-ops-mcGHjph4.js";const Ye={key:1},De={key:0},Ie={key:1},Se={class:"table-summary"},Ee={class:"table-summary-container"},Re=k("span",{class:"cl-table__expand-footer-title"},"数量总计：",-1),Me={class:"outbound-images-preview"},$e=W({name:"pms-warehouse-inbound"}),Ae=W({...$e,setup(Ve){const{dict:z}=Te(),r=y(0),{service:h}=Be(),j=z.get("color"),R=y([{label:"草稿",value:0,type:"info",count:0},{label:"待处理",value:1,type:"info",count:0},{label:"入库中",value:2,type:"info",count:0},{label:"已入库",value:3,type:"success",count:0}]),I=[{label:"订单入库",value:0,type:"success"},{label:"手动入库",value:1,type:"warning"},{label:"排产计划",value:2,type:"primary"}],q=y({"slot-btn-confirm":{width:110,permission:h.pms.warehouse.source.inbound.permission.confirm,show:D(()=>r.value===0||r.value===1)},"slot-btn-edit":{width:60,permission:h.pms.warehouse.source.inbound.permission.add,show:D(()=>r.value===0||r.value===1)},"slot-btn-delete":{width:60,permission:h.pms.warehouse.source.inbound.permission.delete,show:D(()=>r.value===0)},"slot-btn-complete":{width:110,permission:h.pms.warehouse.source.inbound.permission.complete,show:D(()=>r.value===2)},"slot-btn-revoke":{width:110,permission:h.pms.warehouse.source.inbound.permission.revoke,show:D(()=>r.value!==0)}}),{getOpWidth:G,checkOpButtonIsAvaliable:P,getOpIsHidden:Z}=Pe(q),L=y(),N=y(!1);U(r,()=>{L.value=G(),N.value=Z()},{immediate:!0});const O=E.useTable({columns:[{label:"#",prop:"products",type:"expand"},{label:"入库单号",prop:"isn",width:220},{label:"入库类型",prop:"type",dict:I,width:150},{label:"关联订单",prop:"order.sn",width:150},{label:"需要入库总数量",prop:"totalQuantity",width:150},{label:"入库状态",prop:"status",dict:R,width:150},{label:"创建时间",prop:"createTime",width:200},{label:"完成时间",prop:"completeTime",component:{name:"cl-date-text",props:{format:"YYYY-MM-DD HH:mm:ss"}},width:200},{label:"入库凭证",prop:"voucher",component:{name:"cl-image",props:{fit:"cover",limit:5,lazy:!0,size:[50,50]}},width:200},{label:"备注",prop:"remark",showOverflowTooltip:!0},{type:"op",label:"操作",width:L,hidden:N,buttons:Object.keys(q.value)}]}),g=E.useCrud({service:h.pms.warehouse.source.inbound,async onRefresh(t,{next:e,render:a}){const{count:d,list:f,pagination:v}=await e(t);R.value.forEach(x=>{x.count=d[x.value]||0}),a(f,v)}},t=>{t.refresh({status:r})});function J(t){var e;r.value=t,(e=g.value)==null||e.refresh()}function X(t,e){var a;(e==null?void 0:e.type)==="expand"||(e==null?void 0:e.type)==="op"||(e==null?void 0:e.property)==="voucher"||(a=O.value)==null||a.toggleRowExpansion(t)}function A(t,e){return new Promise((a,d)=>{h.pms.warehouse.source.inbound.confirm({...t,id:e.id}).then(f=>{a(f)}).catch(f=>{d(f)})})}const F=E.useForm();function ee(t){var e;if(!t.id)return!1;if(t.status===0)return A({},t).then(a=>{w.success("提交成功"),r.value=(a==null?void 0:a.status)||0}).catch(a=>{w.error(a.message||"提交失败")});(e=F.value)==null||e.open({title:"上传入库凭证",width:"350px",dialog:{controls:["close"]},items:[{label:"入库凭证",prop:"voucher",required:!0,component:{name:"cl-upload",props:{multiple:!0,limit:5,accept:"image/jpg,image/jpeg,image/png",text:"上传入库凭证",type:"image",disabled:!1,isPrivate:!1}}}],on:{submit:async(a,{done:d,close:f})=>{A(a,t).then(v=>{w.success("开始入库成功"),f(),r.value=(v==null?void 0:v.status)||0}).catch(v=>{w.error(v.message||"开始入库失败")}),d()}}})}function te(t){V.confirm("确认完成入库吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{h.pms.warehouse.source.inbound.complete({id:t.id}).then(e=>{w.success("完成入库成功"),r.value=(e==null?void 0:e.status)||0}).catch(e=>{w.error(e.message)})}).catch(()=>{})}function oe(){return"primary-row"}function ne(){B.push("/pms/warehouse/source/inbound/add")}function ae(t){B.push(`/pms/warehouse/source/inbound/add?id=${t}`)}function le(t){if(!t)return!1;V.confirm("确认删除入库单吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{h.pms.warehouse.source.inbound.delete({ids:[t]}).then(()=>{var e;w.success("入库单删除成功"),(e=g.value)==null||e.refresh()}).catch(e=>{w.error(e.message)})}).catch(()=>{})}const se=E.useSearch({items:[{label:"下单时间",prop:"dateRange",props:{labelWidth:"80px"},component:{name:"el-date-picker",props:{type:"daterange","value-format":"YYYY-MM-DD",format:"YYYY-MM-DD",rangeSeparator:"至",startPlaceholder:"开始日期",endPlaceholder:"结束日期",clearable:!0,onChange(t){var e;(e=g.value)==null||e.refresh({dateRange:t})}}}},{label:"类型",props:{labelWidth:"40px"},prop:"type",component:{name:"el-select",options:I,props:{style:"width: 120px",clearable:!0,onChange(t){var e;(e=g.value)==null||e.refresh({type:t})}}}}]});function re(t){let e="确定撤销入库单吗？<br /> 撤销后，该入库单将更新到草稿状态。<br />";t.status===3&&(e+="该入库单已完成入库，撤销时会扣除已入库的数量，请确保库存充足？<br />"),V.confirm(e,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning",dangerouslyUseHTMLString:!0}).then(()=>{h.pms.warehouse.source.inbound.revoke({id:t.id}).then(a=>{w.success("撤销入库单成功"),r.value=(a==null?void 0:a.status)||0}).catch(a=>{w.error(a.message||"撤销入库单失败")})}).catch(()=>{})}function ie(t){return t.status===0?!1:Q(t.createTime).add(7,"days").isAfter(Q())}const M=y(!1),$=y([]);function ue(t){const e=t.split(",").map(a=>a.trim());$.value=e,M.value=!0}function ce(){$.value=[],M.value=!1}const H=y([]);return ye(()=>{var a;const t=B.currentRoute.value.query.tab;t&&(r.value=Number.parseInt(t.toString()),(a=g.value)==null||a.refresh(),B.replace({query:{tab:void 0}}));const e=B.currentRoute.value.query.expand;e&&(H.value=[Number.parseInt(e.toString())],B.replace({query:{expand:void 0}}))}),U(r,()=>{var t;(t=g.value)==null||t.refresh()}),(t,e)=>{const a=p("cl-refresh-btn"),d=p("el-button"),f=p("cl-flex1"),v=p("cl-search"),x=p("el-row"),pe=p("el-tab-pane"),Y=p("el-tag"),u=p("el-table-column"),de=p("el-table"),me=p("cl-table"),he=p("cl-pagination"),be=p("el-tabs"),fe=p("cl-form"),_e=p("el-image-viewer"),we=p("cl-crud"),ve=ke("permission");return s(),c(we,{ref_key:"Crud",ref:g},{default:o(()=>[l(x,null,{default:o(()=>[l(a),ge((s(),c(d,{text:"",bg:"",type:"success",onClick:ne},{default:o(()=>[m(" 创建入库单 ")]),_:1})),[[ve,_(h).pms.warehouse.source.inbound.permission.add]]),l(f),l(v,{ref_key:"Search",ref:se},null,512)]),_:1}),l(be,{modelValue:r.value,"onUpdate:modelValue":e[0]||(e[0]=n=>r.value=n),type:"border-card",onTabChange:J},{default:o(()=>[(s(!0),S(Ce,null,xe(R.value,n=>(s(),c(pe,{key:n.value,label:`${n.label}(${n.count})`,name:n.value},null,8,["label","name"]))),128)),l(x,null,{default:o(()=>[l(me,{ref_key:"Table",ref:O,"row-key":"id","expand-row-keys":H.value,class:"table-row-pointer",onRowClick:X},{"slot-btn-revoke":o(({scope:n})=>[_(P)("slot-btn-revoke")&&ie(n.row)?(s(),c(d,{key:0,text:"",bg:"",type:"danger",onClick:T(i=>re(n.row),["stop"])},{default:o(()=>[m(" 撤销入库 ")]),_:2},1032,["onClick"])):b("",!0)]),"slot-btn-confirm":o(({scope:n})=>[_(P)("slot-btn-confirm")?(s(),c(d,{key:0,text:"",bg:"",type:"success",onClick:T(i=>ee(n.row),["stop"])},{default:o(()=>[m(C(n.row.status===0?"提交":"开始入库"),1)]),_:2},1032,["onClick"])):b("",!0)]),"slot-btn-edit":o(({scope:n})=>[_(P)("slot-btn-edit")?(s(),c(d,{key:0,text:"",bg:"",type:"primary",onClick:T(i=>ae(n.row.id),["stop"])},{default:o(()=>[m(" 编辑 ")]),_:2},1032,["onClick"])):b("",!0)]),"slot-btn-delete":o(({scope:n})=>[_(P)("slot-btn-delete")?(s(),c(d,{key:0,text:"",bg:"",type:"danger",onClick:T(i=>le(n.row.id),["stop"])},{default:o(()=>[m(" 删除 ")]),_:2},1032,["onClick"])):b("",!0)]),"slot-btn-complete":o(({scope:n})=>[_(P)("slot-btn-complete")?(s(),c(d,{key:0,text:"",bg:"",type:"success",onClick:T(i=>te(n.row),["stop"])},{default:o(()=>[m(" 完成入库 ")]),_:2},1032,["onClick"])):b("",!0)]),"column-type":o(({scope:n})=>[n.row.isAdjust===0?(s(),c(Y,{key:0,type:I[n.row.type].type},{default:o(()=>[m(C(I[n.row.type].label),1)]),_:2},1032,["type"])):b("",!0),n.row.isAdjust===1?(s(),c(Y,{key:1,type:"success"},{default:o(()=>[m(" 库存调整 ")]),_:1})):b("",!0)]),"column-voucher":o(({scope:n})=>[n.row.voucher&&n.row.voucher.split(",").length>0?(s(),c(d,{key:0,text:"",type:"primary",onClick:T(i=>ue(n.row.voucher),["stop"])},{default:o(()=>[m(" 点击查看 ")]),_:2},1032,["onClick"])):(s(),S("span",Ye,"无数据"))]),"column-products":o(({scope:n})=>[l(de,{data:n.row.products,style:{width:"100%"},border:"","row-class-name":oe},{default:o(()=>[l(u,{label:"产品名称","min-width":"500",align:"center"},{default:o(()=>[l(u,{prop:"name",label:"单位",width:"100",align:"center"},{default:o(i=>[i.row.unit===0?(s(),c(Y,{key:0,type:"info"},{default:o(()=>[m(" 单 品 ")]),_:1})):b("",!0),i.row.unit===1?(s(),c(Y,{key:1,type:"warning"},{default:o(()=>[m(" 展示盒 ")]),_:1})):b("",!0),i.row.unit===2?(s(),c(Y,{key:2,type:"success"},{default:o(()=>[m(" 箱 装 ")]),_:1})):b("",!0)]),_:2},1024),l(u,{prop:"name",label:"中文名",align:"center"}),l(u,{prop:"nameEn",label:"英文名",align:"center"})]),_:2},1024),l(u,{prop:"sku",label:"SKU",width:"130",align:"center"}),l(u,{prop:"upc",label:"UPC",width:"130",align:"center"}),l(u,{prop:"color",label:"颜色",width:"120",align:"center"},{default:o(i=>[k("span",null,C(_(K)(_(j),parseInt(i.row.color))),1)]),_:2},1024),l(u,{prop:"quantity",label:"订单数量",width:"100",align:"center"}),l(u,{label:"包装单位信息",align:"center",width:"300"},{default:o(()=>[l(u,{prop:"pieceProduct.sku",label:"SKU",width:"130",align:"center"}),l(u,{prop:"pieceProduct.upc",label:"UPC",width:"130",align:"center"}),l(u,{prop:"unitQuantity",label:"单位数量",width:"130",align:"center"},{default:o(i=>[i.row.unit>0?(s(),S("span",De,C(i.row.unitQuantity),1)):(s(),S("span",Ie," - "))]),_:2},1024),l(u,{prop:"piece",label:"订单数量",width:"100",align:"center"}),l(u,{prop:"color",label:"颜色",width:"120",align:"center"},{default:o(i=>[k("span",null,C(_(K)(_(j),parseInt(i.row.pieceProduct.color))),1)]),_:2},1024)]),_:2},1024),l(u,{prop:"inboundQuantity",label:"需要入库数量",width:"120",align:"center"})]),_:2},1032,["data"]),k("div",Se,[k("div",Ee,[Re,k("span",null,C(n.row.totalQuantity),1)])])]),_:1},8,["expand-row-keys"])]),_:1}),l(x,null,{default:o(()=>[l(f),l(he)]),_:1})]),_:1},8,["modelValue"]),l(fe,{ref_key:"InboundForm",ref:F},null,512),k("div",Me,[M.value?(s(),c(_e,{key:0,"url-list":$.value,teleported:"",onClose:ce},null,8,["url-list"])):b("",!0)])]),_:1},512)}}});export{Ae as default};
