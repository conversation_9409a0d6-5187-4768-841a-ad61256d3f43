import{s as O}from"./index-BtOcqcNl.js";import{c as w,n as D,b as p,A as E,f as v,h as t,y as h,w as a,q as _,B as y,v as l,j as r,t as n,i as s,F as L,s as M,o as c}from"./.pnpm-hVqhwuVC.js";import{OUTBOUND_TYPE as z}from"./constant-C2dsBPRR.js";const A={class:"mt2 b-1px b-gray-400 b-dashed"},F=w({name:"AuditMaterialOutboundView"}),j=w({...F,props:{modelValue:{},modelModifiers:{}},emits:["update:modelValue"],setup(x){const u=D(x,"modelValue"),N=p(z);function m(b){return N.value.find(g=>g.value===b)||{}}const e=p({}),f=p([]);return E(async()=>{u.value&&u.value>0&&(e.value=await O.pms.material.outbound.detail({id:u.value}),e.value.voucher&&(f.value=e.value.voucher.split(",")))}),(b,g)=>{const o=s("el-descriptions-item"),V=s("el-tag"),k=s("el-image"),B=s("el-descriptions"),i=s("el-table-column"),T=s("el-table");return c(),v("div",null,[t(B,{size:"small",title:"",border:"",class:"purchase-order-info"},{default:a(()=>[l(e).orderNo?(c(),_(o,{key:0,label:"订单号",align:"center"},{default:a(()=>[r(n(l(e).orderNo),1)]),_:1})):y("",!0),t(o,{label:"创建时间",align:"center"},{default:a(()=>[r(n(l(e).createTime),1)]),_:1}),t(o,{label:"出库单号",align:"center"},{default:a(()=>[r(n(l(e).no),1)]),_:1}),t(o,{label:"出库类型",align:"center"},{default:a(()=>[t(V,{type:m(l(e).type).type},{default:a(()=>[r(n(m(l(e).type).label),1)]),_:1},8,["type"])]),_:1}),t(o,{label:"出库（总）数量",align:"center"},{default:a(()=>[r(n(l(e).totalQuantity),1)]),_:1}),t(o,{label:"入库日期",align:"center"},{default:a(()=>[r(n(l(e).outboundTime),1)]),_:1}),t(o,{label:"入库凭证",align:"center","class-name":"voucher_image"},{default:a(()=>[(c(!0),v(L,null,M(l(f),(d,C)=>(c(),_(k,{key:C,style:{width:"60px",height:"60px"},src:d,"preview-src-list":[d],"zoom-rate":1.2,"max-scale":7,"min-scale":.2,"initial-index":4,fit:"cover"},null,8,["src","preview-src-list"]))),128))]),_:1})]),_:1}),h("div",A,[t(T,{data:l(e).products||[],stripe:"",border:"",class:"cl-table"},{default:a(()=>[l(e).orderNo?(c(),_(i,{key:0,prop:"po",label:"PO",width:"250",align:"center","show-overflow-tooltip":""},{default:a(({row:d})=>[h("span",null,n(d.po),1)]),_:1})):y("",!0),t(i,{prop:"code",label:"物料代码",align:"center"}),t(i,{prop:"name",label:"物料名称",align:"center"}),t(i,{prop:"orderQuantity",label:"出库数量",align:"center"}),t(i,{prop:"unit",label:"单位",align:"center"})]),_:1},8,["data"])])])}}});export{j as _};
