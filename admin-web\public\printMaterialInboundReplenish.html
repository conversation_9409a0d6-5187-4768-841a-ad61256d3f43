<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Document</title>
  <style>
    @media print{
      @page{
        margin: 0 0 0 2.5cm;
      }
    }
  </style>
</head>

<body>
<div id="print_main"></div>
</body>
<script>
  const Print = function (dom, options) {
    if (!(this instanceof Print)) return new Print(dom, options);

    this.options = this.extend(
      {
        noPrint: ".no-print",
      },
      options
    );

    if (typeof dom === "string") {
      this.dom = document.querySelector(dom);
    } else {
      this.isDOM(dom);
      this.dom = this.isDOM(dom) ? dom : dom.$el;
    }

    this.init();
  };
  Print.prototype = {
    init: function () {
      var content = this.getStyle() + this.getHtml();
      this.writeIframe(content);
    },
    extend: function (obj, obj2) {
      for (var k in obj2) {
        obj[k] = obj2[k];
      }
      return obj;
    },

    getStyle: function () {
      var str = "",
        styles = document.querySelectorAll("style,link");
      for (var i = 0; i < styles.length; i++) {
        str += styles[i].outerHTML;
      }
      str +=
        "<style>" +
        (this.options.noPrint ? this.options.noPrint : ".no-print") +
        "{display:none;}</style>";
      return str;
    },

    getHtml: function () {
      var inputs = document.querySelectorAll("input");
      var textareas = document.querySelectorAll("textarea");
      var selects = document.querySelectorAll("select");

      for (var k = 0; k < inputs.length; k++) {
        if (inputs[k].type == "checkbox" || inputs[k].type == "radio") {
          if (inputs[k].checked == true) {
            inputs[k].setAttribute("checked", "checked");
          } else {
            inputs[k].removeAttribute("checked");
          }
        } else if (inputs[k].type == "text") {
          inputs[k].setAttribute("value", inputs[k].value);
        } else {
          inputs[k].setAttribute("value", inputs[k].value);
        }
      }

      for (var k2 = 0; k2 < textareas.length; k2++) {
        if (textareas[k2].type == "textarea") {
          textareas[k2].innerHTML = textareas[k2].value;
        }
      }

      for (var k3 = 0; k3 < selects.length; k3++) {
        if (selects[k3].type == "select-one") {
          var child = selects[k3].children;
          for (var i in child) {
            if (child[i].tagName == "OPTION") {
              if (child[i].selected == true) {
                child[i].setAttribute("selected", "selected");
              } else {
                child[i].removeAttribute("selected");
              }
            }
          }
        }
      }
      // 包裹要打印的元素
      let outerHTML = this.wrapperRefDom(this.dom).outerHTML;
      return outerHTML;
    },
    // 向父级元素循环，包裹当前需要打印的元素
    // 防止根级别开头的 css 选择器不生效
    wrapperRefDom: function (refDom) {
      let prevDom = null;
      let currDom = refDom;
      // 判断当前元素是否在 body 中，不在文档中则直接返回该节点
      if (!this.isInBody(currDom)) return currDom;

      while (currDom) {
        if (prevDom) {
          let element = currDom.cloneNode(false);
          element.appendChild(prevDom);
          prevDom = element;
        } else {
          prevDom = currDom.cloneNode(true);
        }
        currDom = currDom.parentElement;
      }
      return prevDom;
    },

    writeIframe: function (content) {
      var w,
        doc,
        iframe = document.createElement("iframe"),
        f = document.body.appendChild(iframe);
      iframe.id = "myIframe";
      //iframe.style = "position:absolute;width:0;height:0;top:-10px;left:-10px;";
      iframe.setAttribute(
        "style",
        "position:absolute;width:0;height:0;top:-10px;left:-10px;"
      );
      w = f.contentWindow || f.contentDocument;
      doc = f.contentDocument || f.contentWindow.document;
      doc.open();
      doc.write(content);
      doc.close();
      var _this = this;
      iframe.onload = function () {
        if (iframe.contentWindow.matchMedia) {
          let mediaQueryList = iframe.contentWindow.matchMedia('print');
          mediaQueryList.addListener( (mql)=> {
            console.log(mql)
            if (mql.matches) {
              onbeforePrint()
            } else {
              onafterprint()
            }
          });
        }
        _this.toPrint(w);
        setTimeout(function () {
          document.body.removeChild(iframe);
        }, 100);
      };
    },

    toPrint: function (frameWindow) {
      try {
        setTimeout(function () {
          frameWindow.focus();
          try {
            if (!frameWindow.document.execCommand("print", false, null)) {
              frameWindow.print();
            }
          } catch (e) {
            console.error("打印出错",e);
            frameWindow.print();
          }
          printFinish()
          frameWindow.close();
        }, 10);
      } catch (err) {
        console.log("err", err);
      }
    },
    // 检查一个元素是否是 body 元素的后代元素且非 body 元素本身
    isInBody: function (node) {
      return node === document.body ? false : document.body.contains(node);
    },
    isDOM:
      typeof HTMLElement === "object"
        ? function (obj) {
          return obj instanceof HTMLElement;
        }
        : function (obj) {
          return (
            obj &&
            typeof obj === "object" &&
            obj.nodeType === 1 &&
            typeof obj.nodeName === "string"
          );
        },
  };

  const print_main = document.getElementById("print_main");
  const printHeight = 445
  const opacity = '0'
  let htmlContentDiv = document.createElement('div')
  function render(){
    // 获取SessionStorage中的数据
    let data = sessionStorage.getItem("printData");
    console.log("打印数据", data)
    if (!data) {
      alert("打印数据不能为空")
      window.close()
      return;
    }
    data = JSON.parse(data);
    let htmlContent = ""
    data.forEach((item,index) => {
      const htmlContentStr = createHtmlTag(item,index)
      if(htmlContentStr) {
        htmlContent += htmlContentStr
      }
    })
    htmlContentDiv.innerHTML = htmlContent
  }

  function createHtmlTag(data) {
    let margonBottom = 0
    // page-break-before:always;page-break-inside: avoid; 解决分页没有margin-top的问题
    let html = `
    <div id="print_content" style="width: 24.13cm;margin-top: 1cm;page-break-before:always;page-break-inside: avoid;">
    <div style="text-align: center;font-weight: bold;font-family: 微软雅黑, sans-serif;font-size: 14pt;">
      深圳市绿烟科技有限公司
    </div>
    <div style="text-align: center;font-family: 微软雅黑, sans-serif;font-size: 14pt;">
      补退货入库单
    </div>
    <div style="display: flex;justify-content: space-between;margin-top: 10px;width: 21.5cm;">
      <div style="font-family: 微软雅黑, sans-serif;font-size: 9pt;width: 7cm;">
        <span>日</span>
        <span style="padding-left: 12px">期：</span>
        <span style="margin-left: 8px">${data.date || data.inboundTime || ''}</span>
      </div>
      <div style="font-family: 微软雅黑, sans-serif;font-size: 9pt;width: 7cm;text-align: center;">
      </div>
      <div style="font-family: 微软雅黑, sans-serif;font-size: 9pt;width: 7.5cm;text-align: right;">
        <span>内部订单号：${data.internalOrderNo || ''}</span>
      </div>
    </div>
    <div style="display: flex;justify-content: space-between;margin-top: 10px;width: 21.5cm;">
      <div style="font-family: 微软雅黑, sans-serif;font-size: 9pt;width: 7cm;">
        <span>No：${data.no || ''}</span>
      </div>
      <div style="font-family: 微软雅黑, sans-serif;font-size: 9pt;width: 7cm;text-align: center;">
        <span>总数：</span>
        <span style="margin-left: 4px">${data.totalQuantity || ''}</span>
      </div>
      <div style="font-family: 微软雅黑, sans-serif;font-size: 9pt;width: 7.5cm;text-align: right;">
        <span>出库单号：${data.orderNo || ''}</span>
      </div>
    </div>
    <table style="margin-top: 10px;margin-bottom: {margonBottom};border-collapse: collapse;">
      <colgroup>
        <col span="1" style="width: 0.8cm">
        <col span="1" style="width: 3cm">
        <col span="1" style="width: 3cm">
        <col span="1" style="width: 9cm">
        <col span="1" style="width: 1.5cm">
        <col span="1" style="width: 1.5cm">
        <col span="1" style="width: 1.5cm">
        <col span="1" style="width: 1cm">
        <col span="1" style="width: 1.5cm">
      </colgroup>
      <thead align="center">
        <tr style="border-top: 1pt solid black;border-bottom: 1pt solid black;">
          <th style="font-family: 微软雅黑, sans-serif;font-size: 9pt;">
            序号
          </th>
          <th style="font-family: 微软雅黑, sans-serif;font-size: 9pt;">
            物料编码
          </th>
          <th style="font-family: 微软雅黑, sans-serif;font-size: 9pt;">
            品名
          </th>
          <th style="font-family: 微软雅黑, sans-serif;font-size: 9pt;">
            规格/型号
          </th>
          <th style="font-family: 微软雅黑, sans-serif;font-size: 9pt;">
            位置
          </th>
          <th style="font-family: 微软雅黑, sans-serif;font-size: 9pt;">
            仓别
          </th>
          <th style="font-family: 微软雅黑, sans-serif;font-size: 9pt;">
            单位
          </th>
          <th style="font-family: 微软雅黑, sans-serif;font-size: 9pt;">
            数量
          </th>
          <th style="font-family: 微软雅黑, sans-serif;font-size: 9pt;">
            备注
          </th>
        </tr>
      </thead>
      <tbody align="center" id="print_content_tbody">${createTr(data.list)}</tbody>
    </table>
    <div
      style="margin-top: 10px;display: flex;justify-content: space-between;align-items: center;width: 21.5cm;font-family: 微软雅黑, sans-serif;font-size: 9pt;">
      <div>存根(白联) 采购(红联)财务(黄联)</div>
      <div>LYKJ-CK-001 A0</div>
    </div>
    <div style="margin-top: 10px;display: flex;justify-content: space-between;align-items: center;width: 20cm;">
      <div style="display: flex;align-items: center;font-family: 微软雅黑, sans-serif;font-size: 9pt;">
        <span>制单：${data.user || ''}</span>
      </div>
      <div style="display: flex;align-items: center;font-family: 微软雅黑, sans-serif;font-size: 9pt;">
        <span>IQC：彭晓彬</span>
      </div>
      <div style="display: flex;align-items: center;font-family: 微软雅黑, sans-serif;font-size: 9pt;">
        <span>仓库：</span>
      </div>
    </div>
  </div>
    `
    const itemDiv = document.createElement('div')
    itemDiv.innerHTML = html
    itemDiv.style.opacity = opacity
    print_main.appendChild(itemDiv)
    const table = itemDiv.querySelector("table")
    if(table){
      if(itemDiv.offsetHeight < printHeight){
        margonBottom = printHeight - itemDiv.offsetHeight
        console.log(margonBottom)
        html = html.replace("{margonBottom}", margonBottom+80+"px;")
      }
    }
    html = html.replace("{margonBottom}", margonBottom+"px;")
    print_main.removeChild(itemDiv)
    return html
  }

  function createTr(data) {
    let tr  = ""
    data.forEach((item,index) => {
      tr += `
        <tr style="text-align: center;">
          <td style="font-family: 微软雅黑, sans-serif;font-size: 9pt;">${index+1}</td>
          <td style="font-family: 微软雅黑, sans-serif;font-size: 9pt;">${item.code || ''}</td>
          <td style="font-family: 微软雅黑, sans-serif;font-size: 9pt;">${item.name || ''}</td>
          <td style="font-family: 微软雅黑, sans-serif;font-size: 9pt;text-align: center">${item.model || ''}</td>
          <td style="font-family: 微软雅黑, sans-serif;font-size: 9pt;text-align: center">${item.address || ''}</td>
          <td style="font-family: 微软雅黑, sans-serif;font-size: 9pt;">${item.warehouse || ''}</td>
          <td style="font-family: 微软雅黑, sans-serif;font-size: 9pt;">${item.unit || '' }</td>
          <td style="font-family: 微软雅黑, sans-serif;font-size: 9pt;">${item.quantity || ''}</td>
          <td style="font-family: 微软雅黑, sans-serif;font-size: 9pt;">${item.remark || ''}</td>
        </tr>
      `
    })
    return tr
  }
  render()
  function handlePrint() {
    new Print(htmlContentDiv)
  }
  window.onload = function () {
    setTimeout(() => {
      handlePrint()
    }, 100)
  }
  // 监听打印开始事件
  function onbeforePrint() {
    console.log('打印开始')
  }

  function onafterprint() {
    closeWindow()
  }
  function printFinish() {
    closeWindow()
  }
  function closeWindow(t = 100) {
    setTimeout(() => {
      console.log('打印完成')
      window.close()
    }, t)
  }
  // 根据宽度,字体大小计算高度
  function getHeight(width, fontSize) {
    return (width / 21.5) * fontSize + 10
  }
</script>

</html>
