import{s as y}from"./index-BtOcqcNl.js";import{c as i,b,n as g,D as h,q as o,i as c,v as x,w as e,h as l,B as d,j as p,E as k,o as r}from"./.pnpm-hVqhwuVC.js";const M=i({name:"AuditLogTable"}),C=i({...M,props:{modelValue:{},modelModifiers:{}},emits:["update:modelValue"],setup(m){const n=b([]),a=g(m,"modelValue");async function _(){if(a.value!==void 0&&a.value>0&&(n.value=await y.pms.audit.process.auditRecordList({id:a.value}),!n.value||n.value.length===0))return k.error("暂无审核记录")}return h(()=>{a.value!==void 0&&a.value>0&&_()}),(V,w)=>{const s=c("el-table-column"),u=c("el-text"),f=c("cl-date-text"),v=c("el-table");return r(),o(v,{data:x(n),stripe:"",border:""},{default:e(()=>[l(s,{prop:"operatorName",label:"操作人",align:"center",width:"100"}),l(s,{prop:"operatorNote",label:"审核意见","show-overflow-tooltip":""}),l(s,{prop:"type",label:"审核结果",align:"center",width:"100"},{default:e(({row:t})=>[t.type===0?(r(),o(u,{key:0,type:"primary"},{default:e(()=>[p(" 提交审核 ")]),_:1})):d("",!0),t.type===1?(r(),o(u,{key:1,type:"success"},{default:e(()=>[p(" 审核通过 ")]),_:1})):d("",!0),t.type===2?(r(),o(u,{key:2,type:"danger"},{default:e(()=>[p(" 驳回审核 ")]),_:1})):d("",!0),t.type===3?(r(),o(u,{key:3,type:"danger"},{default:e(()=>[p(" 撤销 ")]),_:1})):d("",!0)]),_:1}),l(s,{prop:"createTime",label:"创建时间",align:"center",width:"180"},{default:e(({row:t})=>[l(f,{"model-value":t.createTime,format:"YYYY-MM-DD HH:mm:ss"},null,8,["model-value"])]),_:1})]),_:1},8,["data"])}}});export{C as _};
