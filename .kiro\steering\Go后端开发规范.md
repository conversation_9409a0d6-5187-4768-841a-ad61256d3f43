# Go 后端开发规范

## GoFrame 框架规范

### 项目结构
```
admin/
├── internal/
│   ├── controller/     # 控制器层
│   ├── service/       # 业务逻辑层
│   ├── dao/           # 数据访问层
│   ├── model/         # 数据模型
│   └── logic/         # 业务逻辑实现
├── modules/           # 业务模块
├── manifest/          # 配置文件
│   ├── config/        # 配置文件
│   └── docker/        # Docker 配置
├── resource/          # 资源文件
└── utility/           # 工具函数
```

### 控制器层规范
```go
package controller

import (
    "context"
    "github.com/gogf/gf/v2/frame/g"
    "github.com/gogf/gf/v2/net/ghttp"
)

type cExample struct{}

var Example = cExample{}

// List 获取列表
func (c *cExample) List(ctx context.Context, req *v1.ExampleListReq) (res *v1.ExampleListRes, err error) {
    // 参数验证
    if err = g.Validator().Data(req).Run(ctx); err != nil {
        return nil, err
    }
    
    // 调用服务层
    result, err := service.Example().List(ctx, req)
    if err != nil {
        return nil, err
    }
    
    return &v1.ExampleListRes{
        List:  result.List,
        Total: result.Total,
    }, nil
}

// Create 创建记录
func (c *cExample) Create(ctx context.Context, req *v1.ExampleCreateReq) (res *v1.ExampleCreateRes, err error) {
    // 参数验证
    if err = g.Validator().Data(req).Run(ctx); err != nil {
        return nil, err
    }
    
    // 调用服务层
    id, err := service.Example().Create(ctx, req)
    if err != nil {
        return nil, err
    }
    
    return &v1.ExampleCreateRes{Id: id}, nil
}
```

### 服务层规范
```go
package service

import (
    "context"
    "github.com/gogf/gf/v2/frame/g"
)

type sExample struct{}

func Example() *sExample {
    return &sExample{}
}

// List 获取列表
func (s *sExample) List(ctx context.Context, req *model.ExampleListInput) (*model.ExampleListOutput, error) {
    // 构建查询条件
    query := dao.Example.Ctx(ctx)
    
    // 添加筛选条件
    if req.Name != "" {
        query = query.Where("name LIKE ?", "%"+req.Name+"%")
    }
    
    if req.Status != 0 {
        query = query.Where("status = ?", req.Status)
    }
    
    // 分页查询
    total, err := query.Count()
    if err != nil {
        return nil, err
    }
    
    var list []*entity.Example
    err = query.Page(req.Page, req.Size).Scan(&list)
    if err != nil {
        return nil, err
    }
    
    return &model.ExampleListOutput{
        List:  list,
        Total: total,
    }, nil
}

// Create 创建记录
func (s *sExample) Create(ctx context.Context, req *model.ExampleCreateInput) (int64, error) {
    // 数据验证
    if req.Name == "" {
        return 0, gerror.New("名称不能为空")
    }
    
    // 检查重复
    count, err := dao.Example.Ctx(ctx).Where("name = ?", req.Name).Count()
    if err != nil {
        return 0, err
    }
    if count > 0 {
        return 0, gerror.New("名称已存在")
    }
    
    // 插入数据
    result, err := dao.Example.Ctx(ctx).Data(req).Insert()
    if err != nil {
        return 0, err
    }
    
    id, err := result.LastInsertId()
    if err != nil {
        return 0, err
    }
    
    return id, nil
}
```

### 数据访问层规范
```go
package dao

import (
    "github.com/gogf/gf/v2/database/gdb"
    "github.com/gogf/gf/v2/frame/g"
)

// internalExampleDao is internal type for wrapping dao logic.
type internalExampleDao struct {
    table   string          // table is the underlying table name of the DAO.
    group   string          // group is the database configuration group name of current DAO.
    columns ExampleColumns  // columns contains all the column names of Table for convenient usage.
}

// ExampleColumns defines and stores column names for table example.
type ExampleColumns struct {
    Id         string
    Name       string
    Status     string
    CreatedAt  string
    UpdatedAt  string
}

var (
    // Example is globally public accessible object for table example operations.
    Example = internalExampleDao{
        table:   "example",
        group:   "default",
        columns: exampleColumns,
    }
    
    // exampleColumns holds the columns for table example.
    exampleColumns = ExampleColumns{
        Id:        "id",
        Name:      "name",
        Status:    "status",
        CreatedAt: "created_at",
        UpdatedAt: "updated_at",
    }
)

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *internalExampleDao) DB() gdb.DB {
    return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *internalExampleDao) Table() string {
    return dao.table
}

// Columns returns the columns of current dao.
func (dao *internalExampleDao) Columns() ExampleColumns {
    return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *internalExampleDao) Group() string {
    return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *internalExampleDao) Ctx(ctx context.Context) *gdb.Model {
    return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}
```

### 模型定义规范
```go
package model

import (
    "github.com/gogf/gf/v2/os/gtime"
)

// Example 示例模型
type Example struct {
    Id        uint64      `json:"id"         description:"主键ID"`
    Name      string      `json:"name"       description:"名称"`
    Status    int         `json:"status"     description:"状态"`
    CreatedAt *gtime.Time `json:"createdAt"  description:"创建时间"`
    UpdatedAt *gtime.Time `json:"updatedAt"  description:"更新时间"`
}

// ExampleListInput 列表查询输入
type ExampleListInput struct {
    Page   int    `json:"page"   description:"页码"`
    Size   int    `json:"size"   description:"每页数量"`
    Name   string `json:"name"   description:"名称"`
    Status int    `json:"status" description:"状态"`
}

// ExampleListOutput 列表查询输出
type ExampleListOutput struct {
    List  []*Example `json:"list"  description:"列表数据"`
    Total int        `json:"total" description:"总数"`
}

// ExampleCreateInput 创建输入
type ExampleCreateInput struct {
    Name   string `json:"name"   v:"required|length:1,50#名称不能为空|名称长度为1-50个字符"`
    Status int    `json:"status" v:"required|in:0,1#状态不能为空|状态值不正确"`
}
```

## 数据库操作规范

### 查询操作
```go
// 基础查询
var user *entity.User
err := dao.User.Ctx(ctx).Where("id = ?", id).Scan(&user)

// 条件查询
var users []*entity.User
err := dao.User.Ctx(ctx).
    Where("status = ?", 1).
    Where("created_at >= ?", startTime).
    OrderBy("created_at DESC").
    Scan(&users)

// 分页查询
var users []*entity.User
err := dao.User.Ctx(ctx).
    Where("status = ?", 1).
    Page(page, size).
    Scan(&users)

// 统计查询
count, err := dao.User.Ctx(ctx).Where("status = ?", 1).Count()
```

### 写入操作
```go
// 插入单条记录
result, err := dao.User.Ctx(ctx).Data(g.Map{
    "name":   "张三",
    "email":  "<EMAIL>",
    "status": 1,
}).Insert()

// 批量插入
data := []g.Map{
    {"name": "张三", "email": "<EMAIL>"},
    {"name": "李四", "email": "<EMAIL>"},
}
result, err := dao.User.Ctx(ctx).Data(data).Insert()

// 更新记录
result, err := dao.User.Ctx(ctx).
    Where("id = ?", id).
    Data(g.Map{"name": "新名称"}).
    Update()

// 删除记录
result, err := dao.User.Ctx(ctx).Where("id = ?", id).Delete()
```

## 错误处理规范

### 错误定义
```go
import (
    "github.com/gogf/gf/v2/errors/gerror"
)

var (
    ErrUserNotFound     = gerror.New("用户不存在")
    ErrInvalidParameter = gerror.New("参数无效")
    ErrPermissionDenied = gerror.New("权限不足")
)

// 自定义错误码
const (
    CodeUserNotFound     = 1001
    CodeInvalidParameter = 1002
    CodePermissionDenied = 1003
)
```

### 错误处理
```go
// 服务层错误处理
func (s *sUser) GetById(ctx context.Context, id uint64) (*entity.User, error) {
    var user *entity.User
    err := dao.User.Ctx(ctx).Where("id = ?", id).Scan(&user)
    if err != nil {
        return nil, gerror.Wrap(err, "查询用户失败")
    }
    
    if user == nil {
        return nil, ErrUserNotFound
    }
    
    return user, nil
}

// 控制器层错误处理
func (c *cUser) GetById(ctx context.Context, req *v1.UserGetReq) (res *v1.UserGetRes, err error) {
    user, err := service.User().GetById(ctx, req.Id)
    if err != nil {
        if gerror.Is(err, ErrUserNotFound) {
            return nil, gerror.NewCode(gcode.CodeNotFound, "用户不存在")
        }
        return nil, err
    }
    
    return &v1.UserGetRes{User: user}, nil
}
```

## 配置管理规范

### 配置文件结构
```yaml
# config.yaml
server:
  address: ":8080"
  serverRoot: "resource/public"

database:
  default:
    link: "mysql:root:password@tcp(127.0.0.1:3306)/database"
    debug: true

redis:
  default:
    address: "127.0.0.1:6379"
    db: 0

logger:
  level: "all"
  stdout: true
```

### 配置使用
```go
// 获取配置
dbConfig := g.Cfg().MustGet(ctx, "database.default")
redisConfig := g.Cfg().MustGet(ctx, "redis.default")

// 使用结构体接收配置
type ServerConfig struct {
    Address    string `json:"address"`
    ServerRoot string `json:"serverRoot"`
}

var config ServerConfig
err := g.Cfg().MustGet(ctx, "server").Struct(&config)
```

## 中间件规范

### 认证中间件
```go
func Auth(r *ghttp.Request) {
    token := r.Header.Get("Authorization")
    if token == "" {
        r.Response.WriteJsonExit(g.Map{
            "code": 401,
            "msg":  "未授权访问",
        })
    }
    
    // 验证 token
    claims, err := jwt.ParseToken(token)
    if err != nil {
        r.Response.WriteJsonExit(g.Map{
            "code": 401,
            "msg":  "token 无效",
        })
    }
    
    // 设置用户信息到上下文
    r.SetCtxVar("userId", claims.UserId)
    r.Middleware.Next()
}
```

### 日志中间件
```go
func Logger(r *ghttp.Request) {
    start := time.Now()
    r.Middleware.Next()
    
    g.Log().Info(r.Context(), g.Map{
        "method":   r.Method,
        "url":      r.URL.String(),
        "status":   r.Response.Status,
        "duration": time.Since(start),
        "ip":       r.GetClientIp(),
    })
}
```

## 测试规范

### 单元测试
```go
package service_test

import (
    "context"
    "testing"
    "github.com/gogf/gf/v2/test/gtest"
)

func TestUserService_Create(t *testing.T) {
    gtest.C(t, func(t *gtest.T) {
        ctx := context.Background()
        
        // 准备测试数据
        input := &model.UserCreateInput{
            Name:  "测试用户",
            Email: "<EMAIL>",
        }
        
        // 执行测试
        id, err := service.User().Create(ctx, input)
        
        // 断言结果
        t.AssertNil(err)
        t.AssertGT(id, 0)
    })
}
```https://kiro.dev/docs/steering/