import{c as P,b as c,K as x,q as f,w as u,h as n,i as s,y,j as b,f as w,F as W,s as T,L as G,B as $,t as A,E as H,o as i}from"./.pnpm-hVqhwuVC.js";import{s as I,i as Y,e as R}from"./index-DkYL1aws.js";import{INBOUND_TYPE as J}from"./constant-C2dsBPRR.js";import{_ as X}from"./_plugin-vue_export-helper-DlAUqK2U.js";const Z={style:{"margin-right":"20px"}},ee=P({name:"MaterialInbound"}),le=P({...ee,props:{api:{default:I.pms.finance},showPrice:{type:Boolean,default:!0}},setup(D){const V=D,_=c(!1),h=c(!1),a=c({date:[],keyWord:"",type:void 0,status:void 0,useStatus:!1,useType:!1}),M=c([]);async function E(){try{const e=await I.pms.supplier.request({url:"/list",method:"POST"});M.value=e}catch(e){console.error(e)}}E();const o=Y.useCrud({dict:{api:{page:"materialInboundPage"}},service:V.api,async onRefresh(e,{next:l,done:r,render:d}){r(),B(e);const{list:m,pagination:v}=await l(e);d(m,v)}},e=>{e.refresh()});function B(e){if(a.value.date&&a.value.date.length>0){const l=x(a.value.date[0]).format("YYYY-MM-DD"),r=x(a.value.date[1]).format("YYYY-MM-DD");e.date=`${l},${r}`}return a.value.keyWord&&(e.keyWord=a.value.keyWord),a.value.supplierId!==void 0&&(e.supplierId=a.value.supplierId),a.value.type!==void 0?(e.type=a.value.type,e.useType=!0):e.useType=!1,a.value.status!==void 0?(e.status=a.value.status,e.useStatus=!0,e.status===0&&(e.status=-1)):e.useStatus=!1,e}const O=c(J),C=c([{label:"草稿",value:0,type:"info"},{label:"待审批",value:4,type:"primary"},{label:"入库中",value:2,type:"info"},{label:"已完成",value:3,type:"success"}]),N=Y.useTable({columns:[{label:"单据时间",prop:"inboundTime",minWidth:90,formatter(e){return e.inboundTime?x(e.inboundTime).format("YYYY-MM-DD"):""}},{label:"入库类型",prop:"type",minWidth:90,dict:O.value},{label:"采购PO",prop:"po",minWidth:150},{label:"物料编码",prop:"code",minWidth:160,showOverflowTooltip:!0},{label:"物料名称",prop:"materialName",minWidth:160,showOverflowTooltip:!0},{label:"物料等级",prop:"level",minWidth:70},{label:"入库数量",prop:"quantity",minWidth:80},{label:"采购数量",prop:"purchaseQuantity",minWidth:80},{label:"采购单价",prop:"unitPrice",minWidth:80},{label:"入库金额",minWidth:100,formatter:e=>(e.unitPrice*e.quantity).toFixed(2)||0},{label:"付款状态",prop:"payment_status",minWidth:120},{label:"供应商",prop:"supplierName",minWidth:180,showOverflowTooltip:!0},{label:"系统单号",prop:"no",minWidth:120},{label:"规格/型号",prop:"model",minWidth:180,showOverflowTooltip:!0}]});function p(){var e,l,r;try{(l=(e=o==null?void 0:o.value)==null?void 0:e.params)!=null&&l.page&&(o.value.params.page=1),_.value=!0,(r=o==null?void 0:o.value)==null||r.refresh()}catch(d){console.error(d)}finally{_.value=!1}}function k(){var e,l;a.value.keyWord="",a.value.date=[],a.value.supplierId=void 0,(e=o==null?void 0:o.value)!=null&&e.params&&(o.value.params.page=1,o.value.params.size=20),(l=o==null?void 0:o.value)==null||l.refresh()}function q(e){e?p():k()}async function S(){h.value=!0;try{const e=B({page:1,size:1e5}),l=await V.api.request({url:"/materialInboundExportExcel",method:"GET",responseType:"blob",params:e});R(l)&&H.success("导出成功")}catch(e){console.error(e)}finally{h.value=!1}}function U(e){let l="";switch(e){case"A":l="BOM用量等于1，单价大于等于1";break;case"B":l="BOM用量大于1，单价大于等于0.1";break;case"C":l="BOM用量等于1，单价大于等于0.5";break;case"D":l="BOM用量小于1，单价大于等于5";break;case"E":l="BOM用量小于1，单价大于等于1并且小于5";break;case"F":l="BOM用量小于1，单价小于1";break;case"G":l="BOM用量等于1，单价小于0.5";break;case"H":l="BOM用量大于1，单价小于0.1";break;default:l=""}return l}return(e,l)=>{const r=s("el-button"),d=s("cl-flex1"),m=s("el-option"),v=s("el-select"),L=s("el-date-picker"),F=s("el-input"),g=s("el-row"),K=s("el-tooltip"),Q=s("cl-table"),j=s("cl-pagination"),z=s("cl-crud");return i(),f(z,{ref_key:"Crud",ref:o},{default:u(()=>[n(g,null,{default:u(()=>[n(r,{onClick:k},{default:u(()=>[b(" 刷新 ")]),_:1}),n(r,{type:"success",icon:"Download",loading:h.value,onClick:S},{default:u(()=>[b(" 导出excel ")]),_:1},8,["loading"]),n(d),y("div",null,[n(v,{modelValue:a.value.status,"onUpdate:modelValue":l[0]||(l[0]=t=>a.value.status=t),placeholder:"请选择状态",clearable:"",class:"mr-20px w-200px",onChange:p},{default:u(()=>[(i(!0),w(W,null,T(C.value,t=>(i(),f(m,{key:t.value,label:t.label,value:t.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),y("div",null,[n(v,{modelValue:a.value.type,"onUpdate:modelValue":l[1]||(l[1]=t=>a.value.type=t),placeholder:"请选择类型",clearable:"",class:"mr-20px w-200px",onChange:p},{default:u(()=>[(i(!0),w(W,null,T(O.value,t=>(i(),f(m,{key:t.value,label:t.label,value:t.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),y("div",null,[n(v,{modelValue:a.value.supplierId,"onUpdate:modelValue":l[2]||(l[2]=t=>a.value.supplierId=t),placeholder:"请选择供应商",clearable:"",class:"mr-20px w-200px",onChange:p},{default:u(()=>[(i(!0),w(W,null,T(M.value,t=>(i(),f(m,{key:t.id,label:t.supplierName,value:t.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),y("div",Z,[n(L,{modelValue:a.value.date,"onUpdate:modelValue":l[3]||(l[3]=t=>a.value.date=t),type:"daterange",clearable:"","range-separator":"~","start-placeholder":"开始日期","end-placeholder":"结束日期",onChange:q},null,8,["modelValue"])]),n(F,{modelValue:a.value.keyWord,"onUpdate:modelValue":l[4]||(l[4]=t=>a.value.keyWord=t),placeholder:"请输入物料编号或物料名称或入库单号或Po",style:{width:"500px"},clearable:"",onClear:k,onKeyup:G(p,["enter"])},null,8,["modelValue"]),n(r,{type:"primary",mx:"10px",loading:_.value,onClick:p},{default:u(()=>[b(" 搜索 ")]),_:1},8,["loading"])]),_:1}),n(g,null,{default:u(()=>[n(Q,{ref_key:"Table",ref:N,"row-key":"rowIndex"},{"column-level":u(({scope:t})=>[t.row.level!=""?(i(),f(K,{key:0,class:"box-item",effect:"dark",content:U(t.row.level),placement:"top-start"},{default:u(()=>[n(r,null,{default:u(()=>[b(A(t.row.level),1)]),_:2},1024)]),_:2},1032,["content"])):$("",!0)]),_:1},512)]),_:1}),n(g,null,{default:u(()=>[n(d),n(j)]),_:1})]),_:1},512)}}}),ue=X(le,[["__scopeId","data-v-8c709fe2"]]);export{ue as M};
