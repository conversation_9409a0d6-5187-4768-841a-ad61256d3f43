import{c as a,e as o,K as s,f as r,o as n,t as l}from"./.pnpm-hVqhwuVC.js";import{_ as m}from"./_plugin-vue_export-helper-DlAUqK2U.js";const c=a({name:"ClDateText",props:{modelValue:[String,Number],format:{type:String,default:"YYYY-MM-DD HH:mm:ss"}},setup(e){return{value:o(()=>e.modelValue?s(e.modelValue).format(e.format):"")}}}),u={class:"cl-date-text"};function d(e,t,f,p,i,_){return n(),r("span",u,l(e.value),1)}const Y=m(c,[["render",d]]);export{Y as default};
