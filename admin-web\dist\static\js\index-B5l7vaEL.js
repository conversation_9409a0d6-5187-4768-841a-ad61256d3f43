import{i as W,h as ee,C as te,d as L}from"./index-BtOcqcNl.js";import{c as T,b as R,S as ne,f as S,y as N,h as n,B as j,G as P,i,w as r,v as w,aB as oe,b8 as le,I as se,j as A,H as Q,q as E,W as ae,t as X,b9 as re,Y as ie,a0 as ce,T as O,E as $,o as y,au as pe,af as de,ag as ue,s as me,F as _e}from"./.pnpm-hVqhwuVC.js";import{a as H}from"./index-D95m1iJL.js";import{u as Z}from"./hook-BXXLBjrX.js";import{_ as fe}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{_ as be}from"./index.vue_vue_type_script_setup_true_name_cl-avatar_lang-Dwbve7L9.js";const he=k=>(de("data-v-64cf6212"),k=k(),ue(),k),ve={class:"dept-tree"},ge={class:"dept-tree__header"},we=he(()=>N("div",null,"组织架构",-1)),ye={class:"dept-tree__op"},ke={class:"no"},xe={class:"dept-tree__node"},Ce=["onClick"],Ie=["onClick"],Ne=T({name:"dept-list"}),$e=T({...Ne,props:{drag:{type:Boolean,default:!0},level:{type:Number,default:99}},emits:["refresh","user-add"],setup(k,{emit:x}){const B=k,M=x,{service:f,browser:C}=H(),{ViewGroup:_}=Z(),b=W.useForm(),d=R([]),I=R(!1),h=R(!1);function s({data:t}){return t.parentId}function c(t,e){return e.data.parentId}async function p(){I.value=!0,h.value=!1,await f.base.sys.department.list().then(t=>{var e;d.value=ee(t),(e=_.value)!=null&&e.selected||v()}),I.value=!1}function v(t){var e;if(t||(t=d.value[0]),t){const l=t.children?te(t.children).map(o=>o.id):[];l.unshift(t.id),(e=_.value)==null||e.select(t),ce(()=>{M("refresh",{page:1,departmentIds:l})})}}function G(t){var l;const e=t.id?"update":"add";(l=b.value)==null||l.open({title:"编辑部门",width:"550px",props:{labelWidth:"100px"},items:[{label:"部门名称",prop:"name",component:{name:"el-input"},required:!0},{label:"上级部门",prop:"parentName",component:{name:"el-input",props:{disabled:!0}}},{label:"排序",prop:"orderNum",component:{name:"el-input-number",props:{"controls-position":"right",min:0,max:100}}}],form:{...t},on:{submit(o,{done:a,close:u}){f.base.sys.department[e]({id:t.id,parentId:t.parentId,name:o.name,orderNum:o.orderNum}).then(()=>{$.success(`新增部门 “${o.name}” 成功`),u(),p()}).catch(V=>{$.error(V.message),a()})}}},[W.setFocus()])}function U(t){async function e(l){await f.base.sys.department.delete({ids:[t.id],deleteUser:l}).then(()=>{var o,a;((a=(o=_.value)==null?void 0:o.selected)==null?void 0:a.id)==t.id&&v(),l?$.success("删除成功"):O.confirm(`“${t.name}” 部门的用户已成功转移到 “${t.parentName}” 部门。`,"删除成功")}),p()}O.confirm(`此操作将会删除 “${t.name}” 部门的所有用户，是否确认？`,"提示",{type:"warning",confirmButtonText:"直接删除",cancelButtonText:"保留用户",distinguishCancelAndClose:!0}).then(()=>{e(!0)}).catch(l=>{l=="cancel"&&e(!1)})}function D(t){t?O.confirm("部门架构已发生改变，是否保存？","提示",{type:"warning"}).then(async()=>{const e=[];function l(o,a){o.forEach(u=>{u.parentId=a,e.push(u),u.children&&pe(u.children)&&l(u.children,u.id)})}l(d.value,null),await f.base.sys.department.order(e.map((o,a)=>({id:o.id,parentId:o.parentId,orderNum:a}))).then(()=>{$.success("更新排序成功")}).catch(o=>{$.error(o.message)}),p(),h.value=!1}).catch(()=>null):p()}function F(t,e,l){e||(e=d.value[0]||{});const o=f.base.sys.department.permission;W.ContextMenu.open(t,{list:[{label:"新增",hidden:l&&l.level>=B.level||!L(o.add),callback(a){G({name:"",parentName:e.name,parentId:e.id}),a()}},{label:"编辑",hidden:!L(o.update),callback(a){G(e),a()}},{label:"删除",hidden:!e.parentId||!L(o.delete),callback(a){U(e),a()}},{label:"新增成员",hidden:!L(o.add),callback(a){M("user-add",e),a()}}]})}return ne(()=>{p()}),(t,e)=>{const l=i("el-icon"),o=i("el-tooltip"),a=i("el-button"),u=i("el-tree"),V=i("el-scrollbar"),z=i("cl-form"),m=Q("loading");return y(),S("div",ve,[N("div",ge,[we,N("ul",ye,[N("li",{onClick:e[0]||(e[0]=g=>p())},[n(o,{content:"刷新"},{default:r(()=>[n(l,null,{default:r(()=>[n(w(oe))]),_:1})]),_:1})]),k.drag&&!w(C).isMini?(y(),S("li",{key:0,onClick:e[1]||(e[1]=g=>h.value=!0)},[n(o,{content:"拖动排序"},{default:r(()=>[n(l,null,{default:r(()=>[n(w(le))]),_:1})]),_:1})])):j("",!0),P(N("li",ke,[n(a,{size:"small",onClick:e[2]||(e[2]=g=>D(!0))},{default:r(()=>[A(" 保存 ")]),_:1}),n(a,{size:"small",onClick:e[3]||(e[3]=g=>D(!1))},{default:r(()=>[A(" 取消 ")]),_:1})],512),[[se,h.value]])])]),N("div",{class:"dept-tree__container",onContextmenu:ie(F,["stop","prevent"])},[n(V,null,{default:r(()=>[P((y(),E(u,{"node-key":"id","default-expand-all":"",data:d.value,props:{label:"name"},draggable:h.value,"allow-drag":s,"allow-drop":c,"expand-on-click-node":!1,onNodeContextmenu:F},{default:r(({node:g,data:q})=>{var Y,J;return[N("div",xe,[N("span",{class:ae(["dept-tree__node-label",{"is-active":q.id==((J=(Y=w(_))==null?void 0:Y.selected)==null?void 0:J.id)}]),onClick:K=>v(q)},X(g.label),11,Ce),w(C).isMini?(y(),S("span",{key:0,class:"dept-tree__node-icon",onClick:K=>F(K,q,g)},[n(l,null,{default:r(()=>[n(w(re))]),_:1})],8,Ie)):j("",!0)])]}),_:1},8,["data","draggable"])),[[m,I.value]])]),_:1})],32),n(z,{ref_key:"Form",ref:b},null,512)])}}}),We=fe($e,[["__scopeId","data-v-64cf6212"]]),Me={class:"dept-move"},Te=T({name:"user-move"}),Be=T({...Te,setup(k,{expose:x}){const{service:B}=H(),M=W.useForm(),f=W.useCrud();async function C(_){var b;(b=M.value)==null||b.open({title:"部门转移",width:"500px",props:{labelWidth:"80px"},items:[{label:"选择部门",prop:"departmentId",component:{name:"cl-dept-select"}}],on:{submit(d,{done:I,close:h}){if(!d.departmentId)return $.warning("请选择部门"),I();O.confirm("转移到新部门，是否继续？","提示",{type:"warning"}).then(()=>{B.base.sys.user.move({...d,userIds:_}).then(()=>{var s;$.success("转移成功"),(s=f.value)==null||s.refresh(),h()}).catch(s=>{$.error(s.message),I()})}).catch(()=>null)}}})}return x({open:C}),(_,b)=>{const d=i("cl-form");return y(),S("div",Me,[n(d,{ref_key:"Form",ref:M},null,512)])}}}),De=T({name:"sys-user"}),Ge=T({...De,setup(k){const{service:x,refs:B,setRefs:M}=H(),{ViewGroup:f}=Z({title:"用户列表"}),C=W.useCrud({service:x.base.sys.user}),_=W.useTable({columns:[{type:"selection",width:60},{prop:"headImg",label:"头像",component:{name:"avatar"}},{prop:"username",label:"用户名",minWidth:150},{prop:"name",label:"姓名",minWidth:150},{prop:"nickName",label:"昵称",minWidth:150},{prop:"departmentName",label:"部门名称",minWidth:150},{prop:"roleName",label:"角色",headerAlign:"center",minWidth:120},{prop:"status",label:"状态",minWidth:120,component:{name:"cl-switch"}},{prop:"phone",label:"手机号码",minWidth:150},{prop:"remark",label:"备注",minWidth:150},{prop:"createTime",label:"创建时间",sortable:"desc",minWidth:160},{type:"op",buttons:["slot-btn","edit","delete"],width:240}]}),b=W.useUpsert({dialog:{width:"800px"},items:[{prop:"headImg",label:"头像",component:{name:"cl-upload",props:{text:"选择头像",isPrivate:!1}}},{prop:"name",label:"姓名",span:12,required:!0,component:{name:"el-input"}},{prop:"nickName",label:"昵称",required:!0,span:12,component:{name:"el-input"}},{prop:"username",label:"用户名",required:!0,span:12,component:{name:"el-input"}},()=>{var s;return{prop:"password",label:"密码",span:12,required:((s=b.value)==null?void 0:s.mode)==="add",component:{name:"el-input",props:{type:"password"}},rules:[{min:6,max:16,message:"密码长度在 6 到 16 个字符"}]}},{prop:"roleIdList",label:"角色",value:[],required:!0,component:{name:"el-select",options:[],props:{multiple:!0,"multiple-limit":10}}},{prop:"phone",label:"手机号码",span:12,component:{name:"el-input"}},{prop:"email",label:"邮箱",span:12,component:{name:"el-input"}},{prop:"remark",label:"备注",component:{name:"el-input",props:{type:"textarea",rows:4}}},{prop:"status",label:"状态",value:1,component:{name:"el-radio-group",options:[{label:"开启",value:1},{label:"关闭",value:0}]}}],onSubmit(s,{next:c}){var p,v;c({...s,departmentId:(v=(p=f.value)==null?void 0:p.selected)==null?void 0:v.id})},async onOpen(){x.base.sys.role.list().then(s=>{var c;(c=b.value)==null||c.setOptions("roleIdList",s.map(p=>({label:p.name||"",value:p.id})))})}});function d(s){var c;(c=C.value)==null||c.refresh(s)}function I({id:s}){var c;(c=C.value)==null||c.rowAppend({departmentId:s})}async function h(s){var p;let c=[];s?c=[s.id]:c=((p=_.value)==null?void 0:p.selection.map(v=>v.id))||[],B.userMove.open(c)}return(s,c)=>{const p=i("cl-refresh-btn"),v=i("cl-add-btn"),G=i("cl-multi-delete-btn"),U=i("el-button"),D=i("cl-flex1"),F=i("cl-search-key"),t=i("cl-row"),e=i("el-tag"),l=i("cl-table"),o=i("cl-pagination"),a=i("cl-upsert"),u=i("cl-crud"),V=i("cl-view-group"),z=Q("permission");return y(),E(V,{ref_key:"ViewGroup",ref:f},{left:r(()=>[n(We,{onRefresh:d,onUserAdd:I})]),right:r(()=>[n(u,{ref_key:"Crud",ref:C},{default:r(()=>[n(t,null,{default:r(()=>{var m;return[n(p),n(v),n(G),P((y(),E(U,{type:"success",disabled:((m=w(_))==null?void 0:m.selection.length)===0,onClick:c[0]||(c[0]=g=>h())},{default:r(()=>[A(" 转移 ")]),_:1},8,["disabled"])),[[z,w(x).base.sys.user.permission.move]]),n(D),n(F,{placeholder:"搜索用户名、姓名"})]}),_:1}),n(t,null,{default:r(()=>[n(l,{ref_key:"Table",ref:_},{"column-headImg":r(({scope:m})=>[n(be,{src:m.row.headImg,title:m.row.name},null,8,["src","title"])]),"column-roleName":r(({scope:m})=>[m.row.roleName?(y(!0),S(_e,{key:0},me(m.row.roleName.split(","),(g,q)=>(y(),E(e,{key:q,"disable-transitions":"",size:"small",effect:"dark",style:{margin:"2px"}},{default:r(()=>[A(X(g),1)]),_:2},1024))),128)):j("",!0)]),"slot-btn":r(({scope:m})=>[P((y(),E(U,{text:"",bg:"",onClick:g=>h(m.row)},{default:r(()=>[A(" 转移 ")]),_:2},1032,["onClick"])),[[z,w(x).base.sys.user.permission.move]])]),_:1},512)]),_:1}),n(t,null,{default:r(()=>[n(D),n(o)]),_:1}),n(a,{ref_key:"Upsert",ref:b},null,512),n(Be,{ref:w(M)("userMove")},null,512)]),_:1},512)]),_:1},512)}}});export{Ge as default};
