import{c as ae,ay as ce,e as S,au as Q,as as pe,b as fe,r as me,z as ge,i as w,f as C,o as c,y,h as f,q as $,B as m,W as X,w as g,J as V,j as ve,t as x,v as d,F as ye,G as he,Y as ee,aL as _e,I as be,aM as ke,at as Se,m as we,aN as Ce,E as q,Z as $e}from"./.pnpm-hVqhwuVC.js";import{f as xe,a as Be,_ as ze,g as Pe,b as De}from"./viewer.vue_vue_type_script_setup_true_name_item-viewer_lang-6n4_YlHr.js";import{k as Fe,n as Ne,o as Ue,q as Ve,t as j}from"./index-DkYL1aws.js";import{a as Me}from"./index-C6cm1h61.js";import{_ as Ie}from"./_plugin-vue_export-helper-DlAUqK2U.js";const Te={class:"cl-upload__item"},Ee={class:"cl-upload__text"},Le={class:"cl-upload__name"},Oe={class:"cl-upload__size"},Ae={class:"cl-upload__actions"},Ge={key:2,class:"cl-upload__progress"},Xe={key:3,class:"cl-upload__error"},qe=ae({name:"cl-upload"}),je=ae({...qe,props:{modelValue:{type:[String,Array],default:()=>[]},whitelist:{type:Array,default:["png","jpg","jpeg","PNG","JPG","JPEG","xls","xlsx","XLS","XLSX","doc","docx","DOC","DOCX","pdf","PDF","ppt","PPT","pptx","PPTX","txt","TXT","DWG","DXF","DWF","SVG","dwg","dxf","dwf","svg","STL","STEP","IGES","OBJ","3MF","FBX","stl","step","iges","obj","3mf","fbx"]},type:{type:String,default:"image"},showResult:{type:Boolean,default:!0},accept:String,multiple:Boolean,limit:Number,limitSize:{type:Number,default:20},limitUpload:{type:Boolean,default:!0},size:[String,Number,Array],text:String,prefixPath:{type:String,default:"app"},menu:{type:String,default:"base"},showFileList:{type:Boolean,default:!0},draggable:Boolean,disabled:Boolean,customClass:String,beforeUpload:Function,isSpace:Boolean,isPrivate:{type:Boolean,default:!0},classifyId:{type:Number,default:0},isEdit:null,scope:null,isDisabled:Boolean,compress:Boolean},emits:["update:modelValue","upload","success","error","progress"],setup(p,{expose:te,emit:le}){ce(a=>({"4b4852c8":R.value[0],"4b48528a":R.value[1]}));const s=p,B=le,{service:M,refs:b,setRefs:z}=Me(),{user:se}=Fe(),{options:P}=Ne.get("upload"),R=S(()=>{const a=s.size||P.size;return(Q(a)?a:[a,a]).map(e=>pe(e)?`${e}px`:e)}),I=S(()=>s.isDisabled||s.disabled),T=S(()=>I.value||s.isSpace),Y=s.limit||P.limit.upload,E=s.limitSize||P.limit.size,J=s.text||P.text,L=S(()=>({Authorization:se.token})),r=fe([]),oe=me({options:{group:"Upload",animation:300,ghostClass:"Ghost",dragClass:"Drag",draggable:".is-drag",disabled:!s.draggable}}),D=S(()=>s.accept||(s.type==="file"?"*":"image/*")),O=S(()=>s.multiple?Y-r.value.length>0:r.value.length===0);function W(a){var e;return s.type==="image"?"image":(e=De(a))==null?void 0:e.value}async function F(a,e){function o(){const t={type:W(a.name),preload:"",progress:0,url:a.url,uid:a.uid,size:a.size};return t.preload=t.url||(t.type==="image"?window.webkitURL.createObjectURL(a):a.name),e?Object.assign(e,t):s.multiple?(O.value||!s.limitUpload)&&r.value.push(t):r.value=[t],B("upload",t),!0}if(s.beforeUpload){const t=s.beforeUpload(a,e);return Ve(t)?t.then(o).catch(()=>null):t&&o(),t}else{const t=a.size/1024/1024;return console.log(`文件大小: ${t.toFixed(2)}MB, 限制大小: ${E}MB`),t>=E?(q.error(`上传文件大小不能超过 ${E}MB! 当前文件: ${t.toFixed(2)}MB`),!1):o()}}function K(a){r.value.splice(a,1),N()}function Z(){r.value=[]}function re(a){var e;a.type==="image"?(e=b.viewer)==null||e.open(a,r.value.map(o=>({...o,url:o.preload}))):window.open(a.url)}async function A(a,e){if(e||(e=r.value.find(t=>t.uid===a.file.uid)),!e)return!1;const o=j("");try{let t=`${o}_${a.file.name}`;const{mode:n,type:k}=await M.base.comm.uploadMode();return new Promise((G,U)=>{async function l({host:i,preview:v,data:H}){const _=new FormData;for(const u in H)_.append(u,H[u]);n==="cloud"&&(t=`${$e().format("YYYYMMDD")}/${t}`,s.isPrivate?t=`private/${t}`:t=`public/${t}`),_.append("key",t),_.append("type",e.type),_.append("classifyId",s.classifyId.toString()),_.append("isPrivate",s.isPrivate.toString()),_.append("file",a.file),await M.request({url:i,method:"POST",headers:{"Content-Type":"multipart/form-data"},timeout:6e5,data:_,onUploadProgress(u){const de=Number.parseInt(String(u.loaded/u.total*100));e.progress=Math.min(de,99),B("progress",e)},proxy:n==="local"}).then(u=>{e.progress=100,n==="local"?(e.url=u,e.key=u.replace(/^https?:\/\/[^/]+/,"")):(e.url=`${v||i}/${t}`,e.key=t),e.fileId=o,e.name=e.preload,B("success",{...e,filename:a.file.name}),G(e.url),N()}).catch(u=>{q.error(u.message),e.error=u.message,B("error",e),U(u)})}n==="local"?l({host:"/admin/base/comm/upload"}):M.base.comm.upload().then(i=>{switch(k){case"cos":l({host:i.url,data:i.credentials});break;case"oss":l({host:i.host,data:{OSSAccessKeyId:i.OSSAccessKeyId,policy:i.policy,signature:i.signature}});break;case"qiniu":l({host:i.uploadUrl,preview:i.publicDomain,data:{token:i.token}});break}}).catch(U)})}catch{q.error("上传配置错误")}}function ie(){return r.value.find(a=>a.progress!==100)}function N(){r.value.find(e=>!e.url)||B("update:modelValue",Pe(r.value))}function ne(a){var e,o,t;Z(),(e=b.upload)==null||e.clearFiles(),(o=b.upload)==null||o.handleStart(a),(t=b.upload)==null||t.submit()}function ue(){b.upload.$el.querySelector("input").click()}const h={data:null,open(a){var e;h.data=a,(e=b.space)==null||e.open({limit:a||!s.multiple?1:Y})},onConfirm(a){a.forEach(e=>{F({uid:j(),...e},h.data)}),N(),h.data=null}};return ge(()=>s.modelValue,a=>{const e=(Q(a)?a:(a||"").split(",")).filter(Boolean),o=[];r.value=e.map(t=>{const n=r.value.find(k=>t===k.url&&!o.includes(k.uid));return n?(o.push(n.uid),n):{type:W(t),progress:0,uid:j(),url:t,preload:t}}).filter((t,n)=>s.multiple?!0:n===0)},{immediate:!0}),te({isAdd:O,list:r,check:ie,clear:Z,remove:K,upload:ne,open,handleOpen:ue}),(a,e)=>{const o=w("el-button"),t=w("el-upload"),n=w("el-icon"),k=w("el-image"),G=w("el-progress"),U=w("cl-upload-space");return c(),C("div",null,[y("div",{class:X(["cl-upload__wrap",[p.customClass]])},[y("div",{class:X(["cl-upload",[`cl-upload--${p.type}`,{"is-disabled":I.value}]])},[p.type==="file"?(c(),C("div",{key:0,class:"cl-upload__file-btn",onClick:e[0]||(e[0]=l=>h.open())},[f(t,{ref:d(z)("upload"),action:"",accept:D.value,"show-file-list":!1,"before-upload":F,"http-request":A,headers:L.value,multiple:p.multiple,disabled:T.value},{default:g(()=>[V(a.$slots,"default",{},()=>[f(o,{type:"success"},{default:g(()=>[ve(x(d(J)),1)]),_:1}),V(a.$slots,"uploadSlot",{},void 0,!0)],!0)]),_:3},8,["accept","headers","multiple","disabled"])])):m("",!0),p.showFileList?(c(),$(d(Ce),we({key:1,modelValue:r.value,"onUpdate:modelValue":e[2]||(e[2]=l=>r.value=l),class:"cl-upload__list",tag:"div"},oe.options,{"item-key":"uid",onEnd:N}),{footer:g(()=>[p.type==="image"&&O.value?(c(),C("div",{key:0,class:"cl-upload__footer",onClick:e[1]||(e[1]=l=>h.open())},[f(t,{ref:d(z)("upload"),action:"",accept:D.value,"show-file-list":!1,"before-upload":F,"http-request":A,headers:L.value,multiple:p.multiple,disabled:T.value},{default:g(()=>[V(a.$slots,"default",{},()=>[y("div",Te,[f(n,{size:24},{default:g(()=>[f(d(Se))]),_:1}),y("span",Ee,x(d(J)),1)])],!0)]),_:3},8,["accept","headers","multiple","disabled"])])):m("",!0)]),item:g(({element:l,index:i})=>[p.showFileList?(c(),$(t,{key:0,action:"",class:"is-drag",accept:D.value,"show-file-list":!1,"http-request":v=>A(v,l),"before-upload":v=>{F(v,l)},headers:L.value,disabled:T.value,onClick:v=>h.open(l)},{default:g(()=>[V(a.$slots,"item",{item:l,index:i},()=>[y("div",{class:X(["cl-upload__item",[`is-${l.type}`]])},[l.type==="image"?(c(),$(k,{key:0,src:l.preload,fit:"cover"},null,8,["src"])):(c(),C(ye,{key:1},[y("span",Le,x(d(xe)(l.preload))+"."+x(d(Ue)(l.preload)),1),y("span",Oe,x(d(Be)(l.size)),1)],64)),y("div",Ae,[p.isPrivate?m("",!0):he((c(),$(n,{key:0,onClick:ee(v=>re(l),["stop"])},{default:g(()=>[f(d(_e))]),_:2},1032,["onClick"])),[[be,l.url]]),I.value?m("",!0):(c(),$(n,{key:1,onClick:ee(v=>K(i),["stop"])},{default:g(()=>[f(d(ke))]),_:2},1032,["onClick"]))]),l.progress>0&&l.progress<100&&!l.url?(c(),C("div",Ge,[f(G,{percentage:l.progress,"show-text":!1},null,8,["percentage"])])):m("",!0),l.error?(c(),C("div",Xe,x(l.error),1)):m("",!0)],2)],!0)]),_:2},1032,["accept","http-request","before-upload","headers","disabled","onClick"])):m("",!0)]),_:3},16,["modelValue"])):m("",!0)],2)],2),f(ze,{ref:d(z)("viewer")},null,512),p.isSpace?(c(),$(U,{key:0,ref:d(z)("space"),"show-btn":!1,accept:D.value,onConfirm:h.onConfirm},null,8,["accept","onConfirm"])):m("",!0)])}}}),Ze=Ie(je,[["__scopeId","data-v-223a0799"]]);export{Ze as default};
