<script lang="ts" name="pms-production-deduct-manhour" setup>
import { useCrud, useSearch, useTable, useUpsert } from "@cool-vue-p/crud";
import { useCool } from "/@/cool"
import { ref } from "vue"
import {number} from "echarts/core";
import {useDict} from "/$/dict";
import upsert from "../../../../../packages/crud/src/components/upsert";
import moment from "moment";
import { ElMessage, ElMessageBox } from 'element-plus'
import { downloadBlob } from '/@/cool/utils'
import * as XLSX from "xlsx"

// 导入文件
const fileInputRef = ref<HTMLInputElement | null>(null)
const isLoading = ref(false)
const { dict } = useDict()
const { service } = useCool()
const orderOption = ref<any[]>([])
const orderSearchList = ref<any[]>([])
const productOptions = ref<any[]>([])
const productList = ref<any[]>([])
// 获取生产订单列表
async function getOrder() {
  try {
    const res = await service.pms.production.schedule.request({
      url: '/list',
      method: 'POST',
    })
    orderOption.value = res
    orderSearchList.value = res?.map((e: any) => {
      return {
        value: e.id,
        label: e.sn,
      }
    })
  }
  catch (e: any) {
    console.error(e)
  }
}
getOrder()
const ProductionStagesOptions = [
  {
    label: '临时1（试产）',
    value: 1,
    name: '-',
    nameEn: '-',
    type: 'info',
  },
  {
    label: '临时2（首次量产）',
    value: 2,
    name: '-',
    nameEn: '-',
    type: 'warning',
  },
  {
    label: '正式（量产）',
    value: 3,
    name: '-',
    nameEn: '-',
    type: 'success',
  },
]

const WorkshopSectionOptions = [
  {
    label: '加工段',
    value: 1,
    name: '-',
    nameEn: '-',
    type: 'info',
  },
  {
    label: '组装段',
    value: 2,
    name: '-',
    nameEn: '-',
    type: 'info',
  },
  {
    label: '老化段',
    value: 3,
    name: '-',
    nameEn: '-',
    type: 'info',
  },
  {
    label: '包装段',
    value: 4,
    name: '-',
    nameEn: '-',
    type: 'info',
  },
  {
    label: '加工段一',
    value: 5,
    name: '-',
    nameEn: '-',
    type: 'info',
  },
  {
    label: '加工段二',
    value: 6,
    name: '-',
    nameEn: '-',
    type: 'info',
  },
  {
    label: '加工段三',
    value: 7,
    name: '-',
    nameEn: '-',
    type: 'info',
  },
  {
    label: '芯子配件加工段一',
    value: 8,
    name: '-',
    nameEn: '-',
    type: 'info',
  },
  {
    label: '芯子配件加工段二',
    value: 9,
    name: '-',
    nameEn: '-',
    type: 'info',
  },
  {
    label: '芯子配件组装段一',
    value: 10,
    name: '-',
    nameEn: '-',
    type: 'info',
  },
  {
    label: '芯子配件组装段二',
    value: 11,
    name: '-',
    nameEn: '-',
    type: 'info',
  },
  {
    label: '芯子配件组装段三',
    value: 12,
    name: '-',
    nameEn: '-',
    type: 'info',
  },
  {
    label: '芯子配件组装段四',
    value: 13,
    name: '-',
    nameEn: '-',
    type: 'info',
  },
  {
    label: '芯子配件组装段五',
    value: 14,
    name: '-',
    nameEn: '-',
    type: 'info',
  },
  {
    label: '芯子配件包装段',
    value: 15,
    name: '-',
    nameEn: '-',
    type: 'info',
  },
]
// cl-upsert 配置
const Upsert = useUpsert({
  props: {
    class: "abnormal-working-form",
    labelWidth: "120px"
  },
  items: [
    {
      label: '日期',
      prop: 'abnormal_date',
      required: true,
      component: {
        name: 'el-date-picker',
        props: {
          'type': 'date',
          'placeholder': '选择日期',
          'value-format': 'YYYY-MM-DD',
          'format': 'YYYY-MM-DD',
          'clearable': true,
          // 不能超过今天
          'disabledDate': (time: any) => {
            return time.getTime() > Date.now()
          },
        },
      },
    },
    {
      label: '生产订单',
      prop: 'order_id',
      required: true,
      component: {
        name: 'slot-order-select',
      },
    },
    {
      label: '机型',
      prop: 'product_id',
      required: true,
      component: {
        name: 'slot-product-select',
      },
    },
    {
      label: '订单数量(pcs)',
      prop: 'order_quantity',
      required: true,
      component: {
        name: 'slot-input-quantity',
      },
    },
    {
      label: '生产阶段',
      prop: 'production_stages',
      required: true,
      component: {
        name: 'el-select',
        props: {
          filterable: true,
        },
        options: ProductionStagesOptions,
      },
    },
    {
      label: '工段',
      prop: 'workshop_section',
      required: true,
      component: {
        name: 'el-select',
        props: {
          filterable: true,
        },
        options: WorkshopSectionOptions,
      },
    },
    {
      label: '人数(人)',
      prop: 'number_of_people',
      required: true,
      component: {
        name: "el-input-number",
        props: {
          type: 'number',
          placeholder: '请输入人数',
          step: 1,
          formatter: formatNumberInt,
          onChange(number_of_people: number) {
            let man_hour = parseFloat(Upsert.value?.getForm('man_hour'))
            if (man_hour && number_of_people) {
              let average_working_hours = man_hour / number_of_people
              Upsert.value?.setForm('average_working_hours', roundToTwoDecimals(average_working_hours))
            }
          },
        },
      },
    },
    {
      label: '生产数量(pcs)',
      prop: "quantity",
      required: true,
      component: {
        name: "el-input-number",
      },
    },
    {
      label: "累计工时(H)",
      prop: "man_hour",
      required: true,
      component: { name: "el-input-number", props: {
          onChange(man_hour: number) {
            let number_of_people = parseFloat(Upsert.value?.getForm('number_of_people'))
            let labor_cost = parseFloat(Upsert.value?.getForm('labor_cost'))
            if (man_hour && number_of_people) {
              let average_working_hours = man_hour / number_of_people
              Upsert.value?.setForm('average_working_hours', roundToTwoDecimals(average_working_hours))
            }
            if (labor_cost && man_hour) {
              let total_cost = man_hour * labor_cost
              Upsert.value?.setForm('total_cost', roundToTwoDecimals(total_cost))
            }
          },
        }
      },
    },
    {
      label: "人均工时(H)",
      prop: "average_working_hours",
      required: true,
      component: { name: "el-input-number", props: {
        disabled:  true,
      }
      },
    },
    {
      label: "人工费用(元/H)",
      prop: "labor_cost",
      required: true,
      component: {
        name: "el-input-number",props: {
          onChange(labor_cost: number) {
            let man_hour = parseFloat(Upsert.value?.getForm('man_hour'))
            if (labor_cost && man_hour) {
              let total_cost = man_hour * labor_cost
              Upsert.value?.setForm('total_cost', roundToTwoDecimals(total_cost))
            }
          },
        },
      },
    },
    {
      label: "累计费用(元)",
      prop: "total_cost",
      required: true,
      component: {
        name: "el-input-number", props: {
          disabled: true,
        },
      },
    },
    {
      label: "责任单位",
      prop: "accountability_unit",
      required: true,
      component: { name: "el-input" },
    },
    {
      label: "描述",
      prop: "description",
      required: true,
      component: { name: "el-input", props: {type:"textarea"} },
    },
    {
      label: "备注",
      prop: "re_mark",
      required: true,
      component: { name: "el-input", props: {type:"textarea"} },
    },
  ],
  async onOpen(data) {
    // console.log('data',data)
    // let id = Upsert.value?.getForm('order_id')
    // getProductList(data.order_id)
  },
  async onOpened() {
    // materialOption.value = materialList.value
  },
  async onClose(action: string, done: Function) {
    // materialOption.value = []
    done()
  },
  async onInfo(data, { done }) {
    getProductList(data.order_id)
    done(data)
  },
});

// cl-table 配置
const Table = useTable({
  columns: [
    { type: 'selection' },
    { label: "ID", prop: "id", width: 60 },
    { label: "订单号", prop: "order_id", width: 120,
      formatter: (row: any) => {
        return (
          orderOption.value.find(e => e.id === Number.parseInt(row.order_id))
            ?.sn || '-'
        )
      },
    },
    { label: "订单数量", prop: "order_quantity", width: 120 },
    { label: "异常日期", prop: "abnormal_date", width: 130,
      component: {
        name: 'cl-date-text',
        props: { format: 'YYYY-MM-DD' },
      },
    },
    { label: "产品名", prop: "product_id", width: 160,
      formatter: (row: any) => {
        return (
          productList.value.find(e => e.value === Number.parseInt(row.product_id))
            ?.label || '-'
        )
      },
    },
    { label: "工段", prop: "workshop_section", width: 160,showOverflowTooltip: true,
      formatter: (row: any) => {
        return (
          WorkshopSectionOptions.find(e => e.value === Number.parseInt(row.workshop_section))
            ?.label || '-'
        )
      },
    },
    { label: "生产阶段", prop: "production_stages", width: 160,showOverflowTooltip: true,
      formatter: (row: any) => {
        return (
          ProductionStagesOptions.find(e => e.value === Number.parseInt(row.production_stages))
            ?.label || '-'
        )
      },
    },
    { label: "sku", prop: "sku", width: 120,showOverflowTooltip: true},
    { label: "生产数量(PCS)", prop: "quantity", width: 120},
    { label: "责任单位", prop: "accountability_unit", width: 160,showOverflowTooltip: true},
    {
      label: '异常工时',
      align: 'center',
      children: [
        {label: "人数(人)", prop: "number_of_people", width: 120},
        {label: "人均工时(H)", prop: "average_working_hours", width: 120},
        {label: "累计工时(H)", prop: "man_hour", width: 120},
      ],
    },
    {
      label: '异常费用',
      align: 'center',
      children: [
        {label: "人工费用(元/H)", prop: "labor_cost", width: 120},
        {label: "累计费用(元)", prop: "total_cost", width: 120},
      ],
    },
    { label: "描述", prop: "description",showOverflowTooltip: true},
    { label: "备注", prop: "re_mark",showOverflowTooltip: true},
    { type: "op", buttons: ["edit", "delete"] },
  ],
});

// cl-crud 配置
const Crud = useCrud(
  {
    service: service.pms.AbnormalWorkingHours,
    async onRefresh(params, { next, render }) {
      // 1 默认调用
      const { list, pagination } = await next(params)

      render(list, pagination)
    },
  },
  (app) => {
    app.refresh({});
  },
)

const Search = useSearch({
  items: [
    {
      label: 'SKU',
      prop: 'keyWord',
      props: {
        labelWidth: '120px',
      },
      component: {
        name: 'el-input',
        props: {
          clearable: false,
          onChange(keyword: string) {
            Crud.value?.refresh({ keyWord: keyword.trim(), page: 1 })
          },
        },
      },
    },
    {
      label: '订单号',
      prop: 'order_id',
      props: { labelWidth: '80px' },
      component: {
        name: 'el-select',
        props: {
          style: 'width: 200px',
          clearable: true,
          filterable: true,
          onChange(order_id) {
            Crud.value?.refresh({ order_id, page: 1 })
          },
        },
        options: orderSearchList,
      },
    },
    {
      label: '下单时间',
      prop: 'dateRange',
      props: {
        labelWidth: '80px',
      },
      component: {
        name: 'el-date-picker',
        props: {
          'type': 'daterange',
          'value-format': 'YYYY-MM-DD',
          'format': 'YYYY-MM-DD',
          'rangeSeparator': '至',
          'startPlaceholder': '开始日期',
          'endPlaceholder': '结束日期',
          'clearable': true,
          onChange(dateRange) {
            Crud.value?.refresh({ dateRange, page: 1 })
          },
        },
      },
    },
  ],
})

// 获取产品列表
async function getAllProductList() {
  try {
    const res = await service.pms.product.request({
      url: '/getAllProduct',
      method: 'GET',
    })
    productList.value = res?.map((e: any) => {
      return {
        group_id: e.groupId,
        value: e.id,
        sku: e.sku,
        label: `${e.sku} ${e.name}`,
      }
    })
  }
  catch (e: any) {
    console.error(e)
  }
}
getAllProductList()

// 根据生产订单获取产品列表
async function getProductList(id: any) {
  try {
    productOptions.value = await service.pms.production.schedule.request({
      url: '/getProductListByOrderId',
      method: 'POST',
      data: { order_id: id },
    })
  }
  catch (e: any) {
    console.error(e)
  }
}

// function truncateToTwoDecimals(num) {
//   // 通过乘 100 → 取整 → 除 100 实现截断
//   return Math.trunc(num * 100) / 100;
// }

function roundToTwoDecimals(num) {
  return Math.round(num * 100) / 100; // 四舍五入后保持数值类型
}

// 获取订单产品数量
function getQuantity(id: number) {
  const order_id = Upsert.value?.getForm('order_id')
  service.pms.production.schedule
    .request({
      url: '/getOrderProductQuantity',
      method: 'POST',
      data: { order_id, product_id: id },
    })
    .then((res: any) => {
      Upsert.value?.setForm('order_quantity', res.quantity)
    })
    .catch((err: any) => {
      // 提示错误消息
      ElMessage.error(err.message || '查询失败')
    })
}
function getSku(id: number) {
  let sku = productOptions.value.find(item => item.id === id)?.sku
  Upsert.value?.setForm('sku', sku)
}

// 导出数据
function handleExport() {
  let keyWord = Search.value?.getForm('keyWord')
  let order_id = Search.value?.getForm('order_id')
  let dateRange = Search.value?.getForm('dateRange')
  let start = ''
  let end = ''
  if (dateRange && dateRange.length > 0) {
    start = moment(dateRange[0]).format('YYYY-MM-DD')
    end = moment(dateRange[1]).format('YYYY-MM-DD')
  }
  const params = {
    url: '/export',
    method: 'GET',
    responseType: 'blob',
    params: {
      start,
      end,
      keyWord,
      order_id,
    },
  }
  service.pms.AbnormalWorkingHours
    .request(params)
    .then((res: any) => {
      if (downloadBlob(res))
        ElMessage.success('导出成功')
      Crud.value?.refresh()
    })
    .catch((err: any) => {
      // 提示错误消息
      ElMessage.error(err.message || '导出失败')
    })
}

/**
 * 限定输入内容为整数
 */
function formatNumberInt(value: string) {
  const reg = /^\d+$/
  if (!reg.test(value))
    return value.slice(0, -1)
  return value
}

function openFileInput() {
  const fileInput = fileInputRef.value
  if (fileInput)
    fileInput.click()
}

function clearInput(fileInput: { value: string }) {
  // 清空文件输入的值
  if (fileInput)
    fileInput.value = ''
}

// 下载模板
function downloadExcelTemplate() {
  const fileName = '异常工时表_模板.xlsx'
  const filePath = '/abnormal_template.xlsx'

  // 发起下载请求
  fetch(filePath)
    .then(response => response.blob())
    .then((blob) => {
      // 保存文件
      downloadBlob(blob, fileName)
    })
    .catch(() => {
      ElMessage.error({
        message: '下载模板文件失败',
      })
    })
}

// 处理文件输入框的change事件
async function handleFileInputChange(event: Event) {
  const fileInput = event.target as HTMLInputElement
  const files = fileInput.files

  const WorkshopSectionMap: any = {}
  if (WorkshopSectionOptions && WorkshopSectionOptions.length > 0) {
    WorkshopSectionOptions.forEach((item: any) => {
      WorkshopSectionMap[item.label] = item.value
    })
  }

  const productionStagesMap: any = {}
  if (ProductionStagesOptions && ProductionStagesOptions.length > 0) {
    ProductionStagesOptions.forEach((item: any) => {
      productionStagesMap[item.label] = item.value
    })
  }

  const orderMap: any = {}
  if (orderOption.value && orderOption.value.length > 0) {
    orderOption.value.forEach((item: any) => {
      orderMap[item.sn] = item.id
    })
  }

  if (files && files.length > 0) {
    isLoading.value = true
    const file = files[0]
    const reader = new FileReader()

    reader.onload = (e: ProgressEvent<FileReader>) => {
      const data = new Uint8Array(e.target?.result as ArrayBuffer)
      const workbook = XLSX.read(data, { type: 'array' })

      // 这里可以根据需要处理读取到的Excel数据
      const worksheet = workbook.Sheets[workbook.SheetNames[0]]
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })
      // 定义非法值
      const illegalValue = [undefined, null, '', 'undefined', 'null', 'NaN']
      // 定义列
      const columns: string[] = ['order_id', 'abnormal_date', 'workshop_section', 'production_stages', 'sku', 'quantity', 'accountability_unit',
        'number_of_people','man_hour','labor_cost','description','re_mark']
      const result: any = []


      if (jsonData && jsonData.length > 0) {
        for (let i = 3; i < jsonData.length; i++) {
          const row = jsonData[i] as string[]
          if (row.length == 0) {
            clearInput(fileInput)
            isLoading.value = false
            continue
          }
          const cell: any = {}
          for (let j = 0; j < row.length; j++) {
            const columnName = columns[j]
            cell[columnName] = (row[j].toString() || '').trim()
            // 检查产品
            if (columnName === 'order_id') {
              const sn = cell.order_id
              cell.order_id = orderMap[cell.order_id] ? orderMap[cell.order_id] : 0
              if (cell.order_id === 0) {
                ElMessage.error(`无效的产品名${sn}`)
                clearInput(fileInput)
                isLoading.value = false
                return
              }
            }
            // 检查工序
            if (columnName === 'workshop_section') {
              const workshop_section_name = cell.workshop_section
              cell.workshop_section = WorkshopSectionMap[cell.workshop_section] ? WorkshopSectionMap[cell.workshop_section] : 0
              if (cell.workshop_section === 0) {
                ElMessage.error(`无效的工序名${workshop_section_name}`)
                clearInput(fileInput)
                isLoading.value = false
                return
              }
            }

            if (columnName === 'production_stages') {
              const production_stages_name = cell.production_stages
              cell.production_stages = productionStagesMap[cell.production_stages] ? productionStagesMap[cell.production_stages] : 0
              if (cell.production_stages === 0) {
                ElMessage.error(`无效的生产阶段名${production_stages_name}`)
                clearInput(fileInput)
                isLoading.value = false
                return
              }
            }

            cell.labor_cost = Number.parseFloat(cell.labor_cost)
            cell.man_hour = Number.parseFloat(cell.man_hour)
            cell.number_of_people = Number.parseInt(cell.number_of_people)
            cell.quantity = Number.parseInt(cell.quantity)
          }
          cell.product_id = productList.value.find(item => item.sku === cell.sku)?.value
          if (illegalValue.includes(cell.order_id)) {
            ElMessage.error(`订单号不能为空`)
            clearInput(fileInput)
            isLoading.value = false
            return
          }
          if (illegalValue.includes(cell.man_hour) || Number.isNaN(cell.man_hour) || cell.man_hour === 0) {
            clearInput(fileInput)
            isLoading.value = false
            break
          }
          if (illegalValue.includes(cell.number_of_people) || Number.isNaN(cell.number_of_people) || cell.number_of_people === 0) {
            clearInput(fileInput)
            isLoading.value = false
            break
          }

          if (illegalValue.includes(cell.labor_cost)) {
            clearInput(fileInput)
            isLoading.value = false
            break
          }

          if (illegalValue.includes(cell.quantity)) {
            clearInput(fileInput)
            isLoading.value = false
            break
          }

          if (illegalValue.includes(cell.abnormal_date)) {
            clearInput(fileInput)
            isLoading.value = false
            break
          }

          if (illegalValue.includes(cell.workshop_section) || Number.isNaN(cell.workshop_section) || cell.workshop_section === 0) {
            clearInput(fileInput)
            isLoading.value = false
            break
          }
          if (illegalValue.includes(cell.production_stages) || Number.isNaN(cell.production_stages) || cell.production_stages === 0) {
            clearInput(fileInput)
            isLoading.value = false
            break
          }
          if (illegalValue.includes(cell.sku) || cell.sku === '') {
            clearInput(fileInput)
            isLoading.value = false
            break
          }
          result.push(cell)
        }
        if (result.length > 0) {
          service.pms.AbnormalWorkingHours.importAbnormalData({ abnormal_working_hours: result }).then((res) => {
            Crud.value?.refresh()
            ElMessage.success(`导入成功：导入${res}条数据！`)
          }).catch((e: any) => {
            ElMessage.error(e.message || '导入失败')
          }).finally(() => {
            isLoading.value = false
          })
        }
        else {
          isLoading.value = false
          ElMessage.error('导入数据为空')
        }
        clearInput(fileInput)
      }
    }
    reader.readAsArrayBuffer(file)
  }
  else {
    isLoading.value = false
    ElMessage.error('请选择文件')
  }
}


</script>

<template>
  <cl-crud ref="Crud">
    <el-row>
      <!-- 刷新按钮 -->
      <cl-refresh-btn />
      <!-- 新增按钮 -->
      <cl-add-btn />
      <input ref="fileInputRef" type="file" style="display: none" accept=".xlsx, .xls" @change="handleFileInputChange">
      <el-button v-permission="service.pms.AbnormalWorkingHours.permission.importAbnormalData" size="default" :loading="isLoading" type="warning" class="mb-10px mr-10px" ml="20px" @click="openFileInput">
        Excel导入
      </el-button>
      <!-- 下载excel模板 -->
      <el-button v-permission="service.pms.AbnormalWorkingHours.permission.importAbnormalData" type="info" class="mb-10px mr-10px" size="default" @click="downloadExcelTemplate">
        下载Excel模板
      </el-button>
      <el-button type="success" @click="handleExport" v-permission="service.pms.AbnormalWorkingHours.permission.export">
        导出
      </el-button>
      <!-- 删除按钮 -->
      <cl-multi-delete-btn v-permission="service.pms.AbnormalWorkingHours.permission.delete"/>
      <cl-flex1 />
      <cl-search ref="Search" />
    </el-row>
    <el-row style="margin-top: 10px">
      <!-- 数据表格 -->
      <cl-table ref="Table"> </cl-table>
    </el-row>

    <el-row>
      <cl-flex1 />
      <!-- 分页控件 -->
      <cl-pagination />
    </el-row>
    <!-- 新增、编辑 -->
    <cl-upsert ref="Upsert">
      <template #slot-order-select="{ scope }">
        <el-select
          v-model="scope.order_id"
          filterable
          placeholder="选择生产订单"
          @change="(val: number) => {
            getProductList(val)
            scope.product_id = undefined
            scope.order_quantity = undefined
          }"
        >
          <el-option
            v-for="item in orderOption"
            :key="item.id"
            :label="item.sn"
            :value="item.id"
          />
        </el-select>
      </template>
        <template #slot-product-select="{ scope }">
          <el-select
            v-model="scope.product_id"
            filterable
            remote
            placeholder="选择产品"
            @change="(val: number) => {
              getSku(val)
              getQuantity(val)
          }"
          >
            <el-option
              v-for="item in productOptions"
              :key="item.id"
              :label="`${item.sku} ${item.name}`"
              :value="item.id"
            />
          </el-select>
      </template>
      <template #slot-input-quantity="{ scope }">
        <el-input
          v-model="scope.order_quantity"
          type="number"
          placeholder="请输入产品数量"
          :formatter="formatNumberInt"
          step="1"
          disabled
        />
      </template>
    </cl-upsert>
  </cl-crud>
</template>
<style lang="scss">
.abnormal-working-form {
  .cl-form__items {
    .el-row {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      .full-line {
        grid-column: 1 / -1;
      }
    }

    // 让input-number的提示语靠左显示
    .el-input-number {
      :deep(.el-input__wrapper) {
        padding-left: 11px;
        input {
          text-align: left;
        }
      }
    }
  }
}
</style>
