import{g as ye,i as P,e as X}from"./index-BtOcqcNl.js";import{c as ee,b as v,A as ve,K as ge,e as ke,q as R,w as C,h as c,y as we,G as j,i as f,H as xe,v as B,j as H,E as D,M as We,N as U,Z as Ee,o as G}from"./.pnpm-hVqhwuVC.js";import{u as Ne}from"./table-ops-mcGHjph4.js";import{a as Ce}from"./index-D95m1iJL.js";const De=ee({name:"pms-material-testing"}),Pe=ee({...De,setup(Ye){const{dict:V}=ye(),F=v([]),T=v([]),Y=v([]),{service:_}=Ce(),I=v([]);async function te(){try{const e=await _.pms.product.request({url:"/getAllProduct",method:"GET"});Y.value=e,T.value=e==null?void 0:e.map(t=>({group_id:t.groupId,value:t.id,sku:t.sku,label:`${t.sku} ${t.name}`}))}catch(e){console.error(e)}}const g=[{label:"加工段",value:1,name:"-",nameEn:"-",type:"info"},{label:"组装段",value:2,name:"-",nameEn:"-",type:"info"},{label:"老化段",value:3,name:"-",nameEn:"-",type:"info"},{label:"包装段",value:4,name:"-",nameEn:"-",type:"info"},{label:"加工段一",value:5,name:"-",nameEn:"-",type:"info"},{label:"加工段二",value:6,name:"-",nameEn:"-",type:"info"},{label:"加工段三",value:7,name:"-",nameEn:"-",type:"info"},{label:"芯子配件加工段一",value:8,name:"-",nameEn:"-",type:"info"},{label:"芯子配件加工段二",value:9,name:"-",nameEn:"-",type:"info"},{label:"芯子配件组装段一",value:10,name:"-",nameEn:"-",type:"info"},{label:"芯子配件组装段二",value:11,name:"-",nameEn:"-",type:"info"},{label:"芯子配件组装段三",value:12,name:"-",nameEn:"-",type:"info"},{label:"芯子配件组装段四",value:13,name:"-",nameEn:"-",type:"info"},{label:"芯子配件组装段五",value:14,name:"-",nameEn:"-",type:"info"},{label:"芯子配件包装段",value:15,name:"-",nameEn:"-",type:"info"}],k=[{label:"临时1（试产）",value:1,name:"-",nameEn:"-",type:"info"},{label:"临时2（首次量产）",value:2,name:"-",nameEn:"-",type:"warning"},{label:"正式（量产）",value:3,name:"-",nameEn:"-",type:"success"}],L=V.get("color"),w=V.get("product_floor"),h=P.useCrud({service:_.pms.daily_report_data},e=>{e.refresh()});async function oe(){try{const e=await _.pms.production.schedule.request({url:"/list",method:"POST"});F.value=e,I.value=e==null?void 0:e.map(t=>({value:t.id,label:t.sn}))}catch(e){console.error(e)}}ve(()=>{oe(),te()});const W=P.useSearch({items:[{label:"下单时间",prop:"dateRange",props:{labelWidth:"80px"},component:{name:"el-date-picker",props:{type:"daterange","value-format":"YYYY-MM-DD",format:"YYYY-MM-DD",rangeSeparator:"至",startPlaceholder:"开始日期",endPlaceholder:"结束日期",clearable:!0,onChange(e){var t;(t=h.value)==null||t.refresh({dateRange:e,page:1})}}}},{label:"工段",prop:"workshop_section",props:{labelWidth:"60px"},component:{name:"el-select",props:{style:"width: 150px",clearable:!0,filterable:!0,onChange(e){var t;(t=h.value)==null||t.refresh({workshop_section:e,page:1})}},options:g}},{label:"生产阶段",prop:"production_stages",props:{labelWidth:"80px"},component:{name:"el-select",props:{style:"width: 150px",clearable:!0,filterable:!0,onChange(e){var t;(t=h.value)==null||t.refresh({production_stages:e,page:1})}},options:k}},{label:"订单号",prop:"order_id",props:{labelWidth:"80px"},component:{name:"el-select",props:{style:"width: 150px",clearable:!0,filterable:!0,onChange(e){var t;(t=h.value)==null||t.refresh({order_id:e,page:1})}},options:I}},{label:"生产车间",prop:"workshop_id",props:{labelWidth:"80px"},component:{name:"el-select",props:{style:"width: 120px",clearable:!0,filterable:!0,onChange(e){var t;(t=h.value)==null||t.refresh({workshop_id:e,page:1})}},options:w}},{label:"sku/作业人员",prop:"keyWord",props:{labelWidth:"100px"},component:{name:"el-input",props:{style:"width: 160px",placeholder:"请输入sku或作业人员",clearable:!0,onChange(e){var t;(t=h.value)==null||t.refresh({keyWord:e.trim(),page:1})}}}}]}),z=v({edit:{width:80,permission:_.pms.material_test.permission.update,show:!0},delete:{width:80,permission:_.pms.material_test.permission.delete,show:!0}}),{getOpWidth:re,getOpIsHidden:ae}=Ne(z),le=v(re()),ne=v(ae()),se=P.useUpsert({props:{labelWidth:"120px",class:"production_daily_report_data-form"},items:[{label:"生产日期",prop:"produced_date",required:!0,component:{name:"el-date-picker",props:{type:"date",placeholder:"选择日期","value-format":"YYYY-MM-DD",format:"YYYY-MM-DD",clearable:!0,disabledDate:e=>e.getTime()>Date.now()}}},{label:"订单号",prop:"order_id",required:!0,component:{options:I,name:"el-select",props:{clearable:!0,filterable:!0}}},{label:"机型",prop:"product_id",required:!0,component:{options:T,name:"el-select",props:{clearable:!0,filterable:!0}}},{label:"生产车间",prop:"workshop_id",required:!0,component:{options:w,name:"el-select",props:{clearable:!0,filterable:!0}}},{label:"产线",prop:"production_line",required:!0,component:{name:"el-input"}},{label:"作业人员",prop:"busywork_group",required:!0,component:{name:"el-input"}},{label:"工段",prop:"workshop_section",required:!0,component:{options:g,name:"el-select",props:{clearable:!0,filterable:!0}}},{label:"生产阶段",prop:"production_stages",required:!0,component:{options:k,name:"el-select",props:{clearable:!0,filterable:!0}}},{label:"工时",prop:"man_hour",required:!0,component:{name:"el-input-number",props:{min:.01}}},{label:"当日产能",prop:"daily_output",required:!0,component:{name:"el-input-number",props:{min:.01}}},{label:"备注",prop:"remark",required:!1,component:{name:"el-input"}},{label:"人数",prop:"number_of_people",required:!0,component:{name:"el-input-number",props:{disabled:!0}}}],async onClose(e,t){t()}}),K=P.useTable({columns:[{type:"selection"},{label:"日期",prop:"produced_date",minWidth:80,align:"center",sortable:"desc",formatter(e){return e.produced_date?ge(e.produced_date).format("YYYY-MM-DD"):""}},{label:"订单号",prop:"order_id",minWidth:60,formatter(e){const t=F.value.find(o=>o.id===e.order_id);return t?t.sn:""}},{label:"订单数量(PCS)",prop:"quantity",minWidth:60},{label:"剩余生产数量(PCS)",prop:"except_quantity",minWidth:60},{label:"机型",prop:"product_id",minWidth:100,formatter(e){const t=Y.value.find(o=>o.id===e.product_id);return t?t.name:""}},{label:"SKU",prop:"sku",minWidth:100,formatter(e){const t=Y.value.find(o=>o.id===e.product_id);return t?t.sku:""}},{label:"颜色",prop:"color",minWidth:100,formatter:e=>{var o;if(!Y.value||!Array.isArray(Y.value))return"";const t=Y.value.find(n=>n.id===e.product_id);return!t||!L.value||!Array.isArray(L.value)?"-":((o=L.value.find(n=>n.value===Number.parseInt(t.color)))==null?void 0:o.label)||"-"}},{label:"生产车间",prop:"workshop_id",minWidth:60,formatter:e=>{var t;return!w.value||!Array.isArray(w.value)?"":((t=w.value.find(o=>o.id===e.workshop_id))==null?void 0:t.label)||""}},{label:"线别",prop:"production_line",minWidth:60},{label:"工位",prop:"station",minWidth:60},{label:"作业人员",prop:"busywork_group",minWidth:60},{label:"工段",prop:"workshop_section",minWidth:60,formatter:e=>{var t;return!g||!Array.isArray(g)?"":((t=g.find(o=>o.value===e.workshop_section))==null?void 0:t.label)||""}},{label:"生产阶段",prop:"production_stages",minWidth:60,formatter:e=>{var t;return!k||!Array.isArray(k)?"":((t=k.find(o=>o.value===e.production_stages))==null?void 0:t.label)||""}},{label:"实际生产数据",align:"center",children:[{label:"人数(人)",prop:"number_of_people",minWidth:60},{label:"合计人数(人)",prop:"total_number_of_people",minWidth:60},{label:"累计工时(H)",prop:"man_hour",minWidth:60},{label:"合计累计工时(H)",prop:"total_man_hour",minWidth:60},{label:"当日产能(pcs)",prop:"daily_output",minWidth:60},{label:"合计当日产能(pcs)",prop:"total_daily_output",minWidth:60},{label:"人均产能（pcs/h)",prop:"average_capacity",minWidth:60},{label:"备注",prop:"remark",minWidth:60}]},{type:"op",label:"操作",width:le,hidden:ne,buttons:Object.keys(z.value)}]});function ie(){var i,d,M,E,N,m;let e=(i=W.value)==null?void 0:i.getForm("keyWord"),t=(d=W.value)==null?void 0:d.getForm("order_id"),o=(M=W.value)==null?void 0:M.getForm("dateRange"),n=(E=W.value)==null?void 0:E.getForm("workshop_section"),s=(N=W.value)==null?void 0:N.getForm("production_stages"),l=(m=W.value)==null?void 0:m.getForm("workshop_id");const a={url:"/export",method:"GET",responseType:"blob",params:{workshop_section:n,production_stages:s,dateRange:o,keyWord:e,order_id:t,workshop_id:l}};_.pms.daily_report_data.request(a).then(u=>{var A;X(u)&&D.success("导出成功"),(A=h.value)==null||A.refresh()}).catch(u=>{D.error(u.message||"导出失败")})}function pe(){const e="生产日报数据_模板.xlsx";fetch("/daily_report_data.xlsx").then(o=>o.blob()).then(o=>{X(o,e)}).catch(()=>{D.error({message:"下载模板文件失败"})})}const $=v(null),p=v(!1);function ue(){const e=$.value;e&&e.click()}async function ce(e){const t=e.target,o=t.files,n={};g&&g.length>0&&g.forEach(a=>{n[a.label]=a.value});const s={};k&&k.length>0&&k.forEach(a=>{s[a.label]=a.value});const l={};if(w.value&&w.value.length>0&&w.value.forEach(a=>{l[a.label]=a.id}),o&&o.length>0){p.value=!0;const a=o[0],i=new FileReader;i.onload=d=>{var Z,J,Q;const M=new Uint8Array((Z=d.target)==null?void 0:Z.result),E=We(M,{type:"array"}),N=E.Sheets[E.SheetNames[0]];de(N);const m=U.sheet_to_json(N,{header:1,defval:""}),u=[void 0,null,"","undefined","null","NaN"],A=["","produced_date","order_id","name","sku","workshop_id","production_line","busywork_group","workshop_section","production_stages","station","daily_output","man_hour","remark"],O=[];if(m&&m.length>0){for(let x=3;x<m.length;x++){const S=m[x].slice(0,14),r={};for(let y=1;y<S.length;y++){const q=A[y];typeof S[y]=="string"&&(S[y]=S[y].trim()),r[q]=S[y],q==="produced_date"&&r.produced_date&&(r.produced_date=Ee(r.produced_date).format("YYYY-MM-DD")),q==="workshop_section"&&(r.workshop_section=n[r.workshop_section]?n[r.workshop_section]:0),q==="workshop_id"&&(r.workshop_id=l[r.workshop_id]?l[r.workshop_id]:0),q==="production_stages"&&(r.production_stages=s[r.production_stages]?s[r.production_stages]:0),!(q==="order_id"&&(r.order_id=(J=F.value.find(be=>be.sn.toLowerCase().includes(r.order_id.toLowerCase())))==null?void 0:J.id,r.order_id===void 0||r.order_id===0))&&(r.man_hour=Number.parseFloat(r.man_hour),r.daily_output=Number.parseFloat(r.daily_output),r.number_of_people=1)}if(r.product_id=(Q=T.value.find(y=>y.sku===r.sku))==null?void 0:Q.value,u.includes(r.daily_output)||Number.isNaN(r.daily_output)){b(t),p.value=!1;break}if(u.includes(r.man_hour)||Number.isNaN(r.man_hour)||r.man_hour===0){b(t),p.value=!1;break}if(u.includes(r.produced_date)){b(t),p.value=!1;break}if(u.includes(r.workshop_section)||Number.isNaN(r.workshop_section)||r.workshop_section===0){b(t),p.value=!1;break}if(u.includes(r.workshop_id)||Number.isNaN(r.workshop_id)||r.workshop_id===0){b(t),p.value=!1;break}if(u.includes(r.production_stages)||Number.isNaN(r.production_stages)||r.production_stages===0){b(t),p.value=!1;break}if(u.includes(r.sku)||r.sku===""){b(t),p.value=!1;break}if(u.includes(r.station)||r.station===""){b(t),p.value=!1;break}O.push(r)}O.length>0?_.pms.daily_report_data.importDailyReportData({daily_report_data:O}).then(()=>{var x;(x=h.value)==null||x.refresh(),D.success("导入成功")}).catch(x=>{D.error(x.message||"导入失败")}).finally(()=>{p.value=!1}):(p.value=!1,D.error("导入有效数据为空")),b(t)}},i.readAsArrayBuffer(a)}else p.value=!1,D.error("请选择文件")}function b(e){e&&(e.value="")}function de(e){(e["!merges"]||[]).forEach(o=>{var l;const n=U.encode_cell({r:o.s.r,c:o.s.c}),s=(l=e[n])==null?void 0:l.v;for(let a=o.s.r;a<=o.e.r;++a)for(let i=o.s.c;i<=o.e.c;++i){const d=U.encode_cell({r:a,c:i});e[d]||(e[d]={t:"s",v:s})}})}const me=["produced_date","order_id","quantity","except_quantity","product_id","sku","color","workshop_id","production_line","workshop_section","production_stages","total_number_of_people","total_man_hour","total_daily_output","average_capacity"];function _e(e){const t={};let o=0;for(;o<e.length;){const n=e[o].group_id;let s=1;for(let l=o+1;l<e.length&&e[l].group_id===n;l++)s++;s>1&&(t[o]=s),o+=s}return t}const fe=ke(()=>{var e;return _e(((e=K.value)==null?void 0:e.data)||[])});function he({row:e,column:t,rowIndex:o,columnIndex:n}){const s=t.property;if(!me.includes(s))return[1,1];const l=fe.value;if(l[o])return[l[o],1];for(const a in l){const i=Number(a),d=l[a];if(o>i&&o<i+d)return[0,0]}return[1,1]}return(e,t)=>{const o=f("cl-refresh-btn"),n=f("el-button"),s=f("cl-multi-delete-btn"),l=f("cl-flex1"),a=f("cl-search"),i=f("el-row"),d=f("cl-table"),M=f("cl-pagination"),E=f("cl-upsert"),N=f("cl-crud"),m=xe("permission");return G(),R(N,{ref_key:"Crud",ref:h},{default:C(()=>[c(i,null,{default:C(()=>[c(o),we("input",{ref_key:"fileInputRef",ref:$,type:"file",style:{display:"none"},accept:".xlsx, .xls",onChange:ce},null,544),j((G(),R(n,{size:"default",loading:p.value,type:"warning",class:"mb-10px mr-10px",ml:"20px",onClick:ue},{default:C(()=>[H(" Excel导入 ")]),_:1},8,["loading"])),[[m,B(_).pms.daily_report_data.permission.importDailyReportData]]),j((G(),R(n,{type:"info",class:"mb-10px mr-10px",size:"default",onClick:pe},{default:C(()=>[H(" 下载Excel模板 ")]),_:1})),[[m,B(_).pms.daily_report_data.permission.importDailyReportData]]),j(c(s,null,null,512),[[m,B(_).pms.daily_report_data.permission.delete]]),c(l),c(a,{ref_key:"Search",ref:W},null,512),c(n,{type:"success",onClick:ie,style:{"margin-left":"20px"}},{default:C(()=>[H(" 导出 ")]),_:1})]),_:1}),c(i,null,{default:C(()=>[c(d,{ref_key:"Table",ref:K,"auto-height":!1,"row-key":"id","span-method":he},null,512)]),_:1}),c(i,null,{default:C(()=>[c(l),c(M)]),_:1}),c(E,{ref_key:"Upsert",ref:se},null,512)]),_:1},512)}}});export{Pe as default};
