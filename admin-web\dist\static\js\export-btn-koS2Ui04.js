import{i as x}from"./index-DkYL1aws.js";import{N as g,aF as p,aG as w,aH as b,c as S,b as _,h as y,i as E,j as A,x as F,E as D}from"./.pnpm-hVqhwuVC.js";function $(e,d){var a=Date.parse(e);return(a-new Date(Date.UTC(1899,11,30)))/(24*60*60*1e3)}function B(e,d){for(var a={},n={s:{c:1e7,r:1e7},e:{c:0,r:0}},s=0;s!=e.length;++s)for(var h=0;h!=e[s].length;++h){n.s.r>s&&(n.s.r=s),n.s.c>h&&(n.s.c=h),n.e.r<s&&(n.e.r=s),n.e.c<h&&(n.e.c=h);var i={v:e[s][h]};if(i.v!=null){var f=g.encode_cell({c:h,r:s});typeof i.v=="number"?i.t="n":typeof i.v=="boolean"?i.t="b":i.v instanceof Date?(i.t="n",i.z=b._table[14],i.v=$(i.v)):i.t="s",a[f]=i}}return n.s.c<1e7&&(a["!ref"]=g.encode_range(n)),a}function v(){if(!(this instanceof v))return new v;this.SheetNames=[],this.Sheets={}}function N(e){for(var d=new ArrayBuffer(e.length),a=new Uint8Array(d),n=0;n!=e.length;++n)a[n]=e.charCodeAt(n)&255;return d}function k({multiHeader:e=[],header:d,data:a,filename:n,merges:s=[],autoWidth:h=!0,bookType:i="xlsx"}={}){n=n||"excel-list",a=[...a],a.unshift(d);for(let r=e.length-1;r>-1;r--)a.unshift(e[r]);var f="SheetJS",l=new v,o=B(a);if(s.length>0&&(o["!merges"]||(o["!merges"]=[]),s.forEach(r=>{o["!merges"].push(g.decode_range(r))})),h){const r=a.map(c=>c.map(m=>m==null?{wch:10}:m.toString().charCodeAt(0)>255?{wch:m.toString().length*2}:{wch:m.toString().length}));let t=r[0];for(let c=1;c<r.length;c++)for(let m=0;m<r[c].length;m++)t[m].wch<r[c][m].wch&&(t[m].wch=r[c][m].wch);o["!cols"]=t}l.SheetNames.push(f),l.Sheets[f]=o;var u=p(l,{bookType:i,bookSST:!1,type:"binary"});w.saveAs(new Blob([N(u)],{type:"application/octet-stream"}),`${n}.${i}`)}function T(){const e=new Date;return{year:e.getFullYear(),month:e.getMonth()+1,day:e.getDate(),hour:e.getHours(),minu:e.getMinutes(),sec:e.getSeconds()}}const C=S({name:"ClExportBtn",props:{filename:[Function,String],autoWidth:{type:Boolean,default:!0},bookType:{type:String,default:"xlsx"},header:Array,columns:{type:Array,required:!0},data:[Function,Array],maxExportLimit:Number},setup(e){const d=_(!1),a=x.useCrud();async function n(f,l){return f.filter(o=>!o.hidden&&l.includes(o.prop)).map(o=>o.label)}function s(){var f,l;return typeof e.data=="function"?e.data():e.data?e.data:(l=a.value)==null?void 0:l.service.page({...(f=a.value)==null?void 0:f.paramsReplace(a.value.params),maxExportLimit:e.maxExportLimit,isExport:!0}).then(o=>o.list.map(u=>{for(const r in u){const t=e.columns.find(c=>c.prop==r);if(t&&(t.formatter&&(u[r]=t.formatter(u)),t.dict)){const c=(F(t.dict)?t.dict.value:t.dict).find(m=>m.value==u[r]);c&&(u[r]=c.label)}}return u})).catch(o=>(D.error(o.message),null))}async function h(){if(typeof e.filename=="function")return await(e==null?void 0:e.filename());{const{year:f,month:l,day:o,hour:u,minu:r,sec:t}=T();return e.filename||`报表（${f}-${l}-${o} ${u}_${r}_${t}）`}}async function i(){if(!e.columns)return console.error("columns is required");d.value=!0;const f=e.columns.filter(t=>!(t.hidden===!0||["selection","expand","index"].includes(t.type)||t.filterExport||t["filter-export"])),l=f.map(t=>t.prop).filter(Boolean),o=await n(f,l);let u=await s();if(!u)return d.value=!1,console.error("导出数据异常");const r=await h();u=u.map(t=>l.map(c=>t[c])),k({header:o,data:u,filename:r,autoWidth:e.autoWidth,bookType:e.bookType}),d.value=!1}return()=>y(E("el-button"),{loading:d.value,onClick:i},{default:()=>[y("slot",null,[A("导出")])]})}});export{C as default};
