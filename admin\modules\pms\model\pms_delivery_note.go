package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/imhuso/lookah-erp/admin/yc"
)

const TableNameDeliveryNote = "pms_delivery_note"

type PmsDeliveryNoteOutput struct {
	ID              int64                           `json:"id"       gorm:"column:id;type:bigint(20);not null;primary_key;auto_increment;comment:ID;"` // ID
	No              string                          `json:"no" gorm:"column:no;type:varchar(100);not null;default:'';comment:入库单号;"`
	Po              string                          `json:"po" gorm:"column:po;type:varchar(500);not null;default:'';comment:po号;"`
	SupplierOrderNo string                          `json:"supplier_order_no" gorm:"column:supplier_order_no;type:varchar(100);not null;default:'';comment:供应商单号;"`
	CompleteTime    *gtime.Time                     `json:"completeTime" gorm:"column:complete_time;type:datetime;comment:入库完成时间;"`
	InboundTime     *gtime.Time                     `json:"inboundTime" gorm:"column:inbound_time;type:date;index;comment:入库时间;"`
	Status          int                             `json:"status" gorm:"column:status;type:int(11);not null;default:0;comment:状态;"`
	InboundType     int                             `json:"inboundType" gorm:"column:inbound_type;type:int(10);not null;default:0;comment:入库类型;"` // 1-采购入库，9-补退货
	Remark          string                          `json:"remark" gorm:"column:remark;type:varchar(500);not null;default:'';comment:备注;"`
	Voucher         string                          `json:"voucher"      gorm:"column:voucher;type:varchar(1000);not null;default:'';comment:凭证;"`                     // 凭证
	InternalOrderNo string                          `json:"internalOrderNo"      gorm:"column:internal_order_no;type:varchar(100);not null;default:'';comment:内部订单号;"` // 内部订单号
	OrderId         int64                           `json:"orderId" gorm:"column:order_id;type:bigint(20);not null;default:0;comment:采购单订单ID;"`
	SupplierId      int64                           `json:"supplier_id" gorm:"column:supplier_id;type:bigint(20);not null;default:0;comment:供应商id;"`
	SupplierName    string                          `json:"supplier_name"      gorm:"column:supplier_name;type:varchar(1000);not null;default:'';comment:供应商名称;"`
	OrderNo         string                          `json:"orderNo" gorm:"column:order_no;type:bigint(20);not null;default:0;comment:采购单订单号;"`
	TotalQuantity   float64                         `json:"totalQuantity" gorm:"column:total_quantity;type:decimal(14,4);not null;default:0.0000;comment:总数量;"`
	CreateTime      *gtime.Time                     `gorm:"column:createTime;not null;index,priority:1;autoCreateTime;comment:创建时间" json:"createTime"` // 创建时间
	DeleteTime      *gtime.Time                     `gorm:"column:deleteTime;index;comment:删除时间" json:"-"`                                             // 删除时间
	Products        []*PmsDeliveryNoteProductOutput `json:"products" orm:"with:inbound_id=id" gorm:"-"`                                                // 物料列表
}

// PmsDeliveryNote mapped from table <pms_delivery_note>
type PmsDeliveryNote struct {
	ID              int64       `json:"id"       gorm:"column:id;type:bigint(20);not null;primary_key;auto_increment;comment:ID;"` // ID
	No              string      `json:"no" gorm:"column:no;type:varchar(100);not null;default:'';comment:入库单号;"`
	Po              string      `json:"po" gorm:"column:po;type:varchar(500);not null;default:'';comment:po号;"`
	SupplierOrderNo string      `json:"supplier_order_no" gorm:"column:supplier_order_no;type:varchar(100);not null;default:'';comment:供应商单号;"`
	CompleteTime    *gtime.Time `json:"completeTime" gorm:"column:complete_time;type:datetime;comment:入库完成时间;"`
	InboundTime     *gtime.Time `json:"inboundTime" gorm:"column:inbound_time;type:date;index;comment:入库时间;"`
	Status          int         `json:"status" gorm:"column:status;type:int(11);not null;default:0;comment:状态;"`
	InboundType     int         `json:"inboundType" gorm:"column:inbound_type;type:int(10);not null;default:0;comment:入库类型;"` // 1-采购入库，9-补退货
	Remark          string      `json:"remark" gorm:"column:remark;type:varchar(500);not null;default:'';comment:备注;"`
	Voucher         string      `json:"voucher"      gorm:"column:voucher;type:varchar(1000);not null;default:'';comment:凭证;"`                     // 凭证
	InternalOrderNo string      `json:"internalOrderNo"      gorm:"column:internal_order_no;type:varchar(100);not null;default:'';comment:内部订单号;"` // 内部订单号
	SupplierId      int64       `json:"supplier_id" gorm:"column:supplier_id;type:bigint(20);not null;default:0;comment:供应商id;"`
	OrderId         int64       `json:"orderId" gorm:"column:order_id;type:bigint(20);not null;default:0;comment:采购单订单ID;"`
	TotalQuantity   float64     `json:"totalQuantity" gorm:"column:total_quantity;type:decimal(14,4);not null;default:0.0000;comment:总数量;"`
	CreateTime      *gtime.Time `gorm:"column:createTime;not null;index,priority:1;autoCreateTime;comment:创建时间" json:"createTime"` // 创建时间
	DeleteTime      *gtime.Time `gorm:"column:deleteTime;index;comment:删除时间" json:"-"`                                             // 删除时间
}

// GroupName 返回分组名
func (m *PmsDeliveryNote) GroupName() string {
	return ""
}

// TableName PmsDeliveryNote's table name
func (*PmsDeliveryNote) TableName() string {
	return TableNameDeliveryNote
}

// NewDeliveryNote 创建一个新的PmsDeliveryNote
func NewDeliveryNote() *PmsDeliveryNote {
	return &PmsDeliveryNote{}
}

// init 创建表
func init() {
	_ = yc.CreateTable(NewDeliveryNote())
}
