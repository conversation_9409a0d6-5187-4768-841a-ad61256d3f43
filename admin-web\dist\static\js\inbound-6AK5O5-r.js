import{c as pe,b as s,Z as n,z as Re,E as _,h as l,i as c,ac as ce,A as ie,j as p,q as K,w as x,y as de,G as L,H as fe,v as me,t as He,W as Ke,ad as Le,o as U}from"./.pnpm-hVqhwuVC.js";import{g as Ue,s as V,j as ve,e as Ye}from"./index-DkYL1aws.js";/* empty css              */import{_ as Ge}from"./_plugin-vue_export-helper-DlAUqK2U.js";const Ne={class:"production-summary"},We={class:"production-summary-title"},ze=pe({name:"undefined"}),je=pe({...ze,setup(Pe){const{dict:he}=Ue(),G=he.get("color"),f=s("month"),y=s(n().format("YYYY-MM")),A=s(y),N=s([]),w=s([]),D=s([]),B=s(""),R=s([]),W=s([]),z=s([]),H=s(!0),v=s([]),j=s([]),M=s(["inbound","outbound"]),E=s(!1),q=s(!1),S=s(!1),h=s([]),P=s([]),Z=s([]);X(),J(),I(),Re(M,(a,e)=>{if(a.length===0){const t=e[0]==="inbound"?"outbound":"inbound";M.value=[t]}});async function I(){if(!A.value)return;const a=n(A.value).year(),e=f.value==="year"?null:n(A.value).month()+1;V.pms.product.inboundSummary({year:a,month:e}).then(async t=>{N.value=t,X(),J(),_e(),H.value=!1}).catch(t=>{_.error((t==null?void 0:t.message)||"读取数据失败，请重试!"),H.value=!1})}function J(){const a=[{width:50,type:"expand",key:"id",align:"center"},{title:"产品名称",width:130,key:"name",dataKey:"name",align:"center",cellRenderer:e=>l(c("el-tooltip"),{class:"box-item",effect:"dark",content:e.cellData||"-"},{default:()=>[l("div",{class:e.class},[e.cellData||"-"])]})},{title:"SKU",width:130,key:"sku",dataKey:"sku",align:"center",headerCellRenderer:e=>xe(e)},{title:"颜色",width:60,key:"color",dataKey:"color",align:"center"},{title:"合计",width:120,key:"total",dataKey:"total",align:"center",headerCellRenderer:e=>Q(e),cellRenderer:e=>{var k,g,m,O,F,$;const t=((k=e.rowData)==null?void 0:k["total-inbound"])||0,r=((g=e.rowData)==null?void 0:g["total-outbound"])||0,u="total",o=Z.value.find(b=>b[u]),i=(m=o==null?void 0:o[u])==null?void 0:m.inbound,Y=(O=o==null?void 0:o[u])==null?void 0:O.outbound,d=(F=M.value)==null?void 0:F.includes("inbound"),C=($=M.value)==null?void 0:$.includes("outbound");return t===0&&r===0?l("div",{class:e.class},null):l("div",{class:`${e.class} data-inbound-outbound`},[l("p",{class:"date-inbound",style:{display:d&&!i?"block":"none"}},[p("入："),t]),l("p",{class:"date-outbound",style:{display:C&&!Y?"block":"none"}},[p("出："),r])])}}];R.value=a.concat(z.value),W.value=R.value.map((e,t)=>{let r;return window.innerWidth<600?r=void 0:t<4&&(r=Le.LEFT),{...e,fixed:r,width:e.width}})}function be(a,e){const t=n(e,"YYYY-MM"),r=[];if(a==="year")for(let u=0;u<12;u++)r.push({year:t.year(),month:u+1,day:0});else if(a==="month"){const u=t.daysInMonth();for(let o=1;o<=u;o++)r.push({year:t.year(),month:t.month()+1,day:o})}return r}function T(){const a=[];D.value=w.value,v.value=v.value.filter(e=>{const{key:t}=e;return R.value.some(r=>r.key===t)}),v.value.length===0&&!E.value?D.value=w.value:(D.value.forEach(e=>{h.value.length>0&&!h.value.includes(e.sku)||v.value.every(t=>{const{key:r,filterValue:u}=t,o=e[r];return!u.includes(o)})&&a.push(e)}),D.value=a)}function ye(a,e,t){const r=[void 0,0],u=e.key;a?v.value.push({key:u,filterValue:r}):v.value=v.value.filter(o=>o.key!==u),T()}function Q(a){const e=s(!1);return e.value=v.value.some(t=>t.key===a.column.key),l("div",{class:`header-cell-with-filter ${e.value?"header-cell-fileted":""}`},[l("div",{class:"header-cell-filter"},[l("p",{class:"header-cell-with-filter-title"},[a.column.title]),l(c("el-popover"),{placement:"bottom",trigger:"click",align:"center"},{default:()=>[l("div",{class:"date-header-cell-filter-popover"},[l(c("el-checkbox"),{modelValue:e.value,"onUpdate:modelValue":t=>e.value=t,label:"过滤空白和0",name:"filter",onChange:t=>ye(t,a.column)},null)])],reference:()=>l(c("el-icon"),{class:"header-cell-with-filter-icon"},{default:()=>[l(ce,null,null)]})})])])}function De(){S.value?h.value=w.value.map(a=>a.sku):h.value=[]}ie(()=>{P.value=w.value.map(a=>{var e,t;return{value:(e=a.sku)==null?void 0:e.trim(),label:(t=a.sku)==null?void 0:t.trim()}}),S.value&&(h.value=w.value.map(a=>a.sku)),E.value=h.value.length>0});function Me(){q.value=!1,T()}function ke(){q.value=!1}function ge(){q.value=!0}function xe(a){return l("div",{class:`header-cell-with-filter ${E.value?"header-cell-fileted":""}`},[l("div",{class:"header-cell-filter"},[l("p",{class:"header-cell-with-filter-title"},[a.column.title]),l(c("el-popover"),{align:"center",placement:"bottom",width:"230",visible:q.value},{default:()=>[l("div",{class:"date-header-cell-filter-popover sku-filter"},[l(c("el-select"),{placeholder:"请筛选SKU",modelValue:h.value,"onUpdate:modelValue":e=>h.value=e,teleported:!1,multiple:!0,clearable:!0,filterable:!0,style:"width: 200px;","collapse-tags":!0},{default:()=>[l(c("el-checkbox"),{style:"display: flex; justify-content: flex-end; margin-right: 10px;",modelValue:S.value,"onUpdate:modelValue":e=>S.value=e,onChange:De},{default:()=>[S.value?"反选":"全选"]}),P.value.map(e=>l(c("el-option"),{key:e.value,label:e.label,value:e.value},null))]})]),l("div",{class:"sku-filter-actions",style:"margin-top: 10px; display: flex; justify-content: flex-end"},[l(c("el-button"),{onClick:ke},{default:()=>[p("取消")]}),l(c("el-button"),{type:"primary",onClick:Me},{default:()=>[p("确定")]})])],reference:()=>l(c("el-icon"),{class:"header-cell-with-filter-icon",onClick:ge},{default:()=>[l(ce,null,null)]})})])])}function X(){j.value=be(f.value,y.value);const a=[];return j.value.forEach(e=>{const t=e.year,r=e.month<10?`0${e.month}`:e.month,u=e.day<10?`0${e.day}`:e.day,o=f.value==="year"?`date-${t}${r}`:`date-${t}${r}${u}`,i=f.value==="year"?`${e.month}月`:`${e.month}月${e.day}日`;if(f.value==="month"&&n(`${t}-${r}`).isSame(n(),"month")){if(e.day>n().date())return}else if(f.value==="year"&&t===n().year()&&e.month>n().month()+1)return;const Y={title:i,key:o,dataKey:o,width:100,align:"center",headerClass:"date-header-cell",headerCellRenderer:d=>Q(d),cellRenderer:d=>{var le,ne,oe,re,ue,se;const C=((le=d.rowData)==null?void 0:le[`${o}-inbound`])||0,k=((ne=d.rowData)==null?void 0:ne[`${o}-outbound`])||0,g=o.replace("date-",""),m=Z.value.find(Be=>Be[g]),O=(oe=m==null?void 0:m[g])==null?void 0:oe.inbound,F=(re=m==null?void 0:m[g])==null?void 0:re.outbound,$=(ue=M.value)==null?void 0:ue.includes("inbound"),b=(se=M.value)==null?void 0:se.includes("outbound");return C===0&&k===0?l("div",{class:d.class},null):l("div",{class:`${d.class} data-inbound-outbound`},[l("p",{class:"date-inbound",style:{display:$&&!O?"block":"none"}},[p("入："),C]),l("p",{class:"date-outbound",style:{display:b&&!F?"block":"none"}},[p("出："),k])])}};a.push(Y)}),z.value=a,a}function _e(){const a=N.value;D.value=[];let e=0;a.forEach(t=>{var o;const r=ve(G.value,Number.parseInt(t.color)).split("/")[0];r?t.color=r:t.color="-",(o=t.children)==null||o.forEach(i=>{const Y=ve(G.value,Number.parseInt(i.color)).split("/")[0];Y?i.color=Y:i.color="-"});const u={...t,index:e};e++,D.value.push(u)}),w.value=D.value,T()}function we({column:a,columnIndex:e}){const t=`hovering-col-${e}`;return{"data-key":t,onMouseenter:()=>{B.value=t},onMouseleave:()=>{B.value=""},onContextmenu:()=>{a.key.startsWith("date-")}}}function Ce(){return{}}function Oe(){I()}function Fe(){Oe()}function $e(){v.value=[],h.value=[],E.value=!1,T()}const ee=s();function Ee(){var a;(a=ee.value)==null||a.open({title:"成品出入库记录导出",width:"500px",dialog:{controls:["close"]},props:{labelWidth:"120px"},items:[{label:"请选择时间周期",prop:"range",required:!0,component:{name:"el-date-picker",props:{type:"daterange",placeholder:"请选择时间周期",shortcuts:[{text:"上月",value:()=>{const e=n().subtract(1,"month").startOf("month").format("YYYY-MM-DD"),t=n().subtract(1,"month").endOf("month").format("YYYY-MM-DD");return[e,t]}},{text:"本月",value:()=>{const e=n().startOf("month").format("YYYY-MM-DD"),t=n().endOf("month").format("YYYY-MM-DD");return[e,t]}},{text:"上季度",value:()=>{const e=n().subtract(1,"quarter").startOf("quarter").format("YYYY-MM-DD"),t=n().subtract(1,"quarter").endOf("quarter").format("YYYY-MM-DD");return[e,t]}},{text:"本季度",value:()=>{const e=n().startOf("quarter").format("YYYY-MM-DD"),t=n().endOf("quarter").format("YYYY-MM-DD");return[e,t]}},{text:"去年",value:()=>{const e=n().subtract(1,"year").startOf("year").format("YYYY-MM-DD"),t=n().subtract(1,"year").endOf("year").format("YYYY-MM-DD");return[e,t]}},{text:"本年",value:()=>{const e=n().startOf("year").format("YYYY-MM-DD"),t=n().endOf("year").format("YYYY-MM-DD");return[e,t]}}],clearable:!0,format:"YYYY-MM-DD","value-format":"YYYY-MM-DD","disabled-date":e=>e.getTime()>Date.now()}}}],on:{submit:async(e,{done:t,close:r})=>{const[u,o]=e.range;if(!u||!o){_.error("请选择时间周期");return}Se(u,o).then(i=>{const d=`成品出入库记录-${n().format("YY-MM-DD")+Math.floor(Math.random()*1e3)}.xlsx`;Ye(i,d)&&_.success("导出成功"),r()}).catch(i=>{_.error(i.message||"导出失败")}).finally(()=>{t()})}}})}async function Se(a,e){const t={url:"/summaryExport",method:"GET",responseType:"blob",params:{start:a,end:e}};return await V.pms.product.request(t)}const te=s();function Ve(){var a;(a=te.value)==null||a.open({title:"成品出入库明细导出",width:"500px",dialog:{controls:["close"]},props:{labelWidth:"120px"},items:[{label:"请选择时间周期",prop:"range",required:!0,component:{name:"el-date-picker",props:{type:"daterange",placeholder:"请选择时间周期",shortcuts:[{text:"上月",value:()=>{const e=n().subtract(1,"month").startOf("month").format("YYYY-MM-DD"),t=n().subtract(1,"month").endOf("month").format("YYYY-MM-DD");return[e,t]}},{text:"本月",value:()=>{const e=n().startOf("month").format("YYYY-MM-DD"),t=n().endOf("month").format("YYYY-MM-DD");return[e,t]}},{text:"上季度",value:()=>{const e=n().subtract(1,"quarter").startOf("quarter").format("YYYY-MM-DD"),t=n().subtract(1,"quarter").endOf("quarter").format("YYYY-MM-DD");return[e,t]}},{text:"本季度",value:()=>{const e=n().startOf("quarter").format("YYYY-MM-DD"),t=n().endOf("quarter").format("YYYY-MM-DD");return[e,t]}},{text:"去年",value:()=>{const e=n().subtract(1,"year").startOf("year").format("YYYY-MM-DD"),t=n().subtract(1,"year").endOf("year").format("YYYY-MM-DD");return[e,t]}},{text:"本年",value:()=>{const e=n().startOf("year").format("YYYY-MM-DD"),t=n().endOf("year").format("YYYY-MM-DD");return[e,t]}}],clearable:!0,format:"YYYY-MM-DD","value-format":"YYYY-MM-DD","disabled-date":e=>e.getTime()>Date.now()}}}],on:{submit:async(e,{done:t,close:r})=>{const[u,o]=e.range;if(!u||!o){_.error("请选择时间周期");return}qe(u,o).then(i=>{const d=`成品出入库明细-${n().format("YY-MM-DD")+Math.floor(Math.random()*1e3)}.xlsx`;Ye(i,d)&&_.success("导出成功"),r()}).catch(i=>{_.error(i.message||"导出失败")}).finally(()=>{t()})}}})}async function qe(a,e){const t={url:"/exportInboundOutboundDetails",method:"GET",responseType:"blob",params:{start:a,end:e}};return await V.pms.product.request(t)}function Ie(a){if(I(),a==="month"&&n(y.value,"YYYY-MM").month()===0){const e=n(y.value,"YYYY-MM").year();y.value=`${e}-01`}}function Te(){I()}function Ae(a){return a.getTime()>Date.now()}const ae=s();return ie(()=>{const a=y.value,e=f.value==="year"?"YYYY年":"YYYY年MM月";ae.value=n(a,"YYYY-MM").format(e)}),(a,e)=>{const t=c("el-button"),r=c("el-radio-button"),u=c("el-radio-group"),o=c("el-date-picker"),i=c("el-checkbox-button"),Y=c("el-checkbox-group"),d=c("el-row"),C=c("el-table-v2"),k=c("cl-form"),g=c("el-auto-resizer"),m=fe("permission"),O=fe("loading");return U(),K(g,null,{default:x(({height:F,width:$})=>[de("div",Ne,[l(d,{class:"production-summary-header"},{default:x(()=>[l(t,{class:"refresh-button",onClick:Fe},{default:x(()=>[p(" 刷新 ")]),_:1}),l(u,{modelValue:f.value,"onUpdate:modelValue":e[0]||(e[0]=b=>f.value=b),label:"size control",style:{"margin-left":"10px"},onChange:Ie},{default:x(()=>[l(r,{value:"year",label:"年"}),l(r,{value:"month",label:"月"})]),_:1},8,["modelValue"]),l(o,{modelValue:y.value,"onUpdate:modelValue":e[1]||(e[1]=b=>y.value=b),type:f.value,placeholder:"选择日期",style:{width:"150px","margin-left":"10px"},format:f.value==="year"?"YYYY":"YYYY-MM","value-format":f.value==="year"?"YYYY":"YYYY-MM","disabled-date":Ae,clearable:!1,onChange:Te},null,8,["modelValue","type","format","value-format"]),l(Y,{modelValue:M.value,"onUpdate:modelValue":e[2]||(e[2]=b=>M.value=b),style:{"margin-left":"10px"}},{default:x(()=>[l(i,{key:"inbound",value:"inbound",label:"显示入库"}),l(i,{key:"outbound",value:"outbound",label:"显示出库"})]),_:1},8,["modelValue"]),l(t,{style:{"margin-left":"10px"},class:"clear-filter-button",type:"success",disabled:v.value.length===0&&!E.value,onClick:$e},{default:x(()=>[p(" 清除过滤 ")]),_:1},8,["disabled"]),L((U(),K(t,{style:{"margin-left":"10px"},class:"export-button",type:"warning",onClick:Ee},{default:x(()=>[p(" 导出 ")]),_:1})),[[m,me(V).pms.material.summaryExport]]),L((U(),K(t,{style:{"margin-left":"10px"},class:"export-button",type:"danger",onClick:Ve},{default:x(()=>[p(" 导出出入库明细 ")]),_:1})),[[m,me(V).pms.material.summaryExport]]),de("div",We,He(ae.value)+"出入库记录 ",1)]),_:1}),L(l(C,{columns:W.value,"row-key":"sku",data:D.value,width:$-10,height:F-60,class:Ke(`production-summary-table ${B.value}`),fixed:"","cell-props":we,"header-cell-props":Ce},null,8,["columns","data","width","height","class"]),[[O,H.value]])]),l(k,{ref_key:"InboundExportForm",ref:ee},null,512),l(k,{ref_key:"InboundDetailsForm",ref:te},null,512)]),_:1})}}}),et=Ge(je,[["__scopeId","data-v-1e6bd2f5"]]);export{et as default};
