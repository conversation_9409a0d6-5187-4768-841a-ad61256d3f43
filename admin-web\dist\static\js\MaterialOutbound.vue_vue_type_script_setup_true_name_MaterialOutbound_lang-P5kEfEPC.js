import{c as q,b as c,K as Y,q as w,w as p,h as n,i as u,y as g,j as x,v as s,f as B,F as E,s as P,L as j,E as z,o as f}from"./.pnpm-hVqhwuVC.js";import{s as F,i as W,e as $}from"./index-BtOcqcNl.js";import{OUTBOUND_TYPE as I}from"./constant-C2dsBPRR.js";const R={style:{"margin-right":"20px"}},A=q({name:"MaterialOutbound"}),X=q({...A,props:{api:{default:F.pms.finance}},setup(C){const k=C,v=c(!1),_=c(!1),l=c({date:[],keyWord:"",type:void 0,status:void 0,useStatus:!1,useType:!1}),t=W.useCrud({dict:{api:{page:"materialOutboundPage"}},service:k.api,async onRefresh(e,{next:a,done:r,render:d}){r(),V(e);const{list:m,pagination:y}=await a(e);d(m,y)}},e=>{e.refresh()});function V(e){if(l.value.date&&l.value.date.length>0){const a=Y(l.value.date[0]).format("YYYY-MM-DD"),r=Y(l.value.date[1]).format("YYYY-MM-DD");e.date=`${a},${r}`}return l.value.keyWord&&(e.keyWord=l.value.keyWord),l.value.type!==void 0?(e.type=l.value.type,e.useType=!0):e.useType=!1,l.value.status!==void 0?(e.status=l.value.status,e.useStatus=!0):e.useStatus=!1,e}const T=c(I),O=c([{label:"草稿",value:0,type:"info"},{label:"待审批",value:4,type:"primary"},{label:"已完成",value:3,type:"success"}]),D=W.useTable({columns:[{label:"出库单号",prop:"no",width:210},{label:"生产单号",prop:"sn",width:180},{label:"PO",prop:"po",width:180},{label:"产品名称",prop:"productName",width:180},{label:"物料编码",prop:"code",width:180},{label:"物料名称",prop:"materialName",width:180,showOverflowTooltip:!0},{label:"规格/型号",prop:"model",width:180,showOverflowTooltip:!0},{label:"单位",prop:"unit",width:80},{label:"出库数量",prop:"quantity",width:120},{label:"Bom用量",prop:"calcBomQuantity",width:120},{label:"采购数量",prop:"purchaseQuantity",width:120},{label:"出库类型",prop:"type",width:120,dict:T.value},{label:"状态",prop:"status",width:120,dict:O.value},{label:"创建时间",prop:"createTime",width:180}]});function i(){var e,a,r;try{(a=(e=t==null?void 0:t.value)==null?void 0:e.params)!=null&&a.page&&(t.value.params.page=1),v.value=!0,(r=t==null?void 0:t.value)==null||r.refresh()}catch(d){console.error(d)}finally{v.value=!1}}function b(){var e,a;l.value.keyWord="",l.value.date=[],(e=t==null?void 0:t.value)!=null&&e.params&&(t.value.params.page=1,t.value.params.size=20),(a=t==null?void 0:t.value)==null||a.refresh()}function M(e){e?i():b()}async function N(){_.value=!0;try{const e=V({page:1,size:1e5}),a=await k.api.request({url:"/materialOutboundExportExcel",method:"POST",responseType:"blob",params:e});$(a)&&z.success("导出成功")}catch(e){console.error(e)}finally{_.value=!1}}return(e,a)=>{const r=u("el-button"),d=u("cl-flex1"),m=u("el-option"),y=u("el-select"),U=u("el-date-picker"),L=u("el-input"),h=u("el-row"),Q=u("cl-table"),S=u("cl-pagination"),K=u("cl-crud");return f(),w(K,{ref_key:"Crud",ref:t},{default:p(()=>[n(h,null,{default:p(()=>[n(r,{onClick:b},{default:p(()=>[x(" 刷新 ")]),_:1}),n(r,{type:"success",icon:"Download",loading:s(_),onClick:N},{default:p(()=>[x(" 导出excel ")]),_:1},8,["loading"]),n(d),g("div",null,[n(y,{modelValue:s(l).status,"onUpdate:modelValue":a[0]||(a[0]=o=>s(l).status=o),placeholder:"请选择状态",clearable:"",class:"mr-20px w-200px",onChange:i},{default:p(()=>[(f(!0),B(E,null,P(s(O),o=>(f(),w(m,{key:o.value,label:o.label,value:o.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),g("div",null,[n(y,{modelValue:s(l).type,"onUpdate:modelValue":a[1]||(a[1]=o=>s(l).type=o),placeholder:"请选择类型",clearable:"",class:"mr-20px w-200px",onChange:i},{default:p(()=>[(f(!0),B(E,null,P(s(T),o=>(f(),w(m,{key:o.value,label:o.label,value:o.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),g("div",R,[n(U,{modelValue:s(l).date,"onUpdate:modelValue":a[2]||(a[2]=o=>s(l).date=o),type:"daterange",clearable:"","range-separator":"~","start-placeholder":"开始日期","end-placeholder":"结束日期",onChange:M},null,8,["modelValue"])]),n(L,{modelValue:s(l).keyWord,"onUpdate:modelValue":a[3]||(a[3]=o=>s(l).keyWord=o),placeholder:"请输入物料名称或编码或生产单号或者产品名称",style:{width:"500px"},clearable:"",onClear:b,onKeyup:j(i,["enter"])},null,8,["modelValue"]),n(r,{type:"primary",mx:"10px",loading:s(v),onClick:i},{default:p(()=>[x(" 搜索 ")]),_:1},8,["loading"])]),_:1}),n(h,null,{default:p(()=>[n(Q,{ref_key:"Table",ref:D,"row-key":"rowIndex"},null,512)]),_:1}),n(h,null,{default:p(()=>[n(d),n(S)]),_:1})]),_:1},512)}}});export{X as _};
