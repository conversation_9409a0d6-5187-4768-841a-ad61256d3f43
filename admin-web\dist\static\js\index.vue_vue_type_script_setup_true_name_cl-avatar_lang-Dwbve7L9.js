import{c as u,e as c,f as _,h as x,i as v,m as z,w as S,j as y,t as C,o as $}from"./.pnpm-hVqhwuVC.js";const b={class:"cl-avatar",flex:"~ itme-center"},F=u({name:"cl-avatar"}),M=u({...F,props:{src:String,size:{type:Number,default:36},title:String,showSystem:{type:Boolean,default:!0}},setup(m){const n=m,f=c(()=>!n.title&&!n.src&&n.showSystem?"系统":n.title),a=c(()=>{var e;let t=((e=f.value)==null?void 0:e.trim().toUpperCase())??"";return/^[\u4E00-\u9FA5]+$/.test(t??"")?t=t.slice(-2):t=t.slice(0,2),t}),p=c(()=>{var o;const t=n.size,e=((o=a.value)==null?void 0:o.length)??0,s=Math.min(t/(e*1.5),t/2);return Math.max(12,s*1)});function h(){const t=g(a.value??""),e=d(t);return{fontSize:`${p.value}px`,fontWeight:"bold",lineHeight:`${n.size}px`,backgroundColor:`rgb(${t.r}, ${t.g}, ${t.b})`,color:e}}function g(t){let e=0;for(let r=0;r<t.length;r++)e=t.charCodeAt(r)+((e<<5)-e);const s=50,l=Math.max(0,(e&255)-s),i=Math.max(0,(e>>8&255)-s),o=Math.max(0,(e>>16&255)-s);return{r:l,g:i,b:o}}function d(t){return(.299*t.r+.587*t.g+.114*t.b)/255>.5?"#33333":"#efefef"}return(t,e)=>{const s=v("el-avatar");return $(),_("div",b,[x(s,z({src:n.src,size:n.size,style:h()},t.$attrs),{default:S(()=>[y(C(a.value),1)]),_:1},16,["src","size","style"])])}}});export{M as _};
