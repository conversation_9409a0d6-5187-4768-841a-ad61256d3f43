import{e as _}from"./index-DkYL1aws.js";import{c as p,b as i,f as g,y as h,h as b,i as v,w as k,j as w,t as x,E as r,o as B}from"./.pnpm-hVqhwuVC.js";const C=p({name:"UploadBtn"}),P=p({...C,props:{api:{},label:{default:"选择文件"},url:{},download:{type:Boolean,default:!1},filePorpName:{default:"file"},params:{}},setup(f){const e=f,t=i(!1),c=i(null);function u(){const a=c.value;a&&a.click()}async function d(a){const l=a.target;try{t.value=!0;let o=null;l!=null&&l.files&&l.files.length>0&&(o=l.files[0]);const s=new FormData;if(s.append(e.filePorpName,o),e.params)for(const n in e.params)s.append(n,e.params[n]);const y={url:e.url.includes("/")?e.url:`/${e.url}`,method:"POST",responseType:"blob",data:s};e.api.request(y).then(n=>{e.download&&_(n)&&r.success("下载成功")}).catch(n=>{r.error(n.message||"上传失败"),console.error(n)})}catch(o){r.error(o.message||"上传失败"),console.error(o)}finally{t.value=!1,m(l)}}function m(a){a&&(a.value="")}return(a,l)=>{const o=v("el-button");return B(),g("div",null,[h("input",{ref_key:"fileInputRef",ref:c,type:"file",style:{display:"none"},accept:".xlsx, .xls",onChange:d},null,544),b(o,{loading:t.value,type:"success",onClick:u},{default:k(()=>[w(x(a.label),1)]),_:1},8,["loading"])])}}});export{P as _};
