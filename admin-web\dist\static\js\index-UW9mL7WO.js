import{c as X,b as f,e as C,z as Be,q as u,w as o,h as a,G as O,i as s,H as Ye,v as _,j as h,f as $,s as Ee,F as Ie,y,t as T,B as g,Y as k,E as b,T as B,o as r,af as Me,ag as Oe}from"./.pnpm-hVqhwuVC.js";import{g as $e,i as J,j as Re,r as R,e as qe}from"./index-DkYL1aws.js";import{u as Ne}from"./table-ops-CrFIfhgA.js";import{S as Ue}from"./schedule-plan-add-PWFA0p-_.js";/* empty css              */import{a as Ae}from"./index-C6cm1h61.js";import{V as Le}from"./index-CBanFtSc.js";import{_ as Qe}from"./_plugin-vue_export-helper-DlAUqK2U.js";const Z=V=>(Me("data-v-f52a14fe"),V=V(),Oe(),V),He={key:0,style:{color:"red"}},je={key:1},Ke={class:"table-summary"},Fe={class:"table-summary-container"},We=Z(()=>y("span",{class:"cl-table__expand-footer-title"},"数量总计：",-1)),ze=Z(()=>y("span",null,"#",-1)),Ge={class:"dialog-footer"},Je=X({name:"pms-production-schedule"}),Xe=X({...Je,setup(V){const{dict:ee}=$e(),{service:d}=Ae(),te=ee.get("color"),i=f(0),S=f(!1),oe=f([]),Y=f(!1),q=f(),N=f("1"),U=f(),x=f({id:0,expectedDeliveryTime:""}),A=f(),le=[{label:"销售订单",value:0,type:"success"},{label:"手动排产",value:1,type:"warning"}],L=f([{label:"草稿",value:0,type:"info",count:0},{label:"排产中",value:1,type:"warning",count:0},{label:"已完成",value:2,type:"success",count:0}]),Q=f({"slot-btn-confirm":{width:110,permission:d.pms.production.schedule.permission.confirm,show:C(()=>i.value===0)},"slot-btn-edit":{width:80,permission:d.pms.production.schedule.permission.update,show:C(()=>i.value===0)},"slot-btn-delete":{width:80,permission:d.pms.production.schedule.permission.delete,show:C(()=>i.value===0)},"slot-btn-add-plan":{width:100,permission:d.pms.production.schedule.plan.permission.add,show:C(()=>i.value===1)},"slot-btn-revoke":{width:110,permission:d.pms.production.schedule.permission.revoke,show:C(()=>i.value!==0)},"slot-btn-purchase-order":{width:110,permission:d.pms.production.purchase.order.permission.add&&d.pms.production.purchase.order.permission.getProductByProductionOrder,show:C(()=>i.value===1)}}),{getOpWidth:ae,checkOpButtonIsAvaliable:D,getOpIsHidden:ne}=Ne(Q),H=f(),j=f(!1);Be(i,()=>{H.value=ae(),j.value=ne()},{immediate:!0});const K=J.useTable({columns:[{label:"#",prop:"products",type:"expand"},{label:"生产单号",prop:"sn"},{label:"来源",prop:"source",dict:le},{label:"订单要求交货日期",prop:"requiredShipDate",component:{name:"cl-date-text",props:{format:"YYYY-MM-DD"}}},{label:"单品总数量",prop:"totalQuantity"},{label:"下单时间",prop:"createTime",component:{name:"cl-date-text",props:{format:"YYYY-MM-DD HH:mm:ss"}}},{label:"预计交货日期",prop:"expectedDeliveryTime",component:{name:"cl-date-text",props:{format:"YYYY-MM-DD"}}},{label:"实际交货日期",prop:"actualDeliveryTime"},{type:"op",label:"操作",width:H,hidden:j,buttons:Object.keys(Q.value)}]}),w=J.useCrud({service:d.pms.production.schedule,async onRefresh(l,{next:e,render:c}){const{count:p,list:P,pagination:E}=await e(l);L.value.forEach(v=>{v.count=p[v.value]||0}),P.forEach(v=>{if(!((v==null?void 0:v.schedulePlan)||[]).length)return!1}),c(P,E)}},l=>{l.refresh({status:i})});function se(l){var e;i.value=l,(e=w.value)==null||e.refresh()}function re(l,e){var c;(e==null?void 0:e.type)==="expand"||(e==null?void 0:e.type)==="op"||(c=K.value)==null||c.toggleRowExpansion(l)}function de(l,e){var c;(e==null?void 0:e.type)==="expand"||(e==null?void 0:e.type)==="op"||(c=U.value)==null||c.toggleRowExpansion(l)}function ce(l){x.value={id:l.id,expectedDeliveryTime:""},A.value=l,S.value=!0}async function F(){await d.pms.production.schedule.confirm(x.value).then(l=>{S.value=!1,b.success("排产确认成功"),i.value=(l==null?void 0:l.status)||0}).catch(l=>{b.error(l.message)})}async function ue(){if(!x.value.expectedDeliveryTime){b.error("请选择预计交货时间");return}const l=A.value.requiredShipDate;x.value.expectedDeliveryTime>l?B.confirm("预计交货日期超过了订单的要求出货日期，是否继续？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{F()}).catch(()=>{}):F()}function ie(l){q.value=l,Y.value=!0}function pe(l){R.push(`/pms/production/purchase/order/add?orderId=${l.id}`)}function me(){R.push("/pms/production/order/add")}function fe(){const l={url:"/exportOrder",method:"POST",responseType:"blob"};d.pms.production.schedule.request(l).then(e=>{var c;qe(e)&&b.success("导出成功"),(c=w.value)==null||c.refresh()}).catch(e=>{b.error(e.message||"导出失败")})}function he(){return"primary-row"}function _e(l){R.push(`/pms/production/order/add?id=${l}`)}function be(l){if(!l)return!1;B.confirm("确认删除生产计划吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{d.pms.production.schedule.delete({ids:[l]}).then(()=>{var e;b.success("生产计划删除成功"),(e=w.value)==null||e.refresh()}).catch(e=>{b.error(e.message)})}).catch(()=>{})}function ve(l){B.confirm("确认撤销排产吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{d.pms.production.schedule.revoke({id:l.id}).then(e=>{var c;i.value=(e==null?void 0:e.status)||0,(c=w.value)==null||c.refresh(),b.success("撤销排产成功")}).catch(e=>{b.error(e.message||"撤销排产失败")})}).catch(()=>{})}function ye(){var l;(l=w.value)==null||l.refresh()}function we(l){B.confirm("确认删除生产计划吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{d.pms.production.schedule.plan.delete({ids:[l.id]}).then(()=>{var e;b.success("生产计划删除成功"),(e=w.value)==null||e.refresh()}).catch(e=>{b.error(e.message)})}).catch(()=>{})}function ge(l,e){return e.isPurchase-l.isPurchase}return(l,e)=>{const c=s("cl-refresh-btn"),p=s("el-button"),P=s("cl-flex1"),E=s("cl-search-key"),v=s("el-row"),W=s("el-tab-pane"),m=s("el-table-column"),z=s("el-tag"),I=s("el-table"),G=s("el-collapse-item"),ke=s("cl-date-text"),xe=s("el-collapse"),De=s("cl-table"),Ce=s("cl-pagination"),Te=s("el-tabs"),Se=s("el-date-picker"),Pe=s("el-dialog"),Ve=s("cl-crud"),M=Ye("permission");return r(),u(Ve,{ref_key:"Crud",ref:w},{default:o(()=>[a(v,null,{default:o(()=>[a(c),O((r(),u(p,{type:"success",onClick:me},{default:o(()=>[h(" 新建生产订单 ")]),_:1})),[[M,_(d).pms.sale.order.permission.add]]),O((r(),u(p,{type:"primary",onClick:fe},{default:o(()=>[h(" 导出 ")]),_:1})),[[M,_(d).pms.sale.order.permission.export]]),a(P),a(E)]),_:1}),a(Te,{modelValue:i.value,"onUpdate:modelValue":e[1]||(e[1]=t=>i.value=t),type:"border-card",onTabChange:se},{default:o(()=>[(r(!0),$(Ie,null,Ee(L.value,t=>(r(),u(W,{key:t.value,label:`${t.label}(${t.count})`,name:t.value},null,8,["label","name"]))),128)),a(v,null,{default:o(()=>[a(De,{ref_key:"Table",ref:K,"row-key":"id",class:"table-row-pointer","expand-row-keys":oe.value,onRowClick:re},{"column-orderSn":o(({scope:t})=>[y("span",null,T(t.row.orderSn||"-"),1)]),"column-actualDeliveryTime":o(({scope:t})=>[t.row.actualDeliveryTime>t.row.expectedDeliveryTime?(r(),$("span",He,T(t.row.actualDeliveryTime||"-"),1)):(r(),$("span",je,T(t.row.actualDeliveryTime||"-"),1))]),"slot-btn-edit":o(({scope:t})=>[_(D)("slot-btn-edit")?(r(),u(p,{key:0,text:"",bg:"",type:"primary",onClick:k(n=>_e(t.row.id),["stop"])},{default:o(()=>[h(" 编辑 ")]),_:2},1032,["onClick"])):g("",!0)]),"slot-btn-delete":o(({scope:t})=>[_(D)("slot-btn-delete")?(r(),u(p,{key:0,text:"",bg:"",type:"danger",onClick:k(n=>be(t.row.id),["stop"])},{default:o(()=>[h(" 删除 ")]),_:2},1032,["onClick"])):g("",!0)]),"slot-btn-confirm":o(({scope:t})=>[_(D)("slot-btn-confirm")?(r(),u(p,{key:0,text:"",bg:"",type:"success",onClick:k(n=>ce(t.row),["stop"])},{default:o(()=>[h(" 确认排产 ")]),_:2},1032,["onClick"])):g("",!0)]),"slot-btn-add-plan":o(({scope:t})=>[_(D)("slot-btn-add-plan")?(r(),u(p,{key:0,text:"",bg:"",type:"success",onClick:k(n=>ie(t.row),["stop"])},{default:o(()=>[h(" 添加计划 ")]),_:2},1032,["onClick"])):g("",!0)]),"slot-btn-purchase-order":o(({scope:t})=>[_(D)("slot-btn-purchase-order")?(r(),u(p,{key:0,text:"",bg:"",type:"warning",disabled:!(t.row.referOrderId===0&&t.row.products.some(n=>!n.isPurchase)),onClick:k(n=>pe(t.row),["stop"])},{default:o(()=>[h(" 下采购单 ")]),_:2},1032,["disabled","onClick"])):g("",!0)]),"slot-btn-revoke":o(({scope:t})=>[_(Le)(t.row.createTime)&&_(D)("slot-btn-revoke")?(r(),u(p,{key:0,text:"",bg:"",type:"danger",onClick:k(n=>ve(t.row),["stop"])},{default:o(()=>[h(" 撤销排产 ")]),_:2},1032,["onClick"])):g("",!0)]),"column-products":o(({scope:t})=>[a(xe,{modelValue:N.value,"onUpdate:modelValue":e[0]||(e[0]=n=>N.value=n),class:"production-schedule-collapse"},{default:o(()=>[a(G,{title:"生产订单信息",name:"1"},{default:o(()=>[a(I,{data:t.row.products,style:{width:"100%"},border:"","row-class-name":he},{default:o(()=>[a(m,{prop:"sku",label:"SKU",align:"center"}),a(m,{prop:"upc",label:"UPC",align:"center"}),a(m,{prop:"color",label:"颜色",align:"center"},{default:o(n=>[y("span",null,T(_(Re)(_(te),parseInt(n.row.color))),1)]),_:2},1024),a(m,{prop:"quantity",label:"排产数量",align:"center"}),a(m,{prop:"inboundQuantity",label:"已入库数量",align:"center"}),a(m,{prop:"productionQuantity",label:"剩余数量",align:"center"},{default:o(n=>[y("span",null,T(n.row.quantity-n.row.inboundQuantity<0?0:n.row.quantity-n.row.inboundQuantity),1)]),_:2},1024),a(m,{prop:"purchaseOrder",label:"已下采购单",align:"center",sortable:"","sort-method":ge},{default:o(n=>[n.row.isPurchase?(r(),u(z,{key:0,type:"success"},{default:o(()=>[h(" 是 ")]),_:1})):(r(),u(z,{key:1,type:"danger"},{default:o(()=>[h(" 否 ")]),_:1}))]),_:2},1024)]),_:2},1032,["data"]),y("div",Ke,[y("div",Fe,[We,y("span",null,T(t.row.totalQuantity),1)])])]),_:2},1024),a(G,{title:"排产计划",name:"2"},{default:o(()=>[a(I,{ref_key:"schedulePlanTable",ref:U,data:t.row.schedulePlan,style:{width:"100%"},stripe:"",border:"",onRowClick:de},{default:o(()=>[a(m,{type:"expand"},{header:o(()=>[ze]),default:o(n=>[a(I,{data:n.row.products,border:""},{default:o(()=>[a(m,{prop:"sku",label:"SKU",align:"center"}),a(m,{prop:"piece",label:"数量",align:"center"})]),_:2},1032,["data"])]),_:2},1024),a(m,{prop:"scheduleDate",label:"排产日期",align:"center"},{default:o(n=>[a(ke,{"model-value":n.row.scheduleDate,format:"YYYY-MM-DD"},null,8,["model-value"])]),_:2},1024),a(m,{prop:"createTime",label:"创建时间",align:"center"}),t.row.status===1?(r(),u(m,{key:0,label:"操作",align:"center"},{default:o(n=>[O((r(),u(p,{text:"",bg:"",type:"danger",onClick:k(Ze=>we(n.row),["stop"])},{default:o(()=>[h(" 删除 ")]),_:2},1032,["onClick"])),[[M,_(d).pms.production.schedule.plan.delete]])]),_:2},1024)):g("",!0)]),_:2},1032,["data"])]),_:2},1024)]),_:2},1032,["modelValue"])]),_:1},8,["expand-row-keys"])]),_:1}),a(v,null,{default:o(()=>[a(P),a(Ce)]),_:1})]),_:1},8,["modelValue"]),a(Pe,{modelValue:S.value,"onUpdate:modelValue":e[4]||(e[4]=t=>S.value=t),title:"请选择预计交货时间",width:"270"},{footer:o(()=>[y("span",Ge,[a(p,{onClick:e[3]||(e[3]=t=>S.value=!1)},{default:o(()=>[h("取消")]),_:1}),a(p,{type:"primary",onClick:ue},{default:o(()=>[h(" 确认 ")]),_:1})])]),default:o(()=>[a(Se,{modelValue:x.value.expectedDeliveryTime,"onUpdate:modelValue":e[2]||(e[2]=t=>x.value.expectedDeliveryTime=t),placeholder:"请选择预计交货时间",type:"date","value-format":"YYYY-MM-DD","disabled-date":t=>t.getTime()<Date.now()-864e5},null,8,["modelValue","disabled-date"])]),_:1},8,["modelValue"]),a(Ue,{modelValue:Y.value,"onUpdate:modelValue":e[5]||(e[5]=t=>Y.value=t),"schedule-data":q.value,onClose:ye},null,8,["modelValue","schedule-data"])]),_:1},512)}}}),dt=Qe(Xe,[["__scopeId","data-v-f52a14fe"]]);export{dt as default};
