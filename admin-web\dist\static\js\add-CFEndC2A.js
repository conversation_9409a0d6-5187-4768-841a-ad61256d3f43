import{g as ce,i as ae,j as U}from"./index-DkYL1aws.js";import{_ as ge,a as we}from"./product-excel-import.vue_vue_type_script_setup_true_lang-CaiF2Sjy.js";import{n as Se}from"./index-CBanFtSc.js";import{a as pe}from"./index-C6cm1h61.js";import{c as H,b as y,q as P,w as u,h as o,i as c,y as g,t as j,v as ue,j as C,o as p,A as ie,f as E,B as V,F as de,s as se,W as xe,E as Q,T as Ve,af as Pe,ag as Ee}from"./.pnpm-hVqhwuVC.js";import{_ as Ce}from"./_plugin-vue_export-helper-DlAUqK2U.js";const qe=H({name:"production-purchase-order-product-selector"}),je=H({...qe,props:{pageSize:{type:Number,default:20}},emits:["selected"],setup(N,{emit:I}){const $=N,G=I,{dict:W}=ce(),M=W.get("color"),{service:l}=pe(),J=y($.pageSize),q=ae.useCrud({service:l.pms.production.schedule},k=>{k.refresh({size:J.value,status:1})}),D=ae.useTable({autoHeight:!0,columns:[{label:"#",prop:"products",type:"expand"},{label:"生产单号",prop:"sn"},{label:"销售订单号",prop:"orderSn"},{label:"单品总数量",prop:"totalQuantity"},{label:"下单时间",prop:"createTime",component:{name:"cl-date-text",props:{format:"YYYY-MM-DD HH:mm:ss"}}},{type:"op",label:"操作",width:160,fixed:"right",buttons:["slot-btn-outbound"]}]});function T(k,m){var _;(m==null?void 0:m.type)==="expand"||(m==null?void 0:m.type)==="op"||(_=D.value)==null||_.toggleRowExpansion(k)}const O=ae.useSearch({items:[{label:"生产单号/SKU",prop:"keyword",props:{labelWidth:"160px"},component:{name:"el-input",props:{placeholder:"输入生产单号/SKU搜索",clearable:!1,onChange(k){var m;(m=q.value)==null||m.refresh({keyWord:k.trim(),page:1})}}}}]});function s(k){G("selected",k)}return(k,m)=>{const _=c("cl-refresh-btn"),K=c("cl-flex1"),X=c("cl-search"),B=c("el-row"),Z=c("el-button"),x=c("el-table-column"),ee=c("el-table"),te=c("cl-table"),Y=c("cl-pagination"),oe=c("cl-crud");return p(),P(oe,{ref_key:"Crud",ref:q},{default:u(()=>[o(B,null,{default:u(()=>[o(_),o(K),o(X,{ref_key:"Search",ref:O},null,512)]),_:1}),o(B,null,{default:u(()=>[o(te,{ref_key:"Table",ref:D,"row-key":"id",class:"cursor-pointer","max-height":"600","highlight-current-row":"",onRowClick:T},{"slot-btn-outbound":u(({scope:A})=>[o(Z,{type:"primary",text:"",bg:"",onClick:w=>s(A.row)},{default:u(()=>[C(" 选择 ")]),_:2},1032,["onClick"])]),"column-receivedQuantity":u(({scope:A})=>{var w;return[C(j(ue(Se)(((w=A.row.contracts)==null?void 0:w.reduce((ne,le)=>ne+le.receivedQuantity,0))||0)),1)]}),"column-products":u(({scope:A})=>[o(ee,{data:A.row.products,style:{width:"100%"},border:""},{default:u(()=>[o(x,{prop:"sku",label:"SKU",align:"center",sortable:""}),o(x,{prop:"upc",label:"UPC",align:"center"}),o(x,{prop:"color",label:"颜色",align:"center"},{default:u(w=>[g("span",null,j(ue(U)(ue(M),parseInt(w.row.color))),1)]),_:2},1024),o(x,{prop:"quantity",label:"排产数量",align:"center"}),o(x,{prop:"inboundQuantity",label:"已入库数量",align:"center"}),o(x,{prop:"productionQuantity",label:"剩余数量",align:"center"},{default:u(w=>[g("span",null,j(w.row.quantity-w.row.inboundQuantity<0?0:w.row.quantity-w.row.inboundQuantity),1)]),_:2},1024)]),_:2},1032,["data"])]),_:1},512)]),_:1}),o(B,null,{default:u(()=>[o(K),o(Y)]),_:1})]),_:1},512)}}}),R=N=>(Pe("data-v-19de5bca"),N=N(),Ee(),N),Ie={class:"cl-crud inbound-create"},Ae={class:"order-create-body"},ze=R(()=>g("div",{class:"inbound-create-header"}," 基础信息 ",-1)),Qe={class:"is-adjust-label"},Oe=R(()=>g("span",null,"库存调整 ",-1)),Ue={key:0,"text-green-6":""},Ne={key:1,"text-red":""},De=R(()=>g("div",{class:"inbound-create-header"}," 产品信息 ",-1)),$e={key:1,flex:"~ items-center",pb3:""},Me={key:2,flex:"~ items-center",pb3:""},Te={key:0,style:{display:"flex","align-items":"center"}},Be={key:1,style:{display:"flex","align-items":"center"}},Fe={key:0,style:{display:"flex","align-items":"center"}},Le=R(()=>g("span",{style:{color:"var(--el-color-danger)"}},"*",-1)),We={flex:"~ justify-end items-center",mt4:"","w-full":""},Ke={class:"dialog-footer"},Ye=H({name:"undefined"}),He=H({...Ye,setup(N){const{service:I,router:$}=pe(),{dict:G}=ce(),W=y(!1),M=y(0),l=y({id:null,isAdjust:0,productionOrderId:0,productionOrderNo:"",remark:"",products:[]}),J=[{label:"单品",value:0},{label:"内盒/展示盒",value:1},{label:"箱装",value:2}],q=y(!1),D=y(!1),T=y(),O=y(!1),s=y([]),k=y([]),m=G.get("color"),_=y([]),K=y(!1);async function X(){T.value&&await T.value.validate(t=>{if(t){if(D.value=!0,s.value.length===0){Q.error({message:"请添加产品"});return}s.value=s.value.filter(e=>e.id!==null&&e.id!==void 0&&e.id!==0),D.value=!1,l.value.products=s.value.map(e=>({productId:e.id,quantity:e.quantity})),q.value=!0,W.value&&l.value.id&&l.value.id>0?I.pms.warehouse.source.inbound.update(l.value).then(e=>{const n=e==null?void 0:e.id;$.push(`/pms/warehouse/source/inbound?tab=${M.value}&expand=${n}`),Q.success({message:"保存成功",onClose:()=>{q.value=!1}})}).catch(e=>{Q.error({message:e.message}),q.value=!1}):(delete l.value.id,I.pms.warehouse.source.inbound.add(l.value).then(e=>{const n=e==null?void 0:e.id;$.push(`/pms/warehouse/source/inbound?tab=${M.value}&expand=${n}`),Q.success({message:"保存成功",onClose:()=>{q.value=!1}})}).catch(e=>{Q.error({message:e.message}),q.value=!1}))}})}function B(t){if(t)return I.pms.product.list({keyWord:t}).then(e=>{e=e||[],_.value=e.map(n=>{var a,d;return{value:n.id,label:`${n.sku}`,name:n.name,nameEn:n.nameEn,color:n.color,disabled:s.value.some(f=>f.id===n.id),unit:n.unit,unitProductSku:n.unitProductSku,stock:((a=n.stock)==null?void 0:a.inStock)-((d=n.stock)==null?void 0:d.freezeStock)||0,quantity:0}})})}const Z=y(),x=y(!1);function ee(){x.value=!0}function te(t){l.value.productionOrderId=t.id,l.value.productionOrderNo=t.sn,x.value=!1;const e=(t==null?void 0:t.products)||[];if(e.length===0)return Q.error("生产订单无产品");_.value=e.map(n=>({...n,value:n.id,label:`${n.sku}`,name:n.name,nameEn:n.nameEn,color:n.color,disabled:s.value.some(a=>a.id===n.id),unit:n.unit,unitProductSku:n.unitProductSku}))}const Y=y(null);function oe(){var t;k.value=[],O.value=!0,(t=Y.value)==null||t.resetData()}function A(t){var a,d,f,i;const e=_.value.find(v=>v.value===t);if(!e)return;const n=s.value.findIndex(v=>v.id===t);if(n!==-1){let v=s.value[n];v={...v,...e,id:e.value,sku:e.label,name:e.name,nameEn:e.nameEn,color:U(m.value,Number.parseInt(e.color)),unit:e.unit,unitProductSku:e.unitProductSku,stock:((a=e.stock)==null?void 0:a.inStock)-((d=e.stock)==null?void 0:d.freezeStock)||0,orderQuantity:e.quantity||0,quantity:null},s.value[n]=v}else s.value.push({...e,index:new Date().getTime()+Math.floor(Math.random()*1e3),id:e.value,sku:e.label,name:e.name,nameEn:e.nameEn,color:U(m.value,Number.parseInt(e.color)),quantity:null,unit:e.unit,unitProductSku:e.unitProductSku,stock:((f=e.stock)==null?void 0:f.inStock)-((i=e.stock)==null?void 0:i.freezeStock)||0,orderQuantity:e.quantity||0})}function w(){O.value=!1,k.value.forEach(t=>{var n,a;s.value.find(d=>d.id===t.id)||s.value.push({index:new Date().getTime()+Math.floor(Math.random()*1e3),id:t.id,sku:t.sku,name:t.name,nameEn:t.nameEn,color:U(m.value,Number.parseInt(t.color)),quantity:null,unit:t.unit,unitProductSku:t.unitProductSku,stock:((n=t==null?void 0:t.stock)==null?void 0:n.inStock)-((a=t.stock)==null?void 0:a.freezeStock)||0,orderQuantity:0})})}function ne(t){s.value=s.value.filter(e=>e.index!==t)}function le(){l.value.isAdjust!==0&&(_.value=[]),s.value.push({index:new Date().getTime()+Math.floor(Math.random()*1e3),id:null,sku:"",name:"",nameEn:"",color:"",quantity:null,unit:null,unitProductSku:"",stock:0,orderQuantity:0})}function me(t){const e=J.find(n=>n.value===t);return(e==null?void 0:e.label)||""}async function fe(t){await I.pms.warehouse.source.inbound.info({id:t}).then(e=>{var a;l.value.isAdjust=(e==null?void 0:e.isAdjust)||0,l.value.remark=(e==null?void 0:e.remark)||"";const n=(e==null?void 0:e.products)||[];e!=null&&e.orderId?(l.value.productionOrderId=e==null?void 0:e.orderId,l.value.productionOrderNo=((a=e==null?void 0:e.order)==null?void 0:a.sn)||"",I.pms.production.schedule.info({id:e==null?void 0:e.orderId}).then(d=>{const f=(d==null?void 0:d.products)||[];if(f.length===0)return Q.error("生产订单无产品");_.value=f.map(i=>({...i,value:i.id,label:`${i.sku}`,name:i.name,nameEn:i.nameEn,color:i.color,disabled:n.some(v=>v.id===i.id),unit:i.unit,unitProductSku:i.unitProductSku})),s.value=n.map(i=>{var v,S,F,L;return{...i,id:i.id,sku:i.sku,name:i.name,nameEn:i.nameEn,color:U(m.value,Number.parseInt(i.color)),unit:i.unit,stock:((v=i.stock)==null?void 0:v.inStock)-((S=i.stock)==null?void 0:S.freezeStock)||0,quantity:i.quantity,inboundQuantity:((F=f.find(h=>h.id===i.id))==null?void 0:F.inboundQuantity)||0,orderQuantity:((L=f.find(h=>h.id===i.id))==null?void 0:L.quantity)||0}})}).catch(d=>{Q.error({message:d.message})})):s.value=n.map(d=>{var f,i;return{id:d.id,sku:d.sku,name:d.name,nameEn:d.nameEn,color:U(m.value,Number.parseInt(d.color)),quantity:d.quantity,unit:d.unit,stock:((f=d.stock)==null?void 0:f.inStock)-((i=d.stock)==null?void 0:i.freezeStock)||0}})})}function ve(t,e){const n=t.map(a=>{var f,i,v;const d=(f=e.find(S=>S.sku===a.sku))==null?void 0:f.quantity;return{index:new Date().getTime()+Math.floor(Math.random()*1e3),id:a==null?void 0:a.id,sku:a.sku,name:a==null?void 0:a.name,nameEn:a==null?void 0:a.nameEn,color:U(m.value,Number.parseInt(a==null?void 0:a.color)),quantity:d,unit:a==null?void 0:a.unit,unitProductSku:a==null?void 0:a.unitProductSku,stock:((i=a.stock)==null?void 0:i.inStock)-((v=a.stock)==null?void 0:v.freezeStock)||0}});s.value.push(...n)}async function _e(t){if(t!==l.value.isAdjust){if(s.value.length===0){l.value.isAdjust=t;return}try{await Ve.confirm("切换后，已选择的产品将会被清空，是否继续？")&&(l.value.productionOrderId=0,l.value.productionOrderNo="",s.value=[],_.value=[],l.value.isAdjust=t)}catch{}}}ie(()=>{_.value=_.value.map(t=>(t.disabled=s.value.some(e=>e.id===t.value),t))}),ie(()=>{const t=$.currentRoute.value.query.id;t&&(W.value=!0,l.value.id=Number.parseInt(t),fe(t))});async function be(){return I.pms.warehouse.source.inbound.info({id:l.value.id}).then(t=>{M.value=t.status??0})}return be(),(t,e)=>{const n=c("cl-svg"),a=c("el-tooltip"),d=c("el-radio"),f=c("el-radio-group"),i=c("el-form-item"),v=c("el-input"),S=c("el-button"),F=c("el-option"),L=c("el-select"),h=c("el-table-column"),ke=c("el-input-number"),he=c("el-table"),ye=c("el-form"),re=c("el-dialog");return p(),E("div",null,[g("div",Ie,[o(ye,{ref_key:"inboundForm",ref:T,model:l.value,"label-width":"90px",size:"large","status-icon":""},{default:u(()=>[g("div",Ae,[ze,o(i,{label:"库存调整",prop:"isAdjust"},{label:u(()=>[g("div",Qe,[Oe,o(a,{effect:"dark",content:"库存调整不计入排产订单，而是直接改变库存，可以为负数",placement:"top"},{default:u(()=>[o(n,{name:"icon-question",class:"tool-tip-icon"})]),_:1})])]),default:u(()=>[o(f,{"model-value":l.value.isAdjust,"onUpdate:modelValue":_e},{default:u(()=>[o(d,{value:1,label:"是"}),o(d,{value:0,label:"否"})]),_:1},8,["model-value"])]),_:1}),l.value.isAdjust===0?(p(),P(i,{key:0,label:"生产单号",prop:"orderId"},{default:u(()=>[l.value.productionOrderNo?(p(),E("div",Ue,j(l.value.productionOrderNo),1)):(p(),E("div",Ne," 请先选择生产订单 "))]),_:1})):V("",!0),o(i,{label:"备注",prop:"remark",style:{width:"500px"}},{default:u(()=>[o(v,{modelValue:l.value.remark,"onUpdate:modelValue":e[0]||(e[0]=r=>l.value.remark=r),type:"textarea",rows:3},null,8,["modelValue"])]),_:1}),De,l.value.isAdjust!==0?(p(),E("div",$e,[o(S,{type:"primary",size:"default",onClick:oe},{default:u(()=>[C(" 选择产品 ")]),_:1}),o(ge,{onSuccess:ve})])):V("",!0),l.value.isAdjust===0?(p(),E("div",Me,[o(S,{type:"primary",size:"default",disabled:s.value.length>0,onClick:ee},{default:u(()=>[C(" 选择生产订单 ")]),_:1},8,["disabled"])])):V("",!0),o(he,{data:s.value,size:"small",style:{width:"100%"},border:""},{default:u(()=>[o(h,{prop:"sku",label:"SKU",width:"200"},{default:u(r=>{var z;return[r.row.id===0||r.row.sku===""?(p(),E("div",Te,[l.value.isAdjust===0?(p(),P(L,{key:0,modelValue:r.row.id,"onUpdate:modelValue":b=>r.row.id=b,size:"small",placeholder:"请选择产品",style:{width:"400px"},filterable:"",onChange:A},{default:u(()=>[(p(!0),E(de,null,se(_.value,b=>(p(),P(F,{key:b.value,disabled:b.disabled,label:b.label,value:b.value},null,8,["disabled","label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])):V("",!0),l.value.isAdjust===1?(p(),P(L,{key:1,modelValue:r.row.id,"onUpdate:modelValue":b=>r.row.id=b,size:"small",filterable:"","reserve-keyword":"",loading:K.value,placeholder:"请选择产品",style:{width:"400px"},remote:"","remote-method":B,onChange:A},{default:u(()=>[(p(!0),E(de,null,se(_.value,b=>(p(),P(F,{key:b.value,disabled:b.disabled,label:b.label,value:b.value},null,8,["disabled","label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","loading"])):V("",!0)])):(p(),E("div",Be,j((z=r.row)==null?void 0:z.sku),1))]}),_:1}),o(h,{prop:"name",label:"名称"},{default:u(r=>{var z,b;return[r.row.name===""&&r.row.nameEn===""?V("",!0):(p(),E("div",Fe,j((z=r.row)==null?void 0:z.name)+" / "+j((b=r.row)==null?void 0:b.nameEn),1))]}),_:1}),o(h,{prop:"unit",label:"单位",width:"150"},{default:u(r=>[g("span",null,j(me(r.row.unit)),1)]),_:1}),o(h,{prop:"color",label:"颜色",width:"200"}),l.value.isAdjust!==0?(p(),P(h,{key:0,prop:"stock",label:"可用库存",width:"100",align:"center"})):V("",!0),l.value.isAdjust===0?(p(),P(h,{key:1,prop:"orderQuantity",label:"排产数量",width:"100",align:"center"})):V("",!0),l.value.isAdjust===0?(p(),P(h,{key:2,prop:"inboundQuantity",label:"已入库数量",width:"100",align:"center"})):V("",!0),l.value.isAdjust===0?(p(),P(h,{key:3,prop:"processingQuantity",label:"入库中数量",width:"100",align:"center"})):V("",!0),l.value.isAdjust===0?(p(),P(h,{key:4,prop:"remainQuantity",label:"剩余数量",width:"100",align:"center"},{default:u(r=>[g("span",null,j(Math.max(0,r.row.orderQuantity-r.row.inboundQuantity-r.row.processingQuantity)||""),1)]),_:1})):V("",!0),o(h,{prop:"quantity",label:"*数量",width:"200",align:"center"},{header:u(()=>[Le,C(" 数量 ")]),default:u(r=>[g("div",{class:xe(D.value&&!(r.row.quantity>0)?"quantity-input-error":"")},[o(ke,{modelValue:r.row.quantity,"onUpdate:modelValue":z=>r.row.quantity=z,style:{width:"180px"},size:"small",min:-r.row.stock,placeholder:"请输出产品数量"},null,8,["modelValue","onUpdate:modelValue","min"])],2)]),_:1}),o(h,{label:"操作",width:"100",align:"center"},{default:u(r=>[o(S,{size:"small",type:"danger",onClick:z=>ne(r.row.index)},{default:u(()=>[C(" 删除 ")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"]),o(S,{disabled:l.value.isAdjust===0&&_.value.length===0,size:"small",h35px:"",style:{width:"100%"},class:"btn-product-add",onClick:le},{default:u(()=>[C(" + 添加产品 ")]),_:1},8,["disabled"]),g("div",We,[o(S,{type:"success",loading:q.value,onClick:X},{default:u(()=>[C(" 保存 ")]),_:1},8,["loading"])])])]),_:1},8,["model"])]),o(re,{modelValue:O.value,"onUpdate:modelValue":e[3]||(e[3]=r=>O.value=r),title:"选择产品",width:"80%"},{footer:u(()=>[g("span",Ke,[o(S,{onClick:e[2]||(e[2]=r=>O.value=!1)},{default:u(()=>[C("取消")]),_:1}),o(S,{type:"primary",onClick:w},{default:u(()=>[C(" 确认选择 ")]),_:1})])]),default:u(()=>[o(we,{ref_key:"productSelector",ref:Y,modelValue:k.value,"onUpdate:modelValue":e[1]||(e[1]=r=>k.value=r),"show-unit":!1},null,8,["modelValue"])]),_:1},8,["modelValue"]),o(re,{modelValue:x.value,"onUpdate:modelValue":e[5]||(e[5]=r=>x.value=r),title:"选择生产订单",width:"80%"},{default:u(()=>[o(je,{ref_key:"productionPurchaseOrderSelector",ref:Z,modelValue:k.value,"onUpdate:modelValue":e[4]||(e[4]=r=>k.value=r),onSelected:te},null,8,["modelValue"])]),_:1},8,["modelValue"])])}}}),tt=Ce(He,[["__scopeId","data-v-19de5bca"]]);export{tt as default};
