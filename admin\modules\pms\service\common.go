package service

import (
	"context"
	"fmt"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/imhuso/lookah-erp/admin/modules/pms/model"
	"github.com/imhuso/lookah-erp/admin/yc"
)

// LABOURCOST 工时费率(元/小时)
const LABOURCOST = 23.0

// 生产阶段常量
const (
	ProductionStagePilot  = 1 // 试产
	ProductionStageFirst  = 2 // 首次量产
	ProductionStageFormal = 3 // 正式量产
)

// CORE_LIST 芯子列表
var CORE_LIST = []string{
	"海马芯",
	"710芯",
	"510芯子",
	"水母芯子",
}

// WORKSHOP_SECTIONS 工序映射
var WORKSHOP_SECTIONS = map[int]string{
	1:  "加工段",
	2:  "组装段",
	3:  "老化段",
	4:  "包装段",
	5:  "加工段一",
	6:  "加工段二",
	7:  "加工段三",
	8:  "芯子配件加工段一",
	9:  "芯子配件加工段二",
	10: "芯子配件组装段一",
	11: "芯子配件组装段二",
	12: "芯子配件组装段三",
	13: "芯子配件组装段四",
	14: "芯子配件组装段五",
	15: "芯子配件包装段",
}

// PRODUCTION_STAGES 生产阶段映射
var PRODUCTION_STAGES = map[int]string{
	1: "临时1（试产）",
	2: "临时2（首次量产）",
	3: "正式（量产）",
}

// RemoveDuplicateInt64 去除int64数组中的重复元素
func RemoveDuplicateInt64(slice []int64) []int64 {
	if len(slice) == 0 {
		return slice
	}

	// 使用map去重
	seen := make(map[int64]bool)
	var result []int64

	for _, item := range slice {
		if !seen[item] {
			seen[item] = true
			result = append(result, item)
		}
	}

	return result
}

// GetInternalInboundNo 获取内部入库单号
func GetInternalInboundNo(ctx context.Context) (internalOrderNo string, err error) {
	todayOrderCount := 0
	todayStart := gtime.Now().StartOfDay()

	// 计算进入仓库入库数量
	todayOrderCount, err = yc.DBM(model.NewPmsMaterialInbound()).Ctx(ctx).WhereGT("createTime", todayStart).Unscoped().Count()
	if err != nil {
		return "", err
	}

	todayOrderCountStr := fmt.Sprintf("%d", todayOrderCount+1)
	// 订单数量不足3位数，前面补0
	if len(todayOrderCountStr) < 3 {
		todayOrderCountStr = fmt.Sprintf("%03s", todayOrderCountStr)
	}
	internalOrderNo = fmt.Sprintf("%s%s", gconv.String(gtime.Now().Format("Ymd")), todayOrderCountStr)

	// 判断是否存在
	count, err := yc.DBM(model.NewPmsMaterialInbound()).Ctx(ctx).Where("no", internalOrderNo).Count()
	if err != nil {
		return "", err
	}
	if count > 0 {
		return "", gerror.New("创建内部订单号失败")
	}
	return
}
