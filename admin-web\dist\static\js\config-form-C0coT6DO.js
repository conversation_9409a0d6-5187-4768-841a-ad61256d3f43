import{c as u,n as i,b as _,q as v,w as l,h as n,v as V,i as t,j as m,o as k}from"./.pnpm-hVqhwuVC.js";const C=u({name:"undefined"}),w=u({...C,props:{modelValue:{},modelModifiers:{}},emits:["update:modelValue"],setup(r){const o=i(r,"modelValue"),c=_();return(a,e)=>{const p=t("el-form"),d=t("el-button"),f=t("el-dialog");return k(),v(f,{title:"项目配置",modelValue:o.value,"onUpdate:modelValue":e[2]||(e[2]=s=>o.value=s),"append-to-body":"","close-on-click-modal":a.closeOnClickModal,"close-on-press-escape":a.closeOnPressEscape},{footer:l(()=>[n(d,{onClick:e[0]||(e[0]=s=>o.value=!1)},{default:l(()=>[m("取 消")]),_:1}),n(d,{type:"primary",onClick:e[1]||(e[1]=s=>o.value=!1)},{default:l(()=>[m("确 定")]),_:1})]),default:l(()=>[n(p,{model:V(c)},null,8,["model"])]),_:1},8,["modelValue","close-on-click-modal","close-on-press-escape"])}}});export{w as default};
