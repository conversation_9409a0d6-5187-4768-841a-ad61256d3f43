<script setup lang="ts" name="OutboundTable">
import { useCrud, useTable } from '@cool-vue-p/crud';
import { service } from '/@/cool';

interface Props {
  outboundType?: number
  supplierId?: number
}

const props = withDefaults(defineProps<Props>(), {
  outboundType: 4,
  supplierId: 0,
})

const emits = defineEmits(['selected'])
const Table = useTable({
  columns: [
    { label: '#', prop: 'products', type: 'expand' },
    { label: '出库单号', prop: 'no', width: 220 },
    { label: '出库总数量', prop: 'totalQuantity' },
    { label: '创建时间', prop: 'createTime' },
    {
      label: '备注',
      prop: 'remark',
      width: 150,
      showOverflowTooltip: true,
    },
    {
      type: 'op',
      label: '操作',
      width: 80,
      buttons: [{
        label: '选择',
        type: 'primary',
        onClick: (params: any) => {
          emits('selected', params.scope)
        },
      }],
    },
  ],
})

// cl-crud 配置
const Crud = useCrud(
  {
    service: service.pms.material.outbound,
    async onRefresh(params, { next, render }) {
      const { _, list, pagination } = await next(params)
      render(list, pagination)
    },
  },
  (app) => {
    app.refresh({ status: 3, supplierId: props.supplierId, type: props.outboundType, finished: true })

    // 监听outboundType变化，重新刷新数据
    watch(() => props.outboundType, (newType) => {
      app.refresh({ status: 3,supplierId: props.supplierId, type: newType, finished: true })
    })
  },
)
</script>

<template>
  <cl-crud ref="Crud">
    <el-row>
      <!-- 刷新按钮 -->
      <cl-refresh-btn />
      <cl-flex1 />
      <cl-search-key />
    </el-row>

    <el-row>
      <!-- 数据表格 -->
      <cl-table ref="Table" row-key="id" :auto-height="false" :style="`height:${600}px`">
        <template #column-products="{ scope }">
          <el-table :data="scope.row.products" style="width: 100%" border>
            <el-table-column label="物料代码" prop="code" align="center" />
            <el-table-column label="物料名称" prop="name" align="center" />
            <el-table-column label="型号" prop="model" align="center" />
            <el-table-column label="退货数量" prop="quantity" align="center" />
            <el-table-column label="已补数量" prop="restockingQty" align="center" />
            <el-table-column label="单位" prop="unit" align="center" />
          </el-table>
        </template>
      </cl-table>
    </el-row>

    <el-row>
      <cl-flex1 />
      <!-- 分页控件 -->
      <cl-pagination />
    </el-row>
  </cl-crud>
</template>

<style scoped lang="scss">

</style>
