import{M as N}from"./MaterialInbound-CdK3V2id.js";import{_ as R}from"./MaterialOutbound.vue_vue_type_script_setup_true_name_MaterialOutbound_lang-P5kEfEPC.js";import{c as x,b,K as Y,q,i as r,w as c,h as n,y as U,j as w,v as p,L as I,E as C,o as P,B as G,ae as F,e as j,f as B,F as A,s as H,bc as J,V as X}from"./.pnpm-hVqhwuVC.js";import{i as W,s as T,e as L}from"./index-BtOcqcNl.js";import{_ as Z}from"./PurchaseOrder.vue_vue_type_script_setup_true_name_ProductOutbound_lang-_WP1f5Ek.js";import{statusList as z}from"./dict-D_Vssv3j.js";import{_ as ee}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./constant-C2dsBPRR.js";const ae={style:{"margin-right":"20px"}},le=x({name:"ProductInbound"}),te=x({...le,setup(O){const _=b(!1),d=b(!1),l=b({date:[],keyWord:""}),t=W.useCrud({dict:{api:{page:"productInboundPage"}},service:T.pms.finance,async onRefresh(a,{next:e,done:o,render:s}){o(),u(a);const{list:i,pagination:v}=await e(a);s(i,v)}},a=>{a.refresh()});function u(a){if(l.value.date&&l.value.date.length>0){const e=Y(l.value.date[0]).format("YYYY-MM-DD"),o=Y(l.value.date[1]).format("YYYY-MM-DD");a.date=`${e},${o}`}return l.value.keyWord&&(a.keyWord=l.value.keyWord),a}const V=W.useTable({columns:[{label:"序号",prop:"rowIndex",width:80},{label:"产品名称",prop:"name"},{label:"产品编码(SKU)",prop:"sku",width:180},{label:"UPC",prop:"upc",width:180},{label:"颜色",prop:"color",width:300},{label:"单位",prop:"unit",width:80},{label:"装箱数量",prop:"quantity",width:120},{label:"状态",prop:"status",width:120,dict:[{label:"备货完成",value:0,type:"success"},{label:"打包中",value:1,type:"warning"},{label:"打包完成",value:2,type:"success"},{label:"交货完成",value:3,type:"success"}]},{label:"生产单号",prop:"sn",width:180},{label:"完成时间",prop:"completeTime",width:180}]});function f(){var a,e,o;try{(e=(a=t==null?void 0:t.value)==null?void 0:a.params)!=null&&e.page&&(t.value.params.page=1),_.value=!0,(o=t==null?void 0:t.value)==null||o.refresh()}catch(s){console.error(s)}finally{_.value=!1}}function m(){var a,e;l.value.keyWord="",l.value.date=[],(a=t==null?void 0:t.value)!=null&&a.params&&(t.value.params.page=1,t.value.params.size=20),(e=t==null?void 0:t.value)==null||e.refresh()}function k(a){a?f():m()}async function g(){d.value=!0;try{const a=u({page:1,size:1e5}),e=await T.pms.finance.request({url:"/productInboundExportExcel",method:"GET",responseType:"blob",params:a});L(e)&&C.success("导出成功")}catch(a){console.error(a)}finally{d.value=!1}}return(a,e)=>{const o=r("el-button"),s=r("cl-flex1"),i=r("el-date-picker"),v=r("el-input"),h=r("el-row"),D=r("cl-table"),$=r("cl-pagination"),M=r("cl-crud");return P(),q(M,{ref_key:"Crud",ref:t},{default:c(()=>[n(h,null,{default:c(()=>[n(o,{onClick:m},{default:c(()=>[w(" 刷新 ")]),_:1}),n(o,{type:"success",icon:"Download",loading:p(d),onClick:g},{default:c(()=>[w(" 导出excel ")]),_:1},8,["loading"]),n(s),U("div",ae,[n(i,{modelValue:p(l).date,"onUpdate:modelValue":e[0]||(e[0]=y=>p(l).date=y),type:"daterange",clearable:"","range-separator":"~","start-placeholder":"开始日期","end-placeholder":"结束日期",onChange:k},null,8,["modelValue"])]),n(v,{modelValue:p(l).keyWord,"onUpdate:modelValue":e[1]||(e[1]=y=>p(l).keyWord=y),placeholder:"请输入产品名称或SKU或生产单号",style:{width:"500px"},clearable:"",onClear:m,onKeyup:I(f,["enter"])},null,8,["modelValue"]),n(o,{type:"primary",mx:"10px",loading:p(_),onClick:f},{default:c(()=>[w(" 搜索 ")]),_:1},8,["loading"])]),_:1}),n(h,null,{default:c(()=>[n(D,{ref_key:"Table",ref:V,"row-key":"rowIndex"},null,512)]),_:1}),n(h,null,{default:c(()=>[n(s),n($)]),_:1})]),_:1},512)}}}),oe={style:{"margin-right":"20px"}},ne=x({name:"ProductOutbound"}),re=x({...ne,setup(O){const _=b(!1),d=b(!1),l=b({date:[],keyWord:""}),t=W.useCrud({dict:{api:{page:"productOutboundPage"}},service:T.pms.finance,async onRefresh(a,{next:e,done:o,render:s}){o(),u(a);const{list:i,pagination:v}=await e(a);s(i,v)}},a=>{a.refresh()});function u(a){if(l.value.date&&l.value.date.length>0){const e=Y(l.value.date[0]).format("YYYY-MM-DD"),o=Y(l.value.date[1]).format("YYYY-MM-DD");a.date=`${e},${o}`}return l.value.keyWord&&(a.keyWord=l.value.keyWord),a}const V=W.useTable({columns:[{label:"序号",prop:"rowIndex",width:80},{label:"产品名称",prop:"name"},{label:"产品编码(SKU)",prop:"sku",width:180},{label:"UPC",prop:"upc",width:180},{label:"颜色",prop:"color",width:300},{label:"单位",prop:"unit",width:80},{label:"装箱数量",prop:"quantity",width:120},{label:"状态",prop:"status",width:120,dict:[{label:"备货完成",value:0,type:"success"},{label:"打包中",value:1,type:"warning"},{label:"打包完成",value:2,type:"success"},{label:"交货完成",value:3,type:"success"}]},{label:"生产单号",prop:"sn",width:180},{label:"发货时间",prop:"completeTime",width:180}]});function f(){var a,e,o;try{(e=(a=t==null?void 0:t.value)==null?void 0:a.params)!=null&&e.page&&(t.value.params.page=1),_.value=!0,(o=t==null?void 0:t.value)==null||o.refresh()}catch(s){console.error(s)}finally{_.value=!1}}function m(){var a,e;l.value.keyWord="",l.value.date=[],(a=t==null?void 0:t.value)!=null&&a.params&&(t.value.params.page=1,t.value.params.size=20),(e=t==null?void 0:t.value)==null||e.refresh()}function k(a){a?f():m()}async function g(){d.value=!0;try{const a=u({page:1,size:1e5}),e=await T.pms.finance.request({url:"/productOutboundExportExcel",method:"GET",responseType:"blob",params:a});L(e)&&C.success("导出成功")}catch(a){console.error(a)}finally{d.value=!1}}return(a,e)=>{const o=r("el-button"),s=r("cl-flex1"),i=r("el-date-picker"),v=r("el-input"),h=r("el-row"),D=r("cl-table"),$=r("cl-pagination"),M=r("cl-crud");return P(),q(M,{ref_key:"Crud",ref:t},{default:c(()=>[n(h,null,{default:c(()=>[n(o,{onClick:m},{default:c(()=>[w(" 刷新 ")]),_:1}),n(o,{type:"success",icon:"Download",loading:p(d),onClick:g},{default:c(()=>[w(" 导出excel ")]),_:1},8,["loading"]),n(s),U("div",oe,[n(i,{modelValue:p(l).date,"onUpdate:modelValue":e[0]||(e[0]=y=>p(l).date=y),type:"daterange",clearable:"","range-separator":"~","start-placeholder":"开始日期","end-placeholder":"结束日期",onChange:k},null,8,["modelValue"])]),n(v,{modelValue:p(l).keyWord,"onUpdate:modelValue":e[1]||(e[1]=y=>p(l).keyWord=y),placeholder:"请输入产品名称或SKU或生产单号",style:{width:"500px"},clearable:"",onClear:m,onKeyup:I(f,["enter"])},null,8,["modelValue"]),n(o,{type:"primary",mx:"10px",loading:p(_),onClick:f},{default:c(()=>[w(" 搜索 ")]),_:1},8,["loading"])]),_:1}),n(h,null,{default:c(()=>[n(D,{ref_key:"Table",ref:V,"row-key":"rowIndex"},null,512)]),_:1}),n(h,null,{default:c(()=>[n(s),n($)]),_:1})]),_:1},512)}}}),se={style:{"margin-right":"20px"}},ce=x({name:"ProductionOrder"}),ue=x({...ce,setup(O){const _=b(!1),d=b(!1),l=b({date:[],keyWord:""}),t=W.useCrud({dict:{api:{page:"productionOrderPage"}},service:T.pms.finance,async onRefresh(a,{next:e,done:o,render:s}){o(),u(a);const{list:i,pagination:v}=await e(a);s(i,v)}},a=>{a.refresh()});function u(a){if(l.value.date&&l.value.date.length>0){const e=Y(l.value.date[0]).format("YYYY-MM-DD"),o=Y(l.value.date[1]).format("YYYY-MM-DD");a.date=`${e},${o}`}return l.value.keyWord&&(a.keyWord=l.value.keyWord),a}const V=W.useTable({columns:[{label:"序号",prop:"rowIndex",width:80},{label:"客户订单号(PO)",prop:"po",width:180},{label:"生产单号",prop:"sn",width:180},{label:"物料编码",prop:"code",width:180},{label:"物料名称",prop:"name"},{label:"规格/型号",prop:"model"},{label:"单位",prop:"unit",width:80},{label:"数量",prop:"quantity",width:120},{label:"下单时间",prop:"createTime",width:180}]});function f(){var a,e,o;try{(e=(a=t==null?void 0:t.value)==null?void 0:a.params)!=null&&e.page&&(t.value.params.page=1),_.value=!0,(o=t==null?void 0:t.value)==null||o.refresh()}catch(s){console.error(s)}finally{_.value=!1}}function m(){var a,e;l.value.keyWord="",l.value.date=[],(a=t==null?void 0:t.value)!=null&&a.params&&(t.value.params.page=1,t.value.params.size=20),(e=t==null?void 0:t.value)==null||e.refresh()}function k(a){a?f():m()}async function g(){d.value=!0;try{const a=u({page:1,size:1e5}),e=await T.pms.finance.request({url:"/productionOrderExportExcel",method:"GET",responseType:"blob",params:a});L(e)&&C.success("导出成功")}catch(a){console.error(a)}finally{d.value=!1}}return(a,e)=>{const o=r("el-button"),s=r("cl-flex1"),i=r("el-date-picker"),v=r("el-input"),h=r("el-row"),D=r("cl-table"),$=r("cl-pagination"),M=r("cl-crud");return P(),q(M,{ref_key:"Crud",ref:t},{default:c(()=>[n(h,null,{default:c(()=>[n(o,{onClick:m},{default:c(()=>[w(" 刷新 ")]),_:1}),n(o,{type:"success",icon:"Download",loading:p(d),onClick:g},{default:c(()=>[w(" 导出excel ")]),_:1},8,["loading"]),n(s),U("div",se,[n(i,{modelValue:p(l).date,"onUpdate:modelValue":e[0]||(e[0]=y=>p(l).date=y),type:"daterange",clearable:"","range-separator":"~","start-placeholder":"开始日期","end-placeholder":"结束日期",onChange:k},null,8,["modelValue"])]),n(v,{modelValue:p(l).keyWord,"onUpdate:modelValue":e[1]||(e[1]=y=>p(l).keyWord=y),placeholder:"请输入物料名称或物料编号或生产单号或po",style:{width:"500px"},clearable:"",onClear:m,onKeyup:I(f,["enter"])},null,8,["modelValue"]),n(o,{type:"primary",mx:"10px",loading:p(_),onClick:f},{default:c(()=>[w(" 搜索 ")]),_:1},8,["loading"])]),_:1}),n(h,null,{default:c(()=>[n(D,{ref_key:"Table",ref:V,"row-key":"rowIndex"},null,512)]),_:1}),n(h,null,{default:c(()=>[n(s),n($)]),_:1})]),_:1},512)}}}),pe={style:{"margin-right":"20px"}},de=x({name:"ProductionOrder"}),ie=x({...de,setup(O){const _=b(!1),d=b(!1),l=b({date:[],keyWord:"",status:void 0}),t=b(z.filter(e=>e.value>3)),u=W.useCrud({dict:{api:{page:"salesOrderPage"}},service:T.pms.finance,async onRefresh(e,{next:o,done:s,render:i}){s(),V(e);const{list:v,pagination:h}=await o(e);i(v,h)}},e=>{e.refresh()});function V(e){if(l.value.date&&l.value.date.length>0){const o=Y(l.value.date[0]).format("YYYY-MM-DD"),s=Y(l.value.date[1]).format("YYYY-MM-DD");e.date=`${o},${s}`}return l.value.keyWord&&(e.keyWord=l.value.keyWord),l.value.status&&l.value.status>3&&(e.status=l.value.status),e}const f=W.useTable({columns:[{label:"序号",prop:"rowIndex",width:80},{label:"产品名称",prop:"name"},{label:"产品编码(SKU)",prop:"sku",width:180},{label:"UPC",prop:"upc",width:180},{label:"颜色",prop:"color",width:300},{label:"实际发货数量",prop:"shippedPiece",width:120},{label:"销售单号",prop:"sn",width:180},{label:"派送地址",prop:"deliverTo"},{label:"状态",prop:"status",width:120,dict:z},{label:"实际发货日期",prop:"shipDate",width:180}]});function m(){var e,o,s;try{(o=(e=u==null?void 0:u.value)==null?void 0:e.params)!=null&&o.page&&(u.value.params.page=1),_.value=!0,(s=u==null?void 0:u.value)==null||s.refresh()}catch(i){console.error(i)}finally{_.value=!1}}function k(){var e,o;l.value.keyWord="",l.value.status=void 0,l.value.date=[],(e=u==null?void 0:u.value)!=null&&e.params&&(u.value.params.page=1,u.value.params.size=20),(o=u==null?void 0:u.value)==null||o.refresh()}function g(e){e?m():k()}async function a(){d.value=!0;try{const e=V({page:1,size:1e5}),o=await T.pms.finance.request({url:"/salesOrderExportExcel",method:"GET",responseType:"blob",params:e});L(o)&&C.success("导出成功")}catch(e){console.error(e)}finally{d.value=!1}}return(e,o)=>{const s=r("el-button"),i=r("cl-flex1"),v=r("el-tag"),h=r("el-option"),D=r("el-select"),$=r("el-date-picker"),M=r("el-input"),y=r("el-row"),K=r("cl-table"),S=r("cl-pagination"),Q=r("cl-crud");return P(),q(Q,{ref_key:"Crud",ref:u},{default:c(()=>[n(y,null,{default:c(()=>[n(s,{onClick:k},{default:c(()=>[w(" 刷新 ")]),_:1}),n(s,{type:"success",icon:"Download",loading:p(d),onClick:a},{default:c(()=>[w(" 导出excel ")]),_:1},8,["loading"]),n(i),G("",!0),U("div",pe,[n($,{modelValue:p(l).date,"onUpdate:modelValue":o[1]||(o[1]=E=>p(l).date=E),type:"daterange",clearable:"","range-separator":"~","start-placeholder":"开始日期","end-placeholder":"结束日期",onChange:g},null,8,["modelValue"])]),n(M,{modelValue:p(l).keyWord,"onUpdate:modelValue":o[2]||(o[2]=E=>p(l).keyWord=E),placeholder:"请输入产品名称或产品编号或销售单号",style:{width:"500px"},clearable:"",onClear:k,onKeyup:I(m,["enter"])},null,8,["modelValue"]),n(s,{type:"primary",mx:"10px",loading:p(_),onClick:m},{default:c(()=>[w(" 搜索 ")]),_:1},8,["loading"])]),_:1}),n(y,null,{default:c(()=>[n(K,{ref_key:"Table",ref:f,"row-key":"rowIndex"},null,512)]),_:1}),n(y,null,{default:c(()=>[n(i),n(S)]),_:1})]),_:1},512)}}}),_e=x({name:"undefined"}),me=x({..._e,setup(O){const _={MaterialInbound:N,MaterialOutbound:R,ProductInbound:te,ProductOutbound:re,PurchaseOrder:Z,ProductionOrder:ue,SalesOrder:ie},d=b("MaterialInbound"),l=b([{label:"物料入库",value:1,type:"info",component:"MaterialInbound"},{label:"物料出库",value:2,type:"info",component:"MaterialOutbound"},{label:"成品入库",value:3,type:"info",component:"ProductInbound"},{label:"成品出库",value:4,type:"info",component:"ProductOutbound"},{label:"采购订单",value:5,type:"success",component:"PurchaseOrder"},{label:"生产单",value:6,type:"success",component:"ProductionOrder"},{label:"销售表",value:7,type:"success",component:"SalesOrder"}]),{height:t}=F(),u=j(()=>t.value-240);return(V,f)=>{const m=r("el-tab-pane"),k=r("el-tabs");return P(),B("div",{style:X(`height:${p(u)}px`)},[n(k,{modelValue:d.value,"onUpdate:modelValue":f[0]||(f[0]=g=>d.value=g),type:"border-card"},{default:c(()=>[(P(!0),B(A,null,H(l.value,g=>(P(),q(m,{key:g.value,label:`${g.label}`,name:g.component},null,8,["label","name"]))),128))]),_:1},8,["modelValue"]),(P(),q(J(_[d.value]),{"show-price":!1}))],4)}}}),xe=ee(me,[["__scopeId","data-v-590ab6e5"]]);export{xe as default};
