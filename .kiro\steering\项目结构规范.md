---
inclusion: always
---

# 项目结构规范

## 整体项目结构

```
lookah-erp/
├── admin/                  # Go 后端项目
├── admin-web/             # Vue 前端项目
├── bin/                   # 可执行文件目录
├── wails/                 # Wails 桌面应用
├── yctl/                  # 命令行工具
├── .kiro/                 # Kiro 配置目录
├── README.MD              # 项目说明文档
├── release.py             # Python 发布脚本
└── release.sh             # Shell 发布脚本
```

## 前端项目结构 (admin-web)

```
admin-web/
├── src/
│   ├── modules/           # 业务模块目录
│   │   ├── pms/          # 生产管理系统模块
│   │   │   ├── views/    # 页面组件
│   │   │   ├── components/ # 模块组件
│   │   │   ├── types/    # 类型定义
│   │   │   ├── services/ # API 服务
│   │   │   └── utils/    # 工具函数
│   │   ├── inventory/    # 库存管理模块
│   │   ├── order/        # 订单管理模块
│   │   └── system/       # 系统管理模块
│   ├── components/        # 全局公共组件
│   │   ├── common/       # 通用组件
│   │   ├── form/         # 表单组件
│   │   ├── table/        # 表格组件
│   │   └── chart/        # 图表组件
│   ├── composables/       # 组合式函数
│   ├── stores/           # Pinia 状态管理
│   ├── router/           # 路由配置
│   ├── utils/            # 全局工具函数
│   ├── types/            # 全局类型定义
│   ├── styles/           # 全局样式
│   ├── assets/           # 静态资源
│   ├── plugins/          # 插件配置
│   └── App.vue           # 根组件
├── packages/             # 本地包
│   └── crud/            # CRUD 组件包
├── public/              # 静态文件
├── build/               # 构建配置
├── dist/                # 构建输出
├── node_modules/        # 依赖包
├── package.json         # 项目配置
├── vite.config.ts       # Vite 配置
├── tsconfig.json        # TypeScript 配置
├── uno.config.ts        # UnoCSS 配置
└── eslint.config.js     # ESLint 配置
```

## 后端项目结构 (admin)

```
admin/
├── internal/            # 内部代码，不对外暴露
│   ├── controller/      # 控制器层
│   │   ├── admin/      # 管理端控制器
│   │   └── api/        # API 控制器
│   ├── service/        # 业务逻辑层
│   ├── dao/            # 数据访问层
│   ├── model/          # 数据模型
│   │   ├── entity/     # 数据库实体
│   │   ├── do/         # 数据对象
│   │   └── dto/        # 数据传输对象
│   ├── logic/          # 业务逻辑实现
│   ├── middleware/     # 中间件
│   ├── packed/         # 打包资源
│   └── cmd/            # 命令行工具
├── modules/            # 业务模块
│   ├── pms/           # 生产管理系统
│   ├── inventory/     # 库存管理
│   ├── order/         # 订单管理
│   └── system/        # 系统管理
├── manifest/          # 配置文件目录
│   ├── config/        # 配置文件
│   │   ├── config.yaml # 主配置文件
│   │   ├── config.prod.yaml # 生产环境配置
│   │   └── config.dev.yaml  # 开发环境配置
│   └── docker/        # Docker 配置
├── resource/          # 资源文件
│   ├── public/        # 静态资源
│   ├── template/      # 模板文件
│   └── i18n/          # 国际化文件
├── utility/           # 工具函数
├── hack/              # 开发脚本
├── logs/              # 日志文件
├── temp/              # 临时文件
├── go.mod             # Go 模块文件
├── go.sum             # Go 依赖锁定文件
├── main.go            # 程序入口
└── README.MD          # 项目说明
```

## 文件命名规范

### 前端文件命名

#### Vue 组件文件
- **页面组件**: kebab-case，如 `user-list.vue`
- **公共组件**: PascalCase，如 `UserForm.vue`
- **组件目录**: kebab-case，如 `user-management/`

#### TypeScript 文件
- **类型定义**: kebab-case，如 `user-types.ts`
- **工具函数**: kebab-case，如 `date-utils.ts`
- **服务文件**: kebab-case，如 `user-service.ts`
- **常量文件**: kebab-case，如 `api-constants.ts`

#### 样式文件
- **全局样式**: kebab-case，如 `global-styles.scss`
- **组件样式**: 与组件同名，如 `user-list.scss`

### 后端文件命名

#### Go 文件
- **包名**: 小写，如 `user`, `order`
- **文件名**: 小写+下划线，如 `user_controller.go`
- **结构体**: PascalCase，如 `UserInfo`
- **函数名**: PascalCase（公开）或 camelCase（私有）

#### 配置文件
- **YAML 配置**: kebab-case，如 `config.yaml`
- **环境配置**: 环境后缀，如 `config.prod.yaml`

## 模块组织规范

### 前端模块组织

#### 业务模块结构
```
modules/pms/
├── views/              # 页面组件
│   ├── production-plan/
│   ├── production-order/
│   └── quality-control/
├── components/         # 模块组件
│   ├── charts/
│   ├── forms/
│   └── tables/
├── types/             # 类型定义
│   ├── production.ts
│   └── quality.ts
├── services/          # API 服务
│   ├── production-api.ts
│   └── quality-api.ts
├── stores/            # 状态管理
│   ├── production-store.ts
│   └── quality-store.ts
└── utils/             # 工具函数
    ├── production-utils.ts
    └── quality-utils.ts
```

#### 组件分层
1. **页面组件** (views): 路由对应的页面级组件
2. **业务组件** (components): 特定业务逻辑的组件
3. **通用组件** (common): 可复用的通用组件
4. **基础组件** (base): 最基础的 UI 组件

### 后端模块组织

#### 业务模块结构
```
modules/pms/
├── controller/        # 控制器
├── service/          # 服务接口
├── logic/            # 业务逻辑
├── dao/              # 数据访问
├── model/            # 数据模型
│   ├── entity/       # 实体定义
│   ├── do/           # 数据对象
│   └── dto/          # 传输对象
└── types/            # 类型定义
```

#### 分层架构
1. **Controller 层**: 处理 HTTP 请求，参数验证
2. **Service 层**: 业务逻辑接口定义
3. **Logic 层**: 业务逻辑具体实现
4. **DAO 层**: 数据访问对象
5. **Model 层**: 数据模型定义

## 导入路径规范

### 前端导入规范
```typescript
// 1. Vue 相关导入
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'

// 2. 第三方库导入
import { ElMessage, ElMessageBox } from 'element-plus'
import dayjs from 'dayjs'

// 3. 项目内部导入
import { useCrud, useTable } from '/@/cool'
import { UserService } from '/@/modules/system/services'
import type { UserInfo } from '/@/modules/system/types'

// 4. 相对路径导入
import UserForm from './components/UserForm.vue'
import { formatDate } from '../utils/date-utils'
```

### 后端导入规范
```go
package controller

import (
    // 1. 标准库
    "context"
    "fmt"
    
    // 2. 第三方库
    "github.com/gogf/gf/v2/frame/g"
    "github.com/gogf/gf/v2/net/ghttp"
    
    // 3. 项目内部
    "github.com/imhuso/lookah-erp/admin/internal/service"
    "github.com/imhuso/lookah-erp/admin/internal/model"
    
    // 4. 相对导入
    v1 "github.com/imhuso/lookah-erp/admin/api/v1"
)
```

## 配置文件组织

### 前端配置
- **vite.config.ts**: Vite 构建配置
- **tsconfig.json**: TypeScript 编译配置
- **uno.config.ts**: UnoCSS 样式配置
- **eslint.config.js**: 代码检查配置
- **package.json**: 项目依赖和脚本

### 后端配置
- **config.yaml**: 主配置文件
- **config.dev.yaml**: 开发环境配置
- **config.prod.yaml**: 生产环境配置
- **go.mod**: Go 模块配置
- **Dockerfile**: 容器化配置

## 资源文件组织

### 静态资源
- **images/**: 图片资源，按模块分类
- **icons/**: 图标资源，SVG 格式优先
- **fonts/**: 字体文件
- **docs/**: 文档资源

### 模板文件
- **email/**: 邮件模板
- **report/**: 报表模板
- **export/**: 导出模板