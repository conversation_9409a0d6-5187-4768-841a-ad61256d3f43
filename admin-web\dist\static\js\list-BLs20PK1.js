import{i as f,h as G}from"./index-BtOcqcNl.js";import{a as I}from"./index-D95m1iJL.js";import{u as U}from"./hook-BXXLBjrX.js";import{c as g,e as R,U as S,q as w,w as i,h as r,i as n,G as B,H as j,v as z,j as y,t as x,o as k}from"./.pnpm-hVqhwuVC.js";const A=g({name:"dict-list"}),J=g({...A,setup(F){const{service:a}=I(),{ViewGroup:b}=U({label:"类型",title:"字典列表",service:a.dict.type,onSelect(t){C({typeId:t.id,page:1})},onEdit(){return{width:"500px",props:{labelWidth:"60px"},items:[{label:"名称",prop:"name",component:{name:"el-input",props:{maxlength:20}},required:!0},{label:"Key",prop:"key",component:{name:"el-input",props:{maxlength:20}},required:!0}]}}}),d=f.useTable({contextMenu:["refresh",t=>{var e;return{label:"新增",hidden:!((e=a.dict.info._permission)!=null&&e.add),callback(o){v(t),o()}}},"edit","delete","order-asc","order-desc"],columns:[{type:"selection"},{label:"名称",prop:"name",align:"left",minWidth:200},{label:"排序",prop:"orderNum",sortable:"desc",width:100},{label:"备注",prop:"remark",showOverflowTooltip:!0,minWidth:150},{label:"创建时间",prop:"createTime",sortable:"custom",minWidth:160},{label:"更新时间",prop:"updateTime",sortable:"custom",minWidth:160},{type:"op",width:250,buttons:["slot-btn","edit","delete"]}]}),h=f.useUpsert({dialog:{width:"600px"},props:{labelWidth:"100px"},items:[{label:"上级节点",prop:"parentId",component:{name:"el-tree-select",props:{data:R(()=>{var o;const t=S((o=d.value)==null?void 0:o.data);function e(l,p){var c;l.id&&l.id===((c=h.value)==null?void 0:c.getForm("id"))&&(p=!0),p&&(l.disabled=!0),l.children&&l.children.forEach(m=>{e(m,p)})}return e({children:t},!1),t}),props:{label:"name",value:"id",children:"children",disabled:"disabled"},clearable:!0,filterable:!0,"default-expand-all":!0,"check-strictly":!0}}},{label:"名称",prop:"name",required:!0,component:{name:"el-input"}},{label:"排序",prop:"orderNum",value:1,component:{name:"el-input-number",props:{min:1}}},{label:"备注",prop:"remark",component:{name:"el-input",props:{type:"textarea",rows:4}}}],onSubmit(t,{next:e}){var o,l;e({...t,typeId:(l=(o=b.value)==null?void 0:o.selected)==null?void 0:l.id})}}),u=f.useCrud({service:a.dict.info,onRefresh(t,{render:e}){a.dict.info.list({...t,page:void 0,size:void 0}).then(o=>{e(G(o,t.sort))})}});function C(t){var e;(e=u.value)==null||e.refresh(t)}function T(t,e){var o;e!=null&&e.property&&t.children&&((o=d.value)==null||o.toggleRowExpansion(t))}function v(t){var e;(e=u.value)==null||e.rowAppend({parentId:t.id,orderNum:1})}return(t,e)=>{const o=n("cl-refresh-btn"),l=n("cl-add-btn"),p=n("cl-multi-delete-btn"),c=n("cl-flex1"),m=n("cl-search-key"),_=n("cl-row"),W=n("el-button"),N=n("cl-table"),V=n("cl-upsert"),q=n("cl-crud"),D=n("cl-view-group"),E=j("permission");return k(),w(D,{ref_key:"ViewGroup",ref:b},{"item-name":i(({item:s})=>[y(x(s.name)+" - "+x(s.key),1)]),right:i(()=>[r(q,{ref_key:"Crud",ref:u},{default:i(()=>[r(_,null,{default:i(()=>[r(o),r(l),r(p),r(c),r(m,{placeholder:"搜索名称"})]),_:1}),r(_,null,{default:i(()=>[r(N,{ref_key:"Table",ref:d,"row-key":"id",onRowClick:T},{"slot-btn":i(({scope:s})=>[B((k(),w(W,{text:"",bg:"",type:"success",onClick:H=>v(s.row)},{default:i(()=>[y(" 新增 ")]),_:2},1032,["onClick"])),[[E,z(a).dict.info.permission.add]])]),_:1},512)]),_:1}),r(_,null,{default:i(()=>[r(c)]),_:1}),r(V,{ref_key:"Upsert",ref:h},null,512)]),_:1},512)]),_:1},512)}}});export{J as default};
