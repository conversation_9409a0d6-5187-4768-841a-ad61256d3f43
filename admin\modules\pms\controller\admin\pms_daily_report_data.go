package admin

import (
	"context"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/imhuso/lookah-erp/admin/modules/pms/model"
	"github.com/imhuso/lookah-erp/admin/modules/pms/service"
	"github.com/imhuso/lookah-erp/admin/yc"
)

type PmsDailyReportDataController struct {
	*yc.Controller
}

// GetEmployeeWorkingReq 获取员工工时统计
type GetEmployeeWorkingReq struct {
	g.Meta `path:"/getEmployeeWorking" method:"POST" summary:"获取员工工时统计" tags:"获取员工工时统计"`
	*model.QueryVo
}

// GetEmployeeWorking 控制器
func (c *PmsDailyReportDataController) GetEmployeeWorking(ctx context.Context, req *GetEmployeeWorkingReq) (res *yc.BaseRes, err error) {
	data, err := service.NewPmsDailyReportDataService().GetEmployeeWorking(ctx, req.QueryVo)
	if err != nil {
		g.Log().Error(ctx, "GetEmployeeWorking error", err)
		return yc.Fail(err.Error()), err
	}
	return yc.Ok(data), nil
}

// ExportDailyReportDataReq 导出每日生产数据
type ExportDailyReportDataReq struct {
	g.Meta           `path:"/export" method:"GET" summary:"导出标准产能汇总" tags:"标准产能"`
	DateRange        []string `json:"dateRange"`
	OrderId          int64    `json:"order_id"`
	KeyWord          string   `json:"keyWord"`
	WorkshopSection  int      `json:"workshop_section"`
	ProductionStages int      `json:"production_stages"`
	WorkshopId       int64    `json:"workshop_id"`
}

// ExportDailyReportData 导出每日生产数据
func (c *PmsDailyReportDataController) ExportDailyReportData(ctx context.Context, req *ExportDailyReportDataReq) (res *yc.BaseRes, err error) {
	fileName, content, err := service.NewPmsDailyReportDataService().ExportDailyReportData(ctx, req.DateRange, req.OrderId,
		req.KeyWord, req.WorkshopSection, req.ProductionStages, req.WorkshopId)
	if err != nil {
		return nil, err
	}

	response := g.RequestFromCtx(ctx).Response
	// 设置响应头
	response.Header().Set("Content-Disposition", "attachment; filename="+fileName)
	response.Header().Set("Content-Type", "application/octet-stream")

	// 返回 Excel 文件内容
	response.WriteExit(content)
	return nil, nil
}

// ImportDailyReportDataReq 导入生产日报数据
type ImportDailyReportDataReq struct {
	g.Meta          `path:"/importDailyReportData" method:"POST" summary:"导入数据" tags:"导入数据"`
	DailyReportData []*model.PmsDailyReportData `json:"daily_report_data"`
}

// ImportDailyReportData 控制器
func (c *PmsDailyReportDataController) ImportDailyReportData(ctx context.Context, req *ImportDailyReportDataReq) (res *yc.BaseRes, err error) {
	data, err := service.NewPmsDailyReportDataService().ImportDailyReportData(ctx, req.DailyReportData)
	if err != nil {
		g.Log().Error(ctx, "ImportData error", err)
		return yc.Fail(err.Error()), err
	}
	return yc.Ok(data), nil
}

func init() {
	var pmsDailyReportDataController = &PmsDailyReportDataController{
		&yc.Controller{
			Prefix:  "/admin/pms/daily_report_data",
			Api:     []string{"Add", "Delete", "Update", "Info", "List", "Page"},
			Service: service.NewPmsDailyReportDataService(),
		},
	}
	// 注册路由
	yc.RegisterController(pmsDailyReportDataController)
}
