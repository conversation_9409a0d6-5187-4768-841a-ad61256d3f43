<script setup lang="ts">
import { Plus, Warning } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { cloneDeep } from 'lodash-es'
import { nextTick, reactive, ref, watchEffect } from 'vue'
import { useStore } from '/$/base/store'
import OutboundTable from '/$/pms/views/material/outbound/OutboundTable.vue'
import { service, useCool } from '/@/cool'

// 常量定义
const WAREHOUSE_ID = 88 // 默认仓位ID

// 定义PO列表项类型
interface PoItem {
  po: string
}

// 定义合同列表项类型
interface ContractItem {
  id: number
  material_code: string
  material_name?: string
  quantity?: number
  unit_price?: number
  [key: string]: any
}

// 物料列表项类型
interface MaterialItem {
  quantity: number | string | undefined
  contractId: number | string | undefined
  materialId: number | string | undefined
  // supplierId: number
  createTime: string
  status: number
  remark: string
  expectedInbound: number | string | undefined
  receivedQuantity: number | string | undefined
  name: string
  model: string
  size: string
  material: string
  unit: string
  process: string
  coverColor: string
  orderId: number
  code: string
  // 补退货相关字段
  returnOrderQuantity?: number
  restockingQty?: number
}

// PO列表
const poList = ref<PoItem[]>([])
const contractList = ref<ContractItem[]>([]) // 合同列表
const availableMaterials = ref<any[]>([]) // 可选物料列表
const poLoading = ref(false)
const contractLoading = ref(false)
const supplier_id = ref<number>(0)
// 服务和路由
const { router } = useCool()
const { user } = useStore()
const isEdit = ref(false)
const isLoadingData = ref(false) // 标记是否正在加载数据

// 检查用户是否为供应商账号
watchEffect(() => {
  // 不在 watchEffect 中使用 async/await，改为直接调用
  checkUserRole()
  const id = router.currentRoute.value.query.id
  // 如果存在订单id，则为编辑订单
  if (id) {
    isEdit.value = true
    fetchById(Number(id))
  }
})
function fetchById(id: number) {
  console.log('fetchById 开始加载数据')
  isLoadingData.value = true // 开始加载数据
  service.pms.delivery_note
    .getDeliveryInfo({ id })
    .then(async (res: any) => {
      console.log('fetchById 收到数据:', { inboundType: res.inboundType, po: res.po })

      // 填充表单基本信息 - 先设置入库类型，避免触发不必要的事件
      deliveryForm.inboundType = res.inboundType || 1 // 先设置入库类型
      console.log('设置入库类型:', deliveryForm.inboundType)

      deliveryForm.id = res.id
      deliveryForm.supplierId = res.supplier_id
      deliveryForm.supplierOrderNo = res.supplier_order_no

      // 只有采购入库才设置PO字段，避免触发不必要的事件
      if (deliveryForm.inboundType === 1) {
        console.log('采购入库，设置PO字段:', res.po)
        deliveryForm.po = res.po
      }
      else {
        console.log('补退货入库，不设置PO字段')
      }

      deliveryForm.voucher = res.voucher
      deliveryForm.remark = res.remark
      deliveryForm.orderId = res.orderId
      deliveryForm.no = res.no

      // 根据入库类型处理数据
      if (deliveryForm.inboundType === 1) {
        // 采购入库：获取合同列表
        if (res.po && supplier_id.value) {
          await fetchContractListForEditAsync(res.po)
        }
      }

      // 填充物料列表，根据入库类型使用不同的数据结构
      const tempMaterialList = res.products?.map((e: any) => {
        const src = cloneDeep(e)

        if (deliveryForm.inboundType === 1) {
          // 采购入库：从 contract 获取数据
          return {
            quantity: src.quantity,
            contractId: src.contractId,
            materialId: src.materialId,
            createTime: src.createTime || '',
            status: src.status || 0,
            remark: src.remark || '',
            expectedInbound: src.contract?.expectedQuantity || 0,
            receivedQuantity: src.contract?.receivedQuantity || 0,
            name: src.contract?.material?.name || src.name,
            model: src.contract?.material?.model || src.model,
            size: src.contract?.material?.size || src.size,
            material: src.contract?.material?.material || src.material,
            unit: src.contract?.material?.unit || src.unit,
            process: src.contract?.material?.process || src.process,
            code: src.contract?.material?.code || src.code,
            coverColor: src.contract?.material?.coverColor || src.coverColor || '',
            orderId: src.contract?.purchaseId || src.orderId,
          }
        }
        else {
          // 补退货入库：需要从关联的出库单产品信息获取退货数量和已补货数量
          // 这些数据应该在后端通过关联查询获取，而不是从物料表的字段
          const returnOrderQuantity = src.returnOrderQuantity || src.expectedInbound || 0
          const restockingQty = src.restockingQty || src.receivedQty || 0

          return {
            quantity: src.quantity,
            contractId: src.contractId || 0,
            materialId: src.materialId,
            createTime: src.createTime || '',
            status: src.status || 0,
            remark: src.remark || '',
            expectedInbound: returnOrderQuantity, // 对于补退货入库，这里存储退货数量
            receivedQuantity: restockingQty, // 对于补退货入库，这里存储已补货数量
            name: src.name || '',
            model: src.model || '',
            size: src.size || '',
            material: src.material || '',
            unit: src.unit || '',
            process: src.process || '',
            code: src.code || '',
            coverColor: src.coverColor || '',
            orderId: src.orderId || 0,
            // 补退货特有字段
            returnOrderQuantity,
            restockingQty,
          }
        }
      }) || []

      // 根据入库类型处理可选物料列表
      if (deliveryForm.inboundType === 1) {
        // 采购入库：将当前送货单中的物料添加到可选物料列表中
        const currentMaterials = tempMaterialList.map((item: any) => ({
          id: item.materialId,
          code: item.code,
          name: item.name,
          model: item.model,
          size: item.size,
          material: item.material,
          unit: item.unit,
          process: item.process,
          coverColor: item.coverColor,
          contractId: item.contractId,
          expectedQuantity: item.expectedInbound,
          receivedQuantity: item.receivedQuantity,
          purchaseId: item.orderId,
        }))

        // 合并当前物料和PO合同物料，去重
        const allMaterials = [...currentMaterials]
        availableMaterials.value.forEach((material) => {
          if (!allMaterials.some(m => m.id === material.id)) {
            allMaterials.push(material)
          }
        })
        availableMaterials.value = allMaterials
      }
      else {
        // 补退货入库：直接设置可选物料列表
        availableMaterials.value = tempMaterialList.map((item: any) => ({
          id: item.materialId,
          code: item.code,
          name: item.name,
          model: item.model,
          size: item.size,
          material: item.material,
          unit: item.unit,
          process: item.process,
          coverColor: item.coverColor,
          returnOrderQuantity: item.returnOrderQuantity,
          restockingQty: item.restockingQty,
          orderId: item.orderId,
        }))
      }

      // 最后赋值给 materialList
      materialList.value = tempMaterialList
    })
    .catch((err: any) => {
      ElMessage.error({ message: err.message || '获取送货单信息失败' })
    })
    .finally(() => {
      isLoadingData.value = false // 数据加载完成
    })
}

// 检查用户角色并获取供应商信息
async function checkUserRole() {
  user.info = user.info || {}
  const user_id = user?.info?.id
  service.pms.supplier_account
    .request({
      url: '/getUserRole',
      method: 'POST',
      data: {
        user_id,
      },
    })
    .then((res: any) => {
      const roleData: string[] = []
      res && res.forEach((row: any) => {
        roleData.push(row.name)
      })
      if (roleData.includes('供应商')) {
        fetchSupplierInfo()
      }
      else {
        ElMessage.error('本页面只供供应商账号访问！')
        router.push('/pms/delivery_note')
      }
    })
    .catch((_e) => {
      ElMessage.error('查询角色信息失败！')
      router.push('/pms/delivery_note')
    })
}

// 获取供应商信息
function fetchSupplierInfo() {
  service.pms.supplier_account
    .request({
      url: '/getUserBindSupplier',
      method: 'GET',
      params: {
        user_id: user?.info?.id,
      },
    })
    .then((res) => {
      supplier_id.value = res.supplier_id // 供应商ID
      deliveryForm.supplierId = supplier_id.value // 设置表单中的供应商ID
      fetchPoList(res.supplier_id) // 获取PO列表
    })
    .catch((_e) => {
      // ElMessage.error("获取供应商信息失败");
      router.push('/pms/delivery_note')
    })
}

// 获取PO列表
function fetchPoList(supplier_id: number) {
  // 只有采购入库才需要获取PO列表
  if (deliveryForm.inboundType !== 1) {
    console.log('非采购入库，跳过PO列表获取')
    return
  }

  console.log('采购入库，开始获取PO列表')
  poLoading.value = true
  service.pms.purchase.contract
    .request({
      url: '/getUnfinishedPo',
      method: 'GET',
      params: {
        supplier_id,
      },
    })
    .then((res) => {
      if (res) {
        // 处理API返回的数据，确保格式正确
        if (Array.isArray(res)) {
          // 数据已经是数组格式
          poList.value = res.map((item: any) => {
            // 如果数据结构不是{po: string}格式，进行转换
            if (typeof item === 'string') {
              return { po: item }
            }
            else if (item && typeof item.po === 'string') {
              return item
            }
            else if (item && item.po_number) {
              return { po: item.po_number }
            }
            else {
              // 尝试找到可能包含PO号的字段
              for (const key in item) {
                if (
                  typeof item[key] === 'string'
                  && /^[A-Z0-9]+$/i.test(item[key])
                ) {
                  return { po: item[key] }
                }
              }
              return { po: JSON.stringify(item) }
            }
          })
        }
        else if (typeof res === 'object') {
          // 数据可能在某个嵌套的字段中
          for (const key in res) {
            if (Array.isArray(res[key])) {
              poList.value = res[key].map((item: any) => {
                if (typeof item === 'string') {
                  return { po: item }
                }
                else if (item && typeof item.po === 'string') {
                  return item
                }
                else {
                  return { po: JSON.stringify(item) }
                }
              })
              break
            }
          }
        }

        ElMessage.success(`已加载${poList.value.length}个PO号`)
      }
      poLoading.value = false
    })
    .catch((_e) => {
      ElMessage.error('获取PO列表失败')
      poLoading.value = false
    })
}

// 表单数据
const deliveryForm = reactive({
  id: '', // 送货单ID
  no: '', // 送货单编号
  po: '', // 送货单PO号
  remark: '', // 送货单备注
  voucher: '', // 送货单图片URL
  orderId: '', // 订单ID
  supplierId: supplier_id.value, // 供应商ID
  supplierOrderNo: '', // 供应商单号
  inboundType: 1, // 入库类型：1-采购入库，9-补退货入库
  // warehouseId: WAREHOUSE_ID, // 仓位ID，默认为88
})

// 物料列表 - 初始为空，只有选择PO后才会填充
const materialList = ref<MaterialItem[]>([])

// 对话框相关状态
const showModeDialog = ref(false)
const selectedPo = ref('')
const showOutboundSelector = ref(false) // 显示退货出库单选择器
const tableKey = ref(0) // 用于强制刷新表格

// 处理图片上传成功
function handleUploadSuccess(data: any) {
  deliveryForm.voucher = data.url || data
}

// 删除物料行
function removeMaterialRow(index: number) {
  if (materialList.value.length > 0) {
    materialList.value.splice(index, 1)
  }
}

// 添加空白物料行
function addEmptyMaterialRow() {
  const emptyRow: MaterialItem = {
    quantity: undefined,
    contractId: undefined,
    materialId: undefined,
    createTime: '',
    status: 0,
    remark: '',
    expectedInbound: undefined,
    receivedQuantity: undefined,
    name: '',
    model: '',
    size: '',
    material: '',
    unit: '',
    process: '',
    coverColor: '',
    orderId: 0,
    code: '',
    // 补退货相关字段
    returnOrderQuantity: undefined,
    restockingQty: undefined,
  }
  materialList.value.push(emptyRow)
}

// 检查物料是否已被其他行选择
function isMaterialSelected(materialId: any, currentRow: MaterialItem) {
  return materialList.value.some(row =>
    row !== currentRow && row.materialId === materialId,
  )
}

// 获取数量输入框的最大值
function getMaxQuantity(row: MaterialItem) {
  if (deliveryForm.inboundType === 1) {
    // 采购入库：最大值为剩余入库数量
    const expectedInbound = Number(row.expectedInbound) || 0
    return Math.max(expectedInbound, 0)
  }
  else if (deliveryForm.inboundType === 9) {
    // 补退货入库：最大值为剩余可补数量
    const remainingQty = (row.returnOrderQuantity || 0) - (row.restockingQty || 0)
    return Math.max(remainingQty, 0)
  }
  return 999999 // 默认值
}

// 处理物料选择
function handleMaterialSelect(materialId: any, row: MaterialItem) {
  const selectedMaterial = availableMaterials.value.find(m => m.id === materialId)
  if (selectedMaterial) {
    // 自动填充物料信息
    row.name = selectedMaterial.name
    row.model = selectedMaterial.model
    row.size = selectedMaterial.size
    row.material = selectedMaterial.material
    row.unit = selectedMaterial.unit
    row.process = selectedMaterial.process
    row.code = selectedMaterial.code
    row.coverColor = selectedMaterial.coverColor || ''

    if (deliveryForm.inboundType === 1) {
      // 采购入库：填充合同相关字段
      if (selectedMaterial.contractId) {
        row.contractId = selectedMaterial.contractId
        row.expectedInbound = selectedMaterial.expectedQuantity
        row.receivedQuantity = selectedMaterial.receivedQuantity
        row.orderId = selectedMaterial.purchaseId
      }
    }
    else if (deliveryForm.inboundType === 9) {
      // 补退货入库：填充退货相关字段
      row.returnOrderQuantity = selectedMaterial.returnOrderQuantity
      row.restockingQty = selectedMaterial.restockingQty || 0
      row.orderId = selectedMaterial.orderId
    }
  }
}

// 手动模式下获取合同物料列表
function fetchContractListForManualMode(po: string) {
  if (!po || !supplier_id.value) {
    return
  }
  contractLoading.value = true
  service.pms.purchase.contract
    .request({
      url: '/getContractListByPoAndSupplierId',
      method: 'GET',
      params: {
        po,
        supplier_id: supplier_id.value,
      },
    })
    .then((res) => {
      if (res && Array.isArray(res)) {
        contractList.value = res
        // 将合同物料转换为可选物料格式
        availableMaterials.value = res.map(contract => ({
          id: contract.materialId,
          code: contract.material.code,
          name: contract.material.name,
          model: contract.material.model,
          size: contract.material.size,
          material: contract.material.material,
          unit: contract.material.unit,
          process: contract.material.process,
          coverColor: contract.material.coverColor || '',
          contractId: contract.id,
          expectedQuantity: contract.expectedQuantity,
          receivedQuantity: contract.receivedQuantity,
          purchaseId: contract.purchaseId,
        }))
        ElMessage.success(`已加载${availableMaterials.value.length}个可选物料`)
      }
      else {
        contractList.value = []
        availableMaterials.value = []
        ElMessage.warning('未找到相关物料')
      }
      contractLoading.value = false
    })
    .catch((_e) => {
      ElMessage.error('获取物料列表失败')
      contractList.value = []
      availableMaterials.value = []
      contractLoading.value = false
    })
}

// 处理模式选择
function handleModeSelection(mode: 'auto' | 'manual') {
  showModeDialog.value = false

  if (mode === 'auto') {
    // 自动填充模式 - 使用现有逻辑
    fetchContractList(selectedPo.value)
  }
  else {
    // 手动输入模式
    if (isEdit.value) {
      // 编辑模式：保留当前物料，并更新可选物料列表
      updateAvailableMaterialsForEdit(selectedPo.value)
    }
    else {
      // 新增模式：创建10行空白行并获取合同物料列表
      materialList.value = []
      for (let i = 0; i < 10; i++) {
        addEmptyMaterialRow()
      }
      // 获取合同物料列表作为待选物料
      fetchContractListForManualMode(selectedPo.value)
    }
  }
}

// 取消对话框选择
function cancelModeSelection() {
  showModeDialog.value = false
  // 重置PO选择
  deliveryForm.po = ''
  selectedPo.value = ''
}

// 提交表单
function submitForm() {
  // 表单验证
  if (deliveryForm.inboundType === 1 && !deliveryForm.po) {
    ElMessage.error('请选择送货单PO号')
    return
  }

  if (deliveryForm.inboundType === 9 && materialList.value.length === 0) {
    ElMessage.error('请选择退货出库单')
    return
  }

  // 过滤出有效的物料行（已选择物料且填写了数量）
  const validMaterials = materialList.value.filter((item) => {
    // 必须选择了物料
    if (!item.materialId)
      return false

    // 必须填写了有效数量
    const qty = Number(item.quantity)
    if (item.quantity === undefined || item.quantity === '' || Number.isNaN(qty) || qty === 0) {
      return false
    }

    // 补退货入库时验证数量不能超过剩余可补数量
    if (deliveryForm.inboundType === 9) {
      const remainingQty = (item.returnOrderQuantity || 0) - (item.restockingQty || 0)
      if (qty > remainingQty) {
        ElMessage.error(`物料 ${item.name} 的补货数量不能超过剩余可补数量 ${remainingQty}`)
        return false
      }
    }

    return true
  })

  // 检查是否有有效的物料信息
  if (validMaterials.length === 0) {
    ElMessage.error('请至少选择一个物料并填写数量')
    return
  }

  // 设置订单ID（如果需要）
  if (validMaterials.length > 0) {
    if (deliveryForm.inboundType === 1) {
      // 采购入库：取第一个有效物料的合同ID作为订单ID
      deliveryForm.orderId = String(validMaterials[0].orderId)
    }
    else if (deliveryForm.inboundType === 9) {
      // 补退货入库：如果表单中没有orderId，则从物料中获取
      if (!deliveryForm.orderId && validMaterials[0].orderId) {
        deliveryForm.orderId = String(validMaterials[0].orderId)
      }
    }
  }
  // 准备提交数据，只提交有效的物料
  const submitData = {
    delivery_note: deliveryForm,
    materials: validMaterials,
  }
  if (isEdit.value) {
    service.pms.delivery_note
      .update(submitData)
      .then((res) => {
        ElMessage.success('修改成功')
        router.push('/pms/delivery_note') // 返回上一页
      })
      .catch((err) => {
        ElMessage.error(err.message || '修改失败')
      })
  }
  else {
    service.pms.delivery_note
      .add(submitData)
      .then((res) => {
        ElMessage.success('创建成功')
        router.push('/pms/delivery_note') // 返回上一页
      })
      .catch((err) => {
        ElMessage.error(err.message || '创建失败')
      })
  }
}

// 选择PO号时触发
function selectPo(val: string) {
  console.log('selectPo 被调用:', {
    val,
    inboundType: deliveryForm.inboundType,
    isLoadingData: isLoadingData.value,
    isEdit: isEdit.value,
  })

  if (val) {
    // 只有采购入库且不在数据加载期间才显示模式选择对话框
    if (deliveryForm.inboundType === 1 && !isLoadingData.value) {
      console.log('显示模式选择对话框')
      selectedPo.value = val
      showModeDialog.value = true
    }
    else {
      console.log('不显示对话框，原因:', {
        inboundType: deliveryForm.inboundType,
        isLoadingData: isLoadingData.value,
      })
    }
  }
  else {
    // 清空列表
    contractList.value = []
    if (!isEdit.value) {
      // 只在新增模式下清空物料列表
      materialList.value = []
    }
  }
}

// 根据po获取合同列表
function fetchContractList(po: string) {
  if (!po || !supplier_id.value) {
    return
  }
  contractLoading.value = true
  service.pms.purchase.contract
    .request({
      url: '/getContractListByPoAndSupplierId',
      method: 'GET',
      params: {
        po,
        supplier_id: supplier_id.value,
      },
    })
    .then((res) => {
      if (res && Array.isArray(res)) {
        contractList.value = res
        // 填充可选物料列表
        availableMaterials.value = res.map(contract => ({
          id: contract.materialId,
          code: contract.material.code,
          name: contract.material.name,
          model: contract.material.model,
          size: contract.material.size,
          material: contract.material.material,
          unit: contract.material.unit,
          process: contract.material.process,
          coverColor: contract.material.coverColor || '',
          contractId: contract.id,
          expectedQuantity: contract.expectedQuantity,
          receivedQuantity: contract.receivedQuantity,
          purchaseId: contract.purchaseId,
        }))
        // 自动填充物料列表
        autoFillMaterialList(res)
        ElMessage.success(`已加载${contractList.value.length}个物料`)
      }
      else {
        contractList.value = []
        materialList.value = []
        availableMaterials.value = []
        ElMessage.warning('未找到相关物料')
      }
      contractLoading.value = false
    })
    .catch((_e) => {
      ElMessage.error('获取物料列表失败')
      contractList.value = []
      materialList.value = []
      availableMaterials.value = []
      contractLoading.value = false
    })
}

// 自动填充物料列表
function autoFillMaterialList(contracts: ContractItem[]) {
  materialList.value = contracts.map(contract => ({
    quantity: undefined, // 数量，不设初始值，需要用户填写
    contractId: contract.id,
    materialId: contract.materialId,
    createTime: '', // 创建时间
    status: 0, // 状态
    remark: '', // 备注
    expectedInbound: contract.expectedQuantity || 0,
    receivedQuantity: contract.receivedQuantity || 0,
    name: contract.material.name,
    model: contract.material.model,
    size: contract.material.size,
    material: contract.material.material,
    unit: contract.material.unit,
    process: contract.material.process,
    code: contract.material.code,
    coverColor: contract.material.coverColor || '',
    orderId: contract.purchaseId,
  }))
}

// 编辑模式下获取合同列表（异步版本）
async function fetchContractListForEditAsync(po: string) {
  if (!po || !supplier_id.value) {
    return
  }
  contractLoading.value = true
  try {
    const res = await service.pms.purchase.contract.request({
      url: '/getContractListByPoAndSupplierId',
      method: 'GET',
      params: {
        po,
        supplier_id: supplier_id.value,
      },
    })

    if (res && Array.isArray(res)) {
      contractList.value = res
      // 编辑模式下也需要填充可选物料列表，以便物料选择框正常工作
      availableMaterials.value = res.map((contract) => {
        const material = {
          id: contract.materialId,
          code: contract.material.code,
          name: contract.material.name,
          model: contract.material.model,
          size: contract.material.size,
          material: contract.material.material,
          unit: contract.material.unit,
          process: contract.material.process,
          coverColor: contract.material.coverColor || '',
          contractId: contract.id,
          expectedQuantity: contract.expectedQuantity,
          receivedQuantity: contract.receivedQuantity,
          purchaseId: contract.purchaseId,
        }

        return material
      })
    }
    else {
      contractList.value = []
      availableMaterials.value = []
    }
  }
  catch (_e) {
    contractList.value = []
    availableMaterials.value = []
  }
  finally {
    contractLoading.value = false
  }
}

// 编辑模式下更新可选物料列表
async function updateAvailableMaterialsForEdit(po: string) {
  if (!po || !supplier_id.value) {
    return
  }
  contractLoading.value = true

  try {
    // 获取新PO的合同物料
    const res = await service.pms.purchase.contract.request({
      url: '/getContractListByPoAndSupplierId',
      method: 'GET',
      params: {
        po,
        supplier_id: supplier_id.value,
      },
    })

    let newPoMaterials = []
    if (res && Array.isArray(res)) {
      contractList.value = res
      newPoMaterials = res.map(contract => ({
        id: contract.materialId,
        code: contract.material.code,
        name: contract.material.name,
        model: contract.material.model,
        size: contract.material.size,
        material: contract.material.material,
        unit: contract.material.unit,
        process: contract.material.process,
        coverColor: contract.material.coverColor || '',
        contractId: contract.id,
        expectedQuantity: contract.expectedQuantity,
        receivedQuantity: contract.receivedQuantity,
        purchaseId: contract.purchaseId,
      }))
    }
    else {
      contractList.value = []
    }

    // 更新可选物料列表为新PO的物料
    availableMaterials.value = newPoMaterials

    // 清空当前物料列表，创建空白行供用户选择新PO的物料
    materialList.value = []

    // 创建10行空白行，与新增模式保持一致
    for (let i = 0; i < 10; i++) {
      addEmptyMaterialRow()
    }

    if (newPoMaterials.length > 0) {
      ElMessage.success(`已切换到新PO，加载了${newPoMaterials.length}个可选物料，创建了10行空白行`)
    }
    else {
      ElMessage.warning('新PO暂无物料，已创建10行空白行')
    }
  }
  catch (_e) {
    contractList.value = []
    availableMaterials.value = []
    // 如果获取新PO物料失败，清空物料列表并创建10行空白行
    materialList.value = []
    for (let i = 0; i < 10; i++) {
      addEmptyMaterialRow()
    }
    ElMessage.error('获取新PO物料失败，已创建10行空白行')
  }
  finally {
    contractLoading.value = false
  }
}

// 处理入库类型切换
function handleInboundTypeChange(type: number) {
  if (type === deliveryForm.inboundType) {
    return
  }

  // 如果当前有物料数据，提示用户
  if (materialList.value.length > 0) {
    ElMessage.warning('切换入库类型将清空当前物料列表和表单信息')
  }

  // 立即设置新的入库类型
  deliveryForm.inboundType = type

  // 保留基本信息
  const supplierId = deliveryForm.supplierId
  const id = deliveryForm.id

  // 重置表单字段
  deliveryForm.no = ''
  deliveryForm.po = ''
  deliveryForm.remark = ''
  deliveryForm.voucher = ''
  deliveryForm.orderId = ''
  deliveryForm.supplierOrderNo = ''

  // 恢复保留的信息
  deliveryForm.supplierId = supplierId
  deliveryForm.id = id
  // deliveryForm.warehouseId = WAREHOUSE_ID // 重置为默认仓位

  // 清空相关数据
  materialList.value.length = 0 // 使用 length = 0 强制清空数组
  availableMaterials.value.length = 0
  contractList.value.length = 0
  selectedPo.value = ''

  // 强制触发响应式更新
  tableKey.value++

  nextTick(() => {
    // 根据入库类型显示相应提示
    if (type === 1) {
      ElMessage.info('已切换到采购入库模式，请选择PO号')
    }
    else if (type === 9) {
      ElMessage.info('已切换到补退货入库模式，请选择退货出库单')
    }
  })
}

// 选择退货出库单
function handleSelectOutboundOrder() {
  showOutboundSelector.value = true
}

// 处理退货出库单选择
async function handleOutboundOrderSelected(params: any) {
  // 从参数中提取实际的退货出库单数据
  const outboundOrder = params.row || params

  if (!outboundOrder || !outboundOrder.products || outboundOrder.products.length === 0) {
    ElMessage.error('该退货出库单没有物料')
    return
  }

  // 如果当前已有物料数据，询问是否清空
  if (materialList.value.length > 0) {
    try {
      await ElMessageBox.confirm(
        '当前物料列表中存在已选数据，如果确定，将先清除列表中的数据，是否继续？',
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        },
      )
    }
    catch (error) {
      return // 用户取消操作
    }
  }

  // 清空当前物料列表
  materialList.value = []
  availableMaterials.value = []

  // 设置补退货入库的订单ID（使用退货出库单的ID）
  if (outboundOrder.id) {
    deliveryForm.orderId = String(outboundOrder.id)
  }

  // 将退货出库单的物料转换为可选物料格式
  availableMaterials.value = outboundOrder.products.map((product: any) => ({
    id: product.materialId,
    code: product.code,
    name: product.name,
    model: product.model,
    size: product.size,
    material: product.material,
    unit: product.unit,
    process: product.process,
    coverColor: product.coverColor || '',
    returnOrderQuantity: product.quantity, // 退货数量
    restockingQty: product.restockingQty || 0, // 已补货数量
    orderId: outboundOrder.id || 0, // 设置订单ID
  }))

  // 询问用户是否自动填充所有物料
  try {
    await ElMessageBox.confirm(
      '是否需要自动填充该退货单的全部物料？',
      '提示',
      {
        confirmButtonText: '自动填充',
        cancelButtonText: '手动输入',
        type: 'warning',
        showClose: false,
      },
    )

    // 自动填充所有物料
    autoFillAllMaterials()
  }
  catch (error) {
    // 手动输入模式，创建空白行
    for (let i = 0; i < 10; i++) {
      addEmptyMaterialRow()
    }
  }

  showOutboundSelector.value = false
  ElMessage.success(`已加载退货出库单物料，共${outboundOrder.products.length}个物料`)
}

// 自动填充所有物料
function autoFillAllMaterials() {
  materialList.value = availableMaterials.value.map((material: any) => ({
    quantity: 0, // 默认数量为0，用户需要手动填写
    contractId: undefined,
    materialId: material.id,
    createTime: '',
    status: 0,
    remark: '',
    expectedInbound: undefined,
    receivedQuantity: undefined,
    name: material.name,
    model: material.model,
    size: material.size,
    material: material.material,
    unit: material.unit,
    process: material.process,
    coverColor: material.coverColor,
    orderId: material.orderId,
    code: material.code,
    returnOrderQuantity: material.returnOrderQuantity,
    restockingQty: material.restockingQty,
  }))
}
</script>

<template>
  <div class="delivery-note-add">
    <div class="header-container">
      <h2>{{ isEdit ? '编辑送货单' : '添加送货单' }}</h2>
    </div>

    <el-form label-width="120px" class="delivery-form">
      <!-- 主信息区域 -->
      <div class="form-section">
        <div class="section-title">
          基本信息
        </div>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="入库类型" required>
              <el-radio-group
                :model-value="deliveryForm.inboundType"
                :disabled="isEdit" @update:model-value="(value: number) => handleInboundTypeChange(value)"
              >
                <el-radio :value="1" label="采购入库" />
                <el-radio :value="9" label="补退货入库" />
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row v-if="deliveryForm.inboundType === 1" :gutter="20">
          <el-col :span="12">
            <el-form-item label="送货单PO号" required>
              <el-select
                v-model="deliveryForm.po"
                filterable
                placeholder="请选择送货单PO号"
                style="width: 100%"
                :loading="poLoading"
                clearable
                @change="selectPo"
              >
                <el-option
                  v-for="item in poList"
                  :key="item.po"
                  :label="item.po"
                  :value="item.po"
                />
              </el-select>
              <!-- 提示信息 -->
              <div
                v-if="poList.length === 0"
                style="color: #999; font-size: 12px; margin-top: 5px"
              >
                PO列表为空，请等待加载或刷新页面
              </div>
              <div
                v-else
                style="color: #67c23a; font-size: 12px; margin-top: 5px"
              >
                共加载了 {{ poList.length }} 个PO号
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="送货单号">
              <el-input
                v-model="deliveryForm.supplierOrderNo"
                placeholder="送货单号"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row v-if="deliveryForm.inboundType === 9" :gutter="20">
          <el-col :span="12">
            <el-form-item label="选择退货出库单" required>
              <el-button
                type="warning"
                :disabled="materialList.length > 0"
                @click="handleSelectOutboundOrder"
              >
                选择退货出库单
              </el-button>
              <div
                v-if="materialList.length === 0"
                style="color: #999; font-size: 12px; margin-top: 5px"
              >
                请选择退货出库单以加载物料信息
              </div>
              <div
                v-else
                style="color: #67c23a; font-size: 12px; margin-top: 5px"
              >
                已加载 {{ availableMaterials.length }} 个可选物料
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="送货单备注">
              <el-input
                v-model="deliveryForm.remark"
                type="textarea"
                placeholder="请输入送货单备注"
                :rows="2"
              />
            </el-form-item>
          </el-col>
          <!--          <el-col :span="12"> -->
          <!--            <el-form-item label="送货单图片"> -->
          <!--              <cl-upload -->
          <!--                v-model="deliveryForm.voucher" -->
          <!--                type="image" -->
          <!--                :limit="1" -->
          <!--                :is-private="true" -->
          <!--                :multiple="false" -->
          <!--                @success="handleUploadSuccess" -->
          <!--              /> -->
          <!--            </el-form-item> -->
          <!--          </el-col> -->
        </el-row>
      </div>

      <!-- 物料列表区域 -->
      <div class="form-section">
        <div class="section-header">
          <div class="section-title">
            物料信息
          </div>
          <el-button
            v-if="deliveryForm.po && !isEdit"
            type="primary"
            :loading="contractLoading"
            @click="() => fetchContractList(deliveryForm.po)"
          >
            刷新物料列表
          </el-button>
          <el-button
            v-if="deliveryForm.po && isEdit"
            type="warning"
            :loading="contractLoading"
            @click="() => fetchContractList(deliveryForm.po)"
          >
            重新加载物料（将覆盖当前数据）
          </el-button>
        </div>

        <!-- 当没有物料数据时显示提示 -->
        <div v-if="materialList.length === 0" class="empty-material-tip">
          <el-empty
            :description="isEdit ? '编辑模式：物料信息加载中...' : '请先选择PO号以加载物料信息'"
            :image-size="100"
          />
        </div>

        <!-- 物料表格 -->
        <el-table
          v-else
          :key="`table-${deliveryForm.inboundType}-${tableKey}`"
          :data="materialList"
          border
          size="small"
          class="compact-table"
        >
          <el-table-column
            label="序号"
            type="index"
            width="70"
            align="center"
          />
          <el-table-column label="物料编号" prop="materialId" min-width="150">
            <template #default="{ row }">
              <!-- 所有模式都显示选择框 -->
              <el-select
                v-model="row.materialId"
                filterable
                placeholder="请选择物料"
                size="small"
                style="width: 100%"
                @change="(val: any) => handleMaterialSelect(val, row)"
              >
                <el-option
                  v-for="material in availableMaterials"
                  :key="material.id"
                  :label="`${material.code}/${material.name}`"
                  :value="material.id"
                  :disabled="isMaterialSelected(material.id, row)"
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="数量" prop="quantity" min-width="120">
            <template #header>
              <span class="required-field">数量</span>
            </template>
            <template #default="{ row }">
              <el-input-number
                v-model="row.quantity"
                :min="0"
                :max="getMaxQuantity(row)"
                :precision="2"
                style="width: 100%"
                placeholder="请输入数量"
              />
              <!--              <div -->
              <!--                v-if="row.quantity === undefined || row.quantity === ''" -->
              <!--                style="color: #f56c6c; font-size: 12px; margin-top: 5px" -->
              <!--              > -->
              <!--                请输入数量 -->
              <!--              </div> -->
            </template>
          </el-table-column>
          <el-table-column label="备注" prop="remark" min-width="200">
            <template #default="{ row }">
              <el-input v-model="row.remark" placeholder="请输入备注" />
            </template>
          </el-table-column>
          <el-table-column label="合同ID" prop="contractId" min-width="50" />
          <el-table-column
            v-if="deliveryForm.inboundType === 1"
            label="剩余入库数量"
            prop="expectedInbound"
            min-width="70"
          />
          <el-table-column
            v-if="deliveryForm.inboundType === 1"
            label="已收数量"
            prop="receivedQuantity"
            min-width="60"
          />
          <el-table-column
            v-if="deliveryForm.inboundType === 9"
            label="退货数量"
            prop="returnOrderQuantity"
            min-width="70"
          />
          <el-table-column
            v-if="deliveryForm.inboundType === 9"
            label="已补货数量"
            prop="restockingQty"
            min-width="80"
          />
          <el-table-column
            v-if="deliveryForm.inboundType === 9"
            label="剩余可补数量"
            min-width="90"
          >
            <template #default="{ row }">
              {{ (row.returnOrderQuantity || 0) - (row.restockingQty || 0) }}
            </template>
          </el-table-column>
          <el-table-column label="物料名称" prop="name" min-width="60">
            <template #default="{ row }">
              <el-tooltip
                :content="row.name"
                placement="top"
                :show-after="500"
                :enterable="false"
              >
                <div class="ellipsis-cell">
                  {{ row.name }}
                </div>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column label="物料型号" prop="model" min-width="60">
            <template #default="{ row }">
              <el-tooltip
                :content="row.model"
                placement="top"
                :show-after="500"
                :enterable="false"
              >
                <div class="ellipsis-cell">
                  {{ row.model }}
                </div>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column label="物料尺寸" prop="size" min-width="80">
            <template #default="{ row }">
              <el-tooltip
                :content="row.size"
                placement="top"
                :show-after="500"
                :enterable="false"
              >
                <div class="ellipsis-cell">
                  {{ row.size }}
                </div>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column label="物料材质" prop="material" min-width="80">
            <template #default="{ row }">
              <el-tooltip
                :content="row.material"
                placement="top"
                :show-after="500"
                :enterable="false"
              >
                <div class="ellipsis-cell">
                  {{ row.material }}
                </div>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column label="物料单位" prop="unit" min-width="50" />
          <el-table-column label="物料工艺" prop="process" min-width="80">
            <template #default="{ row }">
              <el-tooltip
                :content="row.process"
                placement="top"
                :show-after="500"
                :enterable="false"
              >
                <div class="ellipsis-cell">
                  {{ row.process }}
                </div>
              </el-tooltip>
            </template>
          </el-table-column>
          <!--          <el-table-column label="颜色" prop="coverColor" min-width="50" /> -->
          <el-table-column label="操作" width="100" align="center">
            <template #default="{ $index }">
              <el-button type="danger" text @click="removeMaterialRow($index)">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 添加物料按钮 -->
        <div v-if="materialList.length > 0" class="add-material-button-container">
          <el-button
            type="primary"
            class="add-material-button"
            @click="addEmptyMaterialRow"
          >
            <el-icon><Plus /></el-icon>
            添加物料
          </el-button>
        </div>
      </div>

      <!-- 提交按钮 -->
      <div class="form-actions">
        <el-button @click="router.go(-1)">
          取消
        </el-button>
        <el-button type="primary" @click="submitForm">
          保存
        </el-button>
      </div>
    </el-form>

    <!-- 模式选择对话框 - 只在采购入库时显示 -->
    <el-dialog
      v-if="deliveryForm.inboundType === 1"
      v-model="showModeDialog"
      title="提示"
      width="400px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
    >
      <div class="mode-dialog-content">
        <div class="dialog-icon">
          <el-icon size="24" color="#E6A23C">
            <Warning />
          </el-icon>
        </div>
        <div class="dialog-message">
          {{ isEdit ? '是否需要自动填充该订单的全部物料（将覆盖当前物料列表）' : '是否需要自动填充该订单的全部物料' }}
        </div>
        <div class="dialog-buttons">
          <el-button @click="cancelModeSelection">
            取消
          </el-button>
          <el-button @click="handleModeSelection('manual')">
            手动输入
          </el-button>
          <el-button type="primary" @click="handleModeSelection('auto')">
            自动填充
          </el-button>
        </div>
      </div>
    </el-dialog>

    <!-- 退货出库单选择对话框 -->
    <el-dialog
      v-model="showOutboundSelector"
      title="选择退货出库单"
      width="80%"
      :close-on-click-modal="false"
    >
      <!--      deliveryForm.supplierId -->
      <OutboundTable
        :outbound-type="1"
        :supplier-id="0"
        @selected="handleOutboundOrderSelected"
      />
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
.delivery-note-add {
  padding: 20px;

  .header-container {
    margin-bottom: 20px;
    border-bottom: 1px solid #ebeef5;
    padding-bottom: 10px;

    h2 {
      margin: 0;
      font-size: 20px;
      color: #303133;
    }
  }

  .delivery-form {
    background-color: #fff;
    padding: 20px;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

    .form-section {
      margin-bottom: 30px;

      .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
      }

      .section-title {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
        padding-bottom: 10px;
        border-bottom: 1px solid #ebeef5;
        margin-bottom: 15px;
      }
    }

    .form-actions {
      display: flex;
      justify-content: center;
      gap: 20px;
      margin-top: 30px;
    }
  }
}

/* 添加省略样式 */
.ellipsis-cell {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

/* 下拉选项样式 */
.option-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.option-disabled-reason {
  color: #f56c6c;
  font-size: 12px;
}

/* 必填字段样式 */
.required-field::before {
  content: '*';
  color: #f56c6c;
  margin-right: 4px;
}

/* 空状态提示样式 */
.empty-material-tip {
  padding: 40px 0;
  text-align: center;
  background-color: #fafafa;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
}

/* 紧凑表格样式 */
.compact-table {
  :deep(.el-table__row) {
    height: 36px;
  }

  :deep(.el-table__cell) {
    padding: 6px 0;
  }

  :deep(.el-input--small) {
    height: 28px;

    .el-input__inner {
      height: 28px;
      line-height: 28px;
    }
  }

  :deep(.el-select--small) {
    height: 28px;

    .el-input__inner {
      height: 28px;
      line-height: 28px;
    }
  }

  :deep(.el-input-number--small) {
    height: 28px;

    .el-input__inner {
      height: 28px;
      line-height: 28px;
    }
  }
}

/* 添加物料按钮区域样式 */
.add-material-button-container {
  margin-top: 0;
  border: 1px solid #ebeef5;
  border-top: none;
  background-color: #fafafa;
  padding: 0;

  .add-material-button {
    width: 100%;
    height: 36px;
    border: none;
    border-radius: 0;
    background-color: #fafafa;
    color: #409eff;
    font-size: 14px;
    font-weight: normal;
    transition: all 0.3s;

    &:hover {
      background-color: #ecf5ff;
      color: #337ecc;
    }

    &:focus {
      background-color: #ecf5ff;
      color: #337ecc;
    }

    .el-icon {
      margin-right: 6px;
      font-size: 16px;
    }
  }
}

/* 模式选择对话框样式 */
.mode-dialog-content {
  text-align: center;
  padding: 20px 0;

  .dialog-icon {
    margin-bottom: 15px;
  }

  .dialog-message {
    font-size: 16px;
    color: #303133;
    margin-bottom: 25px;
    line-height: 1.5;
  }

  .dialog-buttons {
    display: flex;
    justify-content: center;
    gap: 15px;
  }
}
</style>
