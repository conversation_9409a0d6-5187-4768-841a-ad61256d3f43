import{g as $,i as N,e as q}from"./index-BtOcqcNl.js";import{c as I,b as u,z as K,q as L,w as c,h as l,i as s,j as W,o as P,f as M,F as G,y as C,M as H,N as Q,E as D}from"./.pnpm-hVqhwuVC.js";import{a as T}from"./index-D95m1iJL.js";const J=I({name:"product-selector"}),ne=I({...J,props:{modelValue:Array,simple:{type:Boolean,default:!1},rowSelect:{type:Boolean,default:!1},showUnit:{type:Boolean,default:!0}},emits:["update:modelValue","selectedRow"],setup(j,{expose:E,emit:B}){const f=j,b=B,p=u(),g=u(),{dict:w}=$(),{service:U}=T(),x=[{label:"单品",value:0,name:"-",name_en:"-",type:"info"},{label:"内盒/展示盒",value:1,name:"展示盒",name_en:"Inner Box",type:"warning"},{label:"箱装",value:2,name:"箱装",name_en:"Outer Box",type:"success"}],R=w.get("color"),r=[{label:"ID",prop:"id",width:80},{label:"SKU",prop:"sku",width:150},{label:"UPC",prop:"upc",width:150},{label:"名称",children:[{label:"中文名称",prop:"name",minWidth:200},{label:"英文名称",prop:"nameEn",minWidth:200}]},{label:"颜色",prop:"color",showOverflowTooltip:!0,width:140,formatter:e=>{var t;return((t=R.value.find(i=>i.value===Number.parseInt(e.color)))==null?void 0:t.label)||"-"}}];f.rowSelect?r.push({label:"操作",type:"op",width:100,fixed:"right",buttons:["slot-btn-select"]}):r.unshift({type:"selection",reserveSelection:!0}),f.simple||r.push({label:"库存信息",children:[{label:"可用",prop:"stock",width:100,formatter:e=>{var t,i;return((t=e.stock)==null?void 0:t.inStock)-((i=e.stock)==null?void 0:i.freezeStock)||0}},{label:"冻结",prop:"freezeStock",width:100,formatter:e=>{var t;return((t=e.stock)==null?void 0:t.freezeStock)||0}},{label:"生产中",prop:"productionStock",width:100,formatter:e=>{var t;return((t=e.stock)==null?void 0:t.productionStock)||0}},{label:"待入库",prop:"WaitInboundStock",width:100,formatter:e=>{var t;return((t=e.stock)==null?void 0:t.WaitInboundStock)||0}}]},{label:"包装产品信息",children:[{label:"包装单位",prop:"unit",width:100,dict:x},{label:"包装产品",prop:"unitProductSku",width:100,formatter:e=>e.unitProductSku||"-"},{label:"单位数量",prop:"quantity",formatter:e=>e.unit===0?"-":e.unitQuantity,width:100}]},{label:"长cm",prop:"length",width:70},{label:"宽cm",prop:"width",width:70},{label:"高cm",prop:"height",width:70},{label:"净量kg",prop:"weight",width:70},{label:"毛量kg",prop:"gross_weight",width:70}),N.useTable({autoHeight:!1,columns:r});const d=u(0),n=N.useCrud({service:U.pms.product},e=>{e.refresh({size:10,unit:d.value})}),m=N.useSearch({items:[{label:"包装单位",props:{labelWidth:"100px"},hidden:!f.showUnit,prop:"unit",value:d.value,component:{name:"el-select",props:{style:"width: 120px",clearable:!0,onChange(e){var t;(t=n.value)==null||t.refresh({unit:e,page:1})}},options:x}},{label:"SKU/UPC/名称",props:{labelWidth:"120px"},prop:"keyword",component:{name:"el-input",props:{clearable:!1,onChange(e){var t;(t=n.value)==null||t.refresh({keyWord:e.trim(),page:1})}}}}]});K(()=>f.modelValue,e=>{g.value=e&&String(e)},{immediate:!0});function h(e){b("update:modelValue",e)}function k(e){p.value.toggleRowSelection(e)}const v=u();function S(){var e,t;p.value.clearSelection(),(e=n.value)==null||e.refresh({unit:void 0,keyWord:void 0,page:1}),(t=v.value)==null||t.reset()}E({resetData:S});function V(e){b("selectedRow",e)}return(e,t)=>{const i=s("cl-refresh-btn"),o=s("cl-flex1"),y=s("cl-search"),_=s("el-row"),a=s("el-button"),z=s("cl-table"),F=s("cl-pagination"),A=s("cl-crud");return P(),L(A,{ref_key:"Crud",ref:n,class:""},{default:c(()=>[l(_,null,{default:c(()=>[l(i),l(o),l(y,{ref_key:"Search",ref:m},null,512)]),_:1}),l(_,null,{default:c(()=>[l(z,{ref_key:"productTable",ref:p,onSelectionChange:h,onRowClick:k},{"slot-btn-select":c(({scope:O})=>[l(a,{type:"success",onClick:ee=>V(O.row)},{default:c(()=>[W(" 选择 ")]),_:2},1032,["onClick"])]),_:1},512)]),_:1}),l(_,null,{default:c(()=>[l(o),l(F,{"page-size":10,layout:"prev, pager, next, jumper"})]),_:1})]),_:1},512)}}}),X={flex:"~ justify-center row items-center"},Y={class:"import-error-dialog"},Z=I({name:"undefined"}),ae=I({...Z,emits:["success"],setup(j,{emit:E}){const B=E,{service:f}=T(),b=u(null),p=u(!1),g=u([]),w=u(!1);function U(){const r=b.value;r&&r.click()}function x(r){const n=r.target.files;if(n&&n.length>0){const m=n[0],h=new FileReader;h.onload=k=>{var i;const v=new Uint8Array((i=k.target)==null?void 0:i.result),S=H(v,{type:"array"}),V=S.Sheets[S.SheetNames[0]],t=Q.sheet_to_json(V,{header:1}).slice(1).filter(o=>o.length===2&&o[0]&&o[1]&&o[1]>0).map(o=>({sku:o[0].toString().trim().replace(/[\r\n]/g,""),quantity:Number.parseInt(o[1].toString().trim().replace(/[\r\n]/g,""))}));p.value=!0,f.pms.product.list({skuList:t.map(o=>o.sku).join(",")}).then(o=>{o=o||[];const y=o.filter(a=>(a==null?void 0:a.id)&&a.id>0).map(a=>a),_=t.filter(a=>!y.find(z=>z.sku===a.sku));_.length>0&&_.forEach(a=>{g.value.push({row:t.indexOf(a)+2,message:`SKU：${a.sku} 不存在`})}),y.length>0&&B("success",y,t),g.value.length>0&&(w.value=!0)}).catch(o=>{D.error({message:o.message||"读取产品信息错误"})}).finally(()=>{p.value=!1})},h.readAsArrayBuffer(m)}}function R(){const r="导入模板.xlsx";fetch("/inbound_template.xlsx").then(n=>n.blob()).then(n=>{q(n,r)}).catch(()=>{D.error({message:"下载模板文件失败"})})}return(r,d)=>{const n=s("el-button"),m=s("el-table-column"),h=s("el-table"),k=s("cl-dialog");return P(),M(G,null,[C("div",null,[C("div",X,[l(n,{size:"default",ml3:"",onClick:R},{default:c(()=>[W(" 下载Excel模板 ")]),_:1}),l(n,{loading:p.value,type:"success",size:"default",onClick:U},{default:c(()=>[W(" Excel导入 ")]),_:1},8,["loading"])]),C("input",{ref_key:"fileInputRef",ref:b,type:"file",style:{display:"none"},accept:".xlsx, .xls",onChange:x},null,544)]),l(k,{modelValue:w.value,"onUpdate:modelValue":d[0]||(d[0]=v=>w.value=v),title:"导入错误信息",width:"500"},{default:c(()=>[C("div",Y,[l(h,{data:g.value,style:{width:"100%"},border:""},{default:c(()=>[l(m,{prop:"row",label:"行号",width:"100"}),l(m,{prop:"message",label:"错误信息"})]),_:1},8,["data"])])]),_:1},8,["modelValue"])],64)}}});export{ae as _,ne as a};
