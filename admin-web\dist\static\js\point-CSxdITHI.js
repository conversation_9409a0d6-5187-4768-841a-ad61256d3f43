import{i as m}from"./index-BtOcqcNl.js";import{c as C,b as v,e as D,q as E,w as c,h as e,i as l,f as b,s as w,F as k,o as d,j as S,y as j,t as x}from"./.pnpm-hVqhwuVC.js";import{a as z}from"./index-D95m1iJL.js";const A=j("br",null,null,-1),G=C({name:"pms-warehouse-point"}),O=C({...G,setup(H){const{service:f}=z(),y=v([]),_=[{label:"出货",value:0,type:"success"},{label:"入仓",value:1,type:"warning"}],p=v();async function h(t){var r;if((r=p.value)==null||r.setForm("userIds",[]),t===void 0)return;const n=t===0?13:16,s=await f.base.sys.user.list({departmentId:n});y.value=(s==null?void 0:s.map(a=>({label:a.name||"",value:a.id})))||[]}const I=D(()=>{var n;return((n=p.value)==null?void 0:n.getForm("type"))===void 0});p.value=m.useUpsert({items:[{label:"仓库名",prop:"name",required:!0,component:{name:"el-input"}},{label:"仓库类型",prop:"type",required:!0,component:{name:"slot-user-select",options:_}},{label:"仓库地址",prop:"address",required:!0,component:{name:"el-input"}},{prop:"userIds",label:"管理员",required:!0,component:{name:"el-select",options:y,props:{disabled:I,multiple:!0,"multiple-limit":3}}}],async onInfo(t,{done:n}){var r;const s=t.type;await h(s),t.userIds=((r=t.users)==null?void 0:r.map(a=>a.id))||[],n(t)}});const g=m.useTable({columns:[{label:"ID",prop:"id",width:80},{label:"仓库名",prop:"name",width:300},{label:"仓库类型",prop:"type",dict:_,width:100},{label:"仓库地址",prop:"address"},{label:"管理员",prop:"userIds"},{type:"op",buttons:["edit","delete"]}]}),V=m.useCrud({service:f.pms.warehouse.point},t=>{t.refresh()});return(t,n)=>{const s=l("cl-refresh-btn"),r=l("cl-add-btn"),a=l("cl-flex1"),T=l("cl-search-key"),i=l("el-row"),U=l("cl-table"),q=l("cl-pagination"),B=l("el-option"),F=l("el-select"),L=l("cl-upsert"),N=l("cl-crud");return d(),E(N,{ref_key:"Crud",ref:V},{default:c(()=>[e(i,null,{default:c(()=>[e(s),e(r),e(a),e(T)]),_:1}),e(i,null,{default:c(()=>[e(U,{ref_key:"Table",ref:g},{"column-userIds":c(({scope:u})=>[(d(!0),b(k,null,w(u.row.users,o=>(d(),b("span",{key:o.id},[S(x(o.name)+" "+x(o.phone),1),A]))),128))]),_:1},512)]),_:1}),e(i,null,{default:c(()=>[e(a),e(q)]),_:1}),e(L,{ref_key:"Upsert",ref:p},{"slot-user-select":c(({scope:u})=>[e(F,{modelValue:u.type,"onUpdate:modelValue":o=>u.type=o,placeholder:"仓库类型",style:{width:"400px"},onChange:h},{default:c(()=>[(d(),b(k,null,w(_,o=>e(B,{key:o.value,label:o.label,value:o.value},null,8,["label","value"])),64))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:1},512)]),_:1},512)}}});export{O as default};
