import{c as ve,b as w,e as q,z as He,A as ce,q as u,w as o,h as t,B as d,G as fe,v as c,i as v,H as Ae,j as s,f as Ge,s as Ke,F as Ze,t as b,y as F,W as me,Y as T,E as g,T as E,Z as he,o as p,af as Je,ag as Xe}from"./.pnpm-hVqhwuVC.js";import{i as K,r as W,e as Z}from"./index-BtOcqcNl.js";import{u as et}from"./table-ops-mcGHjph4.js";/* empty css              */import{n as x,V as tt}from"./index-CBanFtSc.js";import{a as at}from"./index-D95m1iJL.js";import{_ as ot}from"./UploadBtn.vue_vue_type_script_setup_true_name_UploadBtn_lang-CcCZ0McC.js";import{a as lt}from"./index-Dw7jsygE.js";import{_ as rt}from"./_plugin-vue_export-helper-DlAUqK2U.js";const nt=R=>(Je("data-v-000b8062"),R=R(),Xe(),R),st=nt(()=>F("div",{"i-material-symbols:sync-problem-outline":"","m-x10px":""},null,-1)),it={class:"my-4 w-full"},pt={class:"dialog-footer"},ut=ve({name:"pms-purchase-order"}),dt=ve({...ut,setup(R){const{service:f}=at(),we=w([]),J=w([]),m=w(0),j=w(!1),X=w([]),Y=w(!1),ee=w([{label:"待处理",value:0,count:0},{label:"待审核",value:1,count:0},{label:"已确认",value:2,count:0},{label:"入库中",value:3,count:0},{label:"已完成",value:4,count:0}]),te=w({"slot-btn-edit":{width:70,permission:f.pms.purchase.order.permission.createContract,show:q(()=>m.value===0)},"slot-btn-confirm":{width:100,permission:f.pms.purchase.order.permission.confirm,show:q(()=>m.value===0)},"slot-btn-reject":{width:80,permission:f.pms.purchase.order.permission.revoke,show:!0},"slot-btn-transfer":{width:100,permission:f.pms.purchase.order.permission.transfer,show:q(()=>m.value===2||m.value===3)},"slot-btn-download-contract":{width:110,permission:f.pms.purchase.order.permission.downloadContract,show:q(()=>m.value===2||m.value===3)},"slot-audit-log":{width:120,permission:f.pms.purchase.order.permission.auditLog,show:q(()=>m.value<=2)},"slot-btn-revoke":{width:70,permission:f.pms.purchase.order.permission.revoke,show:q(()=>m.value===2)},"slot-btn-delete-suborder":{width:70,permission:f.pms.purchase.order.permission.deleteSuborder,show:q(()=>m.value===0)},"slot-btn-export":{width:80,permission:f.pms.purchase.order.permission.export,show:!0}}),{getOpWidth:be,checkOpButtonIsAvaliable:O,getOpIsHidden:ge}=et(te),ae=w(),oe=w(!1);He(m,()=>{ae.value=be(),oe.value=ge()},{immediate:!0});const le=w([]),re=K.useTable({columns:[{type:"expand",prop:"contracts"},{prop:"orderNo",label:"订单号",align:"left",showOverflowTooltip:!1},{prop:"total",label:"总采购数量",width:120,showOverflowTooltip:!0},{prop:"receivedQuantity",label:"已收货数量",width:120,showOverflowTooltip:!0},{prop:"amount",label:"订单总金额",width:120,showOverflowTooltip:!0},{prop:"createTime",label:"订单日期",width:180,showOverflowTooltip:!0},{type:"op",label:"操作",width:ae,hidden:oe,buttons:Object.keys(te.value)}]}),S=K.useCrud({service:f.pms.purchase.order,async onRefresh(l,{next:e,render:i}){const{count:h,list:_,pagination:D}=await e(l);ee.value.forEach(C=>{C.count=h[C.value]||0}),i(_,D)}},l=>{l.refresh({status:m})});function ye(l){var e;l>=0&&(m.value=l),(e=S.value)==null||e.refresh()}const $=K.useSearch({items:[{label:"下单时间",prop:"dateRange",props:{labelWidth:"80px"},component:{name:"el-date-picker",props:{type:"daterange","value-format":"YYYY-MM-DD",format:"YYYY-MM-DD",rangeSeparator:"至",startPlaceholder:"开始日期",endPlaceholder:"结束日期",clearable:!0,onChange(l){var e;(e=S.value)==null||e.refresh({dateRange:l})}}}},{label:"订单 / PO / 物料编码",prop:"keyword",props:{labelWidth:"150px"},component:{name:"el-input",props:{placeholder:"输入订单/PO/物料编码搜索",clearable:!1,onChange(l){var e;(e=S.value)==null||e.refresh({keyWord:l.trim(),page:1})}}}},{label:"选择供应商数据导出",prop:"supplier_id",props:{labelWidth:"150px"},component:{name:"el-select",props:{style:"width: 200px",clearable:!0,filterable:!0},options:J}}]});function _e(l,e){var i;(e==null?void 0:e.type)==="expand"||(e==null?void 0:e.type)==="op"||(i=re.value)==null||i.toggleRowExpansion(l)}function xe(l){const e=l.label,i=l.count;return`${e}(${i})`}function ke(l){E.confirm("驳回到生产需求会删除该订单所有数据，是否继续？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{f.pms.purchase.order.rejectProduction(l).then(()=>{var e;m.value=0,(e=S.value)==null||e.refresh()}).catch(e=>{g.error(e.message||"删除子订单失败")})}).catch(()=>{})}function Ce(l){E.confirm("确认删除子订单吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{f.pms.purchase.order.deleteSuborder({id:l}).then(()=>{var e;m.value=0,(e=S.value)==null||e.refresh()}).catch(e=>{g.error(e.message||"删除子订单失败")})}).catch(()=>{})}function Te(l){W.push(`/pms/purchase/order/add?id=${l}&step=1`)}function Oe(l){f.pms.purchase.order.confirm({id:l}).then(e=>{var i;m.value=(e==null?void 0:e.status)||1,(i=S.value)==null||i.refresh()}).catch(e=>{g.error(e.message||"确认订单失败")})}function Se(l){const e=l.id;if(!e)return!1;f.pms.purchase.order.auditLog({id:e}).then(i=>{X.value=i,j.value=!0}).catch(i=>{g.error(i.message||"获取审核记录失败")})}function Ie(l){E.confirm("确认撤销订单吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{f.pms.purchase.order.revoke({id:l}).then(()=>{var e;m.value=0,(e=S.value)==null||e.refresh()}).catch(e=>{g.error(e.message||"撤销订单失败")})}).catch(()=>{})}function Qe(l){const e=l.id;if(!e)return!1;E.confirm("确认下载合同吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{const h={url:"/downloadContract",method:"POST",data:{id:e},responseType:"blob"};f.pms.purchase.order.request(h).then(_=>{Z(_,`采购合同-${l.orderNo}`)&&g.success("合同下载成功")}).catch(_=>{g.error(_.message||"合同下载失败")})}).catch(()=>{})}const U=w({purchaseOrderId:0,items:[]}),L=w(),B=w(!1),ne=w(!1),k=w(1),I=w([]),V=w(!1),z=w([]);async function se(){try{const l=await f.pms.supplier.list();we.value=l,J.value=l==null?void 0:l.map(e=>({label:e.supplierName,value:e.id}))}catch(l){g.error(l.message||"获取供应商列表失败")}}async function qe(l){const e=l.id;if(!e)return!1;await se(),z.value=l.contracts.filter(i=>i.quantity>i.receivedQuantity+i.transfer),B.value=!0,U.value.purchaseOrderId=e}function Be(){var l;if(I.value=((l=L.value)==null?void 0:l.getSelectionRows())||[],I.value.length===0)return g.error("请选择要转移的物料");I.value=I.value.map(e=>(e.transferQuantity=null,e)),k.value=2}function De(){k.value=1}function Ve(){k.value=1,I.value=[],z.value=[],B.value=!1}const H=w(!1);function Ne(){const l=I.value.every(e=>e.transferQuantity>0);if(H.value=!0,!l)return g.error("请填写正确的转单数量");H.value=!1,E.confirm("确认要提交子订单吗？请仔细检查订单内容！！！","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{U.value.items=I.value.map(e=>({contractId:e.id,quantity:e.transferQuantity})),V.value=!0,f.pms.purchase.order.transfer(U.value).then(e=>{const{id:i}=e;if(!i)return g.error("子订单生成失败！");g.success("子订单生成成功！"),B.value=!1,W.push(`/pms/purchase/order/add?id=${i}&step=1`)}).catch(e=>{g.error(e.message||"子订单生成失败")}).finally(()=>{V.value=!1})}).catch(()=>{})}function Me(l){var e;(e=L.value)==null||e.toggleRowSelection(l)}function Ee(l){if(!l)return!1;const e={url:"/export",method:"GET",responseType:"blob",params:{id:l}};f.pms.purchase.order.request(e).then(i=>{Z(i)&&g.success("导出成功")}).catch(i=>{g.error(i.message||"导出失败")})}ce(()=>{var l,e;ne.value=((e=(l=L.value)==null?void 0:l.getSelectionRows())==null?void 0:e.length)>0,se()});function Re(l){var i;if(l.status!==0||((i=l.contracts)==null?void 0:i.length)>0||!l.createTime)return!1;const e=l.createTime;return he(e).isSame(he(),"day")}function Ye({row:l}){return l.receivedQuantity&&l.receivedQuantity>=l.quantity-l.transfer?"success-row":""}function ie(){var _,D,C;const l=(_=$.value)==null?void 0:_.getForm("supplier_id"),e=(D=$.value)==null?void 0:D.getForm("keyword"),i=(C=$.value)==null?void 0:C.getForm("dateRange"),h={url:"/exportDetail",method:"GET",responseType:"blob",params:{supplier_id:l,keyword:e,dateRange:i}};Y.value=!0,f.pms.purchase.order.request(h).then(N=>{Z(N)&&g.success("导出成功")}).catch(N=>{g.error(N.message||"导出失败")}).finally(()=>{Y.value=!1})}return ce(()=>{const l=W.currentRoute.value.query.expand;l&&(le.value=[Number.parseInt(l.toString())],W.replace({query:{expand:void 0}}))}),(l,e)=>{const i=v("cl-refresh-btn"),h=v("el-button"),_=v("cl-flex1"),D=v("cl-search"),C=v("el-row"),N=v("el-tab-pane"),pe=v("el-badge"),A=v("el-tooltip"),n=v("el-table-column"),Q=v("el-text"),ue=v("cl-date-text"),P=v("el-table"),$e=v("cl-table"),Le=v("cl-pagination"),Pe=v("el-tabs"),We=v("el-dialog"),G=v("el-step"),Fe=v("el-steps"),je=v("el-input-number"),Ue=v("cl-dialog"),ze=v("cl-crud"),de=Ae("permission");return p(),u(ze,{ref_key:"Crud",ref:S},{default:o(()=>[t(C,null,{default:o(()=>[c(lt)===1?(p(),u(ot,{key:0,api:c(f).pms.purchase.contract,url:"importContractExcelData"},null,8,["api"])):d("",!0),t(i),fe((p(),u(h,{loading:Y.value,type:"primary",onClick:ie},{default:o(()=>[s(" 导出汇总 ")]),_:1},8,["loading"])),[[de,c(f).pms.purchase.order.permission.exportSummary]]),fe((p(),u(h,{loading:Y.value,type:"warning",onClick:ie},{default:o(()=>[s(" 导出采购订单明细表 ")]),_:1},8,["loading"])),[[de,c(f).pms.purchase.order.permission.exportSummary]]),t(_),t(D,{ref_key:"Search",ref:$},null,512)]),_:1}),t(Pe,{modelValue:m.value,"onUpdate:modelValue":e[0]||(e[0]=a=>m.value=a),type:"border-card",onTabChange:ye},{default:o(()=>[(p(!0),Ge(Ze,null,Ke(ee.value,a=>(p(),u(N,{key:a.value,label:xe(a),name:a.value},null,8,["label","name"]))),128)),t(C,null,{default:o(()=>[t($e,{ref_key:"Table",ref:re,"row-key":"id","expand-row-keys":le.value,class:"table-row-pointer",onRowClick:_e},{"slot-btn-confirm":o(({scope:a})=>[c(O)("slot-btn-confirm")?(p(),u(h,{key:0,text:"",bg:"",type:"success",onClick:T(r=>Oe(a.row.id),["stop"])},{default:o(()=>[s(" 提交审核 ")]),_:2},1032,["onClick"])):d("",!0)]),"slot-btn-delete-suborder":o(({scope:a})=>[c(O)("slot-btn-delete-suborder")?(p(),u(h,{key:0,disabled:a.row.parentOrderId<=0,text:"",bg:"",type:"danger",onClick:T(r=>Ce(a.row.id),["stop"])},{default:o(()=>[s(" 删除 ")]),_:2},1032,["disabled","onClick"])):d("",!0)]),"slot-btn-edit":o(({scope:a})=>[c(O)("slot-btn-edit")?(p(),u(h,{key:0,text:"",bg:"",type:"primary",onClick:T(r=>Te(a.row.id),["stop"])},{default:o(()=>[s(" 处理 ")]),_:2},1032,["onClick"])):d("",!0)]),"slot-btn-download-contract":o(({scope:a})=>[c(O)("slot-btn-download-contract")?(p(),u(h,{key:0,text:"",bg:"",type:"primary",onClick:T(r=>Qe(a.row),["stop"])},{default:o(()=>[s(" 下载合同 ")]),_:2},1032,["onClick"])):d("",!0)]),"slot-btn-transfer":o(({scope:a})=>[c(O)("slot-btn-transfer")?(p(),u(h,{key:0,text:"",bg:"",type:"success",disabled:a.row.parentOrderId>0,onClick:T(r=>qe(a.row),["stop"])},{default:o(()=>[s(" 转子订单 ")]),_:2},1032,["disabled","onClick"])):d("",!0)]),"slot-btn-revoke":o(({scope:a})=>[c(tt)(a.row.createTime)&&c(O)("slot-btn-revoke")?(p(),u(h,{key:0,text:"",bg:"",type:"danger",onClick:T(r=>Ie(a.row.id),["stop"])},{default:o(()=>[s(" 撤销 ")]),_:2},1032,["onClick"])):d("",!0)]),"slot-audit-log":o(({scope:a})=>[c(O)("slot-audit-log")?(p(),u(h,{key:0,text:"",bg:"",type:"warning",onClick:T(r=>Se(a.row),["stop"])},{default:o(()=>[s(" 审核记录 ")]),_:2},1032,["onClick"])):d("",!0)]),"slot-btn-export":o(({scope:a})=>{var r;return[c(O)("slot-btn-export")?(p(),u(h,{key:0,disabled:((r=a.row.contracts)==null?void 0:r.length)===0,text:"",bg:"",type:"primary",onClick:T(y=>Ee(a.row.id),["stop"])},{default:o(()=>[s(" 导出 ")]),_:2},1032,["disabled","onClick"])):d("",!0)]}),"slot-btn-reject":o(({scope:a})=>[a.row.parentOrderId===0&&(m.value===0||m.value===1||m.value===2)?(p(),u(h,{key:0,text:"",bg:"",type:"danger",onClick:T(r=>ke(a.row),["stop"])},{default:o(()=>[s(" 驳回 ")]),_:2},1032,["onClick"])):d("",!0)]),"column-orderNo":o(({scope:a})=>[F("div",{class:me(`${a.row.hasWarning?"c-red":""} flex items-center`)},[s(b(a.row.orderNo||"-")+" ",1),a.row.parentOrderId>0?(p(),u(A,{key:0,effect:"dark",content:"这是一个子订单",placement:"top-start"},{default:o(()=>[t(pe,{class:"order-no-badge",type:"danger",value:"子"})]),_:1})):d("",!0),Re(a.row)?(p(),u(A,{key:1,effect:"dark",content:"今日订单，请及时处理",placement:"top-start"},{default:o(()=>[t(pe,{class:"order-no-badge",type:"success",value:"NEW"})]),_:1})):d("",!0),a.row.hasWarning?(p(),u(A,{key:2,effect:"dark",content:"提交订单时BOM版本有更新，请仔细核对",placement:"top-start"},{default:o(()=>[st]),_:1})):d("",!0)],2)]),"column-receivedQuantity":o(({scope:a})=>{var r;return[s(b(c(x)(((r=a.row.contracts)==null?void 0:r.reduce((y,M)=>y+M.receivedQuantity,0))||0)),1)]}),"column-total":o(({scope:a})=>{var r;return[s(b(c(x)(((r=a.row.contracts)==null?void 0:r.reduce((y,M)=>y+M.quantity,0))||0)),1)]}),"column-amount":o(({scope:a})=>{var r;return[s(b(c(x)(((r=a.row.contracts)==null?void 0:r.reduce((y,M)=>y+M.subtotal,0))||0)),1)]}),"column-contracts":o(({scope:a})=>[t(P,{"row-class-name":Ye,data:a.row.contracts,border:"","max-height":"600"},{default:o(()=>[t(n,{prop:"po",label:"PO",align:"left",fixed:"left",width:"180","show-overflow-tooltip":""}),t(n,{prop:"material.code",label:"物料编码",align:"left",width:"150","show-overflow-tooltip":""}),t(n,{prop:"material.name",label:"物料名称",align:"left",width:"150","show-overflow-tooltip":""}),t(n,{prop:"material.model",label:"物料型号",align:"left","min-width":"150","show-overflow-tooltip":""}),t(n,{prop:"material.size",label:"物料尺寸",align:"left",width:"150","show-overflow-tooltip":""}),t(n,{prop:"material.material",label:"物料材质",align:"left",width:"100","show-overflow-tooltip":""}),t(n,{prop:"material.process",label:"物料工艺",align:"left",width:"100","show-overflow-tooltip":""}),t(n,{prop:"material.coverColor",label:"物料颜色",align:"left",width:"100","show-overflow-tooltip":""}),t(n,{prop:"purchaseTotal",label:"需求数量",align:"center",width:"100","show-overflow-tooltip":""},{default:o(({row:r})=>{var y;return[s(b((y=r.extra)!=null&&y.purchaseTotal?c(x)(r.extra.purchaseTotal):0),1)]}),_:1}),t(n,{prop:"extra.deductInventory",label:"库存抵扣",align:"center",width:"100","show-overflow-tooltip":""},{default:o(({row:r})=>{var y;return[s(b((y=r.extra)!=null&&y.deductInventory?c(x)(r.extra.deductInventory):0),1)]}),_:1}),t(n,{prop:"preparesTotal",label:"备料数量",align:"center",width:"100","show-overflow-tooltip":""},{default:o(({row:r})=>[s(b(r!=null&&r.preparesTotal?c(x)(r.preparesTotal):0),1)]),_:1}),t(n,{prop:"quantity",label:"采购数量",align:"center",width:"100","show-overflow-tooltip":""}),t(n,{prop:"transfer",label:"转单数量",align:"center",width:"100","show-overflow-tooltip":""}),t(n,{prop:"extra.totalCanInStock",label:"需入库数量",align:"center",width:"100","show-overflow-tooltip":""},{default:o(({row:r})=>[s(b(c(x)(r.quantity-r.transfer)),1)]),_:1}),t(n,{prop:"receivedQuantity",label:"已入库数量",align:"center",width:"100","show-overflow-tooltip":""},{default:o(({row:r})=>[r.receivedQuantity>r.quantity?(p(),u(Q,{key:0,type:"danger"},{default:o(()=>[s(b(Math.max(r.receivedQuantity-r.transfer,0)),1)]),_:2},1024)):d("",!0),r.receivedQuantity===r.quantity?(p(),u(Q,{key:1,type:"success"},{default:o(()=>[s(b(Math.max(r.receivedQuantity-r.transfer,0)),1)]),_:2},1024)):d("",!0),r.receivedQuantity<r.quantity?(p(),u(Q,{key:2},{default:o(()=>[s(b(Math.max(r.receivedQuantity-r.transfer,0)),1)]),_:2},1024)):d("",!0)]),_:1}),t(n,{prop:"material.unit",label:"单位",align:"center",width:"70","show-overflow-tooltip":""}),t(n,{prop:"supplierName",label:"供应商",align:"center",width:"140","show-overflow-tooltip":""}),t(n,{prop:"unitPrice",label:"含税单价",align:"center",width:"90","show-overflow-tooltip":""}),t(n,{prop:"subtotal",label:"小记",align:"center",width:"90","show-overflow-tooltip":""},{default:o(({row:r})=>[s(b(r.subtotal.toFixed(2)),1)]),_:1}),t(n,{prop:"taxRate",label:"税率",align:"center",width:"60","show-overflow-tooltip":""},{default:o(({row:r})=>[s(b(r.taxRate)+"% ",1)]),_:1}),t(n,{prop:"paymentTerm",label:"付款方式",align:"center",width:"100","show-overflow-tooltip":""},{default:o(({row:r})=>[r.paymentTerm===0?(p(),u(Q,{key:0,type:"primary"},{default:o(()=>[s(" 月结 ")]),_:1})):d("",!0),r.paymentTerm===1?(p(),u(Q,{key:1,type:"success"},{default:o(()=>[s(" 现结 ")]),_:1})):d("",!0)]),_:1}),t(n,{prop:"deliveryDate",label:"交货日期",align:"center",width:"100","show-overflow-tooltip":""},{default:o(({row:r})=>[t(ue,{"model-value":r.deliveryDate,format:"YYYY-MM-DD"},null,8,["model-value"])]),_:1})]),_:2},1032,["data"])]),_:1},8,["expand-row-keys"])]),_:1}),t(C,null,{default:o(()=>[t(_),t(Le)]),_:1})]),_:1},8,["modelValue"]),t(We,{modelValue:j.value,"onUpdate:modelValue":e[1]||(e[1]=a=>j.value=a),title:"审核日志",width:"50%","close-on-click-modal":!1},{default:o(()=>[t(P,{data:X.value,stripe:"",border:""},{default:o(()=>[t(n,{prop:"operatorName",label:"操作人",align:"center",width:"100"}),t(n,{prop:"operatorNote",label:"审核细节","show-overflow-tooltip":""}),t(n,{prop:"type",label:"审核类型",align:"center",width:"100"},{default:o(({row:a})=>[a.type===0?(p(),u(Q,{key:0,type:"primary"},{default:o(()=>[s(" 提交审核 ")]),_:1})):d("",!0),a.type===1?(p(),u(Q,{key:1,type:"success"},{default:o(()=>[s(" 审核通过 ")]),_:1})):d("",!0),a.type===2?(p(),u(Q,{key:2,type:"danger"},{default:o(()=>[s(" 驳回审核 ")]),_:1})):d("",!0)]),_:1}),t(n,{prop:"createTime",label:"创建时间",align:"center",width:"180"},{default:o(({row:a})=>[t(ue,{"model-value":a.createTime,format:"YYYY-MM-DD HH:mm:ss"},null,8,["model-value"])]),_:1})]),_:1},8,["data"])]),_:1},8,["modelValue"]),t(Ue,{modelValue:B.value,"onUpdate:modelValue":e[3]||(e[3]=a=>B.value=a),width:"90%",title:"转子订单","close-on-click-modal":!1,"close-on-press-escape":!1,onClose:Ve},{footer:o(()=>[F("span",pt,[t(h,{onClick:e[2]||(e[2]=a=>B.value=!1)},{default:o(()=>[s("取消")]),_:1}),k.value>1?(p(),u(h,{key:0,loading:V.value,type:"primary",onClick:De},{default:o(()=>[s("上一步")]),_:1},8,["loading"])):d("",!0),k.value===1?(p(),u(h,{key:1,loading:V.value,type:"success",disabled:k.value===1&&!ne.value,onClick:Be},{default:o(()=>[s(" 下一步 ")]),_:1},8,["loading","disabled"])):d("",!0),k.value===2?(p(),u(h,{key:2,type:"success",loading:V.value,onClick:Ne},{default:o(()=>[s(" 生成子订单 ")]),_:1},8,["loading"])):d("",!0)])]),default:o(()=>[F("div",it,[t(Fe,{active:k.value,"finish-status":"success",simple:"","align-center":""},{default:o(()=>[t(G,{title:"选择物料",description:"选择未交完货的供应商物料"}),t(G,{title:"确认物料并填写转单数量",description:"确认物料并填写转单数量"}),t(G,{title:"生成子订单",description:"生成子订单"})]),_:1},8,["active"])]),k.value===1?(p(),u(P,{key:0,ref_key:"transferOrderContractItemTable",ref:L,stripe:"",border:"",data:z.value,"max-height":600,onRowClick:Me},{default:o(()=>[t(n,{type:"selection",width:"50",align:"center"}),t(n,{prop:"material.code",label:"物料编码",align:"left",width:"150","show-overflow-tooltip":""}),t(n,{prop:"material.name",label:"物料名称",align:"left",width:"150","show-overflow-tooltip":""}),t(n,{prop:"material.model",label:"物料型号",align:"left","min-width":"150","show-overflow-tooltip":""}),t(n,{prop:"material.size",label:"物料尺寸",align:"left",width:"150","show-overflow-tooltip":""}),t(n,{prop:"material.material",label:"物料材质",align:"left",width:"100","show-overflow-tooltip":""}),t(n,{prop:"material.process",label:"物料工艺",align:"left",width:"100","show-overflow-tooltip":""}),t(n,{prop:"material.coverColor",label:"物料颜色",align:"left",width:"100","show-overflow-tooltip":""}),t(n,{prop:"supplierName",label:"供应商",align:"left",width:"140","show-overflow-tooltip":""}),t(n,{prop:"quantity",label:"采购数量",align:"center",width:"100","show-overflow-tooltip":""},{default:o(({row:a})=>[s(b(c(x)(a.quantity)),1)]),_:1}),t(n,{prop:"material.unit",label:"单位",align:"center",width:"70","show-overflow-tooltip":""}),t(n,{prop:"receivedQuantity",label:"已收货数量",align:"center",width:"100","show-overflow-tooltip":""},{default:o(({row:a})=>[s(b(c(x)(a.receivedQuantity)),1)]),_:1}),t(n,{prop:"transfer",label:"已转单数量",align:"center",width:"100","show-overflow-tooltip":""},{default:o(({row:a})=>[s(b(c(x)(a.transfer)),1)]),_:1}),t(n,{label:"可转移数量",align:"center",width:"100","show-overflow-tooltip":""},{default:o(({row:a})=>[s(b(c(x)(Math.max(a.quantity-a.receivedQuantity-a.transfer,0))),1)]),_:1})]),_:1},8,["data"])):d("",!0),k.value===2?(p(),u(P,{key:1,stripe:"",border:"",data:I.value,"max-height":600},{default:o(()=>[t(n,{prop:"material.code",label:"物料编码",align:"left",width:"150","show-overflow-tooltip":""}),t(n,{prop:"material.name",label:"物料名称",align:"left",width:"150","show-overflow-tooltip":""}),t(n,{prop:"material.model",label:"物料型号",align:"left","min-width":"150","show-overflow-tooltip":""}),t(n,{prop:"material.size",label:"物料尺寸",align:"left",width:"150","show-overflow-tooltip":""}),t(n,{prop:"material.material",label:"物料材质",align:"left",width:"100","show-overflow-tooltip":""}),t(n,{prop:"material.process",label:"物料工艺",align:"left",width:"100","show-overflow-tooltip":""}),t(n,{prop:"material.coverColor",label:"物料颜色",align:"left",width:"100","show-overflow-tooltip":""}),t(n,{prop:"supplierName",label:"供应商",align:"left",width:"140","show-overflow-tooltip":""}),t(n,{prop:"material.unit",label:"单位",align:"center",width:"70","show-overflow-tooltip":""}),t(n,{prop:"quantity",label:"采购数量",align:"center",width:"100","show-overflow-tooltip":""}),t(n,{prop:"receivedQuantity",label:"已收货数量",align:"center",width:"100","show-overflow-tooltip":""}),t(n,{label:"可转移数量",align:"center",width:"100","show-overflow-tooltip":""},{default:o(({row:a})=>[s(b(Math.max(a.quantity-a.receivedQuantity-a.transfer,0)),1)]),_:1}),t(n,{prop:"transfer",label:"转单数量",align:"center",width:"200"},{default:o(({row:a})=>[t(je,{modelValue:a.transferQuantity,"onUpdate:modelValue":r=>a.transferQuantity=r,class:me(H.value&&!(a.transferQuantity>0)?"transfer-quantity-input-error":""),min:1e-4,max:Math.max(a.quantity-a.receivedQuantity-a.transfer,0),precision:4},null,8,["modelValue","onUpdate:modelValue","class","max"])]),_:1})]),_:1},8,["data"])):d("",!0)]),_:1},8,["modelValue"])]),_:1},512)}}}),_t=rt(dt,[["__scopeId","data-v-000b8062"]]);export{_t as default};
