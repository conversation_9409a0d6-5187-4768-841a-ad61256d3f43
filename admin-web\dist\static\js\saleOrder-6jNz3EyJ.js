import{g as se,i as Y,j as re}from"./index-DkYL1aws.js";import{a as ue}from"./index-C6cm1h61.js";/* empty css              */import{S as ie}from"./schedule-order-process-CmDGq_c5.js";import{u as pe}from"./table-ops-CrFIfhgA.js";import{c as U,b as v,e as B,z as de,q as f,w as c,h as l,i as s,f as E,s as _e,F as be,y as m,t as C,v as y,j as k,B as O,E as T,T as fe,o as p}from"./.pnpm-hVqhwuVC.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";const ke={key:0},me={key:1},he={class:"cl-table__expand-footer"},ge={class:"table-summary-container"},ve=m("span",{class:"cl-table__expand-footer-title"},"订单总数：",-1),ye=U({name:"pms-production-sale-order"}),Be=U({...ye,setup(we){const{service:h}=ue(),{dict:q}=se(),F=q.get("color"),d=v(0),M=v([{label:"未处理",value:0,type:"warning",count:0},{label:"生产中",value:1,type:"info",count:0},{label:"已完成",value:2,type:"success",count:0}]),R=v({"slot-btn-process":{width:70,show:B(()=>d.value===0||d.value===1),permission:h.pms.production.sale.order.permission.outbound},"slot-btn-schedule":{width:70,show:B(()=>d.value===0),permission:h.pms.production.sale.order.permission.schedule},"slot-btn-lock-stock":{width:70,show:B(()=>d.value===0),permission:h.pms.production.sale.order.permission.lockStock},"slot-btn-revoke":{width:70,show:!0,permission:h.pms.production.sale.order.permission.revoke}}),{getOpWidth:A,checkOpButtonIsAvaliable:P,getOpIsHidden:K}=pe(R),V=v(),N=v(!1);de(d,()=>{V.value=A(),N.value=K()},{immediate:!0});const z=Y.useTable({columns:[{label:"#",prop:"products",type:"expand",width:50},{prop:"orderSn",label:"订单号"},{prop:"isEnough",label:"库存状态"},{prop:"createTime",label:"下单时间"},{prop:"requiredShipDate",label:"要求出货日期",component:{name:"cl-date-text",props:{format:"YYYY-MM-DD"}}},{type:"op",label:"操作",width:V,hidden:N,buttons:Object.keys(R.value)}]}),w=Y.useCrud({service:h.pms.production.sale.order,async onRefresh(e,{next:t,render:o}){const{count:n,list:r,pagination:_}=await t(e);M.value.forEach(u=>{u.count=n[u.value]||0}),o(r,_)}},e=>{e.refresh({status:d})}),Q=Y.useSearch({items:[{label:"下单时间",prop:"dateRange",props:{labelWidth:"80px"},component:{name:"el-date-picker",props:{type:"daterange","value-format":"YYYY-MM-DD",format:"YYYY-MM-DD",rangeSeparator:"至",startPlaceholder:"开始日期",endPlaceholder:"结束日期",clearable:!0,onChange(e){var t;(t=w.value)==null||t.refresh({dateRange:e})}}}},{label:"订单号",prop:"keyword",props:{labelWidth:"60px"},component:{name:"el-input",props:{placeholder:"输入订单号搜索",clearable:!0,onChange(e){var t;(t=w.value)==null||t.refresh({keyWord:e.trim(),page:1})}}}}]});function G({row:e}){var t,o,n,r;if(e.status<2)return(((o=(t=e.pieceProduct)==null?void 0:t.stock)==null?void 0:o.inStock)||0)-(((r=(n=e.pieceProduct)==null?void 0:n.stock)==null?void 0:r.freezeStock)||0)>=e.piece?"success-row":"danger-row"}function S(e){var o,n,r,_;let t=!0;for(const u of e==null?void 0:e.products){const g=((n=(o=u.pieceProduct)==null?void 0:o.stock)==null?void 0:n.inStock)-((_=(r=u.pieceProduct)==null?void 0:r.stock)==null?void 0:_.freezeStock)||0,x=u.piece-u.lockStock;if(g<x){t=!1;break}}return t}const I=v();async function D(e,t){var o;await((o=I.value)==null?void 0:o.processOrder(e,t,n=>{n&&(d.value=(n==null?void 0:n.status)||0,T.success((n==null?void 0:n.message)||"处理成功"))}))}function J({row:e}){if(e.status<2)return S(e)?"success-row":"danger-row"}function X(e){var t;d.value=e,(t=w.value)==null||t.refresh()}function Z(e,t){var o;(t==null?void 0:t.type)==="expand"||(t==null?void 0:t.type)==="op"||(o=z.value)==null||o.toggleRowExpansion(e)}function ee(e){var n,r,_,u;const t=((r=(n=e.pieceProduct)==null?void 0:n.stock)==null?void 0:r.inStock)-((u=(_=e.pieceProduct)==null?void 0:_.stock)==null?void 0:u.freezeStock)||0,o=e.piece-e.lockStock;return t>=o?0:o-t}function te(e){let t="确定撤销该订单吗？<br />撤销后销售订单将更新为草稿状态<br />";e.status===2&&(t+="<font color=red>该订单已经出货，将自动撤销出货并删除出库单</font><br />"),fe.confirm(t,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning",dangerouslyUseHTMLString:!0}).then(async()=>{h.pms.production.sale.order.revoke({id:e.id}).then(o=>{var n;o&&((n=w.value)==null||n.refresh(),T.success((o==null?void 0:o.message)||"撤销成功"))}).catch(o=>{T.error((o==null?void 0:o.message)||"撤销失败")})})}return(e,t)=>{const o=s("cl-refresh-btn"),n=s("cl-flex1"),r=s("cl-search"),_=s("cl-row"),u=s("el-tab-pane"),g=s("el-button"),x=s("el-tag"),i=s("el-table-column"),oe=s("el-table"),ne=s("cl-table"),$=s("el-row"),ae=s("cl-pagination"),le=s("el-tabs"),ce=s("cl-crud");return p(),f(ce,{ref_key:"Crud",ref:w},{default:c(()=>[l(_,null,{default:c(()=>[l(o),l(n),l(r,{ref_key:"Search",ref:Q},null,512)]),_:1}),l(le,{modelValue:d.value,"onUpdate:modelValue":t[0]||(t[0]=a=>d.value=a),type:"border-card",onTabChange:X},{default:c(()=>[(p(!0),E(be,null,_e(M.value,a=>(p(),f(u,{key:a.value,label:`${a.label}(${a.count})`,name:a.value},null,8,["label","name"]))),128)),l($,null,{default:c(()=>[l(ne,{ref_key:"Table",ref:z,"row-key":"id",class:"table-row-pointer","row-class-name":J,onRowClick:Z},{"slot-btn-revoke":c(({scope:a})=>[y(P)("slot-btn-revoke")?(p(),f(g,{key:0,text:"",bg:"",type:"danger",onClick:b=>te(a.row)},{default:c(()=>[k(" 撤销 ")]),_:2},1032,["onClick"])):O("",!0)]),"slot-btn-process":c(({scope:a})=>[y(P)("slot-btn-process")&&S(a.row)?(p(),f(g,{key:0,text:"",bg:"",type:S(a.row)?"success":"danger",onClick:b=>D(a.row,"outbound")},{default:c(()=>[k(" 发货 ")]),_:2},1032,["type","onClick"])):O("",!0)]),"slot-btn-schedule":c(({scope:a})=>[y(P)("slot-btn-schedule")&&!S(a.row)?(p(),f(g,{key:0,text:"",bg:"",type:"primary",onClick:b=>D(a.row,"schedule")},{default:c(()=>[k(" 排产 ")]),_:2},1032,["onClick"])):O("",!0)]),"slot-btn-lock-stock":c(({scope:a})=>[y(P)("slot-btn-lock-stock")?(p(),f(g,{key:0,text:"",bg:"",type:"warning",onClick:b=>D(a.row,"lockStock")},{default:c(()=>[k(" 锁定库存 ")]),_:2},1032,["onClick"])):O("",!0)]),"column-isEnough":c(({scope:a})=>[a.row.status===2?(p(),E("span",ke," - ")):(p(),E("div",me,[S(a.row)?(p(),f(x,{key:0,type:"success"},{default:c(()=>[k(" 库存充足 ")]),_:1})):(p(),f(x,{key:1,type:"danger"},{default:c(()=>[k(" 库存不足 ")]),_:1}))]))]),"column-products":c(({scope:a})=>[l(oe,{data:a.row.products,style:{width:"100%"},border:"","row-class-name":G},{default:c(()=>[l(i,{prop:"pieceProduct.name",label:"产品名称",align:"center"}),l(i,{prop:"pieceProduct.sku",label:"SKU",align:"center"}),l(i,{prop:"pieceProduct.color",label:"颜色",align:"center"},{default:c(b=>[m("span",null,C(y(re)(y(F),parseInt(b.row.pieceProduct.color))),1)]),_:2},1024),l(i,{prop:"piece",label:"订单数量",align:"center"}),l(i,{prop:"lockStock",label:"锁定库存",align:"center"}),l(i,{prop:"need",label:"需要生产",align:"center"},{default:c(b=>[m("span",null,C(ee(b.row)),1)]),_:2},1024),l(i,{label:"库存状态",align:"center"},{default:c(()=>[l(i,{prop:"pieceProduct.stock.availableQuantity",label:"可用",width:"100",align:"center"},{default:c(b=>{var L,W,j,H;return[k(C((((W=(L=b.row.pieceProduct)==null?void 0:L.stock)==null?void 0:W.inStock)||0)-(((H=(j=b.row.pieceProduct)==null?void 0:j.stock)==null?void 0:H.freezeStock)||0)),1)]}),_:2},1024),l(i,{prop:"pieceProduct.stock.freezeStock",label:"冻结",width:"100",align:"center"}),l(i,{prop:"pieceProduct.stock.productionStock",label:"在产",width:"100",align:"center"}),l(i,{prop:"pieceProduct.stock.waitInboundStock",label:"待入库",width:"100",align:"center"})]),_:2},1024)]),_:2},1032,["data"]),m("div",he,[m("div",ge,[m("div",null,[ve,m("span",null,C(a.row.total),1)])])])]),_:1},512)]),_:1}),l($,null,{default:c(()=>[l(n),l(ae)]),_:1})]),_:1},8,["modelValue"]),l(ie,{ref_key:"ScheduleOrderProcessDialog",ref:I},null,512)]),_:1},512)}}});export{Be as default};
