import{c as D,b as j,A as G,q as y,i as r,w as l,h as o,G as v,B as H,H as O,v as x,j as C,I as P,E as _,o as w}from"./.pnpm-hVqhwuVC.js";import{i as b,d as F,e as L}from"./index-DkYL1aws.js";import{a as M}from"./index-C6cm1h61.js";/* empty css              */const R=D({name:"pms-material-drawing"}),Y=D({...R,props:{material:{type:Object,required:!0},readonly:{type:Boolean,default:!1}},setup(T){const u=T,h=j(u.material),m=[{label:"3D图纸",value:1,type:"success"},{label:"2D图纸",value:2,type:"info"},{label:"工艺图纸",value:3,type:"warning"},{label:"其他图纸",value:4,type:"danger"}],{service:s}=M(),S=10,q=b.useUpsert({dialog:{title:"新增图纸",controls:["close"]},items:[{prop:"name",label:"图纸名称",required:!0,component:{name:"el-input"}},{prop:"path",label:"图纸文件",required:!0,component:{name:"cl-upload",props:{accept:"*",text:"上传图纸",type:"image",disabled:!1,isPrivate:!0}}},{prop:"type",label:"图纸类型",value:1,component:{name:"el-radio-group",options:m},required:!0},{prop:"remark",label:"图纸备注",component:{name:"el-input",props:{type:"textarea",rows:4}}}],async onSubmit(e,{done:t,close:a,next:c}){var n;c({...e,materialId:(n=h.value)==null?void 0:n.id}),t(),a()}}),B=b.useTable({autoHeight:!1,defaultSort:{prop:"version",order:"descending"},columns:[{prop:"name",label:"图纸名称"},{prop:"path",label:"图纸文件",width:100},{prop:"type",label:"图纸类型",dict:m},{prop:"version",label:"图纸版本"},{prop:"remark",label:"图纸备注",showOverflowTooltip:!0},{prop:"createTime",label:"创建时间",width:160},{type:"op",hidden:u.readonly||!F(s.pms.material.drawing.permission.setAsDefault),width:110,buttons:["slot-set-as-default"]}]}),p=b.useCrud({service:s.pms.material.drawing}),E=b.useSearch({items:[{label:"图纸类型",prop:"type",props:{labelWidth:"80px"},component:{name:"el-select",options:m,props:{clearable:!0,placeholder:"请选择图纸类型",onChange(e){var t;(t=p.value)==null||t.refresh({type:e,page:1})}}}},{label:"图纸名称",props:{labelWidth:"80px"},prop:"keyWord",component:{name:"el-input",props:{clearable:!0,onChange(e){var t;(t=p.value)==null||t.refresh({keyWord:e.trim(),page:1})}}}}]});function N(e){const t=e.id;if(!t)return!1;s.pms.material.drawing.request({url:"/download",params:{id:t},method:"GET",responseType:"blob"}).then(a=>{var d;const c=(d=m.find(i=>i.value===e.type))==null?void 0:d.label,n=`物料-${e.name}-${c}-v${e.version}.zip`;L(a,n)&&_.success("图纸下载成功")}).catch(a=>{_.error(a.message)})}function $(e){const t=e.id;if(!t)return!1;s.pms.material.drawing.setAsDefault({id:t}).then(()=>{var a;_.success("设置成功"),(a=p.value)==null||a.refresh()}).catch(a=>{_.error(a.message)})}function A({row:e}){return e.isDefault?"success-row":""}return G(()=>{var e;h.value=u.material,(e=p.value)==null||e.refresh({materialId:h.value.id,size:S})}),(e,t)=>{const a=r("cl-refresh-btn"),c=r("cl-add-btn"),n=r("cl-flex1"),d=r("cl-search"),i=r("cl-row"),g=r("el-button"),W=r("cl-table"),z=r("cl-pagination"),I=r("cl-upsert"),U=r("cl-crud"),k=O("permission");return w(),y(U,{ref_key:"Crud",ref:p},{default:l(()=>[o(i,null,{default:l(()=>[o(a),u.readonly?H("",!0):v((w(),y(c,{key:0},null,512)),[[k,x(s).pms.material.drawing.permission.add]]),o(n),o(d,{ref_key:"Search",ref:E},null,512)]),_:1}),o(i,null,{default:l(()=>[o(W,{ref_key:"Table",ref:B,"row-class-name":A},{"slot-set-as-default":l(({scope:f})=>[v(o(g,{type:"primary",text:"",onClick:V=>$(f.row)},{default:l(()=>[C(" 设为默认 ")]),_:2},1032,["onClick"]),[[P,!f.row.isDefault]])]),"column-path":l(({scope:f})=>[v((w(),y(g,{text:"",type:"primary",onClick:V=>N(f.row)},{default:l(()=>[C(" 下载图纸 ")]),_:2},1032,["onClick"])),[[k,x(s).pms.material.drawing.permission.download]])]),_:1},512)]),_:1}),o(i,null,{default:l(()=>[o(n),o(z,{layout:"total, prev, pager, next"})]),_:1}),o(I,{ref_key:"Upsert",ref:q},null,512)]),_:1},512)}}});export{Y as _};
