import{i as l,s as h}from"./index-DkYL1aws.js";import{c as d,b as r,E as _,q as v,w as t,h as e,i as s,j as x,y as c,o as y}from"./.pnpm-hVqhwuVC.js";const k=c("div",{flex:"~ items-center",style:{position:"relative"}},[c("div",{class:"item"},[c("div",{class:"head"},[c("div"),c("div",null,"1")]),c("div",{class:"main"})])],-1),C=d({name:"undefined"}),N=d({...C,setup(g){r(["A"]),r("base");const i=l.useCrud({service:h.pims.config},n=>{n.refresh()});return l.useTable({contextMenu:["refresh","check","order-desc","order-asc"],columns:[{label:"状态",prop:"status",dict:[{label:"开启",value:1,type:"success"},{label:"关闭",value:0,type:"danger"}]},{label:"创建时间",prop:"createTime",sortable:"desc"},{type:"op",buttons:[{label:"修改",type:"primary",onClick(){_.success("点击")}},{label:"删除",type:"danger",onClick(){_.success("点击")}}]}]}),(n,w)=>{const p=s("cl-refresh-btn"),a=s("cl-flex1"),u=s("cl-add-btn"),m=s("cl-search-key"),o=s("cl-row"),f=s("cl-pagination"),b=s("cl-crud");return y(),v(b,{ref_key:"Crud",ref:i},{default:t(()=>[e(o,null,{default:t(()=>[e(p),e(a),e(u,null,{default:t(()=>[x("新增")]),_:1}),e(m,{field:"year"})]),_:1}),e(o,{mt:"10px"},{default:t(()=>[k]),_:1}),e(o,{mt:"10px"},{default:t(()=>[e(a),e(f)]),_:1})]),_:1},512)}}});export{N as default};
