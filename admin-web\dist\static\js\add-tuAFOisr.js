import{g as ne,i as Y,j as R}from"./index-DkYL1aws.js";import{a as re}from"./index-C6cm1h61.js";import{c as j,b as s,z as me,q as Z,w as r,h as n,i as c,y as m,t as f,v as ae,o as b,A as te,f as C,F as le,s as oe,j as x,B as ve,W as fe,E as V,af as _e,ag as ge}from"./.pnpm-hVqhwuVC.js";import{_ as ye}from"./_plugin-vue_export-helper-DlAUqK2U.js";const be=j({name:"delivery-order-product-selector"}),we=j({...be,props:{modelValue:Array,warehouseId:{type:Number,default:0}},emits:["update:modelValue"],setup($,{expose:E,emit:U}){const B=$,A=U,d=s(),w=s(),{dict:T}=ne(),{service:q}=re(),k=T.get("color"),i=[{label:"单品",value:0},{label:"内盒/展示盒",value:1},{label:"箱装",value:2}];Y.useTable({autoHeight:!1,columns:[{type:"selection"},{label:"产品SKU",prop:"product.sku",width:150},{label:"产品名",prop:"product.nameEn",align:"left"},{label:"颜色",prop:"productColor",width:150},{label:"外箱PO#",prop:"cartonPo",width:150},{label:"空运提单号",prop:"airway",width:150},{label:"箱号",prop:"carton",width:150},{label:"派送单位",prop:"originalProductUnit",width:150},{label:"单位数量",prop:"originalProductUnitQuantity",width:150},{label:"可用库存",prop:"available",width:150}]});const h=Y.useCrud({service:q.pms.warehouse.destination.stock.record},u=>{u.refresh({size:10,destinationPointId:B.warehouseId})}),S=Y.useSearch({items:[{label:"SKU/UPC/名称",prop:"keyWord",props:{labelWidth:"120px"},component:{name:"el-input",props:{clearable:!0,onChange(u){var p;(p=h.value)==null||p.refresh({keyWord:u.trim(),page:1})}}}}]});me(()=>B.modelValue,u=>{w.value=u&&String(u)},{immediate:!0});function P(u){A("update:modelValue",u)}function K(u){d.value.toggleRowSelection(u)}const W=s();function N(){var u,p;d.value.clearSelection(),(u=h.value)==null||u.refresh({keyWord:void 0,page:1}),(p=W.value)==null||p.reset()}function z(u){const p=i.find(Q=>Q.value===u);return(p==null?void 0:p.label)||""}return E({resetData:N}),(u,p)=>{const Q=c("cl-refresh-btn"),L=c("cl-flex1"),G=c("cl-search"),D=c("el-row"),H=c("cl-table"),J=c("cl-pagination"),X=c("cl-crud");return b(),Z(X,{ref_key:"Crud",ref:h,class:"delivery-order-product-selector"},{default:r(()=>[n(D,null,{default:r(()=>[n(Q),n(L),n(G,{ref_key:"Search",ref:S},null,512)]),_:1}),n(D,null,{default:r(()=>[n(H,{ref_key:"productTable",ref:d,onSelectionChange:P,onRowClick:K},{"column-originalProductUnit":r(({scope:O})=>[m("span",null,f(z(O.row.originalProductUnit)),1)]),"column-productColor":r(({scope:O})=>[m("span",null,f(ae(R)(ae(k),parseInt(O.row.product.color))),1)]),_:1},512)]),_:1}),n(D,null,{default:r(()=>[n(L),n(J,{"page-size":10,layout:"prev, pager, next, jumper"})]),_:1})]),_:1},512)}}}),ee=$=>(_e("data-v-6424f980"),$=$(),ge(),$),he={class:"cl-crud delivery-order-create"},Pe=ee(()=>m("div",{class:"delivery-order-create-header"}," 基础信息 ",-1)),Ue={class:"delivery-order-create-body"},ke={class:"delivery-order-create-body"},Ie=ee(()=>m("div",{class:"delivery-order-create-header"}," 产品信息 ",-1)),xe={key:0,style:{display:"flex","align-items":"center"}},Ve={key:1,style:{display:"flex","align-items":"center"}},$e={key:0,style:{display:"flex","align-items":"center"}},Se=ee(()=>m("span",{style:{color:"var(--el-color-danger)"}},"*",-1)),Ce={class:"dialog-footer"},Ee=j({name:"undefined"}),qe=j({...Ee,setup($){const{router:E,service:U}=re(),{dict:B}=ne(),A=[{label:"单品",value:0},{label:"内盒/展示盒",value:1},{label:"箱装",value:2}],d=s({id:null,orderSn:"",ref:"",consignee:"",products:[]}),w=s(!1),T=s(!1),q=s(),k=s(!1),i=s([]),h=s([]),S=B.get("color"),P=s([]),K=s(!1),W=s([]),N=s(!1),z=s(),u=s(!1);function p(o){const e=A.find(t=>t.value===o);return(e==null?void 0:e.label)||""}async function Q(){var o;await((o=U.pms)==null?void 0:o.warehouse.point.list().then(e=>{W.value=e.map(t=>({value:t.id,label:`${t.name}-${t.address}`,dir:t.type})).filter(t=>t.dir===1)}))}function L(){var o;h.value=[],k.value=!0,(o=z.value)==null||o.resetData()}function G(o){const e=P.value.find(a=>a.id===o);if(!e)return;const t=i.value.findIndex(a=>a.id===o);t!==-1?(i.value[t].productId=e.productId,i.value[t].sku=e.sku,i.value[t].name=e.name,i.value[t].nameEn=e.nameEn,i.value[t].color=R(S.value,Number.parseInt(e.color)),i.value[t].available=e.available,i.value[t].originalProductUnit=e.originalProductUnit,i.value[t].originalProductUnitName=p(e.originalProductUnit),i.value[t].originalProductUnitQuantity=e.originalProductUnitQuantity,i.value[t].carton=e.carton,i.value[t].cartonPo=e.cartonPo,i.value[t].airway=e.airway,i.value[t].disabled=!0):i.value.push({id:e.id,index:new Date().getTime()+Math.floor(Math.random()*1e3),productId:e.productId,sku:e.sku,label:`${e.sku}-${e.airway}-${e.cartonPo}-${e.carton}`,name:e.name,nameEn:e.nameEn,color:R(S.value,Number.parseInt(e.color)),available:0,disabled:!1,originalProductUnit:e.originalProductUnit,originalProductUnitName:p(e.originalProductUnit),originalProductUnitQuantity:e.originalProductUnitQuantity,carton:e.carton,cartonPo:e.cartonPo,airway:e.airway}),P.value=P.value.map(a=>(a.id===o&&(a.disabled=!0),a))}const D=s();function H(o){var e;i.value=i.value.filter(t=>t.index!==o),(e=D.value)==null||e.resetData()}function J(o){if(!o)return;const e=d.value.destinationPointId;return U.pms.warehouse.destination.stock.record.list({destinationPointId:e,keyWord:o}).then(t=>{P.value=t.filter(a=>a.available>0).map(a=>{var I,M,g,F;return{index:new Date().getTime()+Math.floor(Math.random()*1e3),id:a.id,quantity:0,productId:a.productId,sku:`${(I=a.product)==null?void 0:I.sku}`,label:`${(M=a.product)==null?void 0:M.sku}-${a.airway}-${a.cartonPo}-${a.carton}`,name:`${(g=a.product)==null?void 0:g.name}`,nameEn:`${(F=a.product)==null?void 0:F.nameEn}`,color:`${a.product.color}`,disabled:!!i.value.some(v=>v.id===a.id),available:a.available,originalProductUnit:a.originalProductUnit,originalProductUnitName:`${p(a.originalProductUnit)}`,originalProductUnitQuantity:a.originalProductUnitQuantity,airway:`${a.airway}`,cartonPo:`${a.cartonPo}`,carton:`${a.carton}`}})})}function X(){P.value=[],i.value.push({index:new Date().getTime()+Math.floor(Math.random()*1e3),id:null,productId:null,quantity:null,sku:"",label:"",name:"",nameEn:"",color:"",available:0,disabled:!1,originalProductUnit:0,originalProductUnitName:"",originalProductUnitQuantity:1,carton:"",cartonPo:"",airway:""})}async function O(){q.value&&await q.value.validate(o=>{if(o){if(T.value=!0,i.value.length===0){V.error({message:"请添加产品"});return}if(i.value.find(a=>a.id===null||a.id===void 0||a.id===0)){V.error({message:"请选择正确的产品"});return}if(i.value=i.value.filter(a=>a.productId!==null&&a.productId!==void 0&&a.productId!==0),i.value.find(a=>a.quantity===null||a.quantity===void 0||a.quantity===0)){V.error({message:"请填写正确的产品数量"});return}d.value.products=i.value.map(a=>({stockRecordId:a.id,productId:a.productId,quantity:a.quantity})),w.value=!0,d.value.id&&d.value.id>0?U.pms.sale.delivery.order.update(d.value).then(()=>{const a=d.value.id;E.push(`/pms/sale/delivery/order?tab=0&expand=${a}`),V.success({message:"保存成功",onClose:()=>{w.value=!1}})}).catch(a=>{V.error({message:a.message}),w.value=!1}):(delete d.value.id,U.pms.sale.delivery.order.add(d.value).then(a=>{const I=a==null?void 0:a.id;E.push(`/pms/sale/delivery/order?tab=0&expand=${I}`),V.success({message:"保存成功",onClose:()=>{w.value=!1}})}).catch(a=>{V.error({message:a.message}),w.value=!1}))}})}function ie(){k.value=!1,h.value.forEach(o=>{var t;i.value.find(a=>a.id===o.id)||i.value.push({index:new Date().getTime()+Math.floor(Math.random()*1e3),id:o.id,productId:o.productId,sku:(t=o.product)==null?void 0:t.sku,label:`${o.product.sku}-${o.airway}-${o.cartonPo}-${o.carton}`,name:o.product.name,nameEn:o.product.nameEn,color:R(S.value,Number.parseInt(o.product.color)),disabled:i.value.some(a=>a.id===o.id),available:o.available,originalProductUnit:o.originalProductUnit,originalProductUnitName:p(o.originalProductUnit),originalProductUnitQuantity:o.originalProductUnitQuantity,airway:o.airway,cartonPo:o.cartonPo,carton:o.carton})})}async function de(o){await U.pms.sale.delivery.order.info({id:o}).then(e=>{d.value={id:e.id,orderSn:`${e.orderSn}`,ref:`${e.ref}`,consignee:`${e.consignee}`,destinationPointId:e.destinationPointId,products:[]},i.value=e.products.map(t=>({id:t.rid,index:new Date().getTime()+Math.floor(Math.random()*1e3),productId:t.id,sku:t.sku,label:`${t.sku}-${t.airway}-${t.cartonPo}-${t.carton}`,name:t.name,nameEn:t.nameEn,color:R(S.value,Number.parseInt(t.color)),available:t.available,disabled:!0,originalProductUnit:t.originalProductUnit,originalProductUnitName:p(t.originalProductUnit),originalProductUnitQuantity:t.originalProductUnitQuantity,carton:t.carton,cartonPo:t.cartonPo,airway:t.airway,quantity:t.quantity}))})}return Q(),te(()=>{var o;N.value=d.value.destinationPointId!=null&&((o=d.value)==null?void 0:o.destinationPointId)>0}),te(()=>{const o=E.currentRoute.value.query.id;o&&(u.value=!0,de(o))}),(o,e)=>{const t=c("el-input"),a=c("el-form-item"),I=c("el-option"),M=c("el-select"),g=c("el-button"),F=c("el-row"),v=c("el-table-column"),ue=c("el-input-number"),ce=c("el-table"),se=c("el-form"),pe=c("el-dialog");return b(),C("div",he,[n(se,{ref_key:"orderForm",ref:q,model:d.value,"label-width":"150px",size:"large","status-icon":""},{default:r(()=>[Pe,m("div",Ue,[n(a,{label:"订单号",prop:"orderSn",rules:{required:!0,message:"订单号不能为空"}},{default:r(()=>[n(t,{modelValue:d.value.orderSn,"onUpdate:modelValue":e[0]||(e[0]=l=>d.value.orderSn=l),placeholder:"请输入订单号"},null,8,["modelValue"])]),_:1}),n(a,{label:"参考号/Ref#",prop:"ref",rules:{required:!0,message:"参考号不能为空"}},{default:r(()=>[n(t,{modelValue:d.value.ref,"onUpdate:modelValue":e[1]||(e[1]=l=>d.value.ref=l),placeholder:"请输入参考号"},null,8,["modelValue"])]),_:1}),n(a,{label:"海外仓",prop:"destinationPointId",rules:{required:!0,message:"海外仓必选"}},{default:r(()=>[n(M,{modelValue:d.value.destinationPointId,"onUpdate:modelValue":e[2]||(e[2]=l=>d.value.destinationPointId=l),placeholder:"请选择海外仓",style:{width:"400px"}},{default:r(()=>[(b(!0),C(le,null,oe(W.value,l=>(b(),Z(I,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),n(a,{label:"派送地址",prop:"consignee",rules:{required:!0,message:"派送地址不能为空"}},{default:r(()=>[n(t,{modelValue:d.value.consignee,"onUpdate:modelValue":e[3]||(e[3]=l=>d.value.consignee=l),placeholder:"请输入派送地址",autosize:{minRows:1,maxRows:4},type:"textarea"},null,8,["modelValue"])]),_:1})]),m("div",ke,[Ie,n(F,null,{default:r(()=>[n(g,{type:"primary",style:{"margin-bottom":"20px"},disabled:!N.value,onClick:L},{default:r(()=>[x(" 选择产品 ")]),_:1},8,["disabled"])]),_:1}),n(ce,{data:i.value,style:{width:"100%"},border:""},{default:r(()=>[n(v,{prop:"sku",label:"SKU",width:"200",align:"left"},{default:r(l=>{var _;return[l.row.id===0||l.row.sku===""?(b(),C("div",xe,[n(M,{modelValue:l.row.id,"onUpdate:modelValue":y=>l.row.id=y,filterable:"","reserve-keyword":"",loading:K.value,placeholder:"请选择产品",style:{width:"400px"},remote:"","remote-method":J,onChange:G},{default:r(()=>[(b(!0),C(le,null,oe(P.value,y=>(b(),Z(I,{key:y.id,disabled:y.disabled,label:y.label,value:y.id},null,8,["disabled","label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","loading"])])):(b(),C("div",Ve,f((_=l.row)==null?void 0:_.sku),1))]}),_:1}),n(v,{prop:"name",label:"名称"},{default:r(l=>{var _,y;return[l.row.name===""&&l.row.nameEn===""?ve("",!0):(b(),C("div",$e,f((_=l.row)==null?void 0:_.name)+" / "+f((y=l.row)==null?void 0:y.nameEn),1))]}),_:1}),n(v,{prop:"color",label:"颜色",width:"150",align:"center"},{default:r(l=>{var _;return[m("span",null,f((_=l.row)==null?void 0:_.color),1)]}),_:1}),n(v,{prop:"cartonPo",label:"外箱PO#",width:"150",align:"center"},{default:r(l=>[m("span",null,f(l.row.cartonPo),1)]),_:1}),n(v,{prop:"airway",label:"空运提单号",width:"150",align:"center"},{default:r(l=>[m("span",null,f(l.row.airway),1)]),_:1}),n(v,{prop:"carton",label:"箱号",width:"100",align:"center"},{default:r(l=>[m("span",null,f(l.row.carton),1)]),_:1}),n(v,{prop:"originalProductUnit",label:"派送单位",width:"100",align:"center"},{default:r(l=>[m("span",null,f(l.row.originalProductUnitName),1)]),_:1}),n(v,{prop:"originalProductUnitQuantity",label:"单位数量",width:"100",align:"center"},{default:r(l=>[m("span",null,f(l.row.originalProductUnitQuantity),1)]),_:1}),n(v,{prop:"available",label:"可用库存",width:"100",align:"center"},{default:r(l=>[m("span",null,f(l.row.available||0),1)]),_:1}),n(v,{prop:"quantity",label:"*派送数量",width:"200"},{header:r(()=>[Se,x(" 派送数量 ")]),default:r(l=>[m("div",{style:{display:"flex","align-items":"center"},class:fe(T.value&&!(l.row.quantity>0)?"quantity-input-error":"")},[n(ue,{modelValue:l.row.quantity,"onUpdate:modelValue":_=>l.row.quantity=_,disabled:!(l.row.productId>0),"step-strictly":"",min:l.row.originalProductUnitQuantity,max:l.row.available,step:l.row.originalProductUnitQuantity,placeholder:"请输入派送数量"},null,8,["modelValue","onUpdate:modelValue","disabled","min","max","step"])],2)]),_:1}),n(v,{label:"操作",width:"200"},{default:r(l=>[n(g,{type:"danger",onClick:_=>H(l.row.index)},{default:r(()=>[x(" 删除 ")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"]),n(g,{style:{width:"100%"},class:"btn-product-add",disabled:!N.value,onClick:X},{default:r(()=>[x(" + 添加产品 ")]),_:1},8,["disabled"]),n(g,{type:"success",style:{"margin-top":"20px"},loading:w.value,onClick:O},{default:r(()=>[x(" 保存为草稿 ")]),_:1},8,["loading"])])]),_:1},8,["model"]),n(pe,{modelValue:k.value,"onUpdate:modelValue":e[6]||(e[6]=l=>k.value=l),title:"选择产品",width:"80%"},{footer:r(()=>[m("span",Ce,[n(g,{onClick:e[5]||(e[5]=l=>k.value=!1)},{default:r(()=>[x("取消")]),_:1}),n(g,{type:"primary",onClick:ie},{default:r(()=>[x(" 确认选择 ")]),_:1})])]),default:r(()=>[n(we,{ref_key:"deliveryOrderProductSelector",ref:z,modelValue:h.value,"onUpdate:modelValue":e[4]||(e[4]=l=>h.value=l),"warehouse-id":d.value.destinationPointId},null,8,["modelValue","warehouse-id"])]),_:1},8,["modelValue"])])}}}),Me=ye(qe,[["__scopeId","data-v-6424f980"]]);export{Me as default};
