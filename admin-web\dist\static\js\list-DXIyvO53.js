import{_ as t}from"./space-inner.vue_vue_type_style_index_0_lang-Bvb2FfQe.js";import{c as o,q as r,o as e}from"./.pnpm-hVqhwuVC.js";import"./index-DkYL1aws.js";import"./index-C6cm1h61.js";import"./hook-CIEgpwJn.js";import"./viewer.vue_vue_type_script_setup_true_name_item-viewer_lang-6n4_YlHr.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";const p=o({name:"upload-list"}),d=o({...p,setup(a){return(m,_)=>(e(),r(t))}});export{d as default};
