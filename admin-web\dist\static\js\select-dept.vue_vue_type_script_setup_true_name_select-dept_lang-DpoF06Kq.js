import{b as $,s as q}from"./index-BtOcqcNl.js";import{c as k,k as w,l as D,n as W,b as I,E as F,e as U,f as N,F as S,h as o,q as A,B as P,i as h,w as p,m as x,v as r,x as y,o as C}from"./.pnpm-hVqhwuVC.js";const R=k({name:"select-dept"}),G=k({...R,props:w({width:{default:"150px"},showUser:{type:Boolean,default:!0},span:{},departmentId:{},labelWidth:{},includeDeptCode:{default:()=>["生产","品质","研发"]}},{modelValue:{},modelModifiers:{}}),emits:w(["update:modelValue","change","update:departmentId"],["update:modelValue"]),setup(v,{emit:B}){const a=v,l=B,M=$(),g=D(),d=W(v,"modelValue"),i=M.getDeptList(),u=I({});q.pims.workitem.systemUserList().then(t=>{t.forEach(e=>{e.label=e.name,e.value=e.id,u.value[e.departmentId]||(u.value[e.departmentId]=[]),u.value[e.departmentId].push(e)})}).catch(t=>{F.error(t.message||"获取用户列表失败")});const n=I([]),m=U({get:()=>{if(!(a.departmentId&&[0,"0"].includes(a.departmentId)))return a.departmentId},set:t=>{l("update:departmentId",t)}}),c=U({get:()=>{if(![0,"0"].includes(d.value))return d.value&&n.value.length===0&&_(d.value),d.value},set:t=>{d.value=t,l("update:modelValue",t)}});function E(t){if(!t){n.value=[],l("update:departmentId",void 0);return}l("update:departmentId",void 0);const e=_(t);l("change",e,i.value)}function L(t){if(!t){l("update:departmentId",void 0);return}const e=n.value.find(s=>s.id===t);l("change",e,n.value),l("update:departmentId",t)}function _(t){const e=i.find(s=>s.id===t)||{};return n.value=u.value[e.id]||[],e}return(t,e)=>{const s=h("el-select-v2"),b=h("el-form-item"),V=h("el-col");return C(),N(S,null,[o(V,{span:a.span||8},{default:p(()=>[o(b,{"label-width":`${a.labelWidth||100}px`,label:"责任单位",required:""},{default:p(()=>[o(s,x({modelValue:r(c),"onUpdate:modelValue":e[0]||(e[0]=f=>y(c)?c.value=f:null),filterable:""},r(g),{clearable:"",style:`width:${a.width}`,options:r(i),onChange:E}),null,16,["modelValue","style","options"])]),_:1},8,["label-width"])]),_:1},8,["span"]),a.showUser?(C(),A(V,{key:0,span:a.span||8},{default:p(()=>[o(b,{"label-width":`${a.labelWidth||100}px`,label:"负责人",required:""},{default:p(()=>[o(s,x({modelValue:r(m),"onUpdate:modelValue":e[1]||(e[1]=f=>y(m)?m.value=f:null),filterable:""},r(g),{clearable:"",style:`width:${a.width}`,options:n.value,onChange:L}),null,16,["modelValue","style","options"])]),_:1},8,["label-width"])]),_:1},8,["span"])):P("",!0)],64)}}});export{G as _};
