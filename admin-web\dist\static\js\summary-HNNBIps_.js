import{c as X,b as d,E as v,h as r,j as w,i as k,ac as ve,q as W,w as b,y as U,G as Y,H as G,v as ke,t as ge,W as Ce,ah as _e,ai as De,ad as Z,aj as we,Z as be,o as F}from"./.pnpm-hVqhwuVC.js";import{g as Se,s as g,j as $e,e as xe,d as M,i as J}from"./index-BtOcqcNl.js";import{S as Pe}from"./schedule-plan-add-BlCJ77H3.js";import{S as Oe}from"./schedule-order-process-D655y8jB.js";/* empty css              */import{_ as Re}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-D95m1iJL.js";const Ke={class:"production-summary"},Le={class:"production-summary-title"},Te=X({name:"undefined"}),Ee=X({...Te,setup(We){const{dict:I}=Se(),ee=I.get("color"),V=d(15),S=d(),$=d([]),C=d([]),x=d(""),P=d([]),B=d([]),Q=d([]),O=d(!0),te=d(!1),m=d([]),_=d([]),R=d(!1),q=d("");re(),z(),K();async function K(){g.pms.production.schedule.summary({days:V.value}).then(async e=>{S.value=e,z(),ie(),O.value=!1,te.value=!0}).catch(e=>{v.error((e==null?void 0:e.message)||"读取数据失败，请重试!"),O.value=!1})}function ae(){var l;const e=((l=S.value)==null?void 0:l.orders)||[],t=[];return e.forEach(a=>{const n=`order-${a.orderId}`,i={title:a.orderSn,key:n,dataKey:n,width:le(a.orderSn),align:"center",headerClass:`order-header-cell ${a.isStockSufficient?"order-sufficient":a.isPending?"order-insufficient-pending":"order-insufficient"}`,createTime:a.createTime,requiredShipDate:a.requiredShipDate,orderId:a.orderId,isPending:a.isPending,isStockSufficient:a.isStockSufficient,headerCellRenderer:o=>y(o),cellRenderer:o=>{var p,j,H;const s=(p=o.column)==null?void 0:p.orderId,u=`orderLockStock-${s}`,h=((j=o.rowData)==null?void 0:j[u])||0,f=`orderRemainQuantity-${s}`,c=((H=o.rowData)==null?void 0:H[f])||0,D=o.cellData||0;return r("div",{class:o.class},[D>0&&r("span",{class:"order-lock-stock"},[h]),D>0&&r("span",null,[w("+"),r("span",{class:"order-remaining"},[c])]),D>0&&r("span",null,[w("="),r("span",{class:"order-total"},[D])])])}};t.push(i)}),t}function le(e){const l=e.length*8+100;return l<100?100:l}function z(){var i,o;const e=[],t=[{title:"产品名称",width:130,key:"name",dataKey:"name",align:"center",cellRenderer:s=>r(k("el-tooltip"),{class:"box-item",effect:"dark",content:s.cellData||"-"},{default:()=>[r("div",{class:s.class},[s.cellData||"-"])]})},{title:"SKU",width:130,key:"sku",dataKey:"sku",align:"center"},{title:"颜色",width:60,key:"color",dataKey:"color",align:"center"},{title:"库存",width:80,key:"availableStock",dataKey:"availableStock",align:"center",headerCellRenderer:s=>y(s)},{title:"未结订单",width:100,key:"unscheduledQuantity",dataKey:"unscheduledQuantity",align:"center",headerCellRenderer:s=>y(s)},{title:"剩余订单",width:100,key:"remainingStock",dataKey:"remainingStock",align:"center",headerCellRenderer:s=>y(s)}],l=ae(),a=[{title:"未出货合计",width:120,key:"unshippedQuantity",dataKey:"unshippedQuantity",align:"center",headerCellRenderer:s=>y(s)},{title:"库存出货差数",width:125,key:"stockShipmentDifference",dataKey:"stockShipmentDifference",align:"center",headerCellRenderer:s=>y(s)}],n=[{title:`截止${(i=_.value[_.value.length-1])==null?void 0:i.month}月${(o=_.value[_.value.length-1])==null?void 0:o.day}日出货差异`,width:130,key:"deadlineDifference",dataKey:"deadlineDifference",align:"center",headerCellRenderer:s=>y(s)},{title:"出货后剩余订单",width:100,key:"remainingOrderQuantity",dataKey:"remainingOrderQuantity",align:"center",headerCellRenderer:s=>y(s)}];P.value=e.concat(t,l,a,B.value,n),Q.value=P.value.map((s,u)=>{let h;return window.innerWidth<600?h=void 0:u<6&&(h=Z.LEFT),{...s,fixed:h,width:s.width}})}function ne(){const e=V.value,t=[];for(let l=0;l<e;l++){const a=new Date;a.setDate(a.getDate()+l);const n=a.getFullYear(),i=a.getMonth()+1,o=a.getDate();t.push({year:n,month:i,day:o})}return t}function L(){const e=[];C.value=$.value,m.value=m.value.filter(t=>{const{key:l}=t;return P.value.some(a=>a.key===l)}),m.value.length===0?C.value=$.value:(C.value.forEach(t=>{m.value.every(l=>{const{key:a,filterValue:n}=l,i=t[a];return!n.includes(i)})&&e.push(t)}),C.value=e)}function se(e,t,l){const a=[void 0,0],n=t.key;e?m.value.push({key:n,filterValue:a}):m.value=m.value.filter(i=>i.key!==n),L()}const T=d(),oe=d();function N(e,t,l,a){e.preventDefault(),e.stopPropagation();const n=[];a&&M(g.pms.production.sale.order.permission.lockStock)&&n.push({label:"锁定库存",callback(s){g.pms.production.sale.order.info({orderId:t}).then(u=>{var f;const h=u;if(!h){v.error("订单不存在");return}(f=T.value)==null||f.processOrder(h,"lockStock",c=>{c&&(v.success((c==null?void 0:c.message)||"处理成功"),E())})}).catch(u=>{v.error(u.message||"获取订单失败")}),s()}});const i=a||l&&!a,o=M({or:[g.pms.production.sale.order.permission.outbound,g.pms.production.sale.order.permission.schedule]});i&&o&&n.push({label:l?"发货":"排产",callback(s){g.pms.production.sale.order.info({orderId:t}).then(u=>{var f;const h=u;if(!h){v.error("订单不存在");return}(f=T.value)==null||f.processOrder(h,l?"outbound":"schedule",c=>{c&&(v.success((c==null?void 0:c.message)||"处理成功"),E())})}).catch(u=>{v.error(u.message||"获取订单失败")}),s()}}),J.ContextMenu.open(e,{list:n})}function A(e,t){e.preventDefault(),e.stopPropagation();const l=t.split("-")[1].replace(/(\d{4})(\d{2})(\d{2})/,"$1-$2-$3"),a=[];M(g.pms.production.schedule.plan.permission.add)&&a.push({label:"添加计划",callback(n){R.value=!0,q.value=l,n()}}),J.ContextMenu.open(e,{list:a})}function y(e){const t=d(!1);t.value=m.value.some(n=>n.key===e.column.key);const l=e.column.key.startsWith("order-")&&e.column.isPending,a=e.column.key.startsWith("order-");return r("div",{class:`header-cell-with-filter ${a?"header-cell-order":""} ${t.value?"header-cell-fileted":""}`},[l?r("div",{class:"header-cell-with-filter-pending"},[w("待处理")]):null,r("div",{class:"header-cell-filter"},[r("p",{class:"header-cell-with-filter-title"},[e.column.title]),r(k("el-popover"),{placement:"bottom",trigger:"click",align:"center"},{default:()=>[r("div",{class:"date-header-cell-filter-popover"},[r(k("el-checkbox"),{modelValue:t.value,"onUpdate:modelValue":n=>t.value=n,label:"过滤空白和0",name:"filter",onChange:n=>se(n,e.column)},null)])],reference:()=>r(k("el-icon"),{class:"header-cell-with-filter-icon"},{default:()=>[r(ve,null,null)]})})])])}function re(){_.value=ne();const e=[];return _.value.forEach(t=>{const l=t.year,a=t.month<10?`0${t.month}`:t.month,n=t.day<10?`0${t.day}`:t.day,i=`date-${l}${a}${n}`,o={title:`${t.month}月${t.day}日`,key:i,dataKey:i,width:110,align:"center",headerClass:"date-header-cell",headerCellRenderer:s=>y(s)};e.push(o)}),B.value=e,e}function ie(){const e=S.value,t=(e==null?void 0:e.products)||[];C.value=[];let l=0;t.forEach(a=>{const n=$e(ee.value,Number.parseInt(a.color)).split("/")[0];n?a.color=n:a.color="-";const i={...a,index:l};l++,C.value.push(i)}),$.value=C.value,L()}const ce=({cells:e,columns:t,headerIndex:l})=>{if(l===2)return e;const a=[];let n=0;return t.forEach((i,o)=>{if(i.placeholderSign===we)a.push(e[o]);else{const s="order-",u=t[o+1],h=i.key.startsWith(s),f=u&&u.key.startsWith(s),c=o>0&&t[o-1].key.startsWith(s),D=o===t.length-1;if(n+=t[o].width,h){const p=t[o];a.push(r("div",{class:"custom-header-cell order-header-cell",role:"columnheader",style:{...e[o].props.style,width:`${i.width}px`}},[l===0?p==null?void 0:p.createTime:p==null?void 0:p.requiredShipDate])),n=0}else if(!h&&f)a.push(r("div",{class:"custom-header-cell",role:"columnheader",style:{...e[o].props.style,width:`${n}px`}},[l===0?"下单日期":"交期"])),n=0;else if(!c){if(D){const p=i.fixed===Z.LEFT;a.push(r("div",{class:"custom-header-cell",role:"columnheader",style:{...e[o].props.style,width:`${n}px`}},[p?l===0?"下单日期":"交期":""]))}}}}),a};function de({column:e,columnIndex:t}){const l=`hovering-col-${t}`;return{"data-key":l,onMouseenter:()=>{x.value=l},onMouseleave:()=>{x.value=""},onContextmenu:a=>{e.key.startsWith("date-")?A(a,e.key):e.key.startsWith("order-")&&N(a,e.orderId,e.isStockSufficient,e.isPending)}}}function ue({column:e}){return{onContextmenu:t=>{e.key.startsWith("date-")?A(t,e.key):e.key.startsWith("order-")&&N(t,e.orderId,e.isStockSufficient,e.isPending)}}}function he({headerIndex:e}){if(e===0)return"first-row-header";if(e===2)return"main-header"}function E(){K()}function fe(){E()}function pe(){K()}function me(){m.value=[],L()}function ye(){const e={url:"/export",method:"POST",responseType:"blob"};g.pms.production.schedule.request(e).then(t=>{const a=`${be().format("YY-MM-DD")}-订单排产计划.xlsx`;xe(t,a)&&v.success("导出成功")}).catch(t=>{v.error(t.message||"导出失败")})}return(e,t)=>{const l=k("el-button"),a=k("el-row"),n=k("el-table-v2"),i=k("cl-form"),o=k("el-auto-resizer"),s=G("permission"),u=G("loading");return F(),W(o,null,{default:b(({height:h,width:f})=>[U("div",Ke,[r(a,{class:"production-summary-header"},{default:b(()=>[r(l,{class:"refresh-button",onClick:fe},{default:b(()=>[w(" 刷新 ")]),_:1}),Y((F(),W(l,{class:"export-button",type:"success",onClick:ye},{default:b(()=>[w(" 导出 ")]),_:1})),[[s,ke(g).pms.production.schedule.permission.export]]),r(l,{class:"clear-filter-button",type:"primary",disabled:m.value.length===0,onClick:me},{default:b(()=>[w(" 清除过滤 ")]),_:1},8,["disabled"]),U("div",Le,ge(new Date().getMonth()+1)+"月订单排产计划 ",1)]),_:1}),Y((F(),W(n,{fixed:"",columns:Q.value,data:C.value,"header-height":[35,35,60],"row-key":"sku","header-class":he,width:f-20,height:h-60,class:Ce(`production-summary-table ${x.value}`),"cell-props":de,"header-cell-props":ue},{header:b(c=>[r(ce,_e(De(c)),null,16)]),_:2},1032,["columns","data","width","height","class"])),[[u,O.value]])]),r(Pe,{modelValue:R.value,"onUpdate:modelValue":t[0]||(t[0]=c=>R.value=c),"schedule-date":q.value,onClose:pe},null,8,["modelValue","schedule-date"]),r(Oe,{ref_key:"ScheduleOrderProcessDialog",ref:T},null,512),r(i,{ref_key:"ScheduleOrderRemark",ref:oe},null,512)]),_:1})}}}),Ne=Re(Ee,[["__scopeId","data-v-5d33bb43"]]);export{Ne as default};
