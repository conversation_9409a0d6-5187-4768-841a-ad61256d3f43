import{U as te}from"./user-avatar-DTjmVWm6.js";import{s as c,i as H,e as ae}from"./index-BtOcqcNl.js";import{_ as oe}from"./select-supplier.vue_vue_type_script_setup_true_name_select-supplier_lang-sfFRiMa7.js";import{_ as ne}from"./select-material.vue_vue_type_script_setup_true_name_select-material_lang-vqQUMFLp.js";import{c as S,k as J,n as re,b as g,A as ie,f as Z,h as l,w as a,i as u,v as o,a_ as ue,q as b,B as I,x as se,j as U,E as V,K as ee,o as v,y as j,af as de,ag as pe,G as R,H as W,U as me,T as ce}from"./.pnpm-hVqhwuVC.js";import{_ as fe}from"./select-product.vue_vue_type_script_setup_true_name_select-product_lang-CZTZXUnb.js";import{_ as _e}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{a as X}from"./index-Dw7jsygE.js";import"./index.vue_vue_type_script_setup_true_name_cl-avatar_lang-Dwbve7L9.js";import"./text-overflow-tooltip.vue_vue_type_script_setup_true_name_text-overflow-tooltip_lang-D_kIBTxD.js";const ve=D=>(de("data-v-c2582f5d"),D=D(),pe(),D),ge=ve(()=>j("span",{"font-size-18px":"",style:{"margin-left":"5px"}},"%",-1)),we=S({name:"undefined"}),ye=S({...we,props:J({rowData:{default:()=>({})},refresh:{type:Function,default:()=>{}}},{modelValue:{type:Boolean},modelModifiers:{}}),emits:J(["update:rowData"],["update:modelValue"]),setup(D,{emit:L}){const w=D,y=L,f=re(D,"modelValue"),k=g(!1),q=g(),e=g({}),B=g(["1"]),Y=g({supplierId:[{required:!0,message:"请输入供应商",trigger:"blur"}],code:[{required:!0,message:"请选择物料名称",trigger:"blur"}],deliveryDate:[{required:!0,message:"请选择交货日期",trigger:"blur"}],deliveryQuantity:[{required:!0,message:"请输入交货数量",trigger:"blur"}],inspectionResult:[{required:!0,message:"请选择检验结果",trigger:"blur"}],abnormalDescription:[{required:!0,message:"请输入异常描述",trigger:"blur"}]});async function G(){var t;if(await((t=q.value)==null?void 0:t.validate())){if(e.value.inspectionResult==="NG"){if(e.value.qualifiedRate===0)throw V.error("不良率不能为0"),new Error("不良率不能为0");if(e.value.abnormalPicture&&e.value.abnormalPicture.length===0)throw V.error("请上传异常图片"),new Error("请上传异常图片")}else e.value.abnormalPicture="",e.value.abnormalDescription="";try{k.value=!0,w.rowData.id&&w.rowData.id>0?await c.pms.productionData.incoming.update(e.value):await c.pms.productionData.incoming.add(e.value),V.success("保存成功"),f.value=!1,w.refresh()}catch(s){V.error(s.message||"保存失败"),console.error(s)}finally{k.value=!1}}}function A(n){if(n===void 0){e.value.modelChName="",e.value.modelSku="";return}e.value.modelChName=n._label,e.value.modelSku=n.sku}function K(){if(e.value.qualifiedRate===0&&!e.value.inspectionResult){e.value.inspectionResult="OK";return}e.value.qualifiedRate&&!e.value.inspectionResult&&(e.value.qualifiedRate>5?e.value.inspectionResult="NG":e.value.inspectionResult="OK")}function $(){f.value=!1,y("update:rowData",{})}async function E(){w.rowData.id&&c.pms.productionData.incoming.info({id:w.rowData.id}).then(n=>{e.value=n}).catch(n=>{console.error(n),V.error(n.message||"获取数据失败")})}function O(n){if(n===void 0){e.value.materialId=0,e.value.materialName="";return}e.value.materialName=n._label,e.value.materialId=n.id}function i(n){if(n===void 0){e.value.supplierName="",e.value.supplierShortName="";return}e.value.supplierName=n.label,e.value.supplierShortName=n.shortName}function p(n){const t=ee(new Date).add(1,"day");return n.getTime()>t-864e5}return ie(()=>{f.value&&E()}),(n,t)=>{const s=u("el-form-item"),m=u("el-col"),h=u("el-row"),Q=u("el-date-picker"),F=u("el-input"),M=u("el-input-number"),T=u("el-option"),z=u("el-select"),x=u("cl-upload"),C=u("el-card"),N=u("cl-editor-wang"),d=u("el-collapse-item"),_=u("el-collapse"),P=u("el-button"),le=u("el-drawer");return v(),Z("div",null,[l(le,{modelValue:f.value,"onUpdate:modelValue":t[17]||(t[17]=r=>f.value=r),title:w.rowData.id?"编辑":"新增","close-on-press-escape":!1,"close-on-click-modal":!1,size:"50%",onClose:$},{footer:a(()=>[l(P,{onClick:$},{default:a(()=>[U(" 取消 ")]),_:1}),l(P,{type:"success",loading:o(k),onClick:G},{default:a(()=>[U(" 确定 ")]),_:1},8,["loading"])]),default:a(()=>[l(C,null,{default:a(()=>[l(o(ue),{ref_key:"FormRef",ref:q,model:o(e),"label-width":"80px",rules:o(Y)},{default:a(()=>[l(h,{gutter:20},{default:a(()=>[l(m,{span:10},{default:a(()=>[l(s,{label:"供应商",prop:"supplierId"},{default:a(()=>[l(oe,{modelValue:o(e).supplierId,"onUpdate:modelValue":t[0]||(t[0]=r=>o(e).supplierId=r),width:"100%",onChange:i},null,8,["modelValue"])]),_:1})]),_:1}),l(m,{span:14},{default:a(()=>[l(s,{label:"物料名称",prop:"code"},{default:a(()=>[l(ne,{modelValue:o(e).code,"onUpdate:modelValue":t[1]||(t[1]=r=>o(e).code=r),width:"100%",onChange:O},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),l(h,{gutter:20},{default:a(()=>[l(m,{span:10},{default:a(()=>[l(s,{label:"日期",prop:"deliveryDate"},{default:a(()=>[l(Q,{modelValue:o(e).deliveryDate,"onUpdate:modelValue":t[2]||(t[2]=r=>o(e).deliveryDate=r),style:{width:"100%"},type:"date",format:"YYYY-MM-DD","disabled-date":p,placeholder:"请选择日期"},null,8,["modelValue"])]),_:1})]),_:1}),l(m,{span:14},{default:a(()=>[l(s,{label:"机型",prop:"modelId"},{default:a(()=>[l(fe,{modelValue:o(e).modelId,"onUpdate:modelValue":t[3]||(t[3]=r=>o(e).modelId=r),width:"100%",onChange:A},null,8,["modelValue"])]),_:1})]),_:1}),I("",!0)]),_:1}),l(h,{gutter:20},{default:a(()=>[l(m,{span:8},{default:a(()=>[l(s,{label:"来料总数",prop:"deliveryQuantity"},{default:a(()=>[l(M,{modelValue:o(e).deliveryQuantity,"onUpdate:modelValue":t[5]||(t[5]=r=>o(e).deliveryQuantity=r),"w-full":"",controls:!1,min:0,max:999999999,precision:0},null,8,["modelValue"])]),_:1})]),_:1}),l(m,{span:8},{default:a(()=>[l(s,{label:"不良率",prop:"qualifiedRate"},{default:a(()=>[l(M,{modelValue:o(e).qualifiedRate,"onUpdate:modelValue":t[6]||(t[6]=r=>o(e).qualifiedRate=r),style:{width:"80%"},controls:!1,min:0,max:100,precision:2,onBlur:K},null,8,["modelValue"]),ge]),_:1})]),_:1}),l(m,{span:8},{default:a(()=>[l(s,{label:"检验结果",prop:"inspectionResult"},{default:a(()=>[l(z,{modelValue:o(e).inspectionResult,"onUpdate:modelValue":t[7]||(t[7]=r=>o(e).inspectionResult=r)},{default:a(()=>[l(T,{label:"OK",value:"OK"}),l(T,{label:"NG",value:"NG"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),o(e).inspectionResult==="NG"?(v(),b(s,{key:0,label:"异常描述",prop:"abnormalDescription",required:""},{default:a(()=>[l(F,{modelValue:o(e).abnormalDescription,"onUpdate:modelValue":t[8]||(t[8]=r=>o(e).abnormalDescription=r),"w-full":"",type:"textarea",rows:2},null,8,["modelValue"])]),_:1})):I("",!0),o(e).inspectionResult==="NG"?(v(),b(s,{key:1,label:"异常图片",prop:"abnormalPicture"},{default:a(()=>[l(x,{modelValue:o(e).abnormalPicture,"onUpdate:modelValue":t[9]||(t[9]=r=>o(e).abnormalPicture=r),type:"image",multiple:!0,limit:1,"is-private":!1},null,8,["modelValue"])]),_:1})):I("",!0),I("",!0),l(s,{label:"备注",prop:"remark"},{default:a(()=>[l(F,{modelValue:o(e).remark,"onUpdate:modelValue":t[11]||(t[11]=r=>o(e).remark=r),"w-full":"",type:"textarea",rows:2},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1}),l(_,{modelValue:o(B),"onUpdate:modelValue":t[16]||(t[16]=r=>se(B)?B.value=r:null)},{default:a(()=>[l(d,{title:"临时措施",name:"2"},{default:a(()=>[l(C,null,{default:a(()=>[l(N,{modelValue:o(e).temporaryMeasures,"onUpdate:modelValue":t[12]||(t[12]=r=>o(e).temporaryMeasures=r),height:"150px"},null,8,["modelValue"])]),_:1})]),_:1}),l(d,{title:"原因分析",name:"3"},{default:a(()=>[l(C,null,{default:a(()=>[l(N,{modelValue:o(e).causeAnalysis,"onUpdate:modelValue":t[13]||(t[13]=r=>o(e).causeAnalysis=r),height:"150px"},null,8,["modelValue"])]),_:1})]),_:1}),l(d,{title:"改善方案",name:"4"},{default:a(()=>[l(C,null,{default:a(()=>[l(N,{modelValue:o(e).improvementPlan,"onUpdate:modelValue":t[14]||(t[14]=r=>o(e).improvementPlan=r),height:"150px"},null,8,["modelValue"])]),_:1})]),_:1}),l(d,{title:"完成状况",name:"5"},{default:a(()=>[l(C,null,{default:a(()=>[l(N,{modelValue:o(e).completionStatus,"onUpdate:modelValue":t[15]||(t[15]=r=>o(e).completionStatus=r),height:"150px"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1},8,["modelValue"])]),_:1},8,["modelValue","title"])])}}}),be=_e(ye,[["__scopeId","data-v-c2582f5d"]]),Ve={"w-full":"",flex:"~ justify-center"},he=S({name:"undefined"}),$e=S({...he,setup(D){const L=g({}),w=g(!1),y=H.useCrud({service:c.pms.productionData.incoming,async onRefresh(i,{next:p,done:n,render:t}){const{list:s,pagination:m}=await p(i);n(),t(s,m)}},i=>{i.refresh()}),f=g(!1),k=g({}),q=g(null),e=g(!1);function B(){const i=q.value;i&&i.click()}const Y=H.useTable({columns:[{label:"供应商",prop:"supplierName",align:"center",width:350,showOverflowTooltip:!0},{label:"物料编号",prop:"code",align:"center",width:180},{label:"物料名称",prop:"materialName",align:"center"},{label:"日期",prop:"deliveryDate",width:160,align:"center",sortable:"desc",formatter(i){return i.deliveryDate?ee(i.deliveryDate).format("YYYY-MM-DD"):""}},{label:"来料总数",prop:"deliveryQuantity",width:160,align:"center"},{label:"检验结果",prop:"inspectionResult",width:100,align:"center"},{label:"不良率",prop:"qualifiedRate",width:120,align:"center",sortable:"desc",formatter(i){return`${i.qualifiedRate}%`}},{label:"机型",prop:"modelChName",align:"center",width:300},{label:"创建人",prop:"creator",align:"center",width:100},{label:"创建时间",prop:"createTime",align:"center",sortable:"desc",width:160},{type:"op",label:"操作",width:160,buttons:["slot-edit-btn","slot-del-btn"]}]});function G(){f.value=!0}async function A(i){k.value=me(i),f.value=!0}async function K(i){ce.confirm("确定删除吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{var p;try{w.value=!0,await c.pms.productionData.incoming.delete({ids:[i.id]}),(p=y==null?void 0:y.value)==null||p.refresh()}catch(n){console.error(n)}finally{w.value=!1}})}async function $(i){var t;const p=i.target,n=p.files;if(n&&n.length>0){e.value=!0;const s=n[0],m=new FormData;m.append("incomingFile",s);try{await c.pms.productionData.incoming.import(m),(t=y==null?void 0:y.value)==null||t.refresh(),V.success("导入成功")}catch(h){V.error(h.message||"导入失败"),console.error(h)}finally{e.value=!1,E(p)}}else e.value=!1,E(p),V.error("请选择文件")}function E(i){i&&(i.value="")}function O(){const i="来料异常导入模板.xlsx";fetch("/incoming_template.xlsx").then(n=>n.blob()).then(n=>{ae(n,i)}).catch(()=>{V.error({message:"下载模板文件失败"})})}return(i,p)=>{var N;const n=u("cl-refresh-btn"),t=u("el-button"),s=u("cl-flex1"),m=u("el-date-picker"),h=u("cl-search-key"),Q=u("el-row"),F=u("cl-table"),M=u("cl-pagination"),T=u("cl-row"),z=u("cl-crud"),x=W("permission"),C=W("loading");return v(),Z("div",null,[R((v(),b(z,{ref_key:"Crud",ref:y},{default:a(()=>[l(Q,null,{default:a(()=>[l(n),R((v(),b(t,{type:"primary",onClick:G},{default:a(()=>[U(" 新增 ")]),_:1})),[[x,o(c).pms.productionData.incoming.add]]),j("input",{ref_key:"fileInputRef",ref:q,type:"file",style:{display:"none"},accept:".xlsx, .xls",onChange:$},null,544),R((v(),b(t,{size:"default",loading:e.value,type:"success",class:"mb-10px mr-10px",ml:"20px",onClick:B},{default:a(()=>[U(" Excel导入 ")]),_:1},8,["loading"])),[[x,o(c).pms.productionData.incoming.import]]),R((v(),b(t,{class:"mb-10px mr-10px",size:"default",onClick:O},{default:a(()=>[U(" 下载Excel模板 ")]),_:1})),[[x,o(c).pms.productionData.incoming.import]]),l(s),I("",!0),R(l(h,{placeholder:"请输入供应商名称或物料名称或编号",width:"600px","h-35px":""},null,512),[[x,o(c).pms.productionData.incoming.page]])]),_:1}),l(F,{ref_key:"Table",ref:Y,"auto-height":!1,"min-height":"200"},{"column-creator":a(({scope:d})=>{var _;return[j("div",Ve,[l(te,{user:(_=d==null?void 0:d.row)==null?void 0:_.creator,"show-name":!1},null,8,["user"])])]}),"slot-edit-btn":a(({scope:d})=>{var _;return[R((v(),b(t,{type:"primary",text:"",onClick:P=>A(d.row)},{default:a(()=>[U(" 编辑 ")]),_:2},1032,["onClick"])),[[x,{and:[o(c).pms.productionData.incoming.update,o(X)===((_=d.row)==null?void 0:_.creatorId)]}]])]}),"slot-del-btn":a(({scope:d})=>{var _;return[R((v(),b(t,{type:"danger",text:"",onClick:P=>K(d.row)},{default:a(()=>[U(" 删除 ")]),_:2},1032,["onClick"])),[[x,{and:[o(c).pms.productionData.incoming.delete,o(X)===((_=d.row)==null?void 0:_.creatorId)]}]])]}),_:1},512),l(T,{mt:"10px"},{default:a(()=>[l(s),l(M)]),_:1})]),_:1})),[[C,w.value]]),f.value?(v(),b(be,{key:0,modelValue:f.value,"onUpdate:modelValue":p[1]||(p[1]=d=>f.value=d),"row-data":k.value,"onUpdate:rowData":p[2]||(p[2]=d=>k.value=d),refresh:(N=o(y))==null?void 0:N.refresh},null,8,["modelValue","row-data","refresh"])):I("",!0)])}}});export{$e as default};
