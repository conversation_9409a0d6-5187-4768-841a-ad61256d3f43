<script lang="ts" setup>
import { useCrud, useSearch, useTable, useUpsert } from '@cool-vue-p/crud'
import type { FormInstance } from 'element-plus'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ref, watchEffect } from 'vue'
import * as XLSX from 'xlsx'
import { useTableOps } from '../../hooks/table-ops'
import { useDict } from '/$/dict'
import { service } from '/@/cool'
import { downloadBlob } from '/@/cool/utils'

const tableLoading = ref<boolean>(false)
const ruleFormRef = ref<FormInstance>()
const showAddChildrenDialog = ref(false)
const standard_capacity_parts = ref<any>({})
const { dict } = useDict()
const colorList = dict.get('color')
// 如果colorList不存在value为0的选项，则添加一个
if (!colorList.value?.find(item => item.value === 0))
  colorList.value?.unshift({ label: '无', value: 0 })

const tableData = ref<any[]>([])
const disabledArr = ref<any[]>([])
const productOptions = ref<any[]>([])
const partsCapacityList = ref<any[]>([])
const partsCapacityOptions = ref<any[]>([])
const flag = ref(false)
const productGroupOptions = ref<any[]>([])
const productGroupList = ref<any[]>([])
const productList = ref<any[]>([])
const dialog_title = ref('')
const Crud = useCrud(
  {
    service: service.pms.StandardCapacity,
    async onRefresh(params, { next, done, render }) {
      done()
      const { list, pagination } = await next(params)
      list.forEach((row) => {
        row.product_ids && (row.product_ids = JSON.parse(row.product_ids))
        row.skus && (row.skus = JSON.parse(row.skus))
      })
      tableData.value = list
      // 渲染数据
      render(list, pagination)
    },
  },
  (app) => {
    app.refresh()
  },
)

const opButtons = ref({
  'slot-btn-addChildren': {
    width: 120,
    show: true,
  },
  'edit': {
    width: 80,
    permission: service.pms.StandardCapacity.permission.update,
    show: true,
  },
  'delete': {
    width: 80,
    permission: service.pms.StandardCapacity.permission.delete,
    show: true,
  },
})

// 获取产品分组列表
async function getProductGroupList() {
  try {
    const res = await service.pms.product.group.request({
      url: '/list',
      method: 'POST',
    })
    productGroupOptions.value = res?.map((e: any) => {
      return {
        value: e.id,
        label: `${e.name}`,
      }
    })
    productGroupList.value = res?.map((e: any) => {
      return {
        value: e.id,
        label: `${e.name}`,
      }
    })
  }
  catch (e: any) {
    console.error(e)
  }
}
const WorkshopSectionOptions = [
  {
    label: '加工段',
    value: 1,
    name: '-',
    nameEn: '-',
    type: 'info',
  },
  {
    label: '组装段',
    value: 2,
    name: '-',
    nameEn: '-',
    type: 'info',
  },
  {
    label: '老化段',
    value: 3,
    name: '-',
    nameEn: '-',
    type: 'info',
  },
  {
    label: '包装段',
    value: 4,
    name: '-',
    nameEn: '-',
    type: 'info',
  },
  {
    label: '加工段一',
    value: 5,
    name: '-',
    nameEn: '-',
    type: 'info',
  },
  {
    label: '加工段二',
    value: 6,
    name: '-',
    nameEn: '-',
    type: 'info',
  },
  {
    label: '加工段三',
    value: 7,
    name: '-',
    nameEn: '-',
    type: 'info',
  },
  {
    label: '芯子配件加工段一',
    value: 8,
    name: '-',
    nameEn: '-',
    type: 'info',
  },
  {
    label: '芯子配件加工段二',
    value: 9,
    name: '-',
    nameEn: '-',
    type: 'info',
  },
  {
    label: '芯子配件组装段一',
    value: 10,
    name: '-',
    nameEn: '-',
    type: 'info',
  },
  {
    label: '芯子配件组装段二',
    value: 11,
    name: '-',
    nameEn: '-',
    type: 'info',
  },
  {
    label: '芯子配件组装段三',
    value: 12,
    name: '-',
    nameEn: '-',
    type: 'info',
  },
  {
    label: '芯子配件组装段四',
    value: 13,
    name: '-',
    nameEn: '-',
    type: 'info',
  },
  {
    label: '芯子配件组装段五',
    value: 14,
    name: '-',
    nameEn: '-',
    type: 'info',
  },
  {
    label: '芯子配件包装段',
    value: 15,
    name: '-',
    nameEn: '-',
    type: 'info',
  },
]

const Upsert = useUpsert({
  props: {
    class: 'product-form',
  },
  items: [
    {
      label: '选择产品',
      prop: 'product_group_id',
      required: true,
      component: {
        name: 'slot-product-group-select',
      },
    },
    {
      label: '选择产品',
      prop: 'product_ids',
      required: true,
      component: {
        name: 'slot-product-checkbox',
      },
    },
    {
      label: '描述',
      prop: 'description',
      required: true,
      component: {
        name: 'el-input',
      },
    },
    {
      label: '工段',
      prop: 'workshop_section',
      required: true,
      component: {
        name: 'slot-workshop-section-select',
      },
    },
    {
      label: '试产',
      prop: 'pilot_production_capacity',
      required: true,
      component: {
        name: 'slot-input-pilot-production',
      },
    },
    {
      label: '首次量产',
      prop: 'first_mass_production_capacity',
      required: true,
      component: {
        name: 'slot-input-first-production',
      },
    },
    {
      label: '量产',
      prop: 'mass_production_capacity',
      required: true,
      component: {
        name: 'slot-input-mass-production',
      },
    },
    {
      label: '备注',
      prop: 're_mark',
      required: true,
      component: {
        name: 'el-input',
      },
    },
  ],
  async onInfo(data, { done }) {
    flag.value = true
    productOptions.value = productList.value.filter(item => data.product_group_id === item.group_id)
    done(data)
  },
  // 打开弹窗
  onOpen() {
    productGroupOptions.value = productGroupList.value
    if (flag.value) {
      flag.value = false
    }
    else {
      productOptions.value = []
    }
  },
  onSubmit(data, { done, close, next }) {
    if (data.pilot_production_capacity <= 0 || data.pilot_production_capacity <= 0 || data.pilot_production_capacity <= 0) {
      ElMessage.error('试产、首次量产、量产不能小于等于0')
      return
    }
    const sku_arr = data.product_ids.map((e: any) => {
      return productList.value.find((item: any) => item.value === e)?.sku
    })

    const skus = JSON.stringify(sku_arr)
    const product_ids = JSON.stringify(data.product_ids)

    const requestData = {
      product_group_id: data.product_group_id,
      description: data.description,
      workshop_section: data.workshop_section,
      pilot_production_capacity: data.pilot_production_capacity,
      first_mass_production_capacity: data.first_mass_production_capacity,
      mass_production_capacity: data.mass_production_capacity,
      re_mark: data.re_mark,
      id: data.id,
      skus,
      product_ids,
    }
    next({
      id: data.id,
      requestData,
      done,
    })
    close()
  },
})

// ============ 配置操作按钮权限 ============
// const opButtons = ref({
//   edit: {
//     width: 80,
//     permission: service.pms.StandardCapacity.permission.update,
//     show: true,
//   },
//   delete: {
//     width: 80,
//     permission: service.pms.StandardCapacity.permission.delete,
//     show: true,
//   },
// })
// const { getOpWidth, getOpIsHidden } = useTableOps(opButtons as any)
// const opWidth = ref(getOpWidth())
// const opIsHidden = ref(getOpIsHidden())
// ============ 配置操作按钮权限 ============
const { getOpWidth, getOpIsHidden } = useTableOps(opButtons as any)
const opWidth = ref(getOpWidth() + 120)
const opIsHidden = ref(getOpIsHidden())

const Table = useTable({
  columns: [
    {
      label: '展开',
      prop: 'parts',
      type: 'expand',
      width: 60,
    },
    {
      label: '创建日期',
      prop: 'createTime',
      minWidth: 120,
    },
    {
      label: '产品分组',
      prop: 'product_group_id',
      minWidth: 120,
      formatter(row) {
        const product = productGroupList.value.find(e => e.value === row.product_group_id)
        return product ? product.label : ''
      },
    },
    {
      label: '工段',
      prop: 'workshop_section',
      minWidth: 120,
      formatter(row) {
        const item = WorkshopSectionOptions.find(e => e.value === row.workshop_section)
        return item ? item.label : ''
      },
    },
    {
      label: '规格型号描述',
      prop: 'description',
      minWidth: 120,
    },
    {
      label: '备注',
      prop: 're_mark',
      minWidth: 120,
    },
    {
      label: '试产产能',
      prop: 'pilot_production_capacity',
      minWidth: 120,
    },
    {
      label: '首次量产产能',
      prop: 'first_mass_production_capacity',
      minWidth: 80,
    },
    {
      label: '量产产能',
      prop: 'mass_production_capacity',
      minWidth: 80,
    },
    {
      type: 'op',
      label: '操作',
      width: opWidth as any,
      hidden: opIsHidden,
      buttons: Object.keys(opButtons.value) as any,
    },
  ],
})

const Search = useSearch({
  items: [
    {
      label: '分组名称',
      prop: 'keyWord',
      props: {
        labelWidth: '120px',
      },
      component: {
        name: 'el-input',
        props: {
          clearable: false,
          onChange(keyword: string) {
            Crud.value?.refresh({ keyWord: keyword.trim(), page: 1 })
          },
        },
      },
    },
  ],
})

// 获取产品列表
async function getProductList() {
  try {
    const res = await service.pms.product.request({
      url: '/getAllProduct',
      method: 'GET',
    })
    productList.value = res?.map((e: any) => {
      return {
        group_id: e.groupId,
        value: e.id,
        sku: e.sku,
        label: `${e.sku} ${e.name}`,
      }
    })
  }
  catch (e: any) {
    console.error(e)
  }
}

watchEffect(() => {
  getProductList()
  getProductGroupList()
  getPartsCapacityList()
})

// 过滤产品分组
function queryProductGroupByName(query: any) {
  query = query.trim()
  if (!query) {
    productGroupOptions.value = productGroupList.value
  }
  productGroupOptions.value = productGroupList.value.filter(item => item.label.toLowerCase().includes(query.toLowerCase()))
}

// 获取产品选项
function getProductOption(val: number) {
  productOptions.value = productList.value.filter(item => item.group_id === val)
}

// 导出数据
function handleExport() {
  const params = {
    url: '/summaryExport',
    method: 'GET',
    responseType: 'blob',
  }
  service.pms.StandardCapacity
    .request(params)
    .then((res: any) => {
      if (downloadBlob(res))
        ElMessage.success('导出成功')
      Crud.value?.refresh()
    })
    .catch((err: any) => {
      // 提示错误消息
      ElMessage.error(err.message || '导出失败')
    })
}

/**
 * 限定输入内容为两位小数
 */
function formatNumber(value: string) {
  const reg = /^\d+(?:\.\d{0,2})?$/
  if (!reg.test(value))
    return value.slice(0, -1)
  return value
}

// 导入文件
const fileInputRef = ref<HTMLInputElement | null>(null)
const isLoading = ref(false)

// 处理文件输入框的change事件
async function handleFileInputChange(event: Event) {
  const fileInput = event.target as HTMLInputElement
  const files = fileInput.files

  const productGroupMap: any = {}
  if (productGroupOptions.value && productGroupOptions.value.length > 0) {
    productGroupOptions.value.forEach((item: any) => {
      productGroupMap[item.label] = item.value
    })
  }

  const WorkshopSectionMap: any = {}
  if (WorkshopSectionOptions && WorkshopSectionOptions.length > 0) {
    WorkshopSectionOptions.forEach((item: any) => {
      WorkshopSectionMap[item.label] = item.value
    })
  }

  if (files && files.length > 0) {
    isLoading.value = true
    const file = files[0]
    const reader = new FileReader()

    reader.onload = (e: ProgressEvent<FileReader>) => {
      const data = new Uint8Array(e.target?.result as ArrayBuffer)
      const workbook = XLSX.read(data, { type: 'array' })

      // 这里可以根据需要处理读取到的Excel数据
      const worksheet = workbook.Sheets[workbook.SheetNames[0]]
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })
      // 定义非法值
      const illegalValue = [undefined, null, '', 'undefined', 'null', 'NaN']
      // 定义列
      const columns: string[] = ['index', 'product_group_id', 'description', 'workshop_section', 'pilot_production_capacity', 'first_mass_production_capacity', 'mass_production_capacity', 're_mark']
      const result: any = []
      if (jsonData && jsonData.length > 0) {
        for (let i = 5; i < jsonData.length; i++) {
          const row = jsonData[i] as string[]
          const cell: any = {}
          for (let j = 0; j < row.length; j++) {
            const columnName = columns[j]
            cell[columnName] = (row[j].toString() || '').trim()
            // 检查产品
            if (columnName === 'product_group_id') {
              const product_name = cell.product_group_id
              cell.product_group_id = productGroupMap[cell.product_group_id] ? productGroupMap[cell.product_group_id] : 0
              if (cell.product_group_id === 0) {
                ElMessage.error(`无效的产品名${product_name}`)
                clearInput(fileInput)
                isLoading.value = false
                return
              }
            }
            // 检查工序
            if (columnName === 'workshop_section') {
              const workshop_section_name = cell.workshop_section
              cell.workshop_section = WorkshopSectionMap[cell.workshop_section] ? WorkshopSectionMap[cell.workshop_section] : 0
              if (cell.workshop_section === 0) {
                ElMessage.error(`无效的工序名${workshop_section_name}`)
                clearInput(fileInput)
                isLoading.value = false
                return
              }
            }
            cell.pilot_production_capacity = Number.parseFloat(cell.pilot_production_capacity)
            cell.first_mass_production_capacity = Number.parseFloat(cell.first_mass_production_capacity)
            cell.mass_production_capacity = Number.parseFloat(cell.mass_production_capacity)
          }
          if (illegalValue.includes(cell.product_group_id)) {
            ElMessage.error(`产品名称不能为空`)
            clearInput(fileInput)
            isLoading.value = false
            return
          }
          if (illegalValue.includes(cell.pilot_production_capacity)) {
            ElMessage.error(`试产产能不能为空`)
            clearInput(fileInput)
            isLoading.value = false
            return
          }
          if (illegalValue.includes(cell.first_mass_production_capacity)) {
            ElMessage.error(`首次量产产能不能为空`)
            clearInput(fileInput)
            isLoading.value = false
            return
          }
          if (illegalValue.includes(cell.mass_production_capacity)) {
            ElMessage.error(`量产产能不能为空`)
            clearInput(fileInput)
            isLoading.value = false
            return
          }
          result.push(cell)
        }
        if (result.length > 0) {
          service.pms.StandardCapacity.importStandardData({ standard_capacity: result }).then((res) => {
            Crud.value?.refresh()
            ElMessage.success(`导入成功：导入${res}条数据！`)
          }).catch((e: any) => {
            ElMessage.error(e.message || '导入失败')
          }).finally(() => {
            isLoading.value = false
          })
        }
        else {
          isLoading.value = false
          ElMessage.error('导入数据为空')
        }
        clearInput(fileInput)
      }
    }
    reader.readAsArrayBuffer(file)
  }
  else {
    isLoading.value = false
    ElMessage.error('请选择文件')
  }
}

function clearInput(fileInput: { value: string }) {
  // 清空文件输入的值
  if (fileInput)
    fileInput.value = ''
}

function downloadExcelTemplate() {
  const fileName = '标准工时统计表_模板.xlsx'
  const filePath = '/standardCapacioty.xlsx'

  // 发起下载请求
  fetch(filePath)
    .then(response => response.blob())
    .then((blob) => {
      // 保存文件
      downloadBlob(blob, fileName)
    })
    .catch(() => {
      ElMessage.error({
        message: '下载模板文件失败',
      })
    })
}

function openFileInput() {
  const fileInput = fileInputRef.value
  if (fileInput)
    fileInput.click()
}

// 添加配件产能
function addChildren(row: any) {
  dialog_title.value = '添加配件产能'
  disabledArr.value = []
  if (row.parts && row.parts.length > 0) {
    disabledArr.value = row.parts.map((e: any) => e.parts_id)
  }
  if (!partsCapacityList.value) {
    ElMessage.warning('请先添加配件产能数据！')
    return
  }
  partsCapacityOptions.value = partsCapacityList.value.filter(item => item.workshop_section === row.workshop_section)
  standard_capacity_parts.value = {}

  resetForm(ruleFormRef.value)
  // 标准产能id
  standard_capacity_parts.value.stand_capacity_id = row.id
  standard_capacity_parts.value.workshop_section = row.workshop_section
  showAddChildrenDialog.value = true
}

function formatNumberInt(val: any) {
  return Number.parseInt(val)
}

// 提交表单
async function submitForm(formEl: FormInstance | undefined) {
  if (!formEl)
    return
  await formEl.validate((valid, _fields) => {
    isLoading.value = true
    if (valid) {
      const requestData = {
        id: standard_capacity_parts.value?.id,
        parts_id: standard_capacity_parts.value?.parts_id,
        stand_capacity_id: standard_capacity_parts.value?.stand_capacity_id,
        quantity: standard_capacity_parts.value?.quantity,
      }
      if (standard_capacity_parts.value?.id) {
        service.pms.StandardCapacity.updatePartsCapacity({ standard_capacity_parts: requestData }).then(() => {
          Crud.value?.refresh()
          ElMessage.success(`修改配件成功！`)
        }).catch((e: any) => {
          ElMessage.error(e.message || '修改配件失败')
        }).finally(() => {
          isLoading.value = false
          showAddChildrenDialog.value = false
        })
      }
      else {
        service.pms.StandardCapacity.addPartsCapacity({ standard_capacity_parts: requestData }).then(() => {
          Crud.value?.refresh()
          ElMessage.success(`添加配件成功！`)
        }).catch((e: any) => {
          ElMessage.error(e.message || '添加配件失败')
        }).finally(() => {
          isLoading.value = false
          showAddChildrenDialog.value = false
        })
      }
    }
    else {
      // 表单验证失败
      isLoading.value = false
    }
  })
}

// 重置表单
function resetForm(formEl: FormInstance | undefined) {
  if (!formEl)
    return
  formEl.resetFields()
}
function tableRowClassName() {
  return 'primary-row'
}

const tableExpandRowKeys = ref<number[]>([])

// 行点击展开
function onRowClick(row: any, column: any) {
  // 获取row的key
  if (column?.type === 'expand' || column?.type === 'op')
    return
  Table.value?.toggleRowExpansion(row)
}

// 编辑
function handleEdit(row: any) {
  dialog_title.value = '编辑配件产能'
  const cur_data = tableData.value.find(e => e.id === row.stand_capacity_id)
  disabledArr.value = []
  disabledArr.value = cur_data.parts.map((e: any) => e.parts_id)
  partsCapacityOptions.value = partsCapacityList.value.filter(item => item.workshop_section === row.parts_capacity.workshop_section)
  standard_capacity_parts.value.id = row.id
  standard_capacity_parts.value.parts_id = row.parts_id
  standard_capacity_parts.value.quantity = row.quantity
  standard_capacity_parts.value.stand_capacity_id = row.stand_capacity_id
  standard_capacity_parts.value.workshop_section = row.parts_capacity.workshop_section
  standard_capacity_parts.value.description = row.parts_capacity.description
  standard_capacity_parts.value.pilot_production_capacity = row.parts_capacity.pilot_production_capacity
  standard_capacity_parts.value.first_mass_production_capacity = row.parts_capacity.first_mass_production_capacity
  standard_capacity_parts.value.mass_production_capacity = row.parts_capacity.mass_production_capacity
  standard_capacity_parts.value.re_mark = row.parts_capacity.re_mark
  showAddChildrenDialog.value = true
}

// 删除数据
function handleDelete(id: any) {
  if (!id) {
    ElMessage.warning('请先选择配件')
    return
  }
  ElMessageBox.confirm('确定删除标准产能数据吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    service.pms.StandardCapacity.deletePartsCapacity({ id }).then(() => {
      ElMessage.success('删除配件产能成功')
      Crud.value?.refresh()
    }).catch((e: any) => {
      ElMessage.error(e.message || '删除产能失败')
    })
  }).catch(() => {
  })
}

// 获取配件产能列表
async function getPartsCapacityList() {
  try {
    partsCapacityList.value = await service.pms.PartsCapacity.request({
      url: '/list',
      method: 'POST',
    })
  }
  catch (e: any) {
    console.error(e)
  }
}

function getPartsCapacityLabel(item: any) {
  let section = ''
  switch (item.workshop_section) {
    case 1:
      section = '加工段'
      break
    case 2:
      section = '组装段'
      break
    case 3:
      section = '老化段'
      break
    case 4:
      section = '包装段'
      break
    default:
      section = '-'
  }
  return `${item.sku} / ${item.product_name} / ${section}`
}

// 选择了配件
function handleSelectedParts(val: any) {
  const parts = partsCapacityList.value.find(e => e.id === val)
  if (parts) {
    standard_capacity_parts.value.first_mass_production_capacity = parts.first_mass_production_capacity
    standard_capacity_parts.value.mass_production_capacity = parts.mass_production_capacity
    standard_capacity_parts.value.pilot_production_capacity = parts.pilot_production_capacity
    standard_capacity_parts.value.re_mark = parts.re_mark
    standard_capacity_parts.value.description = parts.description
  }
}
</script>

<template>
  <cl-crud ref="Crud" v-loading="tableLoading">
    <cl-dialog
      v-model="showAddChildrenDialog"
      :title="dialog_title"
      :close-on-click-modal="false"
      :controls="['close']"
      width="600"
      class="import-step-dialog"
    >
      <el-form ref="ruleFormRef" :model="standard_capacity_parts" label-width="auto">
        <el-form-item label="选择配件" prop="parts_id" required :rules="[{ required: true, message: '请选择配件', trigger: 'change' }]">
          <el-select v-model="standard_capacity_parts.parts_id" placeholder="请选择配件" style="width: 340px" filterable @change="handleSelectedParts">
            <el-option
              v-for="item in partsCapacityOptions"
              :key="item.id"
              :label="getPartsCapacityLabel(item)"
              :value="item.id"
              :disabled="disabledArr.includes(item.id)"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="数量" prop="quantity" required :rules="[{ required: true, message: '请输入数量', trigger: 'blur' }]">
          <el-input v-model="standard_capacity_parts.quantity" :formatter="formatNumberInt" type="number" placeholder="请输入数量" style="width: 340px" step="1" />
        </el-form-item>
        <el-form-item label="工段" prop="workshop_section" required :rules="[{ required: true, message: '请输入工段', trigger: 'change' }]">
          <el-select
            v-model="standard_capacity_parts.workshop_section"
            placeholder="请选择工段"
            style="width: 340px"
            disabled
          >
            <el-option
              v-for="item in WorkshopSectionOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="试产产能" prop="pilot_production_capacity" required :rules="[{ required: true, message: '请输入试产产能', trigger: 'blur' }]">
          <el-input
            v-model="standard_capacity_parts.pilot_production_capacity"
            type="number"
            style="width: 340px"
            placeholder="请输入试产产能"
            :formatter="formatNumber"
            step="0.01"
            disabled
          >
            <template #append>
              pcs/h
            </template>
          </el-input>
        </el-form-item>
        <el-form-item label="首次量产产能" prop="first_mass_production_capacity" required :rules="[{ required: true, message: '请输入首次量产产能', trigger: 'blur' }]">
          <el-input
            v-model="standard_capacity_parts.first_mass_production_capacity"
            type="number"
            style="width: 340px"
            placeholder="请输入首次量产产能"
            :formatter="formatNumber"
            step="0.01"
            disabled
          >
            <template #append>
              pcs/h
            </template>
          </el-input>
        </el-form-item>

        <el-form-item label="量产产能" prop="mass_production_capacity" required :rules="[{ required: true, message: '请输入量产产能', trigger: 'blur' }]">
          <el-input
            v-model="standard_capacity_parts.mass_production_capacity"
            type="number"
            style="width: 340px"
            placeholder="请输入量产产能"
            :formatter="formatNumber"
            step="0.01"
            disabled
          >
            <template #append>
              pcs/h
            </template>
          </el-input>
        </el-form-item>
        <el-form-item label="描述" prop="description" required :rules="[{ required: true, message: '请输入描述', trigger: 'blur' }]">
          <el-input v-model="standard_capacity_parts.description" placeholder="请输入描述" style="width: 340px" disabled />
        </el-form-item>
        <el-form-item label="备注" prop="re_mark" required :rules="[{ required: true, message: '请输入备注', trigger: 'blur' }]">
          <el-input v-model="standard_capacity_parts.re_mark" placeholder="请输入备注" disabled style="width: 340px" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :loading="isLoading" @click="submitForm(ruleFormRef)">
            提交
          </el-button>
          <el-button @click="resetForm(ruleFormRef)">
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </cl-dialog>
    <cl-row>
      <cl-refresh-btn />
      <cl-add-btn />
      <input ref="fileInputRef" type="file" style="display: none" accept=".xlsx, .xls" @change="handleFileInputChange">
      <el-button v-permission="service.pms.StandardCapacity.permission.importStandardData" size="default" :loading="isLoading" type="warning" class="mb-10px mr-10px" ml="20px" @click="openFileInput">
        Excel导入
      </el-button>
      <!-- 下载excel模板 -->
      <el-button v-permission="service.pms.StandardCapacity.permission.importStandardData" type="info" class="mb-10px mr-10px" size="default" @click="downloadExcelTemplate">
        下载Excel模板
      </el-button>
      <el-button type="success" @click="handleExport">
        导出
      </el-button>
      <cl-flex1 />
      <cl-search ref="Search" />
    </cl-row>
    <cl-row>
      <cl-table ref="Table" :auto-height="false" row-key="id" :expand-row-keys="tableExpandRowKeys" class="table-row-pointer" @row-click="onRowClick">
        <template #slot-btn-addChildren="{ scope }">
          <el-button v-permission="service.pms.StandardCapacity.permission.addPartsCapacity" type="success" @click="addChildren(scope.row)">
            添加配件产能
          </el-button>
        </template>
        <template #column-parts="{ scope }">
          <el-table :data="scope.row.parts" style="width: 100%" border :row-class-name="tableRowClassName">
            <el-table-column label="配件名称" width="320" align="center">
              <template #default="scope">
                <span>{{ scope.row.parts_capacity.product_name }}</span>
              </template>
            </el-table-column>
            <el-table-column label="配件数量" width="320" align="center">
              <template #default="scope">
                <span>{{ scope.row.quantity }}</span>
              </template>
            </el-table-column>
            <el-table-column label="描述" width="320" align="center">
              <template #default="scope">
                <span>{{ scope.row.parts_capacity.description }}</span>
              </template>
            </el-table-column>
            <el-table-column label="备注" align="center">
              <template #default="scope">
                <span>{{ scope.row.parts_capacity.re_mark }}</span>
              </template>
            </el-table-column>
            <el-table-column label="试产产能" width="320" align="center">
              <template #default="scope">
                <span>{{ scope.row.parts_capacity.pilot_production_capacity }}</span>
              </template>
            </el-table-column>
            <el-table-column label="第一次量产产能" width="320" align="center">
              <template #default="scope">
                <span>{{ scope.row.parts_capacity.first_mass_production_capacity }}</span>
              </template>
            </el-table-column>
            <el-table-column label="量产产能" width="320" align="center">
              <template #default="scope">
                <span>{{ scope.row.parts_capacity.mass_production_capacity }}</span>
              </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" min-width="220" align="center">
              <template #default="scope">
                <el-button v-permission="service.pms.StandardCapacity.permission.updatePartsCapacity" link type="primary" size="small" @click="handleEdit(scope.row)">
                  编辑
                </el-button>
                <el-button v-permission="service.pms.StandardCapacity.permission.deletePartsCapacity" link type="warning" size="small" @click="handleDelete(scope.row?.id)">
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </template>
      </cl-table>
    </cl-row>
    <cl-row>
      <cl-flex1 />
      <cl-pagination />
    </cl-row>
    <cl-upsert ref="Upsert">
      <!-- 产品选择 -->
      <template #slot-product-group-select="{ scope }">
        <el-select
          v-model="scope.product_group_id"
          filterable
          remote
          placeholder="输入产品分组名查询"
          :remote-method="queryProductGroupByName"
          @change="(val: number) => {
            scope.product_group_id = val
            // 保存当前选择的产品ID并重置其他字段
            scope.description = ''
            scope.workshop_section = undefined
            scope.pilot_production_capacity = undefined
            scope.first_mass_production_capacity = undefined
            scope.mass_production_capacity = undefined
            scope.re_mark = ''
            getProductOption(val)
          }"
        >
          <el-option
            v-for="item in productGroupOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </template>
      <template #slot-product-checkbox="{ scope }">
        <el-checkbox-group v-model="scope.product_ids">
          <el-checkbox v-for="item in productOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-checkbox-group>
      </template>
      <template #slot-workshop-section-select="{ scope }">
        <el-select
          v-model="scope.workshop_section"
          placeholder="请选择工段"
        >
          <el-option
            v-for="item in WorkshopSectionOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </template>
      <template #slot-input-pilot-production="{ scope }">
        <el-input
          v-model="scope.pilot_production_capacity"
          type="number"
          placeholder="请输入试产产能"
          :formatter="formatNumber"
          step="0.01"
        >
          <template #append>
            pcs/h
          </template>
        </el-input>
      </template>
      <template #slot-input-first-production="{ scope }">
        <el-input
          v-model="scope.first_mass_production_capacity"
          type="number"
          placeholder="请输入首次量产产能"
          :formatter="formatNumber"
          step="0.01"
        >
          <template #append>
            pcs/h
          </template>
        </el-input>
      </template>
      <template #slot-input-mass-production="{ scope }">
        <el-input
          v-model="scope.mass_production_capacity"
          type="number"
          placeholder="请输入量产产能"
          :formatter="formatNumber"
          step="0.01"
        >
          <template #append>
            pcs/h
          </template>
        </el-input>
      </template>
    </cl-upsert>
  </cl-crud>
</template>

<style lang="scss">
.production-schedule-collapse {
  :deep(.el-collapse-item__content) {
    .el-table {
      --el-table-tr-bg-color: #f5f7fa;
      --el-table-header-bg-color: #e4e7ed;

      .el-table__header th {
        background-color: var(--el-table-header-bg-color) !important;
      }

      .el-table__row {
        background-color: var(--el-table-tr-bg-color) !important;

        &:nth-child(even) {
          background-color: #ebeef5 !important;
        }
      }
    }
  }
}

.product-form {
  .cl-form__items {
    .el-row {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      .full-line {
        grid-column: 1 / -1;
      }
    }

    // 让input-number的提示语靠左显示
    .el-input-number {
      :deep(.el-input__wrapper) {
        padding-left: 11px;
        input {
          text-align: left;
        }
      }
    }
  }
}

.sku-select-error,
.quantity-input-error {
  :deep {
    .el-input__wrapper {
      border: 1px solid var(--el-color-danger);
      &.is-focus {
        box-shadow: none;
      }
    }
  }
}

.cell .holiday {
  position: absolute;
  width: 6px;
  height: 6px;
  background: var(--el-color-danger);
  border-radius: 50%;
  bottom: 0px;
  left: 50%;
  transform: translateX(-50%);
}
</style>
