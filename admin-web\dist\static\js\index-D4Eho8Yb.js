import{g as ze,i as ae,s as v,e as me}from"./index-DkYL1aws.js";import{c as ye,b as _,E as d,A as Je,G as L,H as fe,q as C,w as l,h as o,i as m,f as J,s as H,F as K,j as b,y as S,v as Q,t as O,M as He,N as Ke,T as Qe,o as g}from"./.pnpm-hVqhwuVC.js";import{u as Xe}from"./table-ops-CrFIfhgA.js";const Ye=ye({name:"undefined"}),la=ye({...Ye,setup(Ze){var ce,de;const ve=_(!1),X=_(),$=_(!1),n=_({}),{dict:he}=ze(),re=he.get("color");(ce=re.value)!=null&&ce.find(e=>e.value===0)||(de=re.value)==null||de.unshift({label:"无",value:0});const ne=_([]),I=_([]),Y=_([]),B=_([]),te=_([]),le=_(!1),V=_([]),R=_([]),Z=_([]),oe=_(""),E=ae.useCrud({service:v.pms.StandardCapacity,async onRefresh(e,{next:a,done:i,render:h}){i();const{list:c,pagination:s}=await a(e);c.forEach(u=>{u.product_ids&&(u.product_ids=JSON.parse(u.product_ids)),u.skus&&(u.skus=JSON.parse(u.skus))}),ne.value=c,h(c,s)}},e=>{e.refresh()}),ie=_({"slot-btn-addChildren":{width:120,show:!0},edit:{width:80,permission:v.pms.StandardCapacity.permission.update,show:!0},delete:{width:80,permission:v.pms.StandardCapacity.permission.delete,show:!0}});async function be(){try{const e=await v.pms.product.group.request({url:"/list",method:"POST"});V.value=e==null?void 0:e.map(a=>({value:a.id,label:`${a.name}`})),R.value=e==null?void 0:e.map(a=>({value:a.id,label:`${a.name}`}))}catch(e){console.error(e)}}const T=[{label:"加工段",value:1,name:"-",nameEn:"-",type:"info"},{label:"组装段",value:2,name:"-",nameEn:"-",type:"info"},{label:"老化段",value:3,name:"-",nameEn:"-",type:"info"},{label:"包装段",value:4,name:"-",nameEn:"-",type:"info"},{label:"加工段一",value:5,name:"-",nameEn:"-",type:"info"},{label:"加工段二",value:6,name:"-",nameEn:"-",type:"info"},{label:"加工段三",value:7,name:"-",nameEn:"-",type:"info"},{label:"芯子配件加工段一",value:8,name:"-",nameEn:"-",type:"info"},{label:"芯子配件加工段二",value:9,name:"-",nameEn:"-",type:"info"},{label:"芯子配件组装段一",value:10,name:"-",nameEn:"-",type:"info"},{label:"芯子配件组装段二",value:11,name:"-",nameEn:"-",type:"info"},{label:"芯子配件组装段三",value:12,name:"-",nameEn:"-",type:"info"},{label:"芯子配件组装段四",value:13,name:"-",nameEn:"-",type:"info"},{label:"芯子配件组装段五",value:14,name:"-",nameEn:"-",type:"info"},{label:"芯子配件包装段",value:15,name:"-",nameEn:"-",type:"info"}],ge=ae.useUpsert({props:{class:"product-form"},items:[{label:"选择产品",prop:"product_group_id",required:!0,component:{name:"slot-product-group-select"}},{label:"选择产品",prop:"product_ids",required:!0,component:{name:"slot-product-checkbox"}},{label:"描述",prop:"description",required:!0,component:{name:"el-input"}},{label:"工段",prop:"workshop_section",required:!0,component:{name:"slot-workshop-section-select"}},{label:"试产",prop:"pilot_production_capacity",required:!0,component:{name:"slot-input-pilot-production"}},{label:"首次量产",prop:"first_mass_production_capacity",required:!0,component:{name:"slot-input-first-production"}},{label:"量产",prop:"mass_production_capacity",required:!0,component:{name:"slot-input-mass-production"}},{label:"备注",prop:"re_mark",required:!0,component:{name:"el-input"}}],async onInfo(e,{done:a}){le.value=!0,Y.value=Z.value.filter(i=>e.product_group_id===i.group_id),a(e)},onOpen(){V.value=R.value,le.value?le.value=!1:Y.value=[]},onSubmit(e,{done:a,close:i,next:h}){if(e.pilot_production_capacity<=0||e.pilot_production_capacity<=0||e.pilot_production_capacity<=0){d.error("试产、首次量产、量产不能小于等于0");return}const c=e.product_ids.map(q=>{var f;return(f=Z.value.find(G=>G.value===q))==null?void 0:f.sku}),s=JSON.stringify(c),u=JSON.stringify(e.product_ids),x={product_group_id:e.product_group_id,description:e.description,workshop_section:e.workshop_section,pilot_production_capacity:e.pilot_production_capacity,first_mass_production_capacity:e.first_mass_production_capacity,mass_production_capacity:e.mass_production_capacity,re_mark:e.re_mark,id:e.id,skus:s,product_ids:u};h({id:e.id,requestData:x,done:a}),i()}}),{getOpWidth:ke,getOpIsHidden:we}=Xe(ie),Ce=_(ke()+120),Ve=_(we()),se=ae.useTable({columns:[{label:"展开",prop:"parts",type:"expand",width:60},{label:"创建日期",prop:"createTime",minWidth:120},{label:"产品分组",prop:"product_group_id",minWidth:120,formatter(e){const a=R.value.find(i=>i.value===e.product_group_id);return a?a.label:""}},{label:"工段",prop:"workshop_section",minWidth:120,formatter(e){const a=T.find(i=>i.value===e.workshop_section);return a?a.label:""}},{label:"规格型号描述",prop:"description",minWidth:120},{label:"备注",prop:"re_mark",minWidth:120},{label:"试产产能",prop:"pilot_production_capacity",minWidth:120},{label:"首次量产产能",prop:"first_mass_production_capacity",minWidth:80},{label:"量产产能",prop:"mass_production_capacity",minWidth:80},{type:"op",label:"操作",width:Ce,hidden:Ve,buttons:Object.keys(ie.value)}]}),xe=ae.useSearch({items:[{label:"分组名称",prop:"keyWord",props:{labelWidth:"120px"},component:{name:"el-input",props:{clearable:!1,onChange(e){var a;(a=E.value)==null||a.refresh({keyWord:e.trim(),page:1})}}}}]});async function qe(){try{const e=await v.pms.product.request({url:"/getAllProduct",method:"GET"});Z.value=e==null?void 0:e.map(a=>({group_id:a.groupId,value:a.id,sku:a.sku,label:`${a.sku} ${a.name}`}))}catch(e){console.error(e)}}Je(()=>{qe(),be(),Re()});function Se(e){e=e.trim(),e||(V.value=R.value),V.value=R.value.filter(a=>a.label.toLowerCase().includes(e.toLowerCase()))}function Ee(e){Y.value=Z.value.filter(a=>a.group_id===e)}function Ue(){const e={url:"/summaryExport",method:"GET",responseType:"blob"};v.pms.StandardCapacity.request(e).then(a=>{var i;me(a)&&d.success("导出成功"),(i=E.value)==null||i.refresh()}).catch(a=>{d.error(a.message||"导出失败")})}function W(e){return/^\d+(?:\.\d{0,2})?$/.test(e)?e:e.slice(0,-1)}const pe=_(null),y=_(!1);async function Ne(e){const a=e.target,i=a.files,h={};V.value&&V.value.length>0&&V.value.forEach(s=>{h[s.label]=s.value});const c={};if(T&&T.length>0&&T.forEach(s=>{c[s.label]=s.value}),i&&i.length>0){y.value=!0;const s=i[0],u=new FileReader;u.onload=x=>{var ee;const q=new Uint8Array((ee=x.target)==null?void 0:ee.result),f=He(q,{type:"array"}),G=f.Sheets[f.SheetNames[0]],N=Ke.sheet_to_json(G,{header:1}),D=[void 0,null,"","undefined","null","NaN"],A=["index","product_group_id","description","workshop_section","pilot_production_capacity","first_mass_production_capacity","mass_production_capacity","re_mark"],k=[];if(N&&N.length>0){for(let w=5;w<N.length;w++){const P=N[w],p={};for(let F=0;F<P.length;F++){const j=A[F];if(p[j]=(P[F].toString()||"").trim(),j==="product_group_id"){const M=p.product_group_id;if(p.product_group_id=h[p.product_group_id]?h[p.product_group_id]:0,p.product_group_id===0){d.error(`无效的产品名${M}`),U(a),y.value=!1;return}}if(j==="workshop_section"){const M=p.workshop_section;if(p.workshop_section=c[p.workshop_section]?c[p.workshop_section]:0,p.workshop_section===0){d.error(`无效的工序名${M}`),U(a),y.value=!1;return}}p.pilot_production_capacity=Number.parseFloat(p.pilot_production_capacity),p.first_mass_production_capacity=Number.parseFloat(p.first_mass_production_capacity),p.mass_production_capacity=Number.parseFloat(p.mass_production_capacity)}if(D.includes(p.product_group_id)){d.error("产品名称不能为空"),U(a),y.value=!1;return}if(D.includes(p.pilot_production_capacity)){d.error("试产产能不能为空"),U(a),y.value=!1;return}if(D.includes(p.first_mass_production_capacity)){d.error("首次量产产能不能为空"),U(a),y.value=!1;return}if(D.includes(p.mass_production_capacity)){d.error("量产产能不能为空"),U(a),y.value=!1;return}k.push(p)}k.length>0?v.pms.StandardCapacity.importStandardData({standard_capacity:k}).then(w=>{var P;(P=E.value)==null||P.refresh(),d.success(`导入成功：导入${w}条数据！`)}).catch(w=>{d.error(w.message||"导入失败")}).finally(()=>{y.value=!1}):(y.value=!1,d.error("导入数据为空")),U(a)}},u.readAsArrayBuffer(s)}else y.value=!1,d.error("请选择文件")}function U(e){e&&(e.value="")}function Pe(){const e="标准工时统计表_模板.xlsx";fetch("/standardCapacioty.xlsx").then(i=>i.blob()).then(i=>{me(i,e)}).catch(()=>{d.error({message:"下载模板文件失败"})})}function Oe(){const e=pe.value;e&&e.click()}function $e(e){if(oe.value="添加配件产能",I.value=[],e.parts&&e.parts.length>0&&(I.value=e.parts.map(a=>a.parts_id)),!B.value){d.warning("请先添加配件产能数据！");return}te.value=B.value.filter(a=>a.workshop_section===e.workshop_section),n.value={},ue(X.value),n.value.stand_capacity_id=e.id,n.value.workshop_section=e.workshop_section,$.value=!0}function Te(e){return Number.parseInt(e)}async function We(e){e&&await e.validate((a,i)=>{var h,c,s,u,x;if(y.value=!0,a){const q={id:(h=n.value)==null?void 0:h.id,parts_id:(c=n.value)==null?void 0:c.parts_id,stand_capacity_id:(s=n.value)==null?void 0:s.stand_capacity_id,quantity:(u=n.value)==null?void 0:u.quantity};(x=n.value)!=null&&x.id?v.pms.StandardCapacity.updatePartsCapacity({standard_capacity_parts:q}).then(()=>{var f;(f=E.value)==null||f.refresh(),d.success("修改配件成功！")}).catch(f=>{d.error(f.message||"修改配件失败")}).finally(()=>{y.value=!1,$.value=!1}):v.pms.StandardCapacity.addPartsCapacity({standard_capacity_parts:q}).then(()=>{var f;(f=E.value)==null||f.refresh(),d.success("添加配件成功！")}).catch(f=>{d.error(f.message||"添加配件失败")}).finally(()=>{y.value=!1,$.value=!1})}else y.value=!1})}function ue(e){e&&e.resetFields()}function De(){return"primary-row"}const Fe=_([]);function Le(e,a){var i;(a==null?void 0:a.type)==="expand"||(a==null?void 0:a.type)==="op"||(i=se.value)==null||i.toggleRowExpansion(e)}function Ie(e){oe.value="编辑配件产能";const a=ne.value.find(i=>i.id===e.stand_capacity_id);I.value=[],I.value=a.parts.map(i=>i.parts_id),te.value=B.value.filter(i=>i.workshop_section===e.parts_capacity.workshop_section),n.value.id=e.id,n.value.parts_id=e.parts_id,n.value.quantity=e.quantity,n.value.stand_capacity_id=e.stand_capacity_id,n.value.workshop_section=e.parts_capacity.workshop_section,n.value.description=e.parts_capacity.description,n.value.pilot_production_capacity=e.parts_capacity.pilot_production_capacity,n.value.first_mass_production_capacity=e.parts_capacity.first_mass_production_capacity,n.value.mass_production_capacity=e.parts_capacity.mass_production_capacity,n.value.re_mark=e.parts_capacity.re_mark,$.value=!0}function Be(e){if(!e){d.warning("请先选择配件");return}Qe.confirm("确定删除标准产能数据吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{v.pms.StandardCapacity.deletePartsCapacity({id:e}).then(()=>{var a;d.success("删除配件产能成功"),(a=E.value)==null||a.refresh()}).catch(a=>{d.error(a.message||"删除产能失败")})}).catch(()=>{})}async function Re(){try{B.value=await v.pms.PartsCapacity.request({url:"/list",method:"POST"})}catch(e){console.error(e)}}function Ge(e){let a="";switch(e.workshop_section){case 1:a="加工段";break;case 2:a="组装段";break;case 3:a="老化段";break;case 4:a="包装段";break;default:a="-"}return`${e.sku} / ${e.product_name} / ${a}`}function Ae(e){const a=B.value.find(i=>i.id===e);a&&(n.value.first_mass_production_capacity=a.first_mass_production_capacity,n.value.mass_production_capacity=a.mass_production_capacity,n.value.pilot_production_capacity=a.pilot_production_capacity,n.value.re_mark=a.re_mark,n.value.description=a.description)}return(e,a)=>{const i=m("el-option"),h=m("el-select"),c=m("el-form-item"),s=m("el-input"),u=m("el-button"),x=m("el-form"),q=m("cl-dialog"),f=m("cl-refresh-btn"),G=m("cl-add-btn"),N=m("cl-flex1"),D=m("cl-search"),A=m("cl-row"),k=m("el-table-column"),ee=m("el-table"),w=m("cl-table"),P=m("cl-pagination"),p=m("el-checkbox"),F=m("el-checkbox-group"),j=m("cl-upsert"),M=m("cl-crud"),z=fe("permission"),je=fe("loading");return L((g(),C(M,{ref_key:"Crud",ref:E},{default:l(()=>[o(q,{modelValue:$.value,"onUpdate:modelValue":a[10]||(a[10]=t=>$.value=t),title:oe.value,"close-on-click-modal":!1,controls:["close"],width:"600",class:"import-step-dialog"},{default:l(()=>[o(x,{ref_key:"ruleFormRef",ref:X,model:n.value,"label-width":"auto"},{default:l(()=>[o(c,{label:"选择配件",prop:"parts_id",required:"",rules:[{required:!0,message:"请选择配件",trigger:"change"}]},{default:l(()=>[o(h,{modelValue:n.value.parts_id,"onUpdate:modelValue":a[0]||(a[0]=t=>n.value.parts_id=t),placeholder:"请选择配件",style:{width:"340px"},filterable:"",onChange:Ae},{default:l(()=>[(g(!0),J(K,null,H(te.value,t=>(g(),C(i,{key:t.id,label:Ge(t),value:t.id,disabled:I.value.includes(t.id)},null,8,["label","value","disabled"]))),128))]),_:1},8,["modelValue"])]),_:1}),o(c,{label:"数量",prop:"quantity",required:"",rules:[{required:!0,message:"请输入数量",trigger:"blur"}]},{default:l(()=>[o(s,{modelValue:n.value.quantity,"onUpdate:modelValue":a[1]||(a[1]=t=>n.value.quantity=t),formatter:Te,type:"number",placeholder:"请输入数量",style:{width:"340px"},step:"1"},null,8,["modelValue"])]),_:1}),o(c,{label:"工段",prop:"workshop_section",required:"",rules:[{required:!0,message:"请输入工段",trigger:"change"}]},{default:l(()=>[o(h,{modelValue:n.value.workshop_section,"onUpdate:modelValue":a[2]||(a[2]=t=>n.value.workshop_section=t),placeholder:"请选择工段",style:{width:"340px"},disabled:""},{default:l(()=>[(g(),J(K,null,H(T,t=>o(i,{key:t.value,label:t.label,value:t.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1}),o(c,{label:"试产产能",prop:"pilot_production_capacity",required:"",rules:[{required:!0,message:"请输入试产产能",trigger:"blur"}]},{default:l(()=>[o(s,{modelValue:n.value.pilot_production_capacity,"onUpdate:modelValue":a[3]||(a[3]=t=>n.value.pilot_production_capacity=t),type:"number",style:{width:"340px"},placeholder:"请输入试产产能",formatter:W,step:"0.01",disabled:""},{append:l(()=>[b(" pcs/h ")]),_:1},8,["modelValue"])]),_:1}),o(c,{label:"首次量产产能",prop:"first_mass_production_capacity",required:"",rules:[{required:!0,message:"请输入首次量产产能",trigger:"blur"}]},{default:l(()=>[o(s,{modelValue:n.value.first_mass_production_capacity,"onUpdate:modelValue":a[4]||(a[4]=t=>n.value.first_mass_production_capacity=t),type:"number",style:{width:"340px"},placeholder:"请输入首次量产产能",formatter:W,step:"0.01",disabled:""},{append:l(()=>[b(" pcs/h ")]),_:1},8,["modelValue"])]),_:1}),o(c,{label:"量产产能",prop:"mass_production_capacity",required:"",rules:[{required:!0,message:"请输入量产产能",trigger:"blur"}]},{default:l(()=>[o(s,{modelValue:n.value.mass_production_capacity,"onUpdate:modelValue":a[5]||(a[5]=t=>n.value.mass_production_capacity=t),type:"number",style:{width:"340px"},placeholder:"请输入量产产能",formatter:W,step:"0.01",disabled:""},{append:l(()=>[b(" pcs/h ")]),_:1},8,["modelValue"])]),_:1}),o(c,{label:"描述",prop:"description",required:"",rules:[{required:!0,message:"请输入描述",trigger:"blur"}]},{default:l(()=>[o(s,{modelValue:n.value.description,"onUpdate:modelValue":a[6]||(a[6]=t=>n.value.description=t),placeholder:"请输入描述",style:{width:"340px"},disabled:""},null,8,["modelValue"])]),_:1}),o(c,{label:"备注",prop:"re_mark",required:"",rules:[{required:!0,message:"请输入备注",trigger:"blur"}]},{default:l(()=>[o(s,{modelValue:n.value.re_mark,"onUpdate:modelValue":a[7]||(a[7]=t=>n.value.re_mark=t),placeholder:"请输入备注",disabled:"",style:{width:"340px"}},null,8,["modelValue"])]),_:1}),o(c,null,{default:l(()=>[o(u,{type:"primary",loading:y.value,onClick:a[8]||(a[8]=t=>We(X.value))},{default:l(()=>[b(" 提交 ")]),_:1},8,["loading"]),o(u,{onClick:a[9]||(a[9]=t=>ue(X.value))},{default:l(()=>[b(" 重置 ")]),_:1})]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),o(A,null,{default:l(()=>[o(f),o(G),S("input",{ref_key:"fileInputRef",ref:pe,type:"file",style:{display:"none"},accept:".xlsx, .xls",onChange:Ne},null,544),L((g(),C(u,{size:"default",loading:y.value,type:"warning",class:"mb-10px mr-10px",ml:"20px",onClick:Oe},{default:l(()=>[b(" Excel导入 ")]),_:1},8,["loading"])),[[z,Q(v).pms.StandardCapacity.permission.importStandardData]]),L((g(),C(u,{type:"info",class:"mb-10px mr-10px",size:"default",onClick:Pe},{default:l(()=>[b(" 下载Excel模板 ")]),_:1})),[[z,Q(v).pms.StandardCapacity.permission.importStandardData]]),o(u,{type:"success",onClick:Ue},{default:l(()=>[b(" 导出 ")]),_:1}),o(N),o(D,{ref_key:"Search",ref:xe},null,512)]),_:1}),o(A,null,{default:l(()=>[o(w,{ref_key:"Table",ref:se,"auto-height":!1,"row-key":"id","expand-row-keys":Fe.value,class:"table-row-pointer",onRowClick:Le},{"slot-btn-addChildren":l(({scope:t})=>[L((g(),C(u,{type:"success",onClick:r=>$e(t.row)},{default:l(()=>[b(" 添加配件产能 ")]),_:2},1032,["onClick"])),[[z,Q(v).pms.StandardCapacity.permission.addPartsCapacity]])]),"column-parts":l(({scope:t})=>[o(ee,{data:t.row.parts,style:{width:"100%"},border:"","row-class-name":De},{default:l(()=>[o(k,{label:"配件名称",width:"320",align:"center"},{default:l(r=>[S("span",null,O(r.row.parts_capacity.product_name),1)]),_:2},1024),o(k,{label:"配件数量",width:"320",align:"center"},{default:l(r=>[S("span",null,O(r.row.quantity),1)]),_:2},1024),o(k,{label:"描述",width:"320",align:"center"},{default:l(r=>[S("span",null,O(r.row.parts_capacity.description),1)]),_:2},1024),o(k,{label:"备注",align:"center"},{default:l(r=>[S("span",null,O(r.row.parts_capacity.re_mark),1)]),_:2},1024),o(k,{label:"试产产能",width:"320",align:"center"},{default:l(r=>[S("span",null,O(r.row.parts_capacity.pilot_production_capacity),1)]),_:2},1024),o(k,{label:"第一次量产产能",width:"320",align:"center"},{default:l(r=>[S("span",null,O(r.row.parts_capacity.first_mass_production_capacity),1)]),_:2},1024),o(k,{label:"量产产能",width:"320",align:"center"},{default:l(r=>[S("span",null,O(r.row.parts_capacity.mass_production_capacity),1)]),_:2},1024),o(k,{fixed:"right",label:"操作","min-width":"220",align:"center"},{default:l(r=>[L((g(),C(u,{link:"",type:"primary",size:"small",onClick:Me=>Ie(r.row)},{default:l(()=>[b(" 编辑 ")]),_:2},1032,["onClick"])),[[z,Q(v).pms.StandardCapacity.permission.updatePartsCapacity]]),L((g(),C(u,{link:"",type:"warning",size:"small",onClick:Me=>{var _e;return Be((_e=r.row)==null?void 0:_e.id)}},{default:l(()=>[b(" 删除 ")]),_:2},1032,["onClick"])),[[z,Q(v).pms.StandardCapacity.permission.deletePartsCapacity]])]),_:2},1024)]),_:2},1032,["data"])]),_:1},8,["expand-row-keys"])]),_:1}),o(A,null,{default:l(()=>[o(N),o(P)]),_:1}),o(j,{ref_key:"Upsert",ref:ge},{"slot-product-group-select":l(({scope:t})=>[o(h,{modelValue:t.product_group_id,"onUpdate:modelValue":r=>t.product_group_id=r,filterable:"",remote:"",placeholder:"输入产品分组名查询","remote-method":Se,onChange:r=>{t.product_group_id=r,t.description="",t.workshop_section=void 0,t.pilot_production_capacity=void 0,t.first_mass_production_capacity=void 0,t.mass_production_capacity=void 0,t.re_mark="",Ee(r)}},{default:l(()=>[(g(!0),J(K,null,H(V.value,r=>(g(),C(i,{key:r.value,label:r.label,value:r.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])]),"slot-product-checkbox":l(({scope:t})=>[o(F,{modelValue:t.product_ids,"onUpdate:modelValue":r=>t.product_ids=r},{default:l(()=>[(g(!0),J(K,null,H(Y.value,r=>(g(),C(p,{key:r.value,label:r.label,value:r.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),"slot-workshop-section-select":l(({scope:t})=>[o(h,{modelValue:t.workshop_section,"onUpdate:modelValue":r=>t.workshop_section=r,placeholder:"请选择工段"},{default:l(()=>[(g(),J(K,null,H(T,r=>o(i,{key:r.value,label:r.label,value:r.value},null,8,["label","value"])),64))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),"slot-input-pilot-production":l(({scope:t})=>[o(s,{modelValue:t.pilot_production_capacity,"onUpdate:modelValue":r=>t.pilot_production_capacity=r,type:"number",placeholder:"请输入试产产能",formatter:W,step:"0.01"},{append:l(()=>[b(" pcs/h ")]),_:2},1032,["modelValue","onUpdate:modelValue"])]),"slot-input-first-production":l(({scope:t})=>[o(s,{modelValue:t.first_mass_production_capacity,"onUpdate:modelValue":r=>t.first_mass_production_capacity=r,type:"number",placeholder:"请输入首次量产产能",formatter:W,step:"0.01"},{append:l(()=>[b(" pcs/h ")]),_:2},1032,["modelValue","onUpdate:modelValue"])]),"slot-input-mass-production":l(({scope:t})=>[o(s,{modelValue:t.mass_production_capacity,"onUpdate:modelValue":r=>t.mass_production_capacity=r,type:"number",placeholder:"请输入量产产能",formatter:W,step:"0.01"},{append:l(()=>[b(" pcs/h ")]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:1},512)]),_:1})),[[je,ve.value]])}}});export{la as default};
