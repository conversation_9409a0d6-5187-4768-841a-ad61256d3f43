import{i as r}from"./index-BtOcqcNl.js";import{c as a,q as k,w as o,h as e,i as t,o as q}from"./.pnpm-hVqhwuVC.js";import{a as x}from"./index-D95m1iJL.js";const C=a({name:"pms-material-drawing"}),U=a({...C,setup(v){const{service:c}=x(),s=r.useUpsert({items:[{prop:"materialId",label:"物料ID",required:!0,component:{name:"el-input"}},{prop:"name",label:"图纸名称",required:!0,component:{name:"el-input"}},{prop:"path",label:"图纸路径",required:!0,component:{name:"el-input"}},{prop:"type",label:"图纸类型",component:{name:"el-radio-group",options:[]},required:!0},{prop:"version",label:"图纸版本",required:!0,component:{name:"el-input"}},{prop:"status",label:"图纸状态",component:{name:"cl-switch"},required:!0},{prop:"remark",label:"图纸备注",component:{name:"el-input",props:{type:"textarea",rows:4}},required:!0}]}),m=r.useTable({columns:[{type:"selection"},{prop:"id",label:"ID"},{prop:"materialId",label:"物料ID"},{prop:"name",label:"图纸名称"},{prop:"path",label:"图纸路径"},{prop:"type",label:"图纸类型",dict:[]},{prop:"version",label:"图纸版本"},{prop:"status",label:"图纸状态",component:{name:"cl-switch"}},{prop:"remark",label:"图纸备注",showOverflowTooltip:!0},{prop:"sort",label:"图纸排序"},{prop:"createTime",label:"创建时间",sortable:"desc",width:160},{prop:"updateTime",label:"更新时间",sortable:"custom",width:160},{type:"op",buttons:["edit","delete"]}]}),u=r.useCrud({service:c.pms.material.drawing},n=>{n.refresh()});return(n,T)=>{const _=t("cl-refresh-btn"),i=t("cl-add-btn"),d=t("cl-multi-delete-btn"),p=t("cl-flex1"),b=t("cl-search-key"),l=t("cl-row"),f=t("cl-table"),h=t("cl-pagination"),w=t("cl-upsert"),y=t("cl-crud");return q(),k(y,{ref_key:"Crud",ref:u},{default:o(()=>[e(l,null,{default:o(()=>[e(_),e(i),e(d),e(p),e(b)]),_:1}),e(l,null,{default:o(()=>[e(f,{ref_key:"Table",ref:m},null,512)]),_:1}),e(l,null,{default:o(()=>[e(p),e(h)]),_:1}),e(w,{ref_key:"Upsert",ref:s},null,512)]),_:1},512)}}});export{U as default};
