import b from"./index-C2ZAXrQl.js";import{_ as k}from"./addWorkOrder.vue_vue_type_script_setup_true_name_addWorkOrder_lang-C-0M4DZa.js";import{c as i,b as a,f as D,h as d,q as V,B as C,w as r,i as O,j as x,o as s}from"./.pnpm-hVqhwuVC.js";import"./index-BtOcqcNl.js";import"./material-table-columns.vue_vue_type_script_setup_true_lang-BFX8eStE.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-Dw7jsygE.js";const y=i({name:"undefined"}),S=i({...y,setup(B){const c=a({buttons:["slot-btn-del","slot-btn-detail","slot-btn-edit","slot-btn-finish"],width:330}),l=a(!1),n=a(null),e=a(null),o=a(!1);function f(){e.value=null,o.value=!1,l.value=!0}function p(){n.value.refresh()}function m(t){e.value=t,e.value.scheduleId=t.productionScheduleId,e.value.scheduleDate=t.planProductionDate,o.value=!1,l.value=!0}function v(t){e.value=t,e.value.scheduleId=t.productionScheduleId,e.value.scheduleDate=t.planProductionDate,o.value=!0,l.value=!0}return(t,u)=>{const _=O("el-button");return s(),D("div",null,[d(b,{ref_key:"WorkOrderRef",ref:n,"table-op":c.value,onDetail:m,onEdit:v},{"add-btn":r(()=>[d(_,{type:"primary",onClick:f},{default:r(()=>[x(" 新增 ")]),_:1})]),_:1},8,["table-op"]),l.value?(s(),V(k,{key:0,modelValue:l.value,"onUpdate:modelValue":u[0]||(u[0]=h=>l.value=h),"detail-data":e.value,"is-edit":o.value,onRefresh:p},null,8,["modelValue","detail-data","is-edit"])):C("",!0)])}}});export{S as default};
