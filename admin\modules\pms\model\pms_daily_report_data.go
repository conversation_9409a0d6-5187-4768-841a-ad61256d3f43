package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
	"github.com/imhuso/lookah-erp/admin/yc"
)

const TableNamePmsDailyReportData = "pms_daily_report_data"

type PmsDailyReportDataOutput struct {
	gmeta.Meta          `orm:"table:pms_daily_report_data"`
	ID                  int64       `json:"id"`            // ID
	GroupId             int         `json:"group_id"`      // 数据组
	ProducedDate        *gtime.Time `json:"produced_date"` // 生产日期
	OrderId             int64       `json:"order_id"`      // OrderId
	Quantity            int         `json:"quantity"`
	ExceptQuantity      int         `json:"except_quantity"` // 订单剩余数量
	ProductId           int64       `json:"product_id"`      // ProductId
	Sku                 string      `json:"sku"`
	WorkshopId          int64       `json:"workshop_id"`
	ProductionLine      string      `json:"production_line"`
	BusyworkGroup       string      `json:"busywork_group"`
	ProductionStages    int         `json:"production_stages"`
	WorkshopSection     int         `json:"workshop_section"`
	ManHour             float32     `json:"man_hour"`
	TotalManHour        float32     `json:"total_man_hour"`
	DailyOutput         float32     `json:"daily_output"`
	TotalDailyOutput    float32     `json:"total_daily_output"`
	AverageCapacity     float32     `json:"average_capacity"`
	Remark              string      `json:"remark"`
	Station             string      `json:"station"`
	NumberOfPeople      int         `json:"number_of_people"`
	TotalNumberOfPeople int         `json:"total_number_of_people"`
	CreateTime          *gtime.Time `json:"createTime"` // 创建时间
	DeleteTime          *gtime.Time `json:"-"`
}

// PmsDailyReportData mapped from table <pms_daily_report_data>
type PmsDailyReportData struct {
	ID                  int64       `json:"id"       gorm:"column:id;type:bigint(20);not null;primary_key;auto_increment;comment:ID;"` // ID
	GroupId             int         `json:"group_id" gorm:"column:group_id;type:bigint(20);not null;default:0;comment:数据组;"`           // 数据组
	ProducedDate        *gtime.Time `json:"produced_date" gorm:"column:produced_date;type:date;comment:生产日期;"`                         // 生产日期
	OrderId             int64       `json:"order_id" gorm:"column:order_id;type:bigint(20);not null;default:0;comment:生产订单id;"`        // OrderId
	Quantity            int         `json:"quantity" gorm:"column:quantity;type:int(11);not null;default:0;comment:订单数量;"`
	ProductId           int64       `json:"product_id" gorm:"column:product_id;type:bigint(20);not null;default:0;comment:ProductId;"` // ProductId
	Sku                 string      `json:"sku" gorm:"column:sku;type:varchar(100);not null;default:'';comment:SKU;"`
	WorkshopId          int64       `json:"workshop_id" gorm:"column:workshop_id;type:int(10);not null;default:0;comment:生产车间;"`
	ProductionLine      string      `json:"production_line" gorm:"column:production_line;type:varchar(100);not null;default:'';comment:产线;"`
	Station             string      `json:"station" gorm:"column:station;type:varchar(20);not null;default:'';comment:工位;"`
	BusyworkGroup       string      `json:"busywork_group" gorm:"column:busywork_group;type:varchar(1000);not null;default:'';comment:作业人员;"`
	ProductionStages    int         `json:"production_stages" gorm:"column:production_stages;type:int(10);not null;default:0;comment:生产阶段;"`
	WorkshopSection     int         `json:"workshop_section" gorm:"column:workshop_section;type:int(10);not null;default:0;comment:工序;"`
	ManHour             float32     `json:"man_hour" gorm:"column:man_hour;type:decimal(10,4);not null;default:0.0000;comment:工时;"`
	TotalManHour        float32     `json:"total_man_hour" gorm:"column:total_man_hour;type:decimal(10,4);not null;default:0.0000;comment:合计工时;"`
	DailyOutput         float32     `json:"daily_output" gorm:"column:daily_output;type:decimal(10,4);not null;default:0.0000;comment:当日产能;"`
	TotalDailyOutput    float32     `json:"total_daily_output" gorm:"column:total_daily_output;type:decimal(10,4);not null;default:0.0000;comment:当日产能总计;"`
	AverageCapacity     float32     `json:"average_capacity" gorm:"column:average_capacity;type:decimal(10,4);not null;default:0.0000;comment:人均产能;"`
	Remark              string      `json:"remark" gorm:"column:remark;type:varchar(100);not null;default:'';comment:备注;"`
	NumberOfPeople      int         `json:"number_of_people" gorm:"column:number_of_people;type:int(11);not null;default:0;comment:人数;"`
	TotalNumberOfPeople int         `json:"total_number_of_people" gorm:"column:total_number_of_people;type:int(11);not null;default:0;comment:合计人数;"`
	CreateTime          *gtime.Time `json:"createTime" gorm:"column:createTime;not null;index,priority:1;autoCreateTime;comment:创建时间"` // 创建时间
	DeleteTime          *gtime.Time `json:"-" gorm:"column:deleteTime;index;comment:删除时间"`                                             // 删除时间
}

// GroupName 返回分组名
func (m *PmsDailyReportData) GroupName() string {
	return ""
}

// TableName PmsSupplier's table name
func (*PmsDailyReportData) TableName() string {
	return TableNamePmsDailyReportData
}

// NewPmsDailyReportData 创建实例
func NewPmsDailyReportData() *PmsDailyReportData {
	return &PmsDailyReportData{}
}

func init() {
	_ = yc.CreateTable(NewPmsDailyReportData())
}
