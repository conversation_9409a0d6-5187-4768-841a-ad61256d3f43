package service

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"github.com/gogf/gf/v2/container/garray"
	"github.com/gogf/gf/v2/container/gmap"
	"github.com/gogf/gf/v2/container/gset"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/text/gstr"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/imhuso/lookah-erp/admin/modules/pms/model"
	"github.com/imhuso/lookah-erp/admin/modules/pms/utils"
	"github.com/imhuso/lookah-erp/admin/yc"
	"github.com/xuri/excelize/v2"
	"math"
	"net/url"
)

// MaterialInboundStatus 状态枚举
type MaterialInboundStatus int

// 0:草稿 1:待处理 2:入库中 3:已完成 4:待审核
const (
	MaterialInboundStatusDraft MaterialInboundStatus = iota
	MaterialInboundStatusPending
	MaterialInboundStatusInbound
	MaterialInboundStatusCompleted
	MaterialInboundStatusPendingReview
)

// MaterialInboundThreshold 入库单超出阈值
const MaterialInboundThreshold = 0

// MaterialInboundCacheKeyPrefix 物料入库缓存KEY前缀
const MaterialInboundCacheKeyPrefix = "pms:material:inbound:"

type PmsMaterialInboundService struct {
	*yc.Service
	*AuditService
}

func (s *PmsMaterialInboundService) ServiceUpdate(ctx context.Context, _ *yc.UpdateReq) (data interface{}, err error) {
	// 订单更新直接调用Add方法
	return s.ServiceAdd(ctx, nil)
}

func (s *PmsMaterialInboundService) ServiceAdd(ctx context.Context, _ *yc.AddReq) (data interface{}, err error) {
	r := g.RequestFromCtx(ctx)
	rMap := r.GetMap()
	isUpdate := false
	inboundType := r.Get("type").Int()
	orderId := r.Get("orderId").Int64()
	workOrderId := r.Get("workOrderId").Int64()

	if inboundType == int(model.MaterialInboundTypePurchase) && orderId == 0 {
		return nil, gerror.New("订单入库必须选择订单")
	}
	if inboundType == int(model.MaterialInboundTypeReturnPolicy) && orderId == 0 {
		return nil, gerror.New("补退货必须选择订单")
	}
	if inboundType == int(model.MaterialInboundTypeProductionReturn) && workOrderId == 0 {
		return nil, gerror.New("生产退料必须选择工单")
	}

	// 获取产品
	products := make([]*model.PmsMaterialInboundProduct, 0)
	type materialInboundProductType struct {
		InboundOutboundKey int     `json:"inbound_outbound_key"`
		ContractId         int64   `json:"contractId"`
		MaterialId         int64   `json:"materialId"`
		Quantity           float64 `json:"quantity"`
		WarehouseId        int64   `json:"warehouseId"`
		Address            string  `json:"address"`
	}

	// 获取产品
	materialInboundProductsInput := make([]materialInboundProductType, 0)
	err = gconv.Scan(rMap["materials"], &materialInboundProductsInput)
	if err != nil {
		return
	}

	if len(materialInboundProductsInput) == 0 {
		return nil, gerror.New("入库物料不能为空")
	}

	contractIds := garray.New()
	// 遍历产品
	for _, v := range materialInboundProductsInput {
		if orderId > 0 {
			// 采购单或补退货
			if inboundType == int(model.MaterialInboundTypePurchase) {
				if v.ContractId == 0 {
					return nil, gerror.New("选择的物料有误")
				}

				// 如果合同ID已存在，则报错
				if contractIds.Contains(v.ContractId) {
					return nil, gerror.New("选择的物料有重复的项，请检查")
				}

				contractIds.Append(v.ContractId)
			}
		} else {
			if v.MaterialId == 0 {
				return nil, gerror.New("选择的物料有误")
			}
		}

		// 如果物料ID为0或者数量为0，则报错
		if inboundType != int(model.MaterialInboundTypeInventoryAdjustment) && v.Quantity <= 0 {
			return nil, gerror.New("选择的物料有误或者数量有误")
		}

		products = append(products, &model.PmsMaterialInboundProduct{
			MaterialId:         v.MaterialId,
			ContractId:         v.ContractId,
			Quantity:           v.Quantity,
			WarehouseId:        v.WarehouseId,
			Address:            v.Address,
			InboundOutboundKey: v.InboundOutboundKey,
		})
	}

	err = s.checkInboundData(ctx, inboundType, products)
	if err != nil {
		return nil, err
	}

	err = s.checkInboundPurchaseOrder(ctx, orderId, inboundType, products)

	if err != nil {
		return nil, err
	}

	// 获取入库单ID
	inboundId := gconv.Int64(rMap["id"])
	if inboundId > 0 {
		// 获取入库单信息
		var inbound *model.PmsMaterialInbound
		err = yc.DBM(s.Model).Where("id", inboundId).WithAll().Scan(&inbound)
		if err != nil {
			return nil, err
		}

		if inbound == nil {
			return nil, gerror.New(yc.T(ctx, "inboundOrderDoesNotExist"))
		}

		if inbound.Status != int(MaterialInboundStatusDraft) {
			return nil, gerror.New(yc.T(ctx, "onlyDraftInboundOrderCanBeModified"))
		}
		isUpdate = true
	} else {
		isUpdate = false
	}

	// 开启事务
	err = yc.DB(s.Model.GroupName()).Ctx(ctx).Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		inboundId, err = s.CreateInbound(ctx, &model.PmsMaterialInbound{
			ID:          inboundId,
			Type:        inboundType,
			OrderId:     orderId,
			Status:      int(MaterialInboundStatusDraft),
			Remark:      r.Get("remark").String(),
			WorkOrderId: workOrderId,
			Products:    products,
		}, isUpdate)

		return nil
	})

	if err != nil {
		return
	}

	return
}

// checkInboundData 检查入库单数据
func (s *PmsMaterialInboundService) checkInboundData(ctx context.Context, inboundType int, products []*model.PmsMaterialInboundProduct) (err error) {
	if inboundType == int(model.MaterialInboundTypeInventoryAdjustment) {
		// 取出所有物料ID
		materialIds := garray.New()
		// 遍历产品
		for _, v := range products {
			materialIds.Append(v.MaterialId)
		}

		// 验证物料是否超出可用在库数量
		materialAvailableInventory := make([]*model.PmsMaterial, 0)
		err = yc.DBM(model.NewPmsMaterial()).Ctx(ctx).WhereIn("id", materialIds.Slice()).Scan(&materialAvailableInventory)
		if err != nil && !errors.Is(err, sql.ErrNoRows) {
			return err
		}

		// 定义一个map，key:物料ID，value:可用在库数量
		materialAvailableInventoryMap := gmap.New()
		for _, v := range materialAvailableInventory {
			materialAvailableInventoryMap.Set(v.ID, math.Max(v.Inventory-v.LockedInventory, 0))
		}

		// 遍历产品，如果是负数，则判断是否超出可用在库数量
		for _, v := range products {
			if v.Quantity < 0 {
				availableInventory := materialAvailableInventoryMap.Get(v.MaterialId)
				if availableInventory == nil {
					return gerror.New("选择的物料有误")
				}

				if gconv.Float64(availableInventory) < gconv.Float64(v.Quantity)*-1 {
					return gerror.New("选择的物料超出可用在库数量")
				}
			}
		}
	}

	return
}

// checkInboundPurchaseOrder 检查入库单的订单是否正确
func (s *PmsMaterialInboundService) checkInboundPurchaseOrder(ctx context.Context, orderId int64, inboundType int, products []*model.PmsMaterialInboundProduct) error {
	if orderId == 0 {
		return nil
	}

	if inboundType == int(model.MaterialInboundTypePurchase) {
		// 先判断采购单的状态是否正确
		order := model.NewPmsPurchaseOrder()
		err := yc.DBM(model.NewPmsPurchaseOrder()).Ctx(ctx).WhereIn("id", orderId).Scan(&order)
		if err != nil && !errors.Is(err, sql.ErrNoRows) {
			return err
		}

		if order == nil {
			return gerror.New("选择的订单不正确，请重试")
		}

		// 只有已确认或者入库中的订单才能入库
		if order.Status != int(PurchaseOrderStatusConfirmed) && order.Status != int(PurchaseOrderStatusInbound) {
			return gerror.New("只有已确认或者入库中的订单才能入库")
		}

		if len(products) == 0 {
			return gerror.New("入库物料不能为空")
		}

		contracts := make([]*model.PmsPurchaseContract, 0)
		err = yc.DBM(model.NewPmsPurchaseContract()).Ctx(ctx).Where("purchase_id", orderId).Scan(&contracts)
		if err != nil && !errors.Is(err, sql.ErrNoRows) {
			return err
		}

		if len(contracts) == 0 {
			return gerror.New("选择的订单不正确，请重试")
		}

		// 查找同物料在入库中的数量
		materialInbounds := make([]*model.PmsMaterialInbound, 0)
		err = yc.DBM(model.NewPmsMaterialInbound()).Ctx(ctx).
			Where("order_id", orderId).
			With(&model.PmsMaterialInboundProduct{}).
			WhereIn("status", []MaterialInboundStatus{MaterialInboundStatusPending, MaterialInboundStatusInbound}).
			Scan(&materialInbounds)
		if err != nil && !errors.Is(err, sql.ErrNoRows) {
			return err
		}

		// key: 物料ID，value: 数量 map[物料ID]数量
		inboundMaterialMap := make(map[int64]float64)
		for _, inbound := range materialInbounds {
			for _, product := range inbound.Products {
				inboundMaterialMap[product.MaterialId] += product.Quantity
			}
		}

		// 校验物料都在合同中，并且数量正确
		for _, product := range products {
			if product.MaterialId == 0 {
				return gerror.New("选择的物料不正确")
			}

			contract := &model.PmsPurchaseContract{}
			for _, c := range contracts {
				if c.ID == product.ContractId && c.MaterialId == product.MaterialId {
					contract = c
					break
				}
			}

			if contract == nil {
				return gerror.New("选择的物料不在订单中")
			}

			// 入库中的数量
			inboundQuantity := 0.0000
			if v, ok := inboundMaterialMap[product.MaterialId]; ok {
				inboundQuantity = v
			}

			remain := contract.Quantity - contract.ReceivedQuantity - inboundQuantity + MaterialInboundThreshold

			g.Log().Debugf(ctx, "物料ID: %d, 订单数量: %f, 转单数量: %f, 已入库数量: %f, 入库中数量: %f, 剩余数量: %f", product.MaterialId, contract.Quantity, contract.Transfer, contract.ReceivedQuantity, inboundQuantity, remain)

			if remain < 0 || product.Quantity > remain {
				return gerror.New("选择的物料数量不能超出可入库数量")
			}
		}
	} else if inboundType == int(model.MaterialInboundTypeProductionReturn) { // 生产退料
		//err := s.checkProductionReturn(ctx, orderId, products)
		//if err != nil {
		//	return err
		//}
		err := s.ValidateProductionReturn(ctx, orderId, products)
		if err != nil {
			return err
		}
	}

	return nil
}

// 校验生产退料
func (s *PmsMaterialInboundService) ValidateProductionReturn(ctx context.Context, orderId int64, products []*model.PmsMaterialInboundProduct) error {
	materialInboundProductVoList, err := NewPmsProductionScheduleService().GetMaterialOutboundRecord(ctx, orderId)
	if err != nil {
		return err
	}
	if len(materialInboundProductVoList) == 0 {
		return gerror.New("选择的生产单没有出库记录")
	}
	productMap := make(map[int64]float64)
	for _, item := range products {
		if _, ok := productMap[item.MaterialId]; ok {
			productMap[item.MaterialId] += item.Quantity
		} else {
			productMap[item.MaterialId] = item.Quantity
		}
	}

	for materialId, item := range productMap {
		for _, el := range materialInboundProductVoList {
			if materialId == el.MaterialId && item > el.Quantity {
				return gerror.New("选择的物料数量不能超出该生产单的出库数量")
			}
		}
	}
	return nil
}

func (s *PmsMaterialInboundService) CreateInbound(ctx context.Context, inbound *model.PmsMaterialInbound, isUpdate bool) (id int64, err error) {
	// 计算入库单总数量
	totalQuantity := 0.0000
	for _, product := range inbound.Products {
		totalQuantity += product.Quantity
	}

	inbound.TotalQuantity = totalQuantity

	inbound.CreateTime = gtime.Now()
	id = inbound.ID

	if isUpdate {
		// 更新入库单
		_, err = yc.DBM(s.Model).Ctx(ctx).Data(inbound).OmitEmptyData().Where("id", id).Update()
		if err != nil {
			return 0, err
		}
		// 删除产品
		_, err = yc.DBM(model.NewPmsMaterialInboundProduct()).Ctx(ctx).Where("inbound_id", id).Delete()
		if err != nil {
			return 0, err
		}
	} else {
		todayOrderCount := 0
		todayStart := gtime.Now().StartOfDay()
		todayOrderCount, err = yc.DBM(s.Model).Ctx(ctx).WhereGT("createTime", todayStart).Unscoped().Count()
		if err != nil {
			return 0, err
		}

		todayOrderCountStr := fmt.Sprintf("%d", todayOrderCount+1)
		// 订单数量不足3位数，前面补0
		if len(todayOrderCountStr) < 3 {
			todayOrderCountStr = fmt.Sprintf("%03s", todayOrderCountStr)
		}
		inboundNo := fmt.Sprintf("IMSN%s%s", gconv.String(gtime.Now().Format("Ymd")), todayOrderCountStr)
		// 判断是否存在
		count, err := yc.DBM(s.Model).Ctx(ctx).Where("no", inboundNo).Count()
		if err != nil {
			return 0, err
		}
		if count > 0 {
			return 0, gerror.New("创建入库单失败！")
		}
		inbound.No = inboundNo

		// 如果是送货单入库，并且没有设置内部订单号
		//if inbound.InternalOrderNo == "" {
		// 生成内部订单号
		internalOrderNo, err := GetInternalInboundNo(ctx)
		inbound.InternalOrderNo = internalOrderNo
		id, err = yc.DBM(s.Model).Ctx(ctx).Data(inbound).InsertAndGetId()
		if err != nil {
			return 0, err
		}
		//} else { // 如果内部订单号已存在，则直接使用
		//	id, err = yc.DBM(s.Model).Ctx(ctx).Data(inbound).InsertAndGetId()
		//	if err != nil {
		//		return 0, err
		//	}
		//}

		//   同步数据至送货单表
		//   如果是送货单入库.
		if inbound.DeliveryNoteId > 0 {
			_, err = yc.DBM(model.NewDeliveryNote()).Ctx(ctx).
				Where("id", inbound.DeliveryNoteId).
				Data(g.Map{
					"internal_order_no": inbound.InternalOrderNo,
				}).Update()
			if err != nil {
				return 0, err
			}
		}
	}

	if id == 0 {
		return 0, gerror.New("创建入库单失败")
	}

	// 创建产品
	products := inbound.Products
	if len(products) > 0 {
		// 遍历添加id
		for _, product := range products {
			product.InboundId = id
		}

		// 添加产品
		_, err = yc.DBM(model.NewPmsMaterialInboundProduct()).Ctx(ctx).Data(products).Insert()
		if err != nil {
			return 0, err
		}
	}

	return
}

// 创建入库单编号
func (s *PmsMaterialInboundService) CreateInboundNo(ctx context.Context, params *model.PmsMaterialInbound) (string, error) {
	result := ""
	// {0: "自定义入库", 1: "采购单入库", 2: "库存调整", 3: "生产退料"}
	typeMap := g.MapIntStr{0: "001", 1: "002", 2: "003", 3: "004"}
	result = typeMap[params.Type] + "-IMSN"
	timeStr := gtime.Now().Format("Ymd")
	result = result + timeStr
	list := make([]*model.PmsMaterialInbound, 0)
	err := yc.DBM(s.Model).Ctx(ctx).Where(fmt.Sprintf("LOCATE(%s,%s)", timeStr, "no")).Scan(&list)
	if err != nil {
		return result, err
	}
	if len(list) == 0 {
		result += "001"
	} else {
		maxNo := 0
		for _, v := range list {
			if !gstr.Contains(v.No, "-") {
				continue
			}
			length := len(v.No)
			if length > 15 {
				no := gstr.SubStr(v.No, 16, length)
				noInt := gconv.Int(no)
				if noInt > maxNo {
					maxNo = noInt
				}
			}
		}
		maxNo = maxNo + 1
		maxNoStr := gconv.String(maxNo)
		if maxNo < 10 {
			maxNoStr = "00" + gconv.String(maxNo)
		} else if maxNo < 100 {
			maxNoStr = "0" + gconv.String(maxNo)
		}
		result = result + maxNoStr
	}
	return result, err
}

func (s *PmsMaterialInboundService) InboundConfirm(ctx context.Context, inboundId int64) (data interface{}, err error) {
	// 获取入库单信息
	var inbound *model.PmsMaterialInbound
	err = yc.DBM(s.Model).Ctx(ctx).Where("id", inboundId).
		With(&model.PmsMaterialInboundProduct{}).
		Scan(&inbound)
	if err != nil {
		return
	}

	if inbound == nil {
		return nil, gerror.New("入库单不存在")
	}

	if inbound.Status != int(MaterialInboundStatusDraft) {
		return nil, gerror.New("只有草稿状态的入库单才能确认")
	}

	err = s.checkInboundData(ctx, inbound.Type, inbound.Products)
	if err != nil {
		return nil, err
	}

	err = s.checkInboundPurchaseOrder(ctx, inbound.OrderId, inbound.Type, inbound.Products)
	if err != nil {
		return nil, err
	}

	targetStatus := MaterialInboundStatusPending
	// 开启事务
	err = yc.DB(s.Model.GroupName()).Ctx(ctx).Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 如果是库存调整直接设置为入库中
		if inbound.Type == int(model.MaterialInboundTypeInventoryAdjustment) || inbound.Type == int(model.MaterialInboundTypeSelfSelect) {
			targetStatus = MaterialInboundStatusInbound
		}
		// 更新入库单状态
		_, err = yc.DBM(s.Model).Ctx(ctx).Data(g.Map{
			"status": targetStatus,
		}).Where("id", inboundId).Update()

		if err != nil {
			return err
		}

		// 如果是订单入库，则更新订单状态
		if inbound.Type == int(model.MaterialInboundTypePurchase) {
			_, err = yc.DBM(model.NewPmsPurchaseOrder()).Ctx(ctx).Data(g.Map{
				"status": PurchaseOrderStatusInbound,
			}).Where("id", inbound.OrderId).Where("status", PurchaseOrderStatusConfirmed).Update()

			if err != nil {
				return err
			}

			// 将关联的采购需求单设置为已确认
			_, err = yc.DBM(model.NewPmsProductionPurchaseOrder()).Ctx(ctx).
				Data("status", int(ProductionPurchaseOrderStatusInbound)).
				Where("purchase_id", inbound.OrderId).
				Where("status", ProductionPurchaseOrderStatusConfirmed).Update()

			if err != nil {
				return err
			}
		}

		return nil
	})

	if err != nil {
		return
	}

	return g.Map{"status": targetStatus}, nil

}

func (s *PmsMaterialInboundService) InboundStart(ctx context.Context, inboundId int64, inboundTime *gtime.Time, voucher string) (res interface{}, err error) {
	// 获取入库单信息
	var inbound *model.PmsMaterialInbound
	err = yc.DBM(s.Model).Ctx(ctx).Where("id", inboundId).Scan(&inbound)
	if err != nil {
		return
	}

	if inbound == nil {
		return nil, gerror.New("入库单不存在")
	}

	if inbound.Status != int(MaterialInboundStatusPending) {
		return nil, gerror.New("只有待处理状态的入库单才能开始")
	}

	// 开启事务
	err = yc.DB(s.Model.GroupName()).Ctx(ctx).Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 更新入库单状态
		_, err = yc.DBM(s.Model).Ctx(ctx).Data(g.Map{
			"status":       MaterialInboundStatusInbound,
			"inbound_time": inboundTime,
			"voucher":      voucher,
		}).Where("id", inboundId).Update()

		if err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return
	}

	return g.Map{"status": MaterialInboundStatusInbound}, nil
}

func (s *PmsMaterialInboundService) InboundComplete(ctx context.Context, inboundId int64) (res interface{}, err error) {
	// 获取入库单信息
	var inbound *model.PmsMaterialInbound
	err = yc.DBM(s.Model).Ctx(ctx).Where("id", inboundId).Scan(&inbound)
	if err != nil {
		return
	}

	if inbound == nil {
		return nil, gerror.New("入库单不存在")
	}

	if inbound.Status != int(MaterialInboundStatusInbound) {
		return nil, gerror.New("只有入库中状态的入库单才能完成")
	}

	actionLogs := make([]*model.PmsActionLog, 0)
	// 开启事务
	err = yc.DB(s.Model.GroupName()).Ctx(ctx).Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		dataMap := g.Map{
			"status":        MaterialInboundStatusCompleted,
			"complete_time": gtime.Now(),
		}

		// 如果没有单据时间，则设置为当前时间
		if inbound.InboundTime == nil || inbound.InboundTime.Timestamp() <= 0 {
			dataMap["inbound_time"] = gtime.Now()
		}

		// 更新入库单状态
		_, err = yc.DBM(s.Model).Ctx(ctx).Data(dataMap).Where("id", inboundId).Update()
		if err != nil {
			return err
		}

		// 获取产品
		var products []*model.PmsMaterialInboundProduct
		err = yc.DBM(model.NewPmsMaterialInboundProduct()).Ctx(ctx).Where("inbound_id", inboundId).Scan(&products)
		if err != nil {
			return err
		}

		// 如果是订单入库，则更新订单已入库数量
		if inbound.Type == int(model.MaterialInboundTypePurchase) {
			err = s.materialInboundPurchaseOrRevert(ctx, inbound, products, false)
			if err != nil {
				return err
			}
		} else if inbound.Type == int(model.MaterialInboundTypeProductionReturn) {
			err = s.materialInboundProductionReturnOrRevoke(ctx, inbound, products, false)
			if err != nil {
				return err
			}
		} else {
			materialStocks := make([]*model.MaterialStock, 0)

			// 遍历产品，更新订单已入库数量
			for _, product := range products {
				materialStocks = append(materialStocks, &model.MaterialStock{
					MaterialId:      product.MaterialId,
					Stock:           product.Quantity,
					LockedStock:     0,
					ExpectedInbound: 0,
					Remark:          fmt.Sprintf("入库单号：%s，入库完成，更新在库库存", inbound.No),
				})
			}

			// 更新库存
			err = NewPmsMaterialService().UpdateMaterialStocks(ctx, materialStocks)
			if err != nil {
				return err
			}
		}

		// 添加操作日志
		actionLogs = append(actionLogs, &model.PmsActionLog{
			UserId:   yc.GetAdmin(ctx).UserId,
			Type:     int(ActionLogTypeWarehouseSourceOutbound),
			ObjectId: inboundId,
			Content:  fmt.Sprintf("入库单号：%s 入库完成", inbound.No),
		})

		return nil
	})

	if err != nil {
		return
	}

	// 添加操作日志
	NewPmsActionLogService().CreateLog(ctx, actionLogs)

	return g.Map{"status": MaterialInboundStatusCompleted}, nil
}
func (s *PmsMaterialInboundService) RevokeAndSync(ctx context.Context, id int64) (res interface{}, err error) {
	// 获取入库单信息
	var inbound *model.PmsMaterialInbound
	err = yc.DBM(s.Model).Ctx(ctx).Where("id", id).Scan(&inbound)
	if err != nil {
		return
	}
	// 如果这条数据是关联的交货单，则直接撤销交货单,并且删除入库记录
	if inbound.DeliveryNoteId > 0 {
		deliveryNote := &model.PmsDeliveryNote{}
		err = yc.DBM(model.NewDeliveryNote()).Ctx(ctx).Where("id", inbound.DeliveryNoteId).Scan(&deliveryNote)
		if err != nil {
			return
		}
		res, err := NewPmsDeliveryNoteService().Revoke(ctx, deliveryNote)
		if err != nil {
			return nil, err
		}

		return res, nil
	} else {
		return nil, gerror.New("入库单不是关联的交货单，操作失败!")
	}
}

// Revoke 撤销入库单。
func (s *PmsMaterialInboundService) Revoke(ctx context.Context, id int64) (res interface{}, err error) {
	// 获取入库单信息
	var inbound *model.PmsMaterialInbound
	err = yc.DBM(s.Model).Ctx(ctx).Where("id", id).Scan(&inbound)
	if err != nil {
		return
	}

	if inbound == nil {
		return nil, gerror.New("入库单不存在")
	}

	if inbound.Status == int(MaterialInboundStatusDraft) {
		return nil, gerror.New("草稿状态的入库单不能撤销")
	}

	if utils.V1_DISABLED(inbound.CreateTime) {
		return nil, gerror.New("V1版本之前的数据不能撤回")
	}

	actionLogs := make([]*model.PmsActionLog, 0)
	materialStocks := make([]*model.MaterialStock, 0)
	extras := make([]*model.PmsPurchaseOrderWithSummaryExtraOutput, 0)

	// 开启事务
	err = yc.DB(s.Model.GroupName()).Ctx(ctx).Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 更新入库单状态
		_, err = yc.DBM(s.Model).Ctx(ctx).Data(g.Map{
			"status":        MaterialInboundStatusDraft,
			"complete_time": nil,
			"inbound_time":  nil,
			"voucher":       "",
		}).Where("id", id).Update()

		if err != nil {
			return err
		}

		// 如果入库单状态为已完成，则需要更新库存
		if inbound.Status == int(MaterialInboundStatusCompleted) {
			// 获取产品
			var products []*model.PmsMaterialInboundProduct
			err = yc.DBM(model.NewPmsMaterialInboundProduct()).Ctx(ctx).Where("inbound_id", id).Scan(&products)
			if err != nil {
				return err
			}

			// 如果是订单入库，减少订单已入库数量
			if inbound.Type == int(model.MaterialInboundTypePurchase) {
				// 遍历产品，进行出库
				err = s.materialInboundPurchaseOrRevert(ctx, inbound, products, true)
				if err != nil {
					return err
				}
			} else if inbound.Type == int(model.MaterialInboundTypeProductionReturn) {
				// 撤销生产退料
				//err = s.materialInboundProductionReturnOrRevoke(ctx, inbound, products, true)
				//if err != nil {
				//	return err
				//}
				materialStocks := make([]*model.MaterialStock, 0)
				// 遍历产品，更新订单已入库数量
				for _, product := range products {
					materialStocks = append(materialStocks, &model.MaterialStock{
						ProductionScheduleId: inbound.OrderId,
						MaterialId:           product.MaterialId,
						Stock:                -product.Quantity,
						UsedQuantity:         0,
						InboundId:            inbound.ID,
						InboundSn:            inbound.OrderNo,
						OperationType:        -s.StatusConvert(inbound.Type),
						Remark:               fmt.Sprintf("入库单号：%s，撤销入库，更新库存", inbound.No),
					})
					extra := &model.PmsPurchaseOrderWithSummaryExtraOutput{MaterialId: product.MaterialId}
					extra.OutQty = product.Quantity
					extras = append(extras, extra)
				}
				// 更新库存
				err = NewPmsMaterialService().UpdateMaterialStockBatchV2(ctx, materialStocks)
				if err != nil {
					return err
				}
				if len(extras) > 0 {
					pmsWorkOrderVo := &model.PmsWorkOrderVo{
						PmsWorkOrder: &model.PmsWorkOrder{ID: inbound.WorkOrderId},
						Extras:       extras,
					}
					_, err = NewPmsWorkOrderService().UpdateDetailBatch(ctx, pmsWorkOrderVo)
					if err != nil {
						return err
					}
				}
			} else if inbound.Type == int(model.MaterialInboundTypeInventoryReturn) || inbound.Type == int(model.MaterialInboundTypeReturnPolicy) { // 撤销库存
				err = yc.DBM(model.NewPmsMaterialInboundProduct()).Ctx(ctx).Where("inbound_id", id).Scan(&inbound.Products)
				if err != nil {
					return err
				}
				_, err2 := s.HandleInventoryReturn(ctx, inbound, true)
				if err2 != nil {
					return err2
				}
			} else {
				// 遍历产品，更新订单已入库数量
				for _, product := range products {
					materialStocks = append(materialStocks, &model.MaterialStock{
						MaterialId:      product.MaterialId,
						InboundId:       id,
						InboundSn:       inbound.No,
						OperationType:   -s.StatusConvert(inbound.Type),
						Stock:           -product.Quantity,
						LockedStock:     0,
						ExpectedInbound: 0,
						Remark:          fmt.Sprintf("入库单号：%s，撤销入库，更新在库库存", inbound.No),
					})
				}
			}
			// 更新库存
			if len(materialStocks) > 0 {
				err = NewPmsMaterialService().UpdateMaterialStockBatchV2(ctx, materialStocks)
				if err != nil {
					return err
				}
			}
		}
		// 添加操作日志
		actionLogs = append(actionLogs, &model.PmsActionLog{
			UserId:   yc.GetAdmin(ctx).UserId,
			Type:     int(ActionLogTypeWarehouseSourceOutbound),
			ObjectId: id,
			Content:  fmt.Sprintf("入库单号：%s 撤销", inbound.No),
		})

		// 添加操作日志
		NewPmsActionLogService().CreateLog(ctx, actionLogs)

		// 结束流程
		err2 := s.Cancel(ctx, id)
		if err2 != nil {
			return err2
		}
		return nil
	})

	if err != nil {
		return
	}

	return
}

// ProductionReturn 生产退料
func (s *PmsMaterialInboundService) checkProductionReturn(ctx context.Context, productionPurchaseOrderId int64, products []*model.PmsMaterialInboundProduct) (err error) {
	// 生产退料
	// 获取生产订单关联的所有采购订单
	purchaseIds, err := yc.DBM(model.NewPmsProductionPurchaseOrder()).Ctx(ctx).Fields("purchase_id").Where("order_id", productionPurchaseOrderId).Array()
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return err
	}

	if len(purchaseIds) == 0 {
		return gerror.New("没有找到关联的采购订单")
	}

	odes := make([]*model.PmsPurchaseOrderDetailExtra, 0)
	err = yc.DBM(model.NewPmsPurchaseOrderDetailExtra()).Ctx(ctx).
		WhereIn("purchase_id", purchaseIds).
		Where("outbound_quantity > 0").
		Group("material_id").
		Scan(&odes)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return
	}

	// 将oq转换为map
	oqMap := gmap.New()
	for _, v := range odes {
		// 如果存在，则累加
		if oqMap.Contains(v.MaterialId) {
			oqMap.Set(v.MaterialId, gconv.Float64(oqMap.Get(v.MaterialId))+v.OutboundQuantity)
		} else {
			oqMap.Set(v.MaterialId, v.OutboundQuantity)
		}
	}

	// 判断已出库数量是否足够
	for _, product := range products {
		// 如果物料ID不存在，则报错
		if product.MaterialId == 0 || oqMap.Get(product.MaterialId) == nil {
			return gerror.New("选择的物料有误")
		}

		// 如果数量大于已出库数量，则报错
		if product.Quantity > gconv.Float64(oqMap.Get(product.MaterialId)) {
			return gerror.New("选择的物料数量不能超出已出库数量")
		}
	}

	return
}

// materialInboundProductionReturnOrRevoke 生产退料或者撤销生产退料
func (s *PmsMaterialInboundService) materialInboundProductionReturnOrRevoke(ctx context.Context, inbound *model.PmsMaterialInbound, products []*model.PmsMaterialInboundProduct, isRevoke bool) error {
	productionPurchaseOrderId := inbound.OrderId

	// 开启事务
	err := yc.DB(s.Model.GroupName()).Ctx(ctx).Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		outboundProducts := make([]*model.PmsMaterialOutboundProduct, 0)
		// 将products转换为outboundProducts
		for _, product := range products {
			outboundProducts = append(outboundProducts, &model.PmsMaterialOutboundProduct{
				MaterialId: product.MaterialId,
				Quantity:   product.Quantity,
			})
		}

		// 生产退料和领料入库刚好是相反操作
		pickOutRevoke := !isRevoke
		remark := fmt.Sprintf("生产退料，入库ID：%d", inbound.ID)
		if isRevoke {
			remark = fmt.Sprintf("撤销生产退料，入库ID：%d", inbound.ID)
		}

		materialStocks, err := NewPmsMaterialOutboundService().PickOutboundOrRevoke(ctx, productionPurchaseOrderId, outboundProducts, remark, pickOutRevoke)
		if err != nil {
			return err
		}

		// 更新库存
		err = NewPmsMaterialService().UpdateMaterialStocks(ctx, materialStocks)
		if err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return err
	}

	return nil
}

// materialInboundPurchaseOrRevert 入库或者撤销入库
func (s *PmsMaterialInboundService) materialInboundPurchaseOrRevert(ctx context.Context, inbound *model.PmsMaterialInbound, products []*model.PmsMaterialInboundProduct, isRevoke bool) error {
	order := model.NewPmsPurchaseOrder()
	err := yc.DBM(model.NewPmsPurchaseOrder()).Ctx(ctx).
		With(&model.PmsPurchaseOrderDetail{}).
		Where("id", inbound.OrderId).Scan(&order)
	if err != nil {
		return err
	}

	if order.ID == 0 {
		return gerror.New("订单不存在")
	}

	purchaseOrderId := order.ID

	// 获取订单合同
	contracts := make([]*model.PmsPurchaseContract, 0)
	err = yc.DBM(model.NewPmsPurchaseContract()).Ctx(ctx).Where("purchase_id", purchaseOrderId).Scan(&contracts)
	if err != nil {
		return err
	}

	// 查询是否生产需求的采购单
	production := model.NewPmsProductionPurchaseOrder()
	err = yc.DBM(model.NewPmsProductionPurchaseOrder()).Ctx(ctx).WhereIn("purchase_id", inbound.OrderId).Scan(&production)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return err
	}

	actionName := "入库"
	if isRevoke {
		actionName = "撤销入库"
	}

	materialStocks := make([]*model.MaterialStock, 0)
	materialIdSet := gset.New()
	// 遍历入库产品
	for _, product := range products {
		contract := &model.PmsPurchaseContract{}
		for _, c := range contracts {
			if c.ID == product.ContractId && c.MaterialId == product.MaterialId {
				contract = c
				break
			}
		}

		if contract.ID == 0 {
			return gerror.New("订单数据有误，请检查")
		}

		var changeMaterial *model.MaterialStock
		var err2 error
		if isRevoke {
			changeMaterial, err2 = NewPmsPurchaseContractService().InboundOrOutboundByContractId(ctx, contract.ID, product.MaterialId, product.Quantity, false, true)
			// 撤销等于 库存 - 入库数量 （UpdateMaterialStockBatchV2的逻辑是+，所以这里是 -product.Quantity）  10 + -3 = 7
			changeMaterial.Stock = -product.Quantity
			changeMaterial.UsedQuantity = 0
			changeMaterial.ExpectedInbound = 0
			changeMaterial.MaterialId = product.MaterialId
			changeMaterial.OperationType = -s.StatusConvert(inbound.Type)
		} else {
			// 这里更新合同的相关逻辑
			changeMaterial, err2 = NewPmsPurchaseContractService().InboundOrOutboundByContractId(ctx, contract.ID, product.MaterialId, product.Quantity, true, true)
			if err2 != nil {
				return err2
			}
			// 改版后入库单的入库数量，就应该直接+到库存，所以这里还是使用原始输入的product.Quantity入库
			changeMaterial.Stock = product.Quantity
			changeMaterial.UsedQuantity = 0
			changeMaterial.ExpectedInbound = 0
			changeMaterial.MaterialId = product.MaterialId
			changeMaterial.OperationType = s.StatusConvert(inbound.Type)
		}
		changeMaterial.ProductionScheduleId = production.ID
		changeMaterial.PurchaseOrderId = inbound.OrderId
		changeMaterial.PurchaseOrderSn = order.OrderNo
		changeMaterial.InboundId = inbound.ID
		changeMaterial.InboundSn = inbound.Sn
		if err2 != nil {
			return gerror.New(fmt.Sprintf("%s失败, 原因：%s", actionName, err2.Error()))
		}

		if changeMaterial == nil || changeMaterial.MaterialId == 0 {
			return gerror.New("操作失败")
		}

		changeMaterial.Remark = fmt.Sprintf("入库单%s，单号：%s，关联订单：%s 入库，更新库存", actionName, inbound.No, order.OrderNo)
		// 3. 更新库存
		materialStocks = append(materialStocks, changeMaterial)
		materialIdSet.Add(product.MaterialId)
	}

	// 遍历materialIds，将缓存中的数据删除
	for _, materialId := range materialIdSet.Slice() {
		cacheKey := fmt.Sprintf("%s%d", MaterialInboundCacheKeyPrefix, materialId)
		_, err2 := yc.CacheManager.Remove(ctx, cacheKey)
		if err2 != nil {
			return err2
		}
	}

	// 更新库存
	//err = NewPmsMaterialService().UpdateMaterialStocks(ctx, materialStocks)
	//if inbound.DeliveryNoteId <= 0 {
	//	err = NewPmsMaterialService().UpdateMaterialStockBatchV2(ctx, materialStocks)
	//	if err != nil {
	//		return err
	//	}
	//} else {
	//	err = NewPmsMaterialService().UpdateMaterialStockBatchV3(ctx, materialStocks)
	//	if err != nil {
	//		return err
	//	}
	//}
	err = NewPmsMaterialService().UpdateMaterialStockBatchV2(ctx, materialStocks)
	if err != nil {
		return err
	}

	// 检查并处理订单是否已完成
	err = NewPmsPurchaseOrderService().CheckAndSetOrderStatus(ctx, purchaseOrderId)
	if err != nil {
		return err
	}

	return nil
}

// GetAuditData 获取审核数据
func (s *PmsMaterialInboundService) GetAuditData(ctx context.Context, auditId int64) (data interface{}, err error) {
	auditData, err := s.GetAuditDataByAuditId(ctx, auditId)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return nil, err
	}

	if auditData.DataId == 0 {
		return nil, gerror.New("审核数据不存在")
	}

	pmsMaterialInbound := &model.PmsMaterialInbound{}

	err = yc.DBM(s.Model).Ctx(ctx).Where("id", auditData.DataId).Scan(&pmsMaterialInbound)

	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return nil, err
	}

	if pmsMaterialInbound == nil {
		return nil, gerror.New("物料入库单不存在")
	}

	return pmsMaterialInbound, nil
}

func (s *PmsMaterialInboundService) Submit(ctx context.Context, inboundId int64) (data interface{}, err error) {
	if inboundId == 0 {
		return nil, gerror.New("id不能为空")
	}
	// 获取入库单信息
	//获取请求参数
	r := g.RequestFromCtx(ctx)
	materialInbound := model.NewPmsMaterialInbound()
	err = gconv.Scan(r.GetMap(), materialInbound)
	if err != nil {
		return nil, err
	}

	inboundTime := materialInbound.InboundTime
	if inboundTime == nil {
		return nil, gerror.New("入库时间不能为空")
	}
	voucher := materialInbound.Voucher
	if voucher == "" {
		return nil, gerror.New("凭证不能为空")
	}

	inbound := &model.PmsMaterialInbound{}
	err = yc.DBM(s.Model).Ctx(ctx).Where("id", inboundId).
		With(&model.PmsMaterialInboundProduct{}).
		Scan(&inbound)
	if err != nil {
		return
	}

	if inbound == nil {
		return nil, gerror.New("入库单不存在")
	}

	if inbound.Status != int(MaterialInboundStatusDraft) {
		return nil, gerror.New("只有草稿状态的入库单才能提交审批")
	}
	if inbound.Type == int(model.MaterialInboundTypeProductionReturn) && inbound.WorkOrderId == 0 {
		return nil, gerror.New("工单不能为空")
	}

	targetStatus := 0
	// 开启事务
	err = yc.DB(s.Model.GroupName()).Ctx(ctx).Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 触发采购订单的审核流程
		isSkipAudit, err2 := s.AddAudit(ctx, inboundId)
		if err2 != nil {
			return err2
		}
		auditLog := &model.PmsPurchaseOrderAuditLog{
			PurchaseId:   inboundId,
			Source:       2,
			OperatorId:   yc.GetAdmin(ctx).UserId,
			OperatorName: yc.GetAdmin(ctx).UserName,
			Type:         0,
			OperatorNote: "提交物料入库单审批",
		}

		if isSkipAudit {
			result, err := s.AuditPassV2(ctx, materialInbound)
			if err != nil {
				return err
			}
			// result 转map
			resultMap := gconv.Map(&result)
			targetStatus = gconv.Int(resultMap["status"])
			auditLog.OperatorNote = "提交入库单审核, 并自动跳过审核"
		} else {
			auditLog.OperatorNote = "提交入库单审核"
			targetStatus = int(MaterialInboundStatusPendingReview)
			// 更新入库单状态
			_, err = yc.DBM(s.Model).Ctx(ctx).Data(g.Map{"status": targetStatus, "is_submit": 1, "inbound_time": inboundTime, "voucher": voucher}).Where("id", inboundId).Update()
			if err != nil {
				return err
			}
		}
		// 增加审核记录
		NewPmsPurchaseOrderAuditLogService().AddLog(ctx, auditLog)
		return nil
	})
	if err != nil {
		return nil, err
	}
	return g.Map{"status": targetStatus}, nil
}

func (s *PmsMaterialInboundService) AuditPass(ctx context.Context, input *model.PmsMaterialInbound) (data interface{}, err error) {
	// 获取入库单信息
	inboundId := input.ID
	var inbound *model.PmsMaterialInbound
	err = yc.DBM(s.Model).Ctx(ctx).Where("id", inboundId).
		With(&model.PmsMaterialInboundProduct{}).
		Scan(&inbound)
	if err != nil {
		return
	}

	if inbound == nil {
		return nil, gerror.New("入库单不存在")
	}

	err = s.checkInboundData(ctx, inbound.Type, inbound.Products)
	if err != nil {
		return nil, err
	}

	err = s.checkInboundPurchaseOrder(ctx, inbound.OrderId, inbound.Type, inbound.Products)
	if err != nil {
		return nil, err
	}

	targetStatus := MaterialInboundStatusInbound
	// 开启事务
	err = yc.DB(s.Model.GroupName()).Ctx(ctx).Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 更新入库单状态
		updateData := g.MapStrAny{"status": targetStatus}
		if input.Voucher != "" {
			updateData["voucher"] = input.Voucher
		}
		if input.InboundTime != nil {
			updateData["inbound_time"] = input.InboundTime
		}
		_, err = yc.DBM(s.Model).Ctx(ctx).Data(updateData).Where("id", inboundId).Update()
		if err != nil {
			return err
		}
		// 订单入库
		if inbound.Type == int(model.MaterialInboundTypePurchase) {
			// 物料修改的核心逻辑都在这个方法里面
			err = s.materialInboundPurchaseOrRevert(ctx, inbound, inbound.Products, false)
			if err != nil {
				return err
			}
			_, err = yc.DBM(model.NewPmsPurchaseOrder()).Ctx(ctx).Data(g.Map{
				"status": PurchaseOrderStatusInbound,
			}).Where("id", inbound.OrderId).Where("status", PurchaseOrderStatusConfirmed).Update()

			if err != nil {
				return err
			}

			// 将关联的采购需求单设置为已确认
			_, err = yc.DBM(model.NewPmsProductionPurchaseOrder()).Ctx(ctx).
				Data("status", int(ProductionPurchaseOrderStatusInbound)).
				Where("purchase_id", inbound.OrderId).
				Where("status", ProductionPurchaseOrderStatusConfirmed).Update()

			if err != nil {
				return err
			}
		} else if inbound.Type == int(model.MaterialInboundTypeProductionReturn) {
			err = s.materialInboundProductionReturnOrRevoke(ctx, inbound, inbound.Products, false)
			if err != nil {
				return err
			}
		}

		return nil
	})

	if err != nil {
		return
	}

	return g.Map{"status": targetStatus}, nil

}

func (s *PmsMaterialInboundService) AuditPassV2(ctx context.Context, input *model.PmsMaterialInbound) (data interface{}, err error) {
	// 获取入库单信息
	inboundId := input.ID
	var inbound *model.PmsMaterialInbound
	err = yc.DBM(s.Model).Ctx(ctx).Where("id", inboundId).With(&model.PmsMaterialInboundProduct{}).Scan(&inbound)
	if err != nil {
		return
	}

	if inbound == nil {
		return nil, gerror.New("入库单不存在")
	}

	err = s.checkInboundPurchaseOrder(ctx, inbound.OrderId, inbound.Type, inbound.Products)
	if err != nil {
		return nil, err
	}

	// 获取采购信息
	purchaseOrder := model.NewPmsPurchaseOrder()
	yc.DBM(model.NewPmsPurchaseOrder()).Ctx(ctx).WhereIn("id", inbound.OrderId).Scan(&purchaseOrder)

	targetStatus := MaterialInboundStatusCompleted
	// 开启事务
	err = yc.DB(s.Model.GroupName()).Ctx(ctx).Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		if inbound.Type == int(model.MaterialInboundTypePurchase) {
			// 采购订单入库
			err = s.materialInboundPurchaseOrRevert(ctx, inbound, inbound.Products, false)
			if err != nil {
				return err
			}
			_, err = yc.DBM(model.NewPmsPurchaseOrder()).Ctx(ctx).Data(g.Map{"status": PurchaseOrderStatusInbound}).
				Where("id", inbound.OrderId).Where("status", PurchaseOrderStatusConfirmed).Update()

			if err != nil {
				return err
			}

			// 将关联的采购需求单设置为已确认
			_, err = yc.DBM(model.NewPmsProductionPurchaseOrder()).Ctx(ctx).
				Data("status", int(ProductionPurchaseOrderStatusInbound)).
				Where("purchase_id", inbound.OrderId).
				Where("status", ProductionPurchaseOrderStatusConfirmed).Update()

			if err != nil {
				return err
			}
		} else if inbound.Type == int(model.MaterialInboundTypeProductionReturn) { // 生产退料
			err = s.ValidateProductionReturn(ctx, inbound.OrderId, inbound.Products)
			if err != nil {
				return err
			}
			materialStocks := make([]*model.MaterialStock, 0)
			extras := make([]*model.PmsPurchaseOrderWithSummaryExtraOutput, 0)
			// 遍历产品，更新订单已入库数量
			for _, product := range inbound.Products {
				materialStocks = append(materialStocks, &model.MaterialStock{
					ProductionScheduleId: inbound.OrderId,
					MaterialId:           product.MaterialId,
					Stock:                product.Quantity,
					UsedQuantity:         0,
					InboundId:            inbound.ID,
					InboundSn:            inbound.OrderNo,
					OperationType:        s.StatusConvert(inbound.Type),
					Remark:               fmt.Sprintf("入库单号：%s，入库完成，更新库存", inbound.No),
				})
				extra := &model.PmsPurchaseOrderWithSummaryExtraOutput{MaterialId: product.MaterialId}
				extra.OutQty = -product.Quantity // 注意这里是负数
				extras = append(extras, extra)
			}
			// 更新库存
			err = NewPmsMaterialService().UpdateMaterialStockBatchV2(ctx, materialStocks)
			if err != nil {
				return err
			}
			// 更新工单详情
			pmsWorkOrderVo := &model.PmsWorkOrderVo{
				Revoke:       true,
				PmsWorkOrder: &model.PmsWorkOrder{ID: inbound.WorkOrderId},
				Extras:       extras,
			}
			_, err = NewPmsWorkOrderService().UpdateDetailBatch(ctx, pmsWorkOrderVo)
			if err != nil {
				g.Log().Error(ctx, "更新工单失败", err)
				return err
			}
		} else if inbound.Type == int(model.MaterialInboundTypeInventoryReturn) || inbound.Type == int(model.MaterialInboundTypeReturnPolicy) { // 库存退货入库
			_, err2 := s.HandleInventoryReturn(ctx, inbound, false)
			if err2 != nil {
				return err2
			}
		} else {
			materialStocks := make([]*model.MaterialStock, 0)
			// 遍历产品，更新订单已入库数量
			for _, product := range inbound.Products {
				materialStocks = append(materialStocks, &model.MaterialStock{
					MaterialId:    product.MaterialId,
					Stock:         product.Quantity,
					UsedQuantity:  0,
					InboundId:     inbound.ID,
					InboundSn:     inbound.OrderNo,
					OperationType: s.StatusConvert(inbound.Type),
					Remark:        fmt.Sprintf("入库单号：%s，入库完成，更新库存", inbound.No),
				})
			}
			// 更新库存
			err = NewPmsMaterialService().UpdateMaterialStockBatchV2(ctx, materialStocks)
			if err != nil {
				return err
			}
		}
		// 更新入库单状态
		updateData := g.MapStrAny{"status": targetStatus, "complete_time": gtime.Now()}
		if input.Voucher != "" {
			updateData["voucher"] = input.Voucher
		}
		if input.InboundTime != nil {
			updateData["inbound_time"] = input.InboundTime
		}
		_, err = yc.DBM(s.Model).Ctx(ctx).Data(updateData).Where("id", inboundId).Update()
		if err != nil {
			return err
		}
		return nil
	})

	if err != nil {
		return
	}

	return g.Map{"status": targetStatus}, nil

}

// 审批通过的回调
func (s *PmsMaterialInboundService) ProcessAuditResult(ctx context.Context, isEnd bool, isPass bool, audit *model.PmsAudit, auditProcessorNode *model.PmsAuditProcessorNode) (err error) {
	// 获取采购需求单
	if audit == nil {
		return gerror.New("审核记录不存在")
	}

	inbound := &model.PmsMaterialInbound{}

	data, err := s.GetAuditData(ctx, audit.ID)
	if err != nil {
		return err
	}

	err = gconv.Scan(data, inbound)
	if err != nil {
		return err
	}
	// 判断数据是否存在
	if inbound.ID == 0 {
		return gerror.New("物料入库单不存在")
	}

	auditLog := &model.PmsPurchaseOrderAuditLog{
		PurchaseId:   inbound.ID,
		OperatorId:   auditProcessorNode.UserId,
		OperatorName: auditProcessorNode.UserName,
		CreateTime:   gtime.Now(),
		Source:       2,
	}

	// 同意
	if isPass {
		auditLog.Type = int(PurchaseOrderAuditTypePass)
		auditLog.OperatorNote = fmt.Sprintf("物料入库单【%s】审核通过，备注：%s", auditProcessorNode.NodeName, auditProcessorNode.Remark)
	} else { // 驳回
		auditLog.Type = int(PurchaseOrderAuditTypeReject)
		auditLog.OperatorNote = fmt.Sprintf("物料入库单【%s】审核不通过，原因：%s", auditProcessorNode.NodeName, auditProcessorNode.Remark)
		_, err = yc.DBM(s.Model).Ctx(ctx).Data(g.Map{"status": int(MaterialInboundStatusDraft)}).Where("id", inbound.ID).Update()
		if err != nil {
			return err
		}
	}

	if isPass && isEnd {
		inbound.InboundTime = nil
		inbound.Voucher = ""
		_, err := s.AuditPassV2(ctx, inbound)
		if err != nil {
			return err
		}
	}

	NewPmsPurchaseOrderAuditLogService().AddLog(ctx, auditLog)

	return nil
}

func (s *PmsMaterialInboundService) Detail(ctx context.Context, id int64) (data *model.PmsMaterialInbound, err error) {
	if id == 0 {
		return nil, gerror.New("id不能为空")
	}
	result := &model.PmsMaterialInbound{}
	err = yc.DBM(s.Model).Ctx(ctx).Where("id", id).With(&model.PmsPurchaseOrderInboundOutput{}).Scan(&result)
	if err != nil {
		return nil, err
	}
	fields := `product.id,
	product.warehouse_id,
	product.contract_id,
	product.inbound_id,
	product.material_id,
	product.inbound_quantity,
	product.quantity,
	material.code,
	material.model,
	material.size,
	material.material,
	material.cover_color,
	material.unit,
	contract.po,
	contract.delivery_date,
	contract.quantity AS orderQuantity,
	contract.received_quantity,
	contract.subtotal,
	contract.transfer,
	contract.expected_quantity,
	contract.prepares_total,
	contract.payment_term,
	supplier.name AS supplierName,
	material.name AS materialName, product.quantity AS inboundQut
`
	if result != nil && result.ID > 0 {
		materialList := make([]*model.MaterialInboundProductOutput, 0)
		err = yc.DBM(model.NewPmsMaterialInboundProduct()).Ctx(ctx).As("product").
			LeftJoin("pms_material AS material", "product.material_id = material.id").
			LeftJoin("pms_purchase_contract AS contract", "product.contract_id=contract.id").
			LeftJoin("pms_supplier AS supplier", "contract.supplier_id=supplier.id").
			Where("product.inbound_id", id).Fields(fields).Scan(&materialList)
		if err != nil {
			return nil, err
		}
		if result.Type == int(model.MaterialInboundTypeProductionReturn) && len(materialList) > 0 {
			for j, el := range materialList {
				// 查询工单信息详情
				workOrderDetail := &model.PmsWorkOrderDetail{}
				err = yc.DBM(model.NewPmsWorkOrderDetail()).Ctx(ctx).Where("work_order_id", result.WorkOrderId).Where("material_id", el.MaterialId).Scan(&workOrderDetail)
				if err != nil {
					return nil, err
				}
				if workOrderDetail.ID > 0 {
					materialList[j].WorkOrderDetail = workOrderDetail
				}
			}
		}
		result.ProductsOutput = materialList
	}
	return result, nil
}

func (s *PmsMaterialInboundService) QueryPage(ctx context.Context, reqVo *model.QueryVo) (result model.PageResult[*model.PmsMaterialInboundVo], err error) {
	result.List = make([]*model.PmsMaterialInboundVo, 0)
	result.MapData = make(map[int64][]*model.PmsMaterialInboundVo)
	result.Pagination = &model.Pagination{}
	result.Pagination.Page = reqVo.Page
	result.Pagination.Size = reqVo.Size
	queryWrap := `	
		WITH query_data AS 
		(SELECT main.*,t1.deleteTime FROM pms_material_inbound_product AS main LEFT JOIN pms_material_inbound AS t1 ON main.inbound_id = t1.id WHERE t1.deleteTime IS NULL)
		SELECT
			#{Fields}
		FROM
			query_data AS main
			LEFT JOIN pms_material_inbound AS t1 ON main.inbound_id = t1.id 
			LEFT JOIN pms_material AS t2 ON main.material_id = t2.id 
			LEFT JOIN pms_purchase_contract AS t3 ON main.contract_id = t3.id
			LEFT JOIN pms_supplier AS t4 ON t4.id = t3.supplier_id
		WHERE
			t1.deleteTime IS NULL
	`
	if reqVo.Date != "" {
		split := gstr.Split(reqVo.Date, ",")
		queryWrap += fmt.Sprintf(` AND DATE_FORMAT(t1.createTime,'%%Y-%%m-%%d') BETWEEN '%s' AND '%s'`, split[0], split[1])
	}
	reqVo.KeyWord = gstr.Trim(reqVo.KeyWord)
	if reqVo.KeyWord != "" {
		likeCond := fmt.Sprintf("(t1.no LIKE '%%%s%%' OR t2.code LIKE '%%%s%%' OR t2.name LIKE '%%%s%%' OR t4.name LIKE '%%%s%%' OR t3.po LIKE '%%%s%%')",
			reqVo.KeyWord, reqVo.KeyWord, reqVo.KeyWord, reqVo.KeyWord, reqVo.KeyWord)
		queryWrap += fmt.Sprintf(" AND %s", likeCond)
	}
	if reqVo.UseType {
		queryWrap += fmt.Sprintf(" AND t1.type = %d", reqVo.Type)
	}
	if reqVo.Status != 0 {
		status := reqVo.Status
		if reqVo.Status == -1 {
			status = 0
		}
		queryWrap += fmt.Sprintf(" AND t1.status = %d", status)
	}
	if reqVo.SupplierId != 0 {
		queryWrap += fmt.Sprintf(" AND t4.id = %d", reqVo.SupplierId)
	}

	countQuery := gstr.Replace(queryWrap, "#{Fields}", "COUNT(1)")
	count, err := g.DB().Query(ctx, countQuery)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return result, err
	}
	result.Pagination.Total = gconv.Int64(count[0]["COUNT(1)"])
	// 查询列表
	reqVo.CalcOffset()
	queryWrap += fmt.Sprintf(" ORDER BY t1.createTime DESC LIMIT %d,%d", reqVo.Page, reqVo.Size)
	fieid := fmt.Sprintf(`t1.*,main.material_id,t2.level,main.contract_id,main.quantity,%s,%s,t4.name AS SupplierName,t3.payment_status`, utils.MATERIAL_FIEIDS, utils.CONTRACT_FIEIDS)
	queryWrap = gstr.Replace(queryWrap, "#{Fields}", fieid)
	queryResult, err := g.DB().Query(ctx, queryWrap)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return result, err
	}
	err = queryResult.Structs(&result.List)
	if err != nil {
		return result, err
	}
	for _, el := range result.List {
		if result.MapData[el.MaterialId] == nil {
			result.MapData[el.MaterialId] = make([]*model.PmsMaterialInboundVo, 0)
		}
		result.MapData[el.MaterialId] = append(result.MapData[el.MaterialId], el)
	}
	return result, nil
}

func (s *PmsMaterialInboundService) ImportInboundRecord(ctx context.Context, list []*model.PmsMaterialInboundVo, queryVo *model.QueryVo) error {
	if len(list) == 0 {
		return gerror.New("导入数据为空")
	}
	result, err := s.QueryPage(ctx, queryVo)
	if err != nil {
		return err
	}
	typeMap := make(map[int]string)
	typeMap[0] = "自定义入库"
	typeMap[1] = "采购单入库"
	typeMap[2] = "库存调整"
	typeMap[3] = "生产退料"
	sheetName := "Sheet1"
	file := excelize.NewFile()
	// 创建一个工作表
	_, _ = file.NewSheet(sheetName)
	_ = file.SetCellValue(sheetName, "A1", "序号")
	_ = file.SetCellValue(sheetName, "B1", "物料编码")
	_ = file.SetCellValue(sheetName, "C1", "系统采购单号(PO)")
	_ = file.SetCellValue(sheetName, "D1", "系统入库单号")
	_ = file.SetCellValue(sheetName, "E1", "入库类型")
	_ = file.SetCellValue(sheetName, "F1", "创建时间")
	_ = file.SetCellValue(sheetName, "G1", "系统备注")
	_ = file.SetCellValue(sheetName, "H1", "系统入库数量")
	_ = file.SetCellValue(sheetName, "I1", "生产导入入库数量")
	_ = file.SetCellValue(sheetName, "J1", "差异数量")
	_ = file.SetCellValue(sheetName, "K1", "生产导入入库备注")

	// 循环写入数据
	row := 2
	for i, item := range result.List {
		_ = file.SetCellValue(sheetName, fmt.Sprintf("A%d", row), i+1)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("B%d", row), item.Code)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("C%d", row), item.Po)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("D%d", row), item.No)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("E%d", row), typeMap[item.Type])
		_ = file.SetCellValue(sheetName, fmt.Sprintf("F%d", row), item.CreateTime)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("G%d", row), item.Remark)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("H%d", row), item.Quantity)
		for _, el := range list {
			// item.Code == el.Code  && item.Po == el.Po
			if item.Code == el.Code && item.Quantity == el.Quantity {
				_ = file.SetCellValue(sheetName, fmt.Sprintf("I%d", row), el.Quantity)
				_ = file.SetCellValue(sheetName, fmt.Sprintf("J%d", row), item.Quantity-el.Quantity)
				_ = file.SetCellValue(sheetName, fmt.Sprintf("K%d", row), el.Remark)
			}
		}
		row++
	}
	materialList := make([]*model.PmsMaterial, 0)
	//err = yc.DBM(model.NewPmsMaterial()).Ctx(ctx).WhereNotIn("id", fmt.Sprintf(`SELECT material_id FROM pms_material_inbound_product`)).Scan(&materialList)
	//if err != nil {
	//	return err
	//}

	if len(materialList) > 0 {
		sheetName1 := "Sheet2"
		_ = file.SetCellValue(sheetName1, "A1", "序号")
		_ = file.SetCellValue(sheetName1, "B1", "物料编码")
		_ = file.SetCellValue(sheetName1, "C1", "物料名称")
		_ = file.SetCellValue(sheetName1, "D1", "在途")
		_ = file.SetCellValue(sheetName1, "E1", "库存")
		for i, item := range materialList {
			row = i + 2
			_ = file.SetCellValue(sheetName, fmt.Sprintf("A%d", row), i+1)
			_ = file.SetCellValue(sheetName, fmt.Sprintf("B%d", row), item.Code)
			_ = file.SetCellValue(sheetName, fmt.Sprintf("C%d", row), item.Name)
			_ = file.SetCellValue(sheetName, fmt.Sprintf("D%d", row), item.ExpectedInbound)
			_ = file.SetCellValue(sheetName, fmt.Sprintf("E%d", row), item.Inventory)
		}
	}

	// 写出到文件
	fileName := fmt.Sprintf("生产入库数据对比-%s.xlsx", gtime.Now().Format("YmdHis"))
	fileName = url.QueryEscape(fileName)

	// 返回文件流
	buffer, err := file.WriteToBuffer()
	if err != nil {
		return err
	}

	utils.SetResponseToStream(ctx, fileName, buffer.Bytes())
	return nil
}

func (s *PmsMaterialInboundService) ExportExcel(ctx context.Context, queryVo *model.QueryVo) error {
	typeMap := make(map[int]string)
	typeMap[0] = "自定义入库"
	typeMap[1] = "采购单入库"
	typeMap[2] = "库存调整"
	typeMap[3] = "生产退料"
	typeMap[4] = "库存退货入库"

	MaterialInboundStatusMap := make(map[int]string)
	MaterialInboundStatusMap[0] = "草稿"
	MaterialInboundStatusMap[4] = "待审核"
	MaterialInboundStatusMap[3] = "已完成"

	queryVo.Page = 1
	queryVo.Size = 100000
	result, err := s.QueryPage(ctx, queryVo)
	if err != nil {
		return err
	}
	sheetName := "入库明细"
	file := excelize.NewFile()
	_ = file.SetSheetName("Sheet1", sheetName)
	_, _ = file.NewSheet(sheetName)
	columnHead := []string{"入库单号", "物料编码", "物料名称", "规格/型号", "单位", "入库类型", "入库数量", "Po", "采购数量", "供应商", "单据时间", "创建时间", "状态", "备注", "付款状态"}
	err = utils.CreateExcelHead(file, sheetName, columnHead)
	if err != nil {
		return err
	}
	row := 2
	for _, item := range result.List {
		_ = file.SetCellValue(sheetName, fmt.Sprintf("A%d", row), item.No)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("B%d", row), item.Code)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("C%d", row), item.MaterialName)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("D%d", row), item.Model)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("E%d", row), item.Unit)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("F%d", row), typeMap[item.Type])
		_ = file.SetCellValue(sheetName, fmt.Sprintf("G%d", row), item.Quantity)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("H%d", row), item.Po)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("I%d", row), item.PurchaseQuantity)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("J%d", row), item.SupplierName)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("K%d", row), item.InboundTime.Format("Y-m-d"))
		_ = file.SetCellValue(sheetName, fmt.Sprintf("L%d", row), item.CreateTime)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("M%d", row), MaterialInboundStatusMap[item.Status])
		_ = file.SetCellValue(sheetName, fmt.Sprintf("N%d", row), item.Remark)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("O%d", row), item.PaymentStatus)
		row++
	}
	sheetName = "物料汇总(不包含生产退料)"
	file.NewSheet(sheetName)
	columnHead = []string{"物料编码", "物料名称", "规格/型号", "单位", "数量"}
	err = utils.CreateExcelHead(file, sheetName, columnHead)
	if err != nil {
		return err
	}
	row = 2
	for _, v := range result.MapData {
		tmp := &model.PmsMaterialInboundVo{}
		qty := 0.0
		if len(v) > 0 {
			tmp = v[0]
		}
		for _, item := range v {
			// 不包含生产退料
			if item.Type == int(model.MaterialInboundTypeProductionReturn) {
				continue
			}
			qty += item.Quantity
		}
		_ = file.SetCellValue(sheetName, fmt.Sprintf("A%d", row), tmp.Code)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("B%d", row), tmp.MaterialName)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("C%d", row), tmp.Model)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("D%d", row), tmp.Unit)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("E%d", row), qty)
		row++
	}
	fileName := fmt.Sprintf("物料入库-%s.xlsx", gtime.Now().Format("YmdHis"))
	fileName = url.QueryEscape(fileName)
	buffer, err := file.WriteToBuffer()
	if err != nil {
		return err
	}
	utils.SetResponseToStream(ctx, fileName, buffer.Bytes())
	return nil
}

func getMaterialInboundCount(ctx context.Context, m *gdb.Model, whereMap *gmap.StrAnyMap) (res interface{}, err error) {
	request := g.RequestFromCtx(ctx)
	key := request.Get("inbound_outbound_key").Int()
	data := gmap.NewIntIntMap()
	// 遍历whereMap,删除status条件
	for key := range whereMap.Map() {
		// 如果key等于status或者是带表前缀的status
		if key == "status" || gstr.HasSuffix(key, ".status") {
			whereMap.Remove(key)
		}
	}
	// 获取每种状态的订单数量
	// 读取 MaterialInboundStatus 所有枚举值
	statusList := []MaterialInboundStatus{
		MaterialInboundStatusDraft,
		MaterialInboundStatusPending,
		MaterialInboundStatusInbound,
		MaterialInboundStatusCompleted,
		MaterialInboundStatusPendingReview,
	}

	// 获取订单数量
	countModel := m.Clone().Ctx(ctx).Where(whereMap.Map())
	for _, status := range statusList {
		if key > 0 {
			// 判断whereBuilder是否存在status条件
			count, err := countModel.Clone().Where("status", status).
				LeftJoin("pms_material_inbound_product", "pms_material_inbound_product.inbound_id = pms_material_inbound.id").
				Where("pms_material_inbound_product.inbound_outbound_key", key).Count()
			if err != nil {
				return nil, err
			}
			// 向map中添加数据
			data.Set(int(status), count)
		} else {
			// 判断whereBuilder是否存在status条件
			count, err := countModel.Clone().Where("status", status).Count()
			if err != nil {
				return nil, err
			}
			// 向map中添加数据
			data.Set(int(status), count)
		}
	}

	return data, nil
}

// addInboundQueryConditions 添加查询条件
func addMaterialInboundQueryConditions(ctx g.Ctx, builder *gdb.WhereBuilder) *gdb.WhereBuilder {
	request := g.RequestFromCtx(ctx)
	// 判断是否传入dateRange
	dateRange := request.Get("dateRange").Array()
	if len(dateRange) > 0 {
		t1 := gconv.GTime(dateRange[0], "Y-m-d")
		t2 := gconv.GTime(dateRange[1], "Y-m-d")
		if t1.Unix() < t2.Unix() {
			builder = builder.Where("createTime between ? and ?", t1, t2)
		} else if t1.Unix() > t2.Unix() {
			builder = builder.Where("createTime between ? and ?", t2, t1)
		} else if t1.Unix() == t2.Unix() {
			builder = builder.Where("createTime", t1)
		}
	}

	return builder
}

func (s *PmsMaterialInboundService) HandleInventoryReturn(ctx context.Context, inbound *model.PmsMaterialInbound, isRevoke bool) (data interface{}, err error) {
	if len(inbound.Products) == 0 {
		return nil, gerror.New("入库单产品为空")
	}
	// 查询出库单
	outbound := model.NewPmsMaterialOutbound()
	err = yc.DBM(model.NewPmsMaterialOutbound()).Ctx(ctx).Where("id", inbound.OrderId).Scan(&outbound)
	if err != nil {
		return nil, err
	}
	// 判断出库单是否存在
	if outbound.ID == 0 {
		return nil, gerror.New("出库单不存在")
	}
	materialStocks := make([]*model.MaterialStock, 0)
	// 遍历产品，更新订单已入库数量
	for _, product := range inbound.Products {
		item := &model.MaterialStock{
			MaterialId:      product.MaterialId,
			Stock:           product.Quantity,
			ExpectedInbound: -product.Quantity,
			InboundId:       inbound.ID,
			InboundSn:       inbound.OrderNo,
			Remark:          fmt.Sprintf("入库单号：%s，库存退货入库完成，更新库存，更新在途", inbound.No),
		}
		if isRevoke { // 撤销
			item.Stock = -product.Quantity
			item.ExpectedInbound = product.Quantity
			item.Remark = fmt.Sprintf("入库单号：%s，库存退货入库撤销，更新库存，更新在途", inbound.No)
		}
		materialStocks = append(materialStocks, item)
	}

	var purchaseId int64
	if inbound.Type == int(model.MaterialInboundTypeReturnPolicy) {
		//	如果是补退货入库,查询采购订单号
		v, err := yc.DBM(model.NewPmsMaterialOutbound()).Ctx(ctx).Where("id", inbound.OrderId).One()
		if err != nil {
			return nil, err
		}
		purchaseId = gconv.Int64(v["order_id"])
		// 更新合同表以及附加表
		err = UpdateContractAndExtraData(ctx, inbound, purchaseId, isRevoke)
		if err != nil {
			return nil, err
		}
	}

	// 更新补货
	for _, product := range inbound.Products {
		quantity := product.Quantity
		if isRevoke {
			quantity = -product.Quantity
		}

		dbData := model.NewPmsMaterialOutboundProduct()
		err = yc.DBM(dbData).Ctx(ctx).Where("material_id", product.MaterialId).Where("outbound_id", inbound.OrderId).Scan(&dbData)
		if err != nil {
			return nil, err
		}
		restockingQty := dbData.RestockingQty + quantity
		// 判断补货数量是否超过退货数量
		if restockingQty > dbData.Quantity {
			return nil, gerror.New(fmt.Sprintf("更新出库单产品失败, 补货数量%v不能大于退货数量%v", restockingQty, dbData.Quantity))
		}
		// 判断补货数量是否小于0
		if dbData.RestockingQty+quantity < 0 {
			return nil, gerror.New("更新出库单产品失败, 补货数量不能小于0")
		}
		// 更新出库单的已入库数量
		_, err = yc.DBM(model.NewPmsMaterialOutboundProduct()).Ctx(ctx).
			Where("material_id", product.MaterialId).Where("outbound_id", inbound.OrderId).
			Increment("restocking_qty", quantity)
		if err != nil {
			return nil, err
		}
	}
	// 更新库存
	err = NewPmsMaterialService().UpdateMaterialStockBatchV2(ctx, materialStocks)
	if err != nil {
		return nil, err
	}
	return inbound, err
}

// 状态转换
func (s *PmsMaterialInboundService) StatusConvert(status int) int {
	statusMap := g.MapIntInt{
		int(model.MaterialInboundTypeSelfSelect):          model.OperationTypeInboundCustom,   // 自定义入库
		int(model.MaterialInboundTypePurchase):            model.OperationTypeInboundPurchase, // 采购单入库
		int(model.MaterialInboundTypeInventoryAdjustment): model.OperationTypeInboundAdjust,   // 库存调整入库
		int(model.MaterialInboundTypeProductionReturn):    model.OperationTypeInboundReturn,   //生产退料
		int(model.MaterialInboundTypeReturnPolicy):        model.OperationTypeReturnPolicy,    // 补退货入库
	}
	return statusMap[status]
}

func NewPmsMaterialInboundService() *PmsMaterialInboundService {
	return &PmsMaterialInboundService{
		&yc.Service{
			Model: model.NewPmsMaterialInbound(),
			PageQueryOp: &yc.QueryOp{
				FieldEQ:      []string{"status", "type"},
				KeyWordField: []string{"no"},
				AdditionFields: []*yc.AdditionField{
					{
						FieldName: "count",
						FieldFunc: getMaterialInboundCount,
					},
				},
				Where: addMaterialInboundQueryConditions,
				With: []interface{}{
					&model.MaterialInboundProductOutput{},
					&model.PmsMaterialOutput{},
					&model.PmsPurchaseOrderInboundOutput{},
					&model.PmsPurchaseOrderContractInboundOutput{},
					&model.PmsSupplierSecretOutput{},
					&model.PmsProductionOrderPurchaseOutput{},
				},
				ResultModify: func(ctx context.Context, data interface{}) (res interface{}, err error) {
					inboundList := make([]*model.PmsMaterialInbound, 0)
					err = gconv.Structs(data, &inboundList)
					if err != nil {
						return nil, err
					}
					if len(inboundList) > 0 {
						for i, item := range inboundList {
							if item.Type == int(model.MaterialInboundTypeProductionReturn) {
								if len(item.ProductsOutput) > 0 {
									for j, el := range item.ProductsOutput {
										if item.WorkOrderId == 0 {
											continue
										}
										// 查询工单信息详情
										workOrderDetail := &model.PmsWorkOrderDetail{}
										err = yc.DBM(model.NewPmsWorkOrderDetail()).Ctx(ctx).Where("work_order_id", item.WorkOrderId).Where("material_id", el.MaterialId).Scan(&workOrderDetail)
										if err != nil && !errors.Is(err, sql.ErrNoRows) {
											return nil, err
										}
										if workOrderDetail.ID > 0 {
											item.ProductsOutput[j].WorkOrderDetail = workOrderDetail
										}
									}
								}
							}
							if item.Type == int(model.MaterialInboundTypeInventoryReturn) || item.Type == int(model.MaterialInboundTypeReturnPolicy) {
								// 根据orderId 查询出库单号
								if item.OrderId > 0 {
									outbound := model.NewPmsMaterialOutbound()
									err := yc.DBM(outbound).Ctx(ctx).Where("id", item.OrderId).Scan(&outbound)
									if err != nil && !errors.Is(err, sql.ErrNoRows) {
										return nil, err
									}
									inboundList[i].PmsPurchaseOrderInboundOutput.OrderNo = outbound.No
								}
							}
						}
					}
					r := g.RequestFromCtx(ctx)
					inboundOutboundKey := r.Get("inbound_outbound_key").Int()
					if inboundOutboundKey > 0 {
						tmpInbounds := make([]*model.PmsMaterialInbound, 0)
						if inboundOutboundKey > 0 {
							for _, item := range inboundList {
								if len(item.ProductsOutput) > 0 {
									tmp := make([]*model.MaterialInboundProductOutput, 0)
									for _, row := range item.ProductsOutput {
										if row.InboundOutboundKey == inboundOutboundKey {
											tmp = append(tmp, row)
										}
									}
									item.ProductsOutput = tmp
								}
								if len(item.ProductsOutput) > 0 {
									tmpInbounds = append(tmpInbounds, item)
								}
							}
						}
						return tmpInbounds, err
					} else {
						return inboundList, err
					}
				},
			},
			InfoQueryOp: &yc.QueryOp{
				With: []interface{}{
					&model.MaterialInboundProductOutput{},
					&model.PmsMaterialOutput{},
					&model.PmsPurchaseOrderContractInboundOutput{},
					&model.PmsSupplierSecretOutput{},
				},
				ResultModify: func(ctx context.Context, data interface{}) (res interface{}, err error) {
					tmp := &model.PmsMaterialInboundInfo{}
					err = gconv.Struct(data, tmp)
					if err != nil {
						return nil, err
					}
					if tmp.WorkOrderId > 0 {
						WorkOrder := &model.PmsWorkOrder{}
						err = yc.DBM(model.NewPmsWorkOrder()).Ctx(ctx).Where("id", tmp.WorkOrderId).Scan(&WorkOrder)
						if err != nil {
							return nil, err
						}
						if WorkOrder.ProductId > 0 {
							Product := &model.PmsProduct{}
							err = yc.DBM(model.NewPmsProduct()).Ctx(ctx).Where("id", WorkOrder.ProductId).Scan(&Product)
							if err != nil {
								return nil, err
							}
							WorkOrder.ProductName = Product.Name
						}
						tmp.WorkOrder = WorkOrder
						if tmp.WorkOrder != nil {
							// 查询生产单Sn
							productionSchedule := &model.PmsProductionSchedule{}
							err = yc.DBM(model.NewPmsProductionSchedule()).Ctx(ctx).Where("id", tmp.WorkOrder.ProductionScheduleId).Scan(&productionSchedule)
							if err != nil {
								return nil, err
							}
							tmp.WorkOrder.ProductionScheduleSn = productionSchedule.Sn
						}
						workOrderDetailList := make([]*model.PmsWorkOrderDetail, 0)
						err = yc.DBM(model.NewPmsWorkOrderDetail()).Ctx(ctx).Where("work_order_id", tmp.WorkOrderId).OrderDesc("id").Scan(&workOrderDetailList)
						if err != nil {
							return nil, err
						}
						tmp.Extras = workOrderDetailList
						if len(tmp.Extras) > 0 {
							materialMap, err := NewPmsMaterialService().QueryAllMaterial(ctx)
							if err != nil {
								return nil, err
							}
							// 兼容旧版前端
							for i, item := range tmp.Extras {
								// 主要这里的赋值顺序不能变
								tmp.Extras[i].WorkOrderDetailID = item.ID
								tmp.Extras[i].ID = item.MaterialId
								if materialMap.MaterialIdMap[item.MaterialId] != nil {
									el := materialMap.MaterialIdMap[item.MaterialId]
									tmp.Extras[i].Inventory = el.Inventory
									tmp.Extras[i].ExpectedInbound = el.ExpectedInbound
									tmp.Extras[i].Code = el.Code
									tmp.Extras[i].Name = el.Name
									tmp.Extras[i].Model = el.Model
									tmp.Extras[i].Unit = el.Unit
									tmp.Extras[i].Material = el.Material
									tmp.Extras[i].CoverColor = el.CoverColor
									tmp.Extras[i].Size = el.Size
									tmp.Extras[i].AddressName = el.AddressName
								}
							}
						}
						if len(tmp.ProductsOutput) > 0 {
							for i, item := range tmp.ProductsOutput {
								if len(tmp.Extras) > 0 {
									for _, el := range tmp.Extras {
										if el.MaterialId == item.MaterialId {
											tmp.ProductsOutput[i].ID = el.MaterialId
											tmp.ProductsOutput[i].WorkOrderDetailID = el.WorkOrderDetailID
											tmp.ProductsOutput[i].WorkOrderID = el.WorkOrderID
											tmp.ProductsOutput[i].BomQuantity = el.BomQuantity
											tmp.ProductsOutput[i].OutboundQuantity = el.OutboundQuantity
											tmp.ProductsOutput[i].OutQty = el.OutboundQuantity
											//if WorkOrder.Quantity > 0 && el.BomQuantity > 0 {
											//	tmp.ProductsOutput[i].CalcBomQuantity = el.BomQuantity * WorkOrder.Quantity
											//}
										}
									}
								}
							}
						}

					}
					if tmp.OrderId > 0 && tmp.Type == int(model.MaterialInboundTypeInventoryReturn) || tmp.Type == int(model.MaterialInboundTypeReturnPolicy) {
						if len(tmp.ProductsOutput) > 0 {
							for i, item := range tmp.ProductsOutput {
								// 查询出库单号
								outboundProduct := model.NewPmsMaterialOutboundProduct()
								err = yc.DBM(outboundProduct).Ctx(ctx).Where("outbound_id", tmp.OrderId).Where("material_id", item.MaterialId).Scan(&outboundProduct)
								if err != nil {
									return nil, err
								}
								tmp.ProductsOutput[i].RestockingQty = outboundProduct.RestockingQty
								tmp.ProductsOutput[i].WorkOrderID = outboundProduct.ID
								//  注意这里是 orderQuantity 看json
								tmp.ProductsOutput[i].ReturnOrderQuantity = outboundProduct.Quantity
								// F\lookah-erp\admin-web\src\modules\pms\components\inbound-outbound-add.vue <el-select v-model="scope.row.id" 所以这里要赋值
								/*
									 &model.PmsMaterialInboundInfo{} []*MaterialInboundProductOutput
									（*PmsMaterialOutput *PmsPurchaseOrderContractInboundOutput，都会封装id进去）重点
									 结构体使用继承，遇到同名字段就不会封装数据了，大坑，不是后面覆盖前面得
								*/
								tmp.ProductsOutput[i].ID = item.MaterialId
							}
						}
					}
					return tmp, nil
				},
				ScanStruct: &model.PmsMaterialInboundInfo{},
			},
		},
		&AuditService{
			Key: AuditMaterialInboundKey,
		},
	}
}
func init() {
	RegisterAuditService(AuditMaterialInboundKey, NewPmsMaterialInboundService())
}
