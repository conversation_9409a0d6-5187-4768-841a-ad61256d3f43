import{g as _}from"./index-DkYL1aws.js";import{c as f,k as c,l as k,n as w,b as V,E as b,q as i,w as x,f as C,s as E,F as M,m as y,v as B,i as m,o as s}from"./.pnpm-hVqhwuVC.js";const F=f({name:"undefined"}),A=f({...F,props:c({width:{default:"150px"},code:{default:""},options:{default:()=>[]}},{modelValue:{},modelModifiers:{}}),emits:c(["update:modelValue","change"],["update:modelValue"]),setup(r,{emit:p}){const e=r,d=p,g=k(),u=w(r,"modelValue"),a=V([]);if(e!=null&&e.options&&e.options.length>0)a.value=e.options;else{const{dict:o}=_();if(!e.code||e.code.length===0){const t="字典编码不能为空";throw b.error(t),new Error(t)}o.refresh().then(t=>{a.value=t[e.code]})}function h(o){if(o===void 0){d("change",o);return}const l=a.value.find(t=>t.id===o);d("change",l)}return(o,l)=>{const t=m("el-option"),v=m("el-select");return s(),i(v,y({modelValue:u.value,"onUpdate:modelValue":l[0]||(l[0]=n=>u.value=n),clearable:""},B(g),{style:`width:${e.width}`,onChange:h}),{default:x(()=>[(s(!0),C(M,null,E(a.value,n=>(s(),i(t,{key:n.value,label:n.label,value:n.value},null,8,["label","value"]))),128))]),_:1},16,["modelValue","style"])}}});export{A as default};
