<script lang="ts" setup>
import { useCrud, useSearch, useTable, useUpsert } from '@cool-vue-p/crud'
import { ElMessage, ElMessageBox } from 'element-plus'
import moment from 'moment/moment'
import { nextTick, onMounted, ref, watchEffect } from 'vue'
import * as XLSX from 'xlsx'
import { useDict } from '/$/dict'
import { useTableOps } from '/$/pms/hooks/table-ops'
import { service } from '/@/cool'
import { downloadBlob } from '/@/cool/utils'

const orderList = ref<any[]>([])
const orderOption = ref<any[]>([])
const orderSearchList = ref<any[]>([])
const productOptions = ref<any[]>([])
const { dict } = useDict()
const productList = ref<any[]>([])
const isInit = ref(true)
const colorList = dict.get('color')
const workshopList = dict.get('product_floor')
// 获取产品列表
async function getAllProductList() {
  try {
    const res = await service.pms.product.request({
      url: '/getAllProduct',
      method: 'GET',
    })
    productList.value = res?.map((e: any) => {
      return {
        group_id: e.groupId,
        value: e.id,
        sku: e.sku,
        label: `${e.sku} ${e.name}`,
      }
    })
  }
  catch (e: any) {
    console.error(e)
  }
}
getAllProductList()

const Crud = useCrud(
  {
    service: service.pms.DailyProductionReport,
  },
  // (app) => {
  //   app.refresh()
  // },
)
const conDateRange = ref([
  moment().startOf('month').format('YYYY-MM-DD'),
  moment().endOf('month').format('YYYY-MM-DD'),
])
const conKeyWord = ref('')
const conOrderId = ref('')
const conAbnormal = ref(0)
const Search = useSearch({
  items: [
    {
      label: '作业人员',
      prop: 'people_name',
      props: {
        labelWidth: '80px',
      },
      component: {
        name: 'el-input',
        props: {
          style: 'width: 120px',
          placeholder: '请输入作业人员',
          clearable: true,
          onChange(people_name: string) {
            if (isInit.value)
              return
            Crud.value?.refresh({ people_name: people_name.trim(), page: 1 })
          },
        },
      },
    },
    {
      label: 'SKU',
      prop: 'keyWord',
      props: {
        labelWidth: '80px',
      },
      component: {
        name: 'el-input',
        props: {
          clearable: true,
          onChange(keyword: string) {
            if (isInit.value)
              return
            conKeyWord.value = keyword.trim()
            Crud.value?.refresh({
              keyWord: keyword.trim(),
              dateRange: conDateRange.value,
              page: 1,
            })
          },
        },
      },
    },
    {
      label: '生产时间',
      prop: 'dateRange',
      props: {
        labelWidth: '100px',
      },
      component: {
        name: 'el-date-picker',
        props: {
          'type': 'daterange',
          'value-format': 'YYYY-MM-DD',
          'format': 'YYYY-MM-DD',
          'rangeSeparator': '至',
          'startPlaceholder': '开始日期',
          'endPlaceholder': '结束日期',
          'clearable': true,
          onChange(dateRange) {
            if (isInit.value)
              return
            conDateRange.value = dateRange
            Crud.value?.refresh({
              keyWord: conKeyWord.value,
              dateRange,
              page: 1,
            })
          },
        },
      },
    },
    {
      label: '生产车间',
      prop: 'workshop_id',
      props: { labelWidth: '80px' },
      component: {
        name: 'el-select',
        props: {
          style: 'width: 160px',
          clearable: true,
          filterable: true,
          onChange(workshop_id) {
            Crud.value?.refresh({ workshop_id, page: 1 })
          },
        },
        options: workshopList,
      },
    },
    {
      label: '订单号',
      prop: 'orderId',
      props: { labelWidth: '80px' },
      component: {
        name: 'el-select',
        props: {
          style: 'width: 200px',
          clearable: true,
          filterable: true,
          onChange(orderId) {
            if (isInit.value)
              return
            conOrderId.value = orderId
            Crud.value?.refresh({
              keyWord: conKeyWord.value,
              dateRange: conDateRange.value,
              orderId,
              page: 1,
            })
          },
        },
        options: orderSearchList,
      },
    },
    {
      prop: 'abnormal',
      label: '是否只显示异常',
      props: { labelWidth: '120px' },
      component: {
        name: 'el-switch',
        props: {
          clearable: true,
          activeValue: 1,
          inactiveValue: 0,
          onChange(abnormal) {
            if (isInit.value)
              return
            conAbnormal.value = abnormal
            Crud.value?.refresh({ abnormal })
          },
        },
      },
    },
    // {
    //   prop: 'show_unreviewed',
    //   label: '是否只显示未审核',
    //   props: { labelWidth: '150px' },
    //   component: {
    //     name: 'el-switch',
    //     props: {
    //       clearable: true,
    //       activeValue: 1,
    //       inactiveValue: 0,
    //       onChange(show_unreviewed) {
    //         Crud.value?.refresh({ show_unreviewed })
    //       },
    //     },
    //   },
    // },
  ],
})
watchEffect(() => {
  getOrder()
})
nextTick(() => {
  isInit.value = true
  Search.value?.setForm('dateRange', conDateRange.value)
  nextTick(() => {
    isInit.value = false
  })
})

onMounted(() => {
  Crud.value?.refresh({
    dateRange: conDateRange.value,
    page: 1,
  })
})



// 如果colorList不存在value为0的选项，则添加一个
if (!colorList.value?.find(item => item.value === 0))
  colorList.value?.unshift({ label: '无', value: 0 })
// ============ 配置操作按钮权限 ============
const opButtons = ref({
  edit: {
    width: 80,
    permission: service.pms.DailyProductionReport.permission.update,
    show: true,
  },
  delete: {
    width: 80,
    permission: service.pms.DailyProductionReport.permission.delete,
    show: true,
  },
  // 'slot-btn-audit': {
  //   width: 80,
  //   permission: service.pms.DailyProductionReport.permission.audit,
  //   show: true,
  // },
})

const { getOpWidth, getOpIsHidden } = useTableOps(opButtons as any)
const opWidth = ref(getOpWidth())
const opIsHidden = ref(getOpIsHidden())

// ============ 配置操作按钮权限 ============

// 获取生产订单列表
async function getOrder() {
  try {
    const res = await service.pms.production.schedule.request({
      url: '/list',
      method: 'POST',
    })
    orderList.value = res
    orderOption.value = res

    orderSearchList.value = res?.map((e: any) => {
      return {
        value: e.id,
        label: e.sn,
      }
    })
  }
  catch (e: any) {
    console.error(e)
  }
}

const ProductionStagesOptions = [
  {
    label: '临时1（试产）',
    value: 1,
    name: '-',
    nameEn: '-',
    type: 'info',
  },
  {
    label: '临时2（首次量产）',
    value: 2,
    name: '-',
    nameEn: '-',
    type: 'warning',
  },
  {
    label: '正式（量产）',
    value: 3,
    name: '-',
    nameEn: '-',
    type: 'success',
  },
]

const WorkshopSectionOptions = [
  {
    label: '加工段',
    value: 1,
    name: '-',
    nameEn: '-',
    type: 'info',
  },
  {
    label: '组装段',
    value: 2,
    name: '-',
    nameEn: '-',
    type: 'info',
  },
  {
    label: '老化段',
    value: 3,
    name: '-',
    nameEn: '-',
    type: 'info',
  },
  {
    label: '包装段',
    value: 4,
    name: '-',
    nameEn: '-',
    type: 'info',
  },
  {
    label: '加工段一',
    value: 5,
    name: '-',
    nameEn: '-',
    type: 'info',
  },
  {
    label: '加工段二',
    value: 6,
    name: '-',
    nameEn: '-',
    type: 'info',
  },
  {
    label: '加工段三',
    value: 7,
    name: '-',
    nameEn: '-',
    type: 'info',
  },
  {
    label: '芯子配件加工段一',
    value: 8,
    name: '-',
    nameEn: '-',
    type: 'info',
  },
  {
    label: '芯子配件加工段二',
    value: 9,
    name: '-',
    nameEn: '-',
    type: 'info',
  },
  {
    label: '芯子配件组装段一',
    value: 10,
    name: '-',
    nameEn: '-',
    type: 'info',
  },
  {
    label: '芯子配件组装段二',
    value: 11,
    name: '-',
    nameEn: '-',
    type: 'info',
  },
  {
    label: '芯子配件组装段三',
    value: 12,
    name: '-',
    nameEn: '-',
    type: 'info',
  },
  {
    label: '芯子配件组装段四',
    value: 13,
    name: '-',
    nameEn: '-',
    type: 'info',
  },
  {
    label: '芯子配件组装段五',
    value: 14,
    name: '-',
    nameEn: '-',
    type: 'info',
  },
  {
    label: '芯子配件包装段',
    value: 15,
    name: '-',
    nameEn: '-',
    type: 'info',
  },
]

const Upsert = useUpsert({
  props: {
    class: 'report-form',
    labelWidth: '160px',
  },
  items: [
    {
      label: '日期',
      prop: 'produced_date',
      required: true,
      component: {
        name: 'el-date-picker',
        props: {
          'type': 'date',
          'placeholder': '选择日期',
          'value-format': 'YYYY-MM-DD',
          'format': 'YYYY-MM-DD',
          'clearable': true,
          // 不能超过今天
          'disabledDate': (time: any) => {
            return time.getTime() > Date.now()
          },
        },
      },
    },
    {
      label: '生产订单',
      prop: 'order_id',
      required: true,
      component: {
        name: 'slot-order-select',
      },
    },
    {
      label: '机型',
      prop: 'product_id',
      required: true,
      component: {
        name: 'slot-product-select',
      },
    },
    {
      label: '生产车间',
      prop: 'workshop_id',
      required: true,
      component: {
        name: 'slot-workshop-select',
      },
    },
    {
      label: '颜色',
      prop: 'color',
      required: false,
      component: {
        name: 'slot-color-select',
      },
    },
    {
      label: '订单数量',
      prop: 'quantity',
      required: true,
      component: {
        name: 'slot-input-quantity',
      },
    },
    // {
    //   label: '可入库数量(pcs)',
    //   prop: 'available_quantity',
    //   required: true,
    //   component: {
    //     name: 'slot-input-availableQuantity',
    //   },
    // },
    {
      label: '生产阶段',
      prop: 'production_stages',
      required: true,
      component: {
        name: 'el-select',
        props: {
          filterable: true,
        },
        options: ProductionStagesOptions,
      },
    },
    {
      label: '人数',
      prop: 'number_of_people',
      required: true,
      component: {
        name: 'slot-input-number-people',
      },
    },
    {
      label: '工时',
      prop: 'man_hour',
      required: true,
      component: {
        name: 'slot-input-man-hour',
      },
    },
    {
      label: '工段',
      prop: 'workshop_section',
      required: true,
      component: {
        name: 'el-select',
        props: {
          filterable: true,
        },
        options: WorkshopSectionOptions,
      },
    },
    {
      label: '日产量',
      prop: 'daily_output',
      required: true,
      component: {
        name: 'slot-input-daily_output',
      },
    },
    {
      label: '备注',
      prop: 're_mark',
      required: true,
      component: {
        name: 'el-input',
      },
    },
  ],
  async onOpened(data) {
    if (data?.id != undefined) {
      await getProductList(data.order_id)
      getColor(data.product_id)
      getQuantity(data.product_id)
    }
  },
  onSubmit(data, { done, close, next }) {
    const requestData = {
      produced_date: data.produced_date,
      order_id: data.order_id,
      product_id: data.product_id,
      quantity: data.quantity,
      production_stages: data.production_stages,
      number_of_people: data.number_of_people,
      man_hour: data.man_hour,
      workshop_section: data.workshop_section,
      daily_output: data.daily_output,
      workshop_id: data.workshop_id,
      sku: productOptions.value.find(item => item.id === data.product_id)
        ?.sku,
      re_mark: data.re_mark,
      id: data.id,
    }
    next({
      id: data.id,
      requestData,
      done,
    })
    close()
  },
})

const Table = useTable({
  columns: [
    { type: 'selection' },
    {
      label: '生产日期',
      prop: 'produced_date',
      minWidth: 60,
      component: {
        name: 'cl-date-text',
        props: { format: 'YYYY-MM-DD' },
      },
    },
    {
      label: '订单',
      prop: 'sn',
      minWidth: 60,
    },
    {
      label: '订单数量(PCS)',
      prop: 'quantity',
      minWidth: 60,
    },
    {
      label: '剩余生产数量(PCS)',
      prop: 'except_quantity',
      minWidth: 70,
    },
    {
      label: '机型',
      prop: 'sku',
      minWidth: 100,
      formatter(row) {
        return `${row.sku}   ${row.product.name}`
      },
    },
    {
      label: '生产车间',
      prop: 'workshop_id',
      minWidth: 65,
      formatter(row) {
        return (
          workshopList.value.find(
            e => e.id === Number.parseInt(row.workshop_id),
          )?.label || '-'
        )
      },
    },
    {
      label: '线别',
      prop: 'production_line',
      minWidth: 70,
    },
    {
      label: '作业人员',
      prop: 'busywork_group',
      minWidth: 70,
    },
    {
      label: '规格型号(长宽高)',
      prop: '',
      minWidth: 80,
      formatter(row) {
        return `${row.product.length} * ${row.product.width} * ${row.product.height}cm`
      },
    },
    {
      label: '工段',
      prop: 'workshop_section',
      minWidth: 40,
      formatter: (row: any) => {
        return (
          WorkshopSectionOptions.find(
            e => e.value === Number.parseInt(row.workshop_section),
          )?.label || '-'
        )
      },
    },
    {
      label: '颜色',
      prop: 'product.color',
      minWidth: 80,
      formatter: (row: any) => {
        return (
          colorList.value?.find(
            e => e.value === Number.parseInt(row.product.color),
          )?.label || '-'
        )
      },
    },
    {
      label: '生产阶段',
      prop: 'production_stages',
      minWidth: 70,
      formatter: (row: any) => {
        return (
          ProductionStagesOptions.find(
            e => e.value === Number.parseInt(row.production_stages),
          )?.label || '-'
        )
      },
    },
    {
      label: '生产标准',
      align: 'center',
      children: [
        {
          label: '标准人均产能(PCS/H)',
          prop: 'standard_capacity',
          minWidth: 70,
          formatter: (row: any) => {
            return row.standard_capacity === 0
              ? '-'
              : row.standard_capacity.toFixed(2)
          },
        },
        {
          label: '标准累计工时(H)',
          // prop: 'number_of_people',
          minWidth: 70,
          formatter: (row: any) => {
            const sum
              = row.standard_capacity === 0
                ? -9999
                : row.daily_output / row.standard_capacity
            return sum === -9999 ? '-' : sum.toFixed(2)
          },
        },
      ],
    },
    {
      label: '实际生产数据',
      align: 'center',
      children: [
        {
          label: '人数(人)',
          prop: 'number_of_people',
          minWidth: 50,
        },
        {
          label: '人均工时(H)',
          prop: 'average_working_hours',
          minWidth: 60,
          formatter(row) {
            return row.average_working_hours.toFixed(2)
          },
        },
        {
          label: '累计工时(H)',
          prop: 'man_hour',
          minWidth: 60,
          formatter(row) {
            return row.man_hour.toFixed(2)
          },
        },
        {
          label: '人均产能(pcs/h)',
          prop: 'average_capacity',
          minWidth: 60,
          formatter(row) {
            return row.average_capacity.toFixed(2)
          },
        },
        {
          label: '当日产能(pcs/h)',
          prop: 'daily_output',
          minWidth: 60,
          formatter(row) {
            return row.daily_output.toFixed(2)
          },
        },
      ],
    },
    {
      label: '制造成本(H)',
      align: 'center',
      children: [
        {
          label: '单机成本',
          align: 'center',
          children: [
            {
              label: '标准',
              prop: 'standard_unit_cost',
              minWidth: 40,
              formatter(row) {
                return row.standard_unit_cost > 0
                  ? row.standard_unit_cost.toFixed(2)
                  : '-'
              },
            },
            {
              label: '实际',
              prop: 'actual_unit_cost',
              minWidth: 40,
              formatter(row) {
                return row.actual_unit_cost > 0
                  ? row.actual_unit_cost.toFixed(2)
                  : '-'
              },
            },
          ],
        },
        {
          label: '成本合计',
          align: 'center',
          children: [
            {
              label: '标准',
              prop: 'total_standard_cost',
              minWidth: 40,
              formatter(row) {
                return row.total_standard_cost > 0
                  ? row.total_standard_cost.toFixed(2)
                  : '-'
              },
            },
            {
              label: '实际',
              prop: 'total_actual_cost',
              minWidth: 40,
              formatter(row) {
                return row.total_actual_cost > 0
                  ? row.total_actual_cost.toFixed(2)
                  : '-'
              },
            },
          ],
        },
        {
          label: '成本差异',
          prop: 'cost_difference',
          minWidth: 60,
          formatter(row) {
            return row.total_standard_cost > 0
              ? row.cost_difference.toFixed(2)
              : '-'
          },
        },
      ],
    },
    {
      label: '异常工时(H)',
      prop: 'abnormal_working_hours',
      minWidth: 60,
      formatter(row) {
        return row.abnormal_working_hours > 0
          ? row.abnormal_working_hours.toFixed(2)
          : '0'
      },
    },
    {
      label: '工时差异(标准-(实际-异常)，H)',
      prop: 'time_variance',
      minWidth: 80,
      formatter(row) {
        return row.time_variance === 0.0 ? '0' : row.time_variance.toFixed(2)
      },
    },
    {
      label: '备注',
      prop: 're_mark',
      minWidth: 60,
    },
    {
      label: '产能描述',
      prop: 'description',
      minWidth: 100,
      showOverflowTooltip: true,
    },
    {
      type: 'op',
      label: '操作',
      width: opWidth as any,
      hidden: opIsHidden,
      buttons: Object.keys(opButtons.value) as any,
    },
  ],
})

// 过滤数组
async function queryOrderBySN(query: string) {
  query = query.trim()
  if (!query) {
    orderOption.value = orderList.value
  }
  orderOption.value = orderList.value.filter(item =>
    item.sn.toLowerCase().includes(query.toLowerCase()),
  )
}

// 判断行是否需要标红
function getRowClassName({ row }: { row: any }) {
  // 当time_variance大于等于-9999时，行显示为红色
  if (row.time_variance !== undefined && row.time_variance <= -9999) {
    return 'variance-warning-row'
  }
  return ''
}

// 根据生产订单获取产品列表
async function getProductList(id: any) {
  try {
    productOptions.value = await service.pms.production.schedule.request({
      url: '/getProductListByOrderId',
      method: 'POST',
      data: { order_id: id },
    })
  }
  catch (e: any) {
    console.error(e)
  }
}

/**
 * 限定输入内容为整数或2位小数
 * @param value 输入内容
 */
function formatNumber(value: string) {
  const reg = /^\d+(?:\.\d{0,2})?$/
  if (!reg.test(value))
    return value.slice(0, -1)
  return value
}

/**
 * 限定输入内容为整数
 */
function formatNumberInt(value: string) {
  const reg = /^\d+$/
  if (!reg.test(value))
    return value.slice(0, -1)
  return value
}
// 导出数据
function handleExport() {
  const people_name = Search.value?.getForm('people_name')
  const workshop_id = Search.value?.getForm('workshop_id')
  let exportParams = {}

  if (conDateRange.value && conDateRange.value.length === 2) {
    const [a, b] = conDateRange.value
    const start = moment(a).startOf('month').format('YYYY-MM-DD')
    const end = moment(b).endOf('month').format('YYYY-MM-DD')
    exportParams = {
      sku: conKeyWord.value,
      orderId: conOrderId.value,
      abnormal: conAbnormal.value,
      start,
      end,
      people_name,
      workshop_id,
    }
  }
  else {
    exportParams = {
      sku: conKeyWord.value,
      orderId: conOrderId.value,
      abnormal: conAbnormal.value,
      people_name,
      workshop_id,
    }
  }

  const params = {
    url: '/export',
    method: 'GET',
    responseType: 'blob',
    params: exportParams,
  }

  service.pms.DailyProductionReport.request(params)
    .then((res: any) => {
      if (downloadBlob(res))
        ElMessage.success('导出成功')
      Crud.value?.refresh()
    })
    .catch((err: any) => {
      ElMessage.error(err.message || '导出失败')
    })
}

// 获取产品颜色
function getColor(id: number) {
  const cur_product = productOptions.value.find((item: any) => item.id === id)
  const color_label
    = colorList.value.find(item => item.id === cur_product?.color)?.label
    || '-'
  Upsert.value?.setForm('color', color_label)
}

const maxQuantity = ref(0)

// 获取订单产品数量
function getQuantity(id: number) {
  const order_id = Upsert.value?.getForm('order_id')
  service.pms.production.schedule
    .request({
      url: '/getOrderProductQuantity',
      method: 'POST',
      data: { order_id, product_id: id },
    })
    .then((res: any) => {
      Upsert.value?.setForm('quantity', res.quantity)
      // Upsert.value?.setForm('available_quantity', res.available_quantity)
      maxQuantity.value = res.quantity
    })
    .catch((err: any) => {
      // 提示错误消息
      ElMessage.error(err.message || '查询失败')
    })
}

// 导入文件
const fileInputRef = ref<HTMLInputElement | null>(null)
const isLoading = ref(false)
// 处理文件输入框的change事件
async function handleFileInputChange(event: Event) {
  const fileInput = event.target as HTMLInputElement
  const files = fileInput.files
  const WorkshopSectionMap: any = {}
  if (WorkshopSectionOptions && WorkshopSectionOptions.length > 0) {
    WorkshopSectionOptions.forEach((item: any) => {
      WorkshopSectionMap[item.label] = item.value
    })
  }
  const ProductionStagesOptionsMap: any = {}
  if (ProductionStagesOptions && ProductionStagesOptions.length > 0) {
    ProductionStagesOptions.forEach((item: any) => {
      ProductionStagesOptionsMap[item.label] = item.value
    })
  }

  const workshopListMap: any = {}
  if (workshopList.value && workshopList.value.length > 0) {
    workshopList.value.forEach((item: any) => {
      workshopListMap[item.label] = item.id
    })
  }
  if (files && files.length > 0) {
    isLoading.value = true
    const file = files[0]
    const reader = new FileReader()

    reader.onload = (e: ProgressEvent<FileReader>) => {
      const data = new Uint8Array(e.target?.result as ArrayBuffer)
      const workbook = XLSX.read(data, { type: 'array' })

      // 这里可以根据需要处理读取到的Excel数据
      const worksheet = workbook.Sheets[workbook.SheetNames[0]]
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })
      // 定义非法值
      const illegalValue = [undefined, null, '', 'undefined', 'null', 'NaN']
      // 定义列
      const columns: string[] = [
        '',
        'produced_date',
        'order_id',
        'name',
        'workshop_id',
        'workshop_section',
        'sku',
        'production_stages',
        'number_of_people',
        'man_hour',
        'daily_output',
        're_mark',
      ]
      const result: any = []
      if (jsonData && jsonData.length > 0) {
        for (let i = 5; i < jsonData.length; i++) {
          const row = jsonData[i] as string[]
          const cell: any = {}
          for (let j = 1; j < row.length; j++) {
            const columnName = columns[j]
            if (typeof row[j] === 'string') {
              row[j] = row[j].trim()
            }
            cell[columnName] = row[j]
            if (columnName === 'produced_date' && cell.produced_date) {
              cell.produced_date = moment(cell.produced_date).format('YYYY-MM-DD')
            }

            if (columnName === 'workshop_section') {
              cell.workshop_section = WorkshopSectionMap[cell.workshop_section]
                ? WorkshopSectionMap[cell.workshop_section]
                : 0
            }
            if (columnName === 'workshop_id') {
              cell.workshop_id = workshopListMap[cell.workshop_id]
                ? workshopListMap[cell.workshop_id]
                : 0
            }
            if (columnName === 'production_stages') {
              cell.production_stages = ProductionStagesOptionsMap[
                cell.production_stages
              ]
                ? ProductionStagesOptionsMap[cell.production_stages]
                : 0
            }
            if (columnName === 'order_id') {
              cell.order_id = orderList.value.find(item =>
                item.sn.toLowerCase().includes(cell.order_id.toLowerCase()),
              )?.id
              if (cell.order_id === undefined || cell.order_id === 0) {
                continue
              }
            }
            cell.number_of_people = Number.parseInt(cell.number_of_people)
            cell.man_hour = Number.parseFloat(cell.man_hour)
            cell.daily_output = Number.parseInt(cell.daily_output)
            cell.quantity = Number.parseInt(cell.quantity)
          }
          cell.product_id = productList.value.find(
            item => item.sku === cell.sku,
          )?.value
          if (
            illegalValue.includes(cell.daily_output)
            || Number.isNaN(cell.daily_output)
          ) {
            clearInput(fileInput)
            isLoading.value = false
            break
          }
          if (
            illegalValue.includes(cell.man_hour)
            || Number.isNaN(cell.man_hour)
            || cell.man_hour === 0
          ) {
            clearInput(fileInput)
            isLoading.value = false
            break
          }
          if (
            illegalValue.includes(cell.number_of_people)
            || Number.isNaN(cell.number_of_people)
            || cell.number_of_people === 0
          ) {
            clearInput(fileInput)
            isLoading.value = false
            break
          }
          if (illegalValue.includes(cell.produced_date)) {
            clearInput(fileInput)
            isLoading.value = false
            break
          }
          if (
            illegalValue.includes(cell.workshop_section)
            || Number.isNaN(cell.workshop_section)
            || cell.workshop_section === 0
          ) {
            clearInput(fileInput)
            isLoading.value = false
            break
          }

          if (
            illegalValue.includes(cell.workshop_id)
            || Number.isNaN(cell.workshop_id)
            || cell.workshop_id === 0
          ) {
            clearInput(fileInput)
            isLoading.value = false
            break
          }

          if (
            illegalValue.includes(cell.production_stages)
            || Number.isNaN(cell.production_stages)
            || cell.production_stages === 0
          ) {
            clearInput(fileInput)
            isLoading.value = false
            break
          }
          if (illegalValue.includes(cell.sku) || cell.sku === '') {
            clearInput(fileInput)
            isLoading.value = false
            break
          }
          result.push(cell)
        }
        if (result.length > 0) {
          service.pms.DailyProductionReport.importDailyProductionData({
            dailyProductionData: result,
          })
            .then(() => {
              Crud.value?.refresh()
              ElMessage.success(`导入成功`)
            })
            .catch((e: any) => {
              ElMessage.error(e.message || '导入失败')
            })
            .finally(() => {
              isLoading.value = false
            })
        }
        else {
          isLoading.value = false
          ElMessage.error('导入有效数据为空')
        }
        clearInput(fileInput)
      }
    }
    reader.readAsArrayBuffer(file)
  }
  else {
    isLoading.value = false
    ElMessage.error('请选择文件')
  }
}

function clearInput(fileInput: { value: string }) {
  // 清空文件输入的值
  if (fileInput)
    fileInput.value = ''
}

function downloadExcelTemplate() {
  const fileName = '生产日报表_模板.xlsx'
  const filePath = '/daily_production.xlsx'

  // 发起下载请求
  fetch(filePath)
    .then(response => response.blob())
    .then((blob) => {
      // 保存文件
      downloadBlob(blob, fileName)
    })
    .catch(() => {
      ElMessage.error({
        message: '下载模板文件失败',
      })
    })
}

function openFileInput() {
  const fileInput = fileInputRef.value
  if (fileInput)
    fileInput.click()
}

// 处理审核操作
function handleAudit(row: any) {
  ElMessageBox.confirm('确定要通过该生产日报记录吗？', '审核确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    // 显示加载状态
    const loading = ElMessage({
      message: '正在提交审核...',
      duration: 0,
      type: 'info',
    })

    // 发送审核请求
    service.pms.DailyProductionReport.request({
      url: '/audit',
      method: 'POST',
      data: { id: row.id },
    }).then(() => {
      ElMessage.success('审核成功')
      // 刷新表格数据
      Crud.value?.refresh()
    }).catch((err: any) => {
      ElMessage.error(err.message || '审核失败')
    }).finally(() => {
      // 关闭加载提示
      loading.close()
    })
  }).catch(() => {
    // 用户取消操作，不做任何处理
  })
}
</script>

<template>
  <cl-crud ref="Crud">
    <cl-row>
      <cl-refresh-btn />
      <cl-add-btn />
      <input
        ref="fileInputRef"
        type="file"
        style="display: none"
        accept=".xlsx, .xls"
        @change="handleFileInputChange"
      >
      <el-button
        v-permission="
          service.pms.DailyProductionReport.permission.importDailyProductionData
        "
        size="default"
        :loading="isLoading"
        type="warning"
        class="mb-10px mr-10px"
        ml="20px"
        @click="openFileInput"
      >
        Excel导入
      </el-button>
      <!-- 下载excel模板 -->
      <el-button
        v-permission="
          service.pms.DailyProductionReport.permission.importDailyProductionData
        "
        type="info"
        class="mb-10px mr-10px"
        size="default"
        @click="downloadExcelTemplate"
      >
        下载Excel模板
      </el-button>
      <el-button type="success" @click="handleExport">
        导出
      </el-button>
      <!-- 删除按钮 -->
      <cl-multi-delete-btn
        v-permission="service.pms.DailyProductionReport.permission.delete"
      />
      <cl-flex1 />

      <cl-search ref="Search" />
    </cl-row>
    <cl-row>
      <cl-table
        ref="Table"
        :auto-height="false"
        :row-class-name="getRowClassName"
      >
        <!--        <template #slot-btn-audit="{ scope }"> -->
        <!--          <el-button -->
        <!--            :type="scope.row.is_audit === 1 ? 'primary' : 'warning'" -->
        <!--            :disabled="scope.row.is_audit === 1" -->
        <!--            @click="handleAudit(scope.row)" -->
        <!--          > -->
        <!--            {{ scope.row.is_audit === 1 ? "已审核" : "审核" }} -->
        <!--          </el-button> -->
        <!--        </template> -->
      </cl-table>
    </cl-row>
    <el-row>
      <cl-flex1 />
      <!-- 分页控件 -->
      <cl-pagination />
    </el-row>
    <cl-upsert ref="Upsert">
      <template #slot-order-select="{ scope }">
        <el-select
          v-model="scope.order_id"
          filterable
          remote
          placeholder="选择生产订单"
          :remote-method="queryOrderBySN"
          @change="
            (val: number) => {
              getProductList(val);
              scope.product_id = undefined;
              scope.quantity = undefined;
            }
          "
        >
          <el-option
            v-for="item in orderOption"
            :key="item.id"
            :label="item.sn"
            :value="item.id"
          />
        </el-select>
      </template>

      <template #slot-product-select="{ scope }">
        <el-select
          v-model="scope.product_id"
          filterable
          remote
          placeholder="选择产品"
          @change="
            (val: number) => {
              getColor(val);
              getQuantity(val);
            }
          "
        >
          <el-option
            v-for="item in productOptions"
            :key="item.id"
            :label="`${item.sku} ${item.name}`"
            :value="item.id"
          />
        </el-select>
      </template>
      <template #slot-workshop-select="{ scope }">
        <el-select v-model="scope.workshop_id" placeholder="选择生产车间">
          <el-option key="0" label="请选择车间" :value="0" />
          <el-option
            v-for="item in workshopList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </template>
      <template #slot-color-select="{ scope }">
        <el-input
          v-model="scope.color"
          placeholder="产品颜色"
          :disabled="true"
        />
      </template>
      <template #slot-input-quantity="{ scope }">
        <el-input
          v-model="scope.quantity"
          type="number"
          placeholder="请输入产品数量"
          :formatter="formatNumberInt"
          step="1"
          disabled
        />
      </template>

      <!--      <template #slot-input-availableQuantity="{ scope }"> -->
      <!--        <el-input -->
      <!--          v-model="scope.available_quantity" -->
      <!--          type="number" -->
      <!--          placeholder="请输入产品数量" -->
      <!--          :formatter="formatNumberInt" -->
      <!--          step="1" -->
      <!--          disabled -->
      <!--        /> -->
      <!--      </template> -->
      <template #slot-input-number-people="{ scope }">
        <el-input
          v-model="scope.number_of_people"
          type="number"
          placeholder="请输入人数"
          :formatter="formatNumberInt"
          step="1"
        />
      </template>
      <template #slot-input-man-hour="{ scope }">
        <el-input
          v-model="scope.man_hour"
          type="number"
          placeholder="请输入工时"
          :formatter="formatNumber"
          :step="0.01"
        />
      </template>
      <template #slot-input-daily_output="{ scope }">
        <el-input-number
          v-model="scope.daily_output"
          type="number"
          :max="maxQuantity"
          placeholder="请输入日产量"
          :formatter="formatNumber"
          :step="0.01"
        />
      </template>
    </cl-upsert>
  </cl-crud>
</template>

<style lang="scss">
.production-schedule-collapse {
  :deep(.el-collapse-item__content) {
    .el-table {
      --el-table-tr-bg-color: #f5f7fa;
      --el-table-header-bg-color: #e4e7ed;

      .el-table__header th {
        background-color: var(--el-table-header-bg-color) !important;
      }

      .el-table__row {
        background-color: var(--el-table-tr-bg-color) !important;

        &:nth-child(even) {
          background-color: #ebeef5 !important;
        }
      }
    }
  }
}

/* 工时差异警告行样式 */
.variance-warning-row {
  background-color: #fef0f0 !important;
  color: #f56c6c;
}

.report-form {
  .cl-form__items {
    .el-row {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      .full-line {
        grid-column: 1 / -1;
      }
    }

    // 让input-number的提示语靠左显示
    .el-input-number {
      :deep(.el-input__wrapper) {
        padding-left: 11px;
        input {
          text-align: left;
        }
      }
    }
  }
}

.sku-select-error,
.quantity-input-error {
  :deep {
    .el-input__wrapper {
      border: 1px solid var(--el-color-danger);
      &.is-focus {
        box-shadow: none;
      }
    }
  }
}

.cell .holiday {
  position: absolute;
  width: 6px;
  height: 6px;
  background: var(--el-color-danger);
  border-radius: 50%;
  bottom: 0px;
  left: 50%;
  transform: translateX(-50%);
}
</style>
