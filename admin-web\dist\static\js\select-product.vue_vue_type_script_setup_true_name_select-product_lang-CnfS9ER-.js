import{s as v}from"./index-DkYL1aws.js";import{c as i,k as c,l as h,n as V,e as _,b,E as k,q as x,i as M,m as $,v as d,x as w,o as y}from"./.pnpm-hVqhwuVC.js";import{g as C}from"./index-BFVs8cCE.js";const E=i({name:"select-product"}),D=i({...E,props:c({width:{default:"150px"},options:{default:()=>[]}},{modelValue:{},modelModifiers:{}}),emits:c(["update:modelValue","change"],["update:modelValue"]),setup(r,{emit:p}){const t=r,s=p,m=h(),n=V(r,"modelValue"),u=_({get:()=>{if(![0,"0"].includes(n.value))return n.value},set:o=>{n.value=o,s("update:modelValue",o)}}),a=b([]);t.options.length===0?v.pms.productionData.processAbnormality.getProductList().then(o=>{o.forEach(e=>{const l=C("color",e.color);e.label=`${e.name}, (SKU:${e.sku})，(颜色:${l})`,e._label=e.name,e.value=e.id}),a.value=o}).catch(o=>{k.error(o.message||"获取用户列表失败")}):a.value=t.options;function f(o){if(o===void 0){s("change",o,a.value);return}const e=a.value.find(l=>l.id===o);s("change",e,a.value)}return(o,e)=>{const l=M("el-select-v2");return y(),x(l,$({modelValue:d(u),"onUpdate:modelValue":e[0]||(e[0]=g=>w(u)?u.value=g:null),filterable:""},d(m),{clearable:"",style:`width:${t.width}`,options:a.value,onChange:f}),null,16,["modelValue","style","options"])}}});export{D as _};
