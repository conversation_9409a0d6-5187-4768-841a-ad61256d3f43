import{d as l}from"./index-BtOcqcNl.js";import"./.pnpm-hVqhwuVC.js";function h(e){function s(){let u=0;for(const r in e.value){const{width:n=0,permission:t,show:i=!1}=e.value[r]??{};i&&t&&l(t)&&(u+=n)}return u}function c(u){return Object.keys(e.value).some(r=>{const{permission:n,show:t=!1}=e.value[r]??{};return u===r&&t&&n&&l(n)})}function f(){return!e.value||s()===0?!0:Object.keys(e.value).every(u=>{const{show:r=!1}=e.value[u]??{};return!r})}return{getOpWidth:s,checkOpButtonIsAvaliable:c,getOpIsHidden:f}}export{h as u};
