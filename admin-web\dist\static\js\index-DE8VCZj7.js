import{c as f,av as p,e as d,au as _,$ as m,aw as g,i as o,f as s,o as l,s as k,h as i,j as h,w as y,t as S,F as L}from"./.pnpm-hVqhwuVC.js";import{_ as $}from"./_plugin-vue_export-helper-DlAUqK2U.js";const x=f({name:"ClLink",components:{IconLink:p},props:{modelValue:[String,Array],href:[String,Array],text:{type:String,default:"查看"},target:{type:String,default:"_blank"}},setup(t){const r=d(()=>{const e=t.modelValue||t.href;return _(e)?e:m(e)?(e||"").split(",").filter(Boolean):[]});function a(e){return g(e.split("/"))}return{urls:r,filename:a}}}),C=["href","target"];function I(t,r,a,e,V,v){const c=o("IconLink"),u=o("el-icon");return l(!0),s(L,null,k(t.urls,n=>(l(),s("a",{key:n,class:"cl-link",href:n,target:t.target},[i(u,null,{default:y(()=>[i(c)]),_:1}),h(S(t.filename(n)),1)],8,C))),128)}const B=$(x,[["render",I],["__scopeId","data-v-175401c8"]]);export{B as default};
