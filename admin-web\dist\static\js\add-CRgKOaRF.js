import{g as me,j as <PERSON>}from"./index-DkYL1aws.js";import{a as pe}from"./index-C6cm1h61.js";import{_ as ve,a as fe}from"./product-excel-import.vue_vue_type_script_setup_true_lang-CaiF2Sjy.js";import{c as j,b as i,A as _e,f as k,y as _,h as u,w as r,q as D,B as z,i as p,j as f,F as $,s as N,t as E,W as be,E as I,o as c}from"./.pnpm-hVqhwuVC.js";const ke={class:"cl-crud order-create"},he=_("div",{class:"order-create-header"}," 基础信息 ",-1),ye={class:"order-create-body"},ge=_("div",{class:"order-create-header"}," 产品信息 ",-1),we={flex:"~ row items-center",mb2:""},Ve={key:0,style:{display:"flex","align-items":"center"}},Se={key:1,style:{display:"flex","align-items":"center"}},qe={key:0,style:{display:"flex","align-items":"center"}},Ie={key:0},Ce=_("span",{style:{color:"var(--el-color-danger)"}},"*",-1),xe={style:{display:"flex","align-items":"center"}},Pe={flex:"~ justify-end items-center",mt5:""},De={class:"dialog-footer"},Ee=j({name:"undefined"}),Be=j({...Ee,setup(Ue){const{router:M,service:w}=pe(),{dict:W}=me(),A=i(!1),n=i({id:0,customerCode:"",orderSn:"",requiredShipDate:"",products:[],port:"",exchangeRate:"",isInvoice:0,isDirect:0,receiptNumber:""}),K=[{label:"单品",value:0},{label:"内盒/展示盒",value:1},{label:"箱装",value:2}],V=i(!1),F=i(!1),O=i(),C=i(!1),d=i([]),U=i([]),x=W.get("color"),S=i([]),G=i(!1),R=i([]),T=i([]),L=i([]),Y=i();async function H(){var o;await((o=w.pms)==null?void 0:o.warehouse.point.list().then(l=>{R.value=l.map(e=>({value:e.id,label:`${e.name}-${e.address}`,dir:e.type})).filter(e=>e.dir===0),T.value=l.map(e=>({value:e.id,label:`${e.name}-${e.address}`,dir:e.type})).filter(e=>e.dir===1)}))}async function J(){var o;await((o=w.pms)==null?void 0:o.freight.forwarder.list().then(l=>{L.value=l.map(e=>({value:e.id,label:e.name}))}))}async function Q(){O.value&&await O.value.validate(o=>{if(o){if(F.value=!0,d.value.length===0){I.error({message:"请添加产品"});return}if(d.value=d.value.filter(e=>e.id!==null&&e.id!==void 0&&e.id!==0),d.value.find(e=>e.quantity===null||e.quantity===void 0||e.quantity===0)){I.error({message:"请填写正确的产品数量"});return}n.value.products=d.value.map(e=>({productId:e.id,quantity:e.quantity,csCode:e.csCode})),V.value=!0,n.value.id&&n.value.id>0?w.pms.sale.order.update(n.value).then(()=>{const e=n.value.id;M.push(`/pms/sale/order?tab=0&expand=${e}`),I.success({message:"保存成功",onClose:()=>{V.value=!1}})}).catch(e=>{I.error({message:e.message}),V.value=!1}):(delete n.value.id,w.pms.sale.order.add(n.value).then(e=>{const a=e==null?void 0:e.id;M.push(`/pms/sale/order?tab=0&expand=${a}`),I.success({message:"保存成功",onClose:()=>{V.value=!1}})}).catch(e=>{I.error({message:e.message}),V.value=!1}))}})}function X(o){if(o)return w.pms.product.list({keyWord:o}).then(l=>{S.value=(l==null?void 0:l.map(e=>{var s,h;return{value:e.id,label:`${e.sku}`,name:`${e.name}`,nameEn:`${e.nameEn}`,color:e.color,disabled:d.value.some(b=>b.id===e.id),unit:e.unit,unitProductSku:`${e.unitProductSku}`,stock:((s=e.stock)==null?void 0:s.inStock)-((h=e.stock)==null?void 0:h.freezeStock)||0}}))||[]})}function Z(){var o;U.value=[],C.value=!0,(o=Y.value)==null||o.resetData()}function ee(o){const l=S.value.find(a=>a.value===o);if(!l)return;const e=d.value.findIndex(a=>a.id===o);e!==-1?(d.value[e].sku=l.label,d.value[e].name=l.name,d.value[e].nameEn=l.nameEn,d.value[e].color=P(x.value,Number.parseInt(l.color)),d.value[e].unit=l.unit,d.value[e].stock=l.stock):d.value.push({index:new Date().getTime()+Math.floor(Math.random()*1e3),id:l.value,sku:l.label,name:l.name,nameEn:l.nameEn,color:P(x.value,Number.parseInt(l.color)),quantity:null,csCode:"",unit:l.unit,unitProductSku:l.unitProductSku,stock:l.stock}),S.value=S.value.map(a=>(a.value===o&&(a.disabled=!0),a))}function le(){C.value=!1,U.value.forEach(o=>{var e,a;d.value.find(s=>s.id===o.id)||d.value.push({index:new Date().getTime()+Math.floor(Math.random()*1e3),id:o.id,sku:o.sku,name:o.name,nameEn:o.nameEn,color:P(x.value,Number.parseInt(o.color)),quantity:null,csCode:"",unit:o.unit,unitProductSku:o.unitProductSku,stock:((e=o==null?void 0:o.stock)==null?void 0:e.inStock)-((a=o==null?void 0:o.stock)==null?void 0:a.freezeStock)||0})})}function te(o){d.value=d.value.filter(l=>l.index!==o)}function ae(){S.value=[],d.value.push({index:new Date().getTime()+Math.floor(Math.random()*1e3),id:null,sku:"",name:"",nameEn:"",color:"",quantity:null,csCode:"",unit:null,unitProductSku:"",stock:null})}async function oe(o){await w.pms.sale.order.info({id:o}).then(l=>{n.value={id:l.id,customerCode:`${l.customerCode}`,orderSn:`${l.orderSn}`,requiredShipDate:`${l.requiredShipDate}`,sourcePointId:1,isInvoice:l.isInvoice,destinationPointId:l.destinationPointId,port:l.port,exchangeRate:`${l.exchangeRate}`,forwarderId:l.forwarderId,isDirect:l.type||0,receiptNumber:`${l.receiptNumber}`,products:[]},d.value=l.products.map(e=>{var a,s;return{index:new Date().getTime()+Math.floor(Math.random()*1e3),id:e.productId,sku:e.sku,name:e.name,nameEn:e.nameEn,color:P(x.value,Number.parseInt(e.color)),quantity:e.quantity,unit:e.unit,unitProductSku:e.unitProductSku,stock:((a=e.stock)==null?void 0:a.inStock)-((s=e.stock)==null?void 0:s.freezeStock)||0,csCode:e.csCode}})})}function ue(o){const l=K.find(e=>e.value===o);return(l==null?void 0:l.label)||""}function re(o){return/^(\d+)((?:\.\d{0,4})?)$/.test(o)?o:o.slice(0,-1)}function ne(o,l){const e=o.map(a=>{var h,b,g;const s=(h=l.find(B=>B.sku===a.sku))==null?void 0:h.quantity;return{index:new Date().getTime()+Math.floor(Math.random()*1e3),id:a.id,sku:a.sku,name:a==null?void 0:a.name,nameEn:a==null?void 0:a.nameEn,color:P(x.value,Number.parseInt(a==null?void 0:a.color)),quantity:s,unit:a==null?void 0:a.unit,unitProductSku:a==null?void 0:a.unitProductSku,stock:((b=a==null?void 0:a.stock)==null?void 0:b.inStock)-((g=a==null?void 0:a.stock)==null?void 0:g.freezeStock)||0,csCode:""}});d.value.push(...e)}return H(),J(),_e(()=>{const o=M.currentRoute.value.query.orderId;o&&(A.value=!0,oe(o))}),(o,l)=>{const e=p("el-input"),a=p("el-form-item"),s=p("el-radio"),h=p("el-radio-group"),b=p("el-option"),g=p("el-select"),B=p("el-date-picker"),q=p("el-button"),y=p("el-table-column"),de=p("el-input-number"),se=p("el-table"),ie=p("el-form"),ce=p("el-dialog");return c(),k("div",null,[_("div",ke,[he,u(ie,{ref_key:"orderForm",ref:O,model:n.value,"label-width":"150px",size:"large","status-icon":""},{default:r(()=>[u(a,{label:"订单号",prop:"orderSn",rules:{required:!0,message:"订单号不能为空"}},{default:r(()=>[u(e,{modelValue:n.value.orderSn,"onUpdate:modelValue":l[0]||(l[0]=t=>n.value.orderSn=t),placeholder:"请输入订单号"},null,8,["modelValue"])]),_:1}),u(a,{label:"客户代码",prop:"customerCode",rules:{required:!0,message:"客户代码不能为空"}},{default:r(()=>[u(e,{modelValue:n.value.customerCode,"onUpdate:modelValue":l[1]||(l[1]=t=>n.value.customerCode=t),placeholder:"请输入客户代码"},null,8,["modelValue"])]),_:1}),u(a,{label:"目的港",prop:"port",rules:{required:!0,message:"目的港不能为空"}},{default:r(()=>[u(e,{modelValue:n.value.port,"onUpdate:modelValue":l[2]||(l[2]=t=>n.value.port=t),placeholder:"请输入目的港"},null,8,["modelValue"])]),_:1}),u(a,{label:"汇率",prop:"exchangeRate",rules:{required:!0,message:"汇率不能为空"}},{default:r(()=>[u(e,{modelValue:n.value.exchangeRate,"onUpdate:modelValue":l[3]||(l[3]=t=>n.value.exchangeRate=t),placeholder:"请输入汇率",type:"number",formatter:re},null,8,["modelValue"])]),_:1}),u(a,{label:"已发INVOICE",prop:"isInvoice",rules:{required:!0,message:"已发INVOICE给客户必选"}},{default:r(()=>[u(h,{modelValue:n.value.isInvoice,"onUpdate:modelValue":l[4]||(l[4]=t=>n.value.isInvoice=t)},{default:r(()=>[u(s,{label:1},{default:r(()=>[f(" 是 ")]),_:1}),u(s,{label:0},{default:r(()=>[f(" 否 ")]),_:1})]),_:1},8,["modelValue"])]),_:1}),u(a,{label:"是否直发",prop:"isDirect",rules:{required:!0,message:"是否直发必选"}},{default:r(()=>[u(h,{modelValue:n.value.isDirect,"onUpdate:modelValue":l[5]||(l[5]=t=>n.value.isDirect=t)},{default:r(()=>[u(s,{label:1},{default:r(()=>[f(" 是 ")]),_:1}),u(s,{label:0},{default:r(()=>[f(" 否 ")]),_:1})]),_:1},8,["modelValue"])]),_:1}),u(a,{label:"出货点",prop:"sourcePointId",rules:{required:!0,message:"出货点必选"}},{default:r(()=>[u(g,{modelValue:n.value.sourcePointId,"onUpdate:modelValue":l[6]||(l[6]=t=>n.value.sourcePointId=t),placeholder:"请选择出货点",style:{width:"400px"}},{default:r(()=>[(c(!0),k($,null,N(R.value,t=>(c(),D(b,{key:t.value,label:t.label,value:t.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),u(a,{label:"入仓号",prop:"receiptNumber",rules:{required:!0,message:"入仓号不能为空"}},{default:r(()=>[u(e,{modelValue:n.value.receiptNumber,"onUpdate:modelValue":l[7]||(l[7]=t=>n.value.receiptNumber=t),placeholder:"请输入入仓号"},null,8,["modelValue"])]),_:1}),n.value.isDirect===0?(c(),D(a,{key:0,label:"收货点",prop:"destinationPointId",rules:{required:!0,message:"收货点必选"}},{default:r(()=>[u(g,{modelValue:n.value.destinationPointId,"onUpdate:modelValue":l[8]||(l[8]=t=>n.value.destinationPointId=t),placeholder:"请选择入仓仓库",style:{width:"400px"}},{default:r(()=>[(c(!0),k($,null,N(T.value,t=>(c(),D(b,{key:t.value,label:t.label,value:t.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):z("",!0),u(a,{label:"货代选择",prop:"forwarderId",rules:{required:!0,message:"货代必选"}},{default:r(()=>[u(g,{modelValue:n.value.forwarderId,"onUpdate:modelValue":l[9]||(l[9]=t=>n.value.forwarderId=t),placeholder:"请选择货代",style:{width:"400px"}},{default:r(()=>[(c(!0),k($,null,N(L.value,t=>(c(),D(b,{key:t.value,label:t.label,value:t.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),u(a,{label:"要求出货日期",prop:"requiredShipDate",rules:{required:!0,message:"请选择出货日期"}},{default:r(()=>[u(B,{modelValue:n.value.requiredShipDate,"onUpdate:modelValue":l[10]||(l[10]=t=>n.value.requiredShipDate=t),type:"date","value-format":"YYYY-MM-DD",placeholder:"请选择出货日期","disabled-date":t=>t.getTime()<Date.now()-864e5},null,8,["modelValue","disabled-date"])]),_:1}),_("div",ye,[ge,_("div",we,[u(q,{type:"primary",size:"default",onClick:Z},{default:r(()=>[f(" 选择产品 ")]),_:1}),u(ve,{onSuccess:ne})]),u(se,{data:d.value,style:{width:"100%"},border:""},{default:r(()=>[u(y,{prop:"sku",label:"SKU",width:"200"},{default:r(t=>{var m;return[t.row.id===0||t.row.sku===""?(c(),k("div",Ve,[u(g,{modelValue:t.row.id,"onUpdate:modelValue":v=>t.row.id=v,filterable:"","reserve-keyword":"",loading:G.value,placeholder:"请选择产品",style:{width:"400px"},remote:"","remote-method":X,onChange:ee},{default:r(()=>[(c(!0),k($,null,N(S.value,v=>(c(),D(b,{key:v.value,disabled:v.disabled,label:v.label,value:v.value},null,8,["disabled","label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","loading"])])):(c(),k("div",Se,E((m=t.row)==null?void 0:m.sku),1))]}),_:1}),u(y,{prop:"name",label:"名称"},{default:r(t=>{var m,v;return[t.row.name===""&&t.row.nameEn===""?z("",!0):(c(),k("div",qe,E((m=t.row)==null?void 0:m.name)+" / "+E((v=t.row)==null?void 0:v.nameEn),1))]}),_:1}),u(y,{prop:"color",label:"颜色",width:"200"}),u(y,{prop:"unit",label:"单位",width:"120",align:"center"},{default:r(t=>[_("span",null,E(ue(t.row.unit)),1)]),_:1}),u(y,{prop:"stock",label:"可用库存",width:"100",align:"center"},{default:r(t=>{var m,v;return[(m=t.row)!=null&&m.stock?(c(),k("span",Ie,E((v=t.row)==null?void 0:v.stock),1)):z("",!0)]}),_:1}),u(y,{prop:"quantity",label:"*数量",width:"200",align:"center"},{header:r(()=>[Ce,f(" 数量 ")]),default:r(t=>[_("div",{style:{display:"flex","align-items":"center"},class:be(F.value&&!(t.row.quantity>0)?"quantity-input-error":"")},[u(de,{modelValue:t.row.quantity,"onUpdate:modelValue":m=>t.row.quantity=m,"w-200px":"",min:1,placeholder:"请输出产品数量"},null,8,["modelValue","onUpdate:modelValue"])],2)]),_:1}),u(y,{prop:"csCode",label:"客编号",width:"200",align:"center"},{default:r(t=>[_("div",xe,[u(e,{modelValue:t.row.csCode,"onUpdate:modelValue":m=>t.row.csCode=m,placeholder:"请输入客编号"},null,8,["modelValue","onUpdate:modelValue"])])]),_:1}),u(y,{label:"操作",width:"120",align:"center"},{default:r(t=>[u(q,{type:"danger",onClick:m=>te(t.row.index)},{default:r(()=>[f(" 删除 ")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"]),u(q,{style:{width:"100%"},class:"btn-product-add",onClick:ae},{default:r(()=>[f(" + 添加产品 ")]),_:1}),_("div",Pe,[u(q,{type:"success",loading:V.value,onClick:Q},{default:r(()=>[f(" 保存为草稿 ")]),_:1},8,["loading"])])])]),_:1},8,["model"])]),u(ce,{modelValue:C.value,"onUpdate:modelValue":l[13]||(l[13]=t=>C.value=t),title:"选择产品",width:"80%"},{footer:r(()=>[_("span",De,[u(q,{onClick:l[12]||(l[12]=t=>C.value=!1)},{default:r(()=>[f("取消")]),_:1}),u(q,{type:"primary",onClick:le},{default:r(()=>[f(" 确认选择 ")]),_:1})])]),default:r(()=>[u(fe,{ref_key:"productSelector",ref:Y,modelValue:U.value,"onUpdate:modelValue":l[11]||(l[11]=t=>U.value=t)},null,8,["modelValue"])]),_:1},8,["modelValue"])])}}});export{Be as default};
