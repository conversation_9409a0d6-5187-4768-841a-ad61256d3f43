import{i as p}from"./index-BtOcqcNl.js";import{c as i,r as q,q as u,w as o,h as e,i as t,y as E,B as F,f as O,s as j,F as D,T as K,o as r,j as L,t as M}from"./.pnpm-hVqhwuVC.js";import{a as S}from"./index-D95m1iJL.js";const z=i({name:"sys-param"}),J=i({...z,setup(A){const{service:b}=S(),l=q({value:"",list:[{label:"代码编辑器",value:"cl-editor-monaco"},{label:"富文本编辑器",value:"cl-editor-wang"}]}),f=p.useCrud({service:b.base.sys.param},a=>{a.refresh()}),h=p.useTable({columns:[{type:"selection",width:60},{label:"名称",prop:"name",minWidth:150},{label:"keyName",prop:"keyName",minWidth:150},{label:"数据",prop:"data",minWidth:150,showOverflowTooltip:!0},{label:"备注",prop:"remark",minWidth:200,showOverflowTooltip:!0},{label:"操作",type:"op",buttons:["edit","delete"]}]}),_=p.useUpsert({dialog:{width:"1000px"},items:[{prop:"name",label:"名称",span:12,required:!0,component:{name:"el-input"}},{prop:"keyName",label:"keyName",span:12,required:!0,component:{name:"el-input",props:{placeholder:"请输入Key"}}},{prop:"data",label:"数据",component:{name:"slot-content"}},{prop:"remark",label:"备注",component:{name:"el-input",props:{placeholder:"请输入备注",rows:3,type:"textarea"}}}],onOpened(a){l.value=/<*>/g.test(a.data)?l.list[1].value:l.list[0].value}});function v(a){K.confirm("切换编辑器会清空输入内容，是否继续？","提示",{type:"warning"}).then(()=>{var c;l.value=a,(c=_.value)==null||c.setForm("data","")}).catch(()=>null)}return(a,c)=>{const y=t("cl-refresh-btn"),k=t("cl-add-btn"),g=t("cl-multi-delete-btn"),m=t("cl-flex1"),w=t("cl-search-key"),s=t("cl-row"),x=t("cl-table"),C=t("cl-pagination"),N=t("el-radio"),T=t("el-radio-group"),V=t("cl-editor"),B=t("cl-upsert"),U=t("cl-crud");return r(),u(U,{ref_key:"Crud",ref:f},{default:o(()=>[e(s,null,{default:o(()=>[e(y),e(k),e(g),e(m),e(w)]),_:1}),e(s,null,{default:o(()=>[e(x,{ref_key:"Table",ref:h},null,512)]),_:1}),e(s,null,{default:o(()=>[e(m),e(C)]),_:1}),e(B,{ref_key:"Upsert",ref:_},{"slot-content":o(({scope:d})=>[E("div",null,[e(T,{"model-value":l.value,onChange:v},{default:o(()=>[(r(!0),O(D,null,j(l.list,(n,W)=>(r(),u(N,{key:W,label:n.value},{default:o(()=>[L(M(n.label),1)]),_:2},1032,["label"]))),128))]),_:1},8,["model-value"]),l.value?(r(),u(V,{key:0,modelValue:d.data,"onUpdate:modelValue":n=>d.data=n,name:l.value},null,8,["modelValue","onUpdate:modelValue","name"])):F("",!0)])]),_:1},512)]),_:1},512)}}});export{J as default};
