import{c as x,b as u,A as F,q as k,w as t,h as l,i as o,Y as M,j as f,f as P,s as R,F as $,E as b,o as v}from"./.pnpm-hVqhwuVC.js";import{i as w}from"./index-DkYL1aws.js";import{a as j}from"./index-C6cm1h61.js";const Y=x({name:"pms-setting"}),Q=x({...Y,setup(z){const{service:c}=j(),_=u([]),h=u([]);async function C(){try{_.value=await c.pms.supplier.request({url:"/list",method:"POST"}),h.value=_.value.map(e=>({label:e.supplierName,value:e.id}))}catch(e){console.error(e)}}F(()=>{C()});const r=u(!1),s=u(""),y=u(null),S=w.useUpsert({props:{class:"supplier-setting-form"},items:[{label:"绑定供应商",prop:"supplier_id",required:!0,component:{name:"el-select"}}],async onInfo(e,{done:n}){n(e)}}),V=w.useTable({columns:[{label:"ID",prop:"id",width:60},{label:"创建时间",prop:"createTime",width:180},{label:"用户名",prop:"name",width:180},{label:"昵称",prop:"nickName",width:180},{label:"姓名",prop:"username",width:180},{prop:"bind_supplier",label:"所绑定的供应商",dict:h},{type:"op",label:"操作",buttons:["slot-btn-bindSupplier"],width:300}]}),g=w.useCrud({dict:{api:{page:"getSupplierAccountList"}},service:c.pms.supplier_account,async onRefresh(e,{next:n,render:d}){const{list:i,pagination:p}=await n(e);d(i,p)}},e=>{e.refresh()});function T(e){y.value=e,s.value=e.bind_supplier||"",r.value=!0}async function E(){var e;if(!s.value){b.warning("请选择供应商");return}try{await c.pms.supplier_account.request({url:"/bindSupplier",method:"POST",data:{user_id:y.value.id,supplier_id:s.value}}),b({type:"success",message:"绑定成功"}),r.value=!1,(e=g.value)==null||e.refresh()}catch{b.error("绑定失败")}}return(e,n)=>{const d=o("cl-refresh-btn"),i=o("cl-flex1"),p=o("el-row"),m=o("el-button"),N=o("cl-table"),U=o("cl-pagination"),q=o("cl-upsert"),B=o("el-option"),I=o("el-select"),L=o("el-form-item"),O=o("el-form"),A=o("el-dialog"),D=o("cl-crud");return v(),k(D,{ref_key:"Crud",ref:g},{default:t(()=>[l(p,null,{default:t(()=>[l(d),l(i)]),_:1}),l(p,{style:{"margin-top":"10px"}},{default:t(()=>[l(N,{ref_key:"Table",ref:V},{"slot-btn-bindSupplier":t(({scope:a})=>[l(m,{text:"",bg:"",type:"primary",onClick:M(G=>T(a.row),["stop"])},{default:t(()=>[f(" 绑定供应商 ")]),_:2},1032,["onClick"])]),_:1},512)]),_:1}),l(p,null,{default:t(()=>[l(i),l(U)]),_:1}),l(q,{ref_key:"Upsert",ref:S},null,512),l(A,{modelValue:r.value,"onUpdate:modelValue":n[2]||(n[2]=a=>r.value=a),title:"选择供应商",width:"500px"},{footer:t(()=>[l(m,{onClick:n[1]||(n[1]=a=>r.value=!1)},{default:t(()=>[f(" 取消 ")]),_:1}),l(m,{type:"primary",onClick:E},{default:t(()=>[f(" 确认 ")]),_:1})]),default:t(()=>[l(O,{"label-width":"100px"},{default:t(()=>[l(L,{label:"选择供应商"},{default:t(()=>[l(I,{modelValue:s.value,"onUpdate:modelValue":n[0]||(n[0]=a=>s.value=a),placeholder:"请选择供应商",filterable:"",clearable:"",style:{width:"100%"}},{default:t(()=>[(v(!0),P($,null,R(_.value,a=>(v(),k(B,{key:a.id,label:a.supplierName,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1},8,["modelValue"])]),_:1},512)}}});export{Q as default};
