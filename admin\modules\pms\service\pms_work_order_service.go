package service

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/text/gstr"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/imhuso/lookah-erp/admin/modules/pms/model"
	"github.com/imhuso/lookah-erp/admin/yc"
	"sort"
)

type PmsWorkOrderService struct {
	*yc.Service
}

func (s *PmsWorkOrderService) QueryPage(ctx context.Context, vo *model.QueryVo) (result *model.PageResult[*model.PmsWorkOrderVo], err error) {
	materialIds := make([]int64, 0)
	pageResult := model.InitPageResult[*model.PmsWorkOrderVo](vo)
	// 应MC要求，不再对生产单的状态限制
	//queryWrap := yc.DBM(s.Model).Ctx(ctx).As("main").
	//	LeftJoin("pms_production_schedule as schedule", "main.production_schedule_id = schedule.id").
	//	LeftJoin("pms_production_schedule_product as schPro", "schPro.schedule_id = schedule.id AND schPro.product_id = main.product_id").
	//	Where("schedule.status = ?", 1)
	queryWrap := yc.DBM(s.Model).Ctx(ctx).As("main").
		LeftJoin("pms_production_schedule as schedule", "main.production_schedule_id = schedule.id").
		LeftJoin("pms_production_schedule_product as schPro", "schPro.schedule_id = schedule.id AND schPro.product_id = main.product_id")
	vo.KeyWord = gstr.Trim(vo.KeyWord)
	vo.IsShowUnfinished = gconv.Bool(vo.IsShowUnfinished)
	vo.IsShowShiftWork = gconv.Bool(vo.IsShowShiftWork)
	if vo.KeyWord != "" {
		queryWrap = queryWrap.Where("main.code_json like ? OR main.work_order_no like ? OR  main.production_schedule_sn like ?",
			"%"+vo.KeyWord+"%", "%"+vo.KeyWord+"%", "%"+vo.KeyWord+"%")
	}
	// 关键条件：存在至少一条未达标的明细记录
	if vo.IsShowUnfinished || vo.IsShowShiftWork {
		queryWrap = queryWrap.Where("EXISTS (SELECT 1 FROM `pms_work_order_detail` detail WHERE " +
			"detail.work_order_id = main.id AND detail.outbound_quantity < detail.calc_bom_quantity AND detail.deleteTime IS NULL)")
	}
	if vo.Status != 0 {
		status := vo.Status
		if status == -1 { // 0:未完成 1:已完成, 数据库默认是 0
			status = 0
		}
		queryWrap.Where("main.status = ?", status)
	}
	countWrap := queryWrap.Clone()
	count, err := countWrap.Count()
	if err != nil {
		return pageResult, err
	}
	pageResult.Total = int64(count)
	// productionScheduleId这个字段经常行封装不上
	err = queryWrap.Fields("main.*,schedule.sn,schPro.quantity AS productQty, schPro.inbound_quantity").
		Page(vo.Page, vo.Size).OrderDesc("main.id").Scan(&pageResult.List)
	if err != nil {
		return pageResult, err
	}
	workOrderIdList := make([]int64, 0)
	if len(pageResult.List) > 0 {
		materialMap, err := NewPmsMaterialService().QueryAllMaterial(ctx)
		idMap := materialMap.MaterialIdMap
		if err != nil {
			return pageResult, err
		}
		workOrderIdMap := make(map[int64][]*model.PmsWorkOrderDetail)
		pmsWorkOrderDetailList := make([]*model.PmsWorkOrderDetail, 0)
		for i := range pageResult.List {
			item := pageResult.List[i]
			if len(item.ProductIds) > 0 {
				// item.ProductIds 解析成数组
				productIds := gconv.SliceInt64(item.ProductIds)
				// 查询产品
				pmsProductList := make([]*model.PmsProduct, 0)
				err = yc.DBM(model.NewPmsProduct()).Ctx(ctx).Where("id in (?)", productIds).Scan(&pmsProductList)
				if err != nil {
					return pageResult, err
				}
				productName := ""
				for _, el := range pmsProductList {
					name := fmt.Sprintf("%s/%s", el.Sku, el.Name)
					if productName == "" {
						productName = name
					} else {
						productName = productName + "," + name
					}
				}
				item.ProductName = productName
			}
			workOrderIdList = append(workOrderIdList, pageResult.List[i].ID)
		}

		m := yc.DBM(model.NewPmsWorkOrderDetail()).Ctx(ctx).
			Where("work_order_id in (?)", workOrderIdList)

		if vo.IsShowShiftWork {
			m = m.Where("outbound_quantity < calc_bom_quantity")
		}
		err = m.OrderDesc("id").
			Scan(&pmsWorkOrderDetailList)

		if err != nil {
			return pageResult, err
		}

		if len(pmsWorkOrderDetailList) > 0 {
			for _, item := range pmsWorkOrderDetailList {
				// 深克隆item
				cloneIem := &model.PmsWorkOrderDetail{}
				err = gconv.Struct(item, cloneIem)
				if err != nil {
					return pageResult, err
				}
				if workOrderIdMap[item.WorkOrderID] == nil {
					workOrderIdMap[item.WorkOrderID] = make([]*model.PmsWorkOrderDetail, 0)
				}
				if idMap[item.MaterialId] != nil {
					pmsMaterial := idMap[item.MaterialId]
					err = gconv.Struct(pmsMaterial, &item)
					if err != nil {
						return pageResult, err
					}
					item.ID = pmsMaterial.ID // 要兼容旧版的前端
				}

				item.WorkOrderDetailID = cloneIem.ID
				item.WorkOrderNo = cloneIem.WorkOrderNo
				item.WorkOrderID = cloneIem.WorkOrderID
				item.ProductId = cloneIem.ProductId
				item.BomID = cloneIem.BomID
				item.BomVersion = cloneIem.BomVersion
				item.MaterialId = cloneIem.MaterialId
				item.BomQuantity = cloneIem.BomQuantity
				item.OutboundQuantity = cloneIem.OutboundQuantity
				item.OutQty = cloneIem.OutboundQuantity
				item.CreatorId = cloneIem.CreatorId
				item.CreateTime = cloneIem.CreateTime
				item.DeleteTime = cloneIem.DeleteTime
				workOrderIdMap[item.WorkOrderID] = append(workOrderIdMap[item.WorkOrderID], item)
				materialIds = append(materialIds, cloneIem.MaterialId)
			}
		}
		for i, item := range pageResult.List {
			if workOrderIdMap[item.ID] != nil {
				extras := make([]*model.PmsPurchaseOrderWithSummaryExtraOutput, 0)
				err = gconv.Struct(workOrderIdMap[item.ID], &extras)
				if err != nil {
					return pageResult, err
				}
				if len(extras) > 0 {
					for j, el := range extras {
						//if el.BomQuantity > 0 && item.Quantity > 0 {
						//	extras[j].CalcBomQuantity = el.BomQuantity * item.Quantity
						//}
						if v, ok := idMap[el.MaterialId]; ok {
							extras[j].InboundOutboundKey = v.InboundOutboundKey
						}
					}
				}
				// 根据物料编号排序
				sort.Slice(extras, func(i, j int) bool {
					return extras[i].Code < extras[j].Code
				})
				pageResult.List[i].Extras = extras
			}
		}
	}
	//查询物料位置信息
	//去重
	materialIds = removeDuplicates(materialIds)
	materialInfos := make([]model.PmsMaterial, 0)
	err = yc.DBM(model.NewPmsMaterial()).Ctx(ctx).WhereIn("id", materialIds).Scan(&materialInfos)
	mMaterial := make(map[int64]model.PmsMaterial, 0)
	for _, item := range materialInfos {
		mMaterial[item.ID] = item
	}
	for i, item := range pageResult.List {
		if len(item.Extras) > 0 {
			for j, row := range item.Extras {
				if v, ok := mMaterial[row.MaterialId]; ok {
					pageResult.List[i].Extras[j].AddressName = v.AddressName
				}
			}
		}
	}
	return pageResult, err
}

func removeDuplicates(ids []int64) []int64 {
	// 创建一个 map 来存储唯一的 ID
	uniqueIDs := make(map[int64]bool)
	result := []int64{}

	// 遍历数组，将每个 ID 添加到 map 中
	for _, id := range ids {
		if !uniqueIDs[id] {
			uniqueIDs[id] = true
			result = append(result, id)
		}
	}

	return result
}

func (s *PmsWorkOrderService) UpdateDetailBatch(ctx context.Context, params *model.PmsWorkOrderVo) (data *model.PmsWorkOrderVo, err error) {
	if params.ID == 0 {
		return params, errors.New("工单ID不能为空")
	}
	extras := params.Extras
	if len(extras) == 0 {
		return params, errors.New("物料不能为空")
	}
	updateList := make([]*model.PmsWorkOrderDetail, 0)
	errFlag := false
	errMsg := ""
	for _, item := range extras {
		if !params.Revoke && item.OutQty <= 0 {
			errFlag = true
			errMsg = "数量不能小于0"
			break
		}
		if item.MaterialId == 0 {
			errFlag = true
			errMsg = "物料ID不能为空"
			break
		}
		dbDtl := &model.PmsWorkOrderDetail{}
		queryWrap := yc.DBM(model.NewPmsWorkOrderDetail()).Ctx(ctx)
		if item.WorkOrderDetailID > 0 {
			queryWrap.Where("id = ?", item.WorkOrderDetailID)
		} else {
			queryWrap.Where("material_id = ?", item.MaterialId).Where("work_order_id = ?", params.ID)
		}
		err := queryWrap.Scan(&dbDtl)
		if err != nil {
			return nil, err
		}
		dbDtl.OutboundQuantity += item.OutQty
		if dbDtl.OutboundQuantity < 0 {
			return nil, errors.New("更新工单详情错误,出库数量不能小于0")
		}
		updateList = append(updateList, dbDtl)
	}
	if errFlag {
		return params, errors.New(errMsg)
	}

	if len(updateList) > 0 {
		for _, item := range updateList {
			_, err := yc.DBM(model.NewPmsWorkOrderDetail()).Ctx(ctx).Data(item).Where("id", item.ID).Update()
			if err != nil {
				return nil, err
			}
		}
	}
	return nil, err
}

func (s *PmsWorkOrderService) UpdateWorkOrder(ctx context.Context, orderVo *model.AddPmsWorkOrderVo) (result *model.AddPmsWorkOrderVo, err error) {
	if orderVo.ScheduleId == 0 {
		return nil, errors.New("生产单ID不能为空")
	}
	if orderVo.ProductionScheduleSn == "" {
		return nil, errors.New("生产单号不能为空")
	}
	if orderVo.WorkOrderNo == "" {
		return nil, errors.New("工单编号不能为空")
	}
	if len(orderVo.Products) == 0 {
		return nil, errors.New("产品不能为空")
	}
	workOrderList := make([]*model.PmsWorkOrder, 0)
	err = yc.DBM(s.Model).Ctx(ctx).Scan(&workOrderList)
	if err != nil {
		return nil, err
	}
	materialMap, err := NewPmsMaterialService().QueryAllMaterial(ctx)
	if err != nil {
		return nil, err
	}
	count, err := yc.DBM(model.NewPmsWorkOrder()).Ctx(ctx).Where("work_order_no", orderVo.WorkOrderNo).Count()
	if err != nil {
		return nil, err
	}
	if count <= 0 {
		return nil, gerror.New("没有查询到工单号")
	}
	admin := yc.GetAdmin(ctx)
	// 查询详情表里面已有的物料数据
	workDetails := make([]*model.PmsWorkOrderDetail, 0)
	err = yc.DBM(model.NewPmsWorkOrderDetail()).Ctx(ctx).Where("work_order_id", orderVo.ID).Scan(&workDetails)

	pmsWorkOrderBomMapList := make([]*model.PmsWorkOrderBomMap, 0)
	// 开启事务
	err = yc.DB(s.Model.GroupName()).Ctx(ctx).Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 删除MAP数据
		_, err = yc.DBM(model.NewPmsWorkOrderBomMap()).Ctx(ctx).Where("work_order_id", orderVo.ID).Delete()
		if err != nil {
			return err
		}

		sumQty := 0
		codeJson := ""
		productIds := make([]int64, 0)
		productQtyList := make([]int, 0)
		productIdMapList := make(map[int64][]*model.PmsWorkOrderDetail)
		for i := range orderVo.Products {
			product := orderVo.Products[i]
			tmp := &model.PmsWorkOrder{
				ProductionScheduleId: orderVo.ScheduleId,     // 生产单 id pms_production_schedule_plan
				ProductId:            product.PieceProductId, // 产品ID
				PlanProductionDate:   orderVo.ScheduleDate,   // 计划生产日期
				Quantity:             float64(product.Piece), // 计划生产数量 前端是planQuantity
				WorkOrderNo:          product.WorkOrderNo,    // 工单编号
				MaterialMap:          materialMap,            // 物料
				PlanProduct:          product,                // 产品
			}
			productQtyList = append(productQtyList, product.Piece)
			productIds = append(productIds, product.PieceProductId)
			// 根据产品的数量计算BOM物料
			calcBomMaterialQtyList, err := NewPmsWorkOrderService().CalcMaterialQtyByBom(ctx, tmp)
			if err != nil {
				return err
			}
			pmsWorkOrderBomMapList = append(pmsWorkOrderBomMapList, &model.PmsWorkOrderBomMap{
				ProductionScheduleId: orderVo.ScheduleId,
				ProductId:            product.PieceProductId,
				ProductQuantity:      product.Piece,
				CreateTime:           gtime.Now(),
				CreatorId:            admin.UserId,
			})

			// 计算总数量
			sumQty += product.Piece
			productIdMapList[product.PieceProductId] = calcBomMaterialQtyList

			// 创建产品与Bom和工单的关联记录
			workOrderBomMap := &model.PmsWorkOrderBomMap{
				ProductionScheduleId: orderVo.ScheduleId,
				ProductId:            product.PieceProductId,
				ProductQuantity:      product.Piece,
				CreateTime:           gtime.Now(),
				CreatorId:            admin.UserId,
			}
			if len(calcBomMaterialQtyList) > 0 {
				workOrderBomMap.BomId = calcBomMaterialQtyList[0].BomID
				workOrderBomMap.BomVersion = calcBomMaterialQtyList[0].BomVersion
			}
		}

		productIdsStr := gconv.String(productIds)
		productQtyListStr := gconv.String(productQtyList)
		// 创建工单
		workOrder := &model.PmsWorkOrder{
			CodeJson:             codeJson,
			ProductIds:           productIdsStr,
			ProductQtyJson:       productQtyListStr,
			ProductionScheduleId: orderVo.ScheduleId,
			ProductionScheduleSn: orderVo.ProductionScheduleSn,
			Quantity:             float64(sumQty), // 计划生产数量
			WorkOrderNo:          orderVo.WorkOrderNo,
			PlanProductionDate:   orderVo.ScheduleDate,
			CreatorId:            admin.UserId,
			CreateTime:           gtime.Now(),
		}
		_, err := yc.DBM(model.NewPmsWorkOrder()).Ctx(ctx).Data(workOrder).Where("id", orderVo.ID).OmitEmptyData().Update()
		if err != nil {
			return err
		}
		sumMaterialMap := make(map[int64]float64)
		// 遍历productIdMapList
		for _, item := range productIdMapList {
			// 遍历item， 把物料id相同的数量累加 sumMaterialMap
			for _, material := range item {
				if _, ok := sumMaterialMap[material.MaterialId]; !ok {
					sumMaterialMap[material.MaterialId] = material.CalcBomQuantity
				} else {
					sumMaterialMap[material.MaterialId] += material.CalcBomQuantity
				}
			}
		}
		// 创建工单详情
		workOrderDetailList := make([]*model.PmsWorkOrderDetail, 0)
		for materialId, quantity := range sumMaterialMap {
			workOrderDetail := &model.PmsWorkOrderDetail{
				WorkOrderID:     orderVo.ID,
				MaterialId:      materialId,
				CalcBomQuantity: quantity,
				CreatorId:       admin.UserId,
				CreateTime:      gtime.Now(),
				Code:            materialMap.MaterialIdMap[materialId].Code,
			}
			if codeJson == "" {
				codeJson = materialMap.MaterialIdMap[materialId].Code
			} else {
				codeJson = codeJson + "," + materialMap.MaterialIdMap[materialId].Code
			}
			workOrderDetailList = append(workOrderDetailList, workOrderDetail)
		}
		// 获取差异数据 没有保存的工单详情
		insertData := make([]*model.PmsWorkOrderDetail, 0)
		for _, item := range workOrderDetailList {
			flag := false
			for _, row := range workDetails {
				if item.MaterialId == row.MaterialId {
					flag = true
				}
			}
			if !flag {
				insertData = append(insertData, item)
			} else {
				_, err = yc.DBM(model.NewPmsWorkOrderDetail()).Ctx(ctx).
					Where("work_order_id", item.WorkOrderID).
					Where("material_id", item.MaterialId).
					Data(g.Map{
						"calc_bom_quantity": item.CalcBomQuantity,
					}).Update()
				if err != nil {
					return err
				}
			}
		}

		workDetailIds := make([]int64, 0)
		// 获取需要删除的工单详情ID
		for _, item := range workDetails {
			flag := false
			for _, row := range workOrderDetailList {
				if item.MaterialId == row.MaterialId {
					flag = true
				}
			}
			if !flag {
				workDetailIds = append(workDetailIds, item.ID)
			}
		}

		// 删除变更的工单详情
		_, err = yc.DBM(model.NewPmsWorkOrderDetail()).Ctx(ctx).WhereIn("ID", workDetailIds).Delete()
		if err != nil {
			return err
		}

		// 如果有差异数据则更新工单详情表
		if len(insertData) > 0 {
			_, err = yc.DBM(model.NewPmsWorkOrderDetail()).Ctx(ctx).Data(insertData).Insert()
			if err != nil {
				return err
			}
		}

		// 创建产品与Bom和工单的关联记录
		for i := range pmsWorkOrderBomMapList {
			pmsWorkOrderBomMapList[i].WorkOrderId = orderVo.ID
		}
		_, err = yc.DBM(model.NewPmsWorkOrderBomMap()).Ctx(ctx).Data(pmsWorkOrderBomMapList).Insert()
		if err != nil {
			return err
		}

		// 更新工单 codeJson
		_, err = yc.DBM(model.NewPmsWorkOrder()).Ctx(ctx).Data(g.Map{"code_json": codeJson}).Where("id", orderVo.ID).Update()
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return nil, err
	}
	return orderVo, err
}

func (s *PmsWorkOrderService) AddWorkOrderBatch(ctx context.Context, orderVo *model.AddPmsWorkOrderVo) (result *model.AddPmsWorkOrderVo, err error) {
	if orderVo.ScheduleId == 0 {
		return nil, errors.New("生产单ID不能为空")
	}
	if orderVo.ProductionScheduleSn == "" {
		return nil, errors.New("生产单号不能为空")
	}
	if orderVo.WorkOrderNo == "" {
		return nil, errors.New("工单编号不能为空")
	}
	if len(orderVo.Products) == 0 {
		return nil, errors.New("产品不能为空")
	}
	workOrderList := make([]*model.PmsWorkOrder, 0)
	err = yc.DBM(s.Model).Ctx(ctx).Scan(&workOrderList)
	if err != nil {
		return nil, err
	}
	materialMap, err := NewPmsMaterialService().QueryAllMaterial(ctx)
	if err != nil {
		return nil, err
	}
	count, err := yc.DBM(model.NewPmsWorkOrder()).Ctx(ctx).Where("work_order_no", orderVo.WorkOrderNo).Count()
	if err != nil {
		return nil, err
	}
	if count > 0 {
		return nil, gerror.New("工单号已存在")
	}
	admin := yc.GetAdmin(ctx)
	pmsWorkOrderBomMapList := make([]*model.PmsWorkOrderBomMap, 0)
	err = yc.DB(s.Model.GroupName()).Ctx(ctx).Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		sumQty := 0
		codeJson := ""
		productIds := make([]int64, 0)
		productQtyList := make([]int, 0)
		productIdMapList := make(map[int64][]*model.PmsWorkOrderDetail)
		for i := range orderVo.Products {
			product := orderVo.Products[i]
			tmp := &model.PmsWorkOrder{
				ProductionScheduleId: orderVo.ScheduleId,     // 生产单 id pms_production_schedule_plan
				ProductId:            product.PieceProductId, // 产品ID
				PlanProductionDate:   orderVo.ScheduleDate,   // 计划生产日期
				Quantity:             float64(product.Piece), // 计划生产数量 前端是planQuantity
				WorkOrderNo:          product.WorkOrderNo,    // 工单编号
				MaterialMap:          materialMap,            // 物料
				PlanProduct:          product,                // 产品
			}
			productQtyList = append(productQtyList, product.Piece)
			productIds = append(productIds, product.PieceProductId)
			// 根据产品的数量计算BOM物料
			calcBomMaterialQtyList, err := NewPmsWorkOrderService().CalcMaterialQtyByBom(ctx, tmp)
			if err != nil {
				return err
			}
			pmsWorkOrderBomMapList = append(pmsWorkOrderBomMapList, &model.PmsWorkOrderBomMap{
				ProductionScheduleId: orderVo.ScheduleId,
				ProductId:            product.PieceProductId,
				ProductQuantity:      product.Piece,
				CreateTime:           gtime.Now(),
				CreatorId:            admin.UserId,
			})

			// 计算总数量
			sumQty += product.Piece
			productIdMapList[product.PieceProductId] = calcBomMaterialQtyList

			// 创建产品与Bom和工单的关联记录
			workOrderBomMap := &model.PmsWorkOrderBomMap{
				ProductionScheduleId: orderVo.ScheduleId,
				ProductId:            product.PieceProductId,
				ProductQuantity:      product.Piece,
				CreateTime:           gtime.Now(),
				CreatorId:            admin.UserId,
			}
			if len(calcBomMaterialQtyList) > 0 {
				workOrderBomMap.BomId = calcBomMaterialQtyList[0].BomID
				workOrderBomMap.BomVersion = calcBomMaterialQtyList[0].BomVersion
			}
		}

		productIdsStr := gconv.String(productIds)
		productQtyListStr := gconv.String(productQtyList)
		// 创建工单
		workOrder := &model.PmsWorkOrder{
			CodeJson:             codeJson,
			ProductIds:           productIdsStr,
			ProductQtyJson:       productQtyListStr,
			ProductionScheduleId: orderVo.ScheduleId,
			ProductionScheduleSn: orderVo.ProductionScheduleSn,
			Quantity:             float64(sumQty), // 计划生产数量
			WorkOrderNo:          orderVo.WorkOrderNo,
			PlanProductionDate:   orderVo.ScheduleDate,
			CreatorId:            admin.UserId,
			CreateTime:           gtime.Now(),
		}
		id, err := yc.DBM(model.NewPmsWorkOrder()).Ctx(ctx).Data(workOrder).OmitEmptyData().InsertAndGetId()
		if err != nil {
			return err
		}
		sumMaterialMap := make(map[int64]float64)
		// 遍历productIdMapList
		for _, item := range productIdMapList {
			// 遍历item， 把物料id相同的数量累加 sumMaterialMap
			for _, material := range item {
				if _, ok := sumMaterialMap[material.MaterialId]; !ok {
					sumMaterialMap[material.MaterialId] = material.CalcBomQuantity
				} else {
					sumMaterialMap[material.MaterialId] += material.CalcBomQuantity
				}
			}
		}
		// 创建工单详情
		workOrderDetailList := make([]*model.PmsWorkOrderDetail, 0)
		for materialId, quantity := range sumMaterialMap {
			workOrderDetail := &model.PmsWorkOrderDetail{
				WorkOrderID:     id,
				MaterialId:      materialId,
				CalcBomQuantity: quantity,
				CreatorId:       admin.UserId,
				CreateTime:      gtime.Now(),
				Code:            materialMap.MaterialIdMap[materialId].Code,
			}
			if codeJson == "" {
				codeJson = materialMap.MaterialIdMap[materialId].Code
			} else {
				codeJson = codeJson + "," + materialMap.MaterialIdMap[materialId].Code
			}
			workOrderDetailList = append(workOrderDetailList, workOrderDetail)
		}
		_, err = yc.DBM(model.NewPmsWorkOrderDetail()).Ctx(ctx).Data(workOrderDetailList).Insert()
		if err != nil {
			return err
		}
		// 创建产品与Bom和工单的关联记录
		for i := range pmsWorkOrderBomMapList {
			pmsWorkOrderBomMapList[i].WorkOrderId = id
		}
		_, err = yc.DBM(model.NewPmsWorkOrderBomMap()).Ctx(ctx).Data(pmsWorkOrderBomMapList).Insert()
		if err != nil {
			return err
		}
		// 更新工单 codeJson
		_, err = yc.DBM(model.NewPmsWorkOrder()).Ctx(ctx).Data(g.Map{"code_json": codeJson}).Where("id", id).Update()
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return nil, err
	}
	return orderVo, err
}

func (s *PmsWorkOrderService) CalcMaterialQtyByBom(ctx context.Context, order *model.PmsWorkOrder) (result []*model.PmsWorkOrderDetail, err error) {
	product := order.PlanProduct
	dbWorkOrder := model.NewPmsWorkOrderBomMap()
	sum, err := yc.DBM(dbWorkOrder).Ctx(ctx).Where("production_schedule_id", order.ProductionScheduleId).
		Where("product_id", order.ProductId).Sum("product_quantity")
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return nil, err
	}
	sum = sum + order.Quantity
	// 查询生产计划
	scheduleProduct := model.NewPmsProductionScheduleProduct()
	err = yc.DBM(scheduleProduct).Ctx(ctx).
		With(&model.PmsProduct{}).
		Where("schedule_id", order.ProductionScheduleId).
		Where("product_id", order.ProductId).Scan(&scheduleProduct)
	if err != nil {
		return nil, err
	}
	// 判断数量是否超出计划数量
	if sum > float64(scheduleProduct.Quantity) {
		productDtl := scheduleProduct.Product.Sku + "/" + scheduleProduct.Product.Name
		return nil, gerror.New(fmt.Sprintf("产品%s数量超出计划数量,计划数量:%v,已分配数量:%v", productDtl, scheduleProduct.Quantity, sum))
	}
	err = s.Validate(ctx, order)
	if err != nil {
		return nil, err
	}
	// 查询产品是否存在
	pmsProduct := &model.PmsProduct{}
	err = yc.DBM(pmsProduct).Ctx(ctx).Where("id", product.PieceProductId).Scan(&pmsProduct)
	if err != nil {
		return nil, err
	}
	// 查询BOM
	pmsBom := model.NewPmsBom()
	err = yc.DBM(model.NewPmsBom()).Ctx(ctx).Where("product_id", product.PieceProductId).Scan(&pmsBom)
	if err != nil {
		return nil, err
	}
	if pmsBom.ID == 0 {
		return nil, gerror.New("BOM不存在")
	}
	order.BomID = pmsBom.ID
	order.BomVersion = pmsBom.Version
	// 查询BOM物料
	pmsBomMaterialList := make([]*model.PmsBomMaterial, 0)
	err = yc.DBM(model.NewPmsBomMaterial()).Ctx(ctx).Where("bom_id", pmsBom.ID).Scan(&pmsBomMaterialList)
	if err != nil {
		return nil, err
	}
	materialVoList := make([]*model.PmsWorkOrderDetail, 0)
	if len(pmsBomMaterialList) > 0 {
		// 计算物料
		for _, bomMaterial := range pmsBomMaterialList {
			tmp := &model.PmsWorkOrderDetail{
				ScheduleId:      order.ProductionScheduleId,
				ProductQty:      product.Piece, // 产品数量
				ProductId:       product.PieceProductId,
				CalcBomQuantity: bomMaterial.Quantity * float64(product.Piece),
				BomID:           pmsBom.ID,
				BomVersion:      pmsBom.Version,
				MaterialId:      bomMaterial.MaterialId,
				BomQuantity:     bomMaterial.Quantity,
			}
			materialVoList = append(materialVoList, tmp)
		}
	}
	return materialVoList, err
}

func (s *PmsWorkOrderService) Validate(_ context.Context, order *model.PmsWorkOrder) error {
	if order.ProductionScheduleId == 0 {
		return errors.New("生产计划ID不能为空")
	}
	if order.ProductId == 0 {
		return errors.New("产品ID不能为空")
	}
	if order.Quantity <= 0 {
		return errors.New("数量不能小于0")
	}
	//if order.PlanProductionDate == nil {
	//	return errors.New("计划生产日期不能为空")
	//}
	return nil
}

func (s *PmsWorkOrderService) DelWorkOrder(ctx context.Context, order *model.PmsWorkOrder) error {
	if order.ID == 0 {
		return errors.New("工单ID不能为空")
	}
	outbound := model.NewPmsMaterialOutbound()
	err := yc.DBM(outbound).Ctx(ctx).Where("work_order_id", order.ID).Scan(&outbound)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return err
	}
	if outbound.ID > 0 {
		return errors.New("工单已领料出库,不能删除")
	}
	// 开启事务
	err = yc.DB(s.Model.GroupName()).Ctx(ctx).Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		_, err := yc.DBM(model.NewPmsWorkOrder()).Ctx(ctx).Where("id", order.ID).Delete()
		if err != nil {
			return err
		}
		_, err = yc.DBM(model.NewPmsWorkOrderDetail()).Ctx(ctx).Where("work_order_id", order.ID).Delete()
		if err != nil {
			return err
		}
		_, err = yc.DBM(model.NewPmsWorkOrderBomMap()).Ctx(ctx).Where("work_order_id", order.ID).Delete()
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

func (s *PmsWorkOrderService) Complete(ctx context.Context, order *model.PmsWorkOrder) error {
	if order.ID == 0 {
		return errors.New("工单ID不能为空")
	}
	// 开启事务
	err := yc.DB(s.Model.GroupName()).Ctx(ctx).Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		_, err := yc.DBM(s.Model).Ctx(ctx).Where("id", order.ID).Data(g.Map{"status": 1}).Update()
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return err
	}
	return err
}

func NewPmsWorkOrderService() *PmsWorkOrderService {
	return &PmsWorkOrderService{
		&yc.Service{
			Model: model.NewPmsWorkOrder(),
		},
	}
}
