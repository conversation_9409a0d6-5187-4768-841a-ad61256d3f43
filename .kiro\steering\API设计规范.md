---
inclusion: fileMatch
fileMatchPattern: "**/*api*"
---

# API 设计规范

## RESTful API 设计原则

### URL 设计规范
```
# 资源命名使用复数形式
GET    /api/v1/users          # 获取用户列表
GET    /api/v1/users/{id}     # 获取单个用户
POST   /api/v1/users          # 创建用户
PUT    /api/v1/users/{id}     # 更新用户
DELETE /api/v1/users/{id}     # 删除用户

# 嵌套资源
GET    /api/v1/users/{id}/orders     # 获取用户的订单列表
POST   /api/v1/users/{id}/orders     # 为用户创建订单

# 查询参数
GET    /api/v1/users?page=1&size=20&status=active
GET    /api/v1/orders?start_date=2024-01-01&end_date=2024-12-31
```

### HTTP 状态码规范
```
# 成功响应
200 OK          # 请求成功
201 Created     # 资源创建成功
204 No Content  # 请求成功但无返回内容

# 客户端错误
400 Bad Request          # 请求参数错误
401 Unauthorized         # 未授权
403 Forbidden           # 权限不足
404 Not Found           # 资源不存在
409 Conflict            # 资源冲突
422 Unprocessable Entity # 请求格式正确但语义错误

# 服务器错误
500 Internal Server Error # 服务器内部错误
502 Bad Gateway          # 网关错误
503 Service Unavailable  # 服务不可用
```

## 请求响应格式规范

### 统一响应格式
```json
{
  "code": 0,
  "message": "success",
  "data": {
    // 具体数据
  },
  "timestamp": "2024-01-01T12:00:00Z",
  "requestId": "uuid-string"
}
```

### 分页响应格式
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "list": [
      // 数据列表
    ],
    "pagination": {
      "page": 1,
      "size": 20,
      "total": 100,
      "totalPages": 5
    }
  }
}
```

### 错误响应格式
```json
{
  "code": 400,
  "message": "参数验证失败",
  "data": null,
  "errors": [
    {
      "field": "email",
      "message": "邮箱格式不正确"
    },
    {
      "field": "password",
      "message": "密码长度不能少于8位"
    }
  ],
  "timestamp": "2024-01-01T12:00:00Z",
  "requestId": "uuid-string"
}
```

## GoFrame API 实现规范

### 控制器实现
```go
package controller

import (
    "context"
    "github.com/gogf/gf/v2/frame/g"
    v1 "github.com/imhuso/lookah-erp/admin/api/v1"
)

type cUser struct{}

var User = cUser{}

// List 获取用户列表
func (c *cUser) List(ctx context.Context, req *v1.UserListReq) (res *v1.UserListRes, err error) {
    // 参数验证
    if err = g.Validator().Data(req).Run(ctx); err != nil {
        return nil, err
    }
    
    // 调用服务层
    result, err := service.User().List(ctx, &model.UserListInput{
        Page:   req.Page,
        Size:   req.Size,
        Name:   req.Name,
        Status: req.Status,
    })
    if err != nil {
        return nil, err
    }
    
    return &v1.UserListRes{
        List:  result.List,
        Total: result.Total,
    }, nil
}

// Create 创建用户
func (c *cUser) Create(ctx context.Context, req *v1.UserCreateReq) (res *v1.UserCreateRes, err error) {
    if err = g.Validator().Data(req).Run(ctx); err != nil {
        return nil, err
    }
    
    id, err := service.User().Create(ctx, &model.UserCreateInput{
        Name:     req.Name,
        Email:    req.Email,
        Password: req.Password,
        Status:   req.Status,
    })
    if err != nil {
        return nil, err
    }
    
    return &v1.UserCreateRes{Id: id}, nil
}
```

### API 版本定义
```go
// api/v1/user.go
package v1

import "github.com/gogf/gf/v2/frame/g"

// UserListReq 用户列表请求
type UserListReq struct {
    g.Meta `path:"/users" method:"get" tags:"用户管理" summary:"获取用户列表"`
    Page   int    `json:"page" v:"min:1" dc:"页码"`
    Size   int    `json:"size" v:"min:1|max:100" dc:"每页数量"`
    Name   string `json:"name" dc:"用户名称"`
    Status int    `json:"status" v:"in:0,1" dc:"用户状态"`
}

type UserListRes struct {
    List  []*entity.User `json:"list" dc:"用户列表"`
    Total int            `json:"total" dc:"总数"`
}

// UserCreateReq 创建用户请求
type UserCreateReq struct {
    g.Meta   `path:"/users" method:"post" tags:"用户管理" summary:"创建用户"`
    Name     string `json:"name" v:"required|length:2,50" dc:"用户名称"`
    Email    string `json:"email" v:"required|email" dc:"邮箱地址"`
    Password string `json:"password" v:"required|length:8,32" dc:"密码"`
    Status   int    `json:"status" v:"required|in:0,1" dc:"用户状态"`
}

type UserCreateRes struct {
    Id uint64 `json:"id" dc:"用户ID"`
}
```

## 前端 API 调用规范

### API 服务封装
```typescript
// services/user-api.ts
import { http } from '/@/utils/http'

export interface UserListParams {
  page: number
  size: number
  name?: string
  status?: number
}

export interface UserInfo {
  id: number
  name: string
  email: string
  status: number
  createdAt: string
  updatedAt: string
}

export interface UserCreateData {
  name: string
  email: string
  password: string
  status: number
}

export class UserApi {
  // 获取用户列表
  static async list(params: UserListParams) {
    return http.get<{
      list: UserInfo[]
      total: number
    }>('/api/v1/users', { params })
  }
  
  // 获取单个用户
  static async get(id: number) {
    return http.get<UserInfo>(`/api/v1/users/${id}`)
  }
  
  // 创建用户
  static async create(data: UserCreateData) {
    return http.post<{ id: number }>('/api/v1/users', data)
  }
  
  // 更新用户
  static async update(id: number, data: Partial<UserCreateData>) {
    return http.put<UserInfo>(`/api/v1/users/${id}`, data)
  }
  
  // 删除用户
  static async delete(id: number) {
    return http.delete(`/api/v1/users/${id}`)
  }
}
```

### HTTP 客户端配置
```typescript
// utils/http.ts
import axios, { AxiosResponse } from 'axios'
import { ElMessage } from 'element-plus'

// 统一响应接口
interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  timestamp: string
  requestId: string
}

// 创建 axios 实例
const http = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器
http.interceptors.request.use(
  (config) => {
    // 添加认证 token
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    
    // 添加请求 ID
    config.headers['X-Request-ID'] = generateRequestId()
    
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
http.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    const { code, message, data } = response.data
    
    if (code === 0) {
      return data
    } else {
      ElMessage.error(message || '请求失败')
      return Promise.reject(new Error(message))
    }
  },
  (error) => {
    if (error.response) {
      const { status, data } = error.response
      
      switch (status) {
        case 401:
          ElMessage.error('未授权，请重新登录')
          // 跳转到登录页
          break
        case 403:
          ElMessage.error('权限不足')
          break
        case 404:
          ElMessage.error('请求的资源不存在')
          break
        case 500:
          ElMessage.error('服务器内部错误')
          break
        default:
          ElMessage.error(data?.message || '请求失败')
      }
    } else {
      ElMessage.error('网络错误')
    }
    
    return Promise.reject(error)
  }
)

function generateRequestId(): string {
  return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
}

export { http }
```

## 参数验证规范

### 后端参数验证
```go
// 使用 GoFrame 验证标签
type UserCreateReq struct {
    Name     string `json:"name" v:"required|length:2,50#用户名不能为空|用户名长度为2-50个字符"`
    Email    string `json:"email" v:"required|email#邮箱不能为空|邮箱格式不正确"`
    Password string `json:"password" v:"required|length:8,32#密码不能为空|密码长度为8-32个字符"`
    Age      int    `json:"age" v:"min:18|max:100#年龄不能小于18岁|年龄不能大于100岁"`
    Status   int    `json:"status" v:"required|in:0,1#状态不能为空|状态值不正确"`
}

// 自定义验证规则
func init() {
    // 注册自定义验证规则
    gvalid.RegisterRule("mobile", func(ctx context.Context, in gvalid.RuleFuncInput) error {
        mobile := gconv.String(in.Value.Val())
        if !gstr.IsNumeric(mobile) || len(mobile) != 11 {
            return errors.New("手机号格式不正确")
        }
        return nil
    })
}
```

### 前端参数验证
```typescript
// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' },
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' },
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 8, max: 32, message: '密码长度为 8-32 个字符', trigger: 'blur' },
    { pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, message: '密码必须包含大小写字母和数字', trigger: 'blur' },
  ],
}
```

## 错误处理规范

### 错误码定义
```go
// 业务错误码
const (
    CodeSuccess           = 0     // 成功
    CodeInvalidParameter  = 1001  // 参数错误
    CodeUserNotFound     = 1002  // 用户不存在
    CodeUserExists       = 1003  // 用户已存在
    CodePasswordWrong    = 1004  // 密码错误
    CodeTokenInvalid     = 1005  // Token 无效
    CodePermissionDenied = 1006  // 权限不足
)

// 错误信息映射
var ErrorMessages = map[int]string{
    CodeSuccess:           "成功",
    CodeInvalidParameter:  "参数错误",
    CodeUserNotFound:     "用户不存在",
    CodeUserExists:       "用户已存在",
    CodePasswordWrong:    "密码错误",
    CodeTokenInvalid:     "Token 无效",
    CodePermissionDenied: "权限不足",
}
```

## API 文档规范

### Swagger 注释
```go
// @Summary 获取用户列表
// @Description 分页获取用户列表，支持按名称和状态筛选
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param size query int false "每页数量" default(20)
// @Param name query string false "用户名称"
// @Param status query int false "用户状态" Enums(0, 1)
// @Success 200 {object} v1.UserListRes "成功"
// @Failure 400 {object} response.ErrorResponse "参数错误"
// @Failure 500 {object} response.ErrorResponse "服务器错误"
// @Router /api/v1/users [get]
func (c *cUser) List(ctx context.Context, req *v1.UserListReq) (res *v1.UserListRes, err error) {
    // 实现代码
}
```

## 性能优化规范

### 缓存策略
```go
// Redis 缓存用户信息
func (s *sUser) GetById(ctx context.Context, id uint64) (*entity.User, error) {
    cacheKey := fmt.Sprintf("user:%d", id)
    
    // 先从缓存获取
    var user *entity.User
    err := g.Redis().Get(ctx, cacheKey, &user)
    if err == nil && user != nil {
        return user, nil
    }
    
    // 缓存未命中，从数据库获取
    err = dao.User.Ctx(ctx).Where("id = ?", id).Scan(&user)
    if err != nil {
        return nil, err
    }
    
    // 写入缓存，过期时间 1 小时
    g.Redis().Set(ctx, cacheKey, user, time.Hour)
    
    return user, nil
}
```

### 分页优化
```go
// 使用游标分页优化大数据量查询
func (s *sUser) ListByCursor(ctx context.Context, cursor uint64, size int) ([]*entity.User, error) {
    query := dao.User.Ctx(ctx).Where("status = ?", 1)
    
    if cursor > 0 {
        query = query.Where("id > ?", cursor)
    }
    
    var users []*entity.User
    err := query.OrderBy("id ASC").Limit(size).Scan(&users)
    
    return users, err
}
```