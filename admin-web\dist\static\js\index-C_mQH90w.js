import{K as ae,c as A,k as ye,n as Ce,b as f,U as ne,r as He,D as Ae,f as P,h as e,i,w as t,q as E,B as Y,v as l,y as u,F as re,s as ue,j as S,E as W,o as r,t as V,bb as at,af as $e,ag as Ve,S as Re,bC as dt,G as De,H as Ue,J as Me,x as de,V as pe,W as ct,T as ze,e as _e,I as pt,z as ft,Y as se,l as Fe,X as mt,m as be,bD as _t,aS as vt,ae as Oe,L as We}from"./.pnpm-hVqhwuVC.js";import{c as Je,i as Pe,e as yt,s as ee}from"./index-BtOcqcNl.js";import{a as Ee}from"./index-D95m1iJL.js";import{_ as Se}from"./index.vue_vue_type_script_setup_true_name_cl-avatar_lang-Dwbve7L9.js";import{d as we,a as me,e as bt}from"./index-Dw7jsygE.js";import{_ as fe}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{U as ge}from"./user-avatar-DTjmVWm6.js";import{u as ht}from"./table-ops-mcGHjph4.js";import{p as Te,D as ot}from"./daliy-BvpLBvhF.js";import nt from"./daliy-form-DA8T3Bt3.js";import gt from"./group-BgvLKoXk.js";import{_ as kt}from"./select-user.vue_vue_type_script_setup_true_name_select-user_lang-Dw22Jqfu.js";import"./text-overflow-tooltip.vue_vue_type_script_setup_true_name_text-overflow-tooltip_lang-D_kIBTxD.js";const Le={finish:"#67c23a",panding:"#0488de",overdue:"#f56c6c",pause:"#909399",cancel:"#909399"},Ye=[{label:"进行中",value:4,type:"primary",color:"#2d8cf0"},{label:"完成",value:5,type:"success",color:"#67c23a"},{label:"取消",value:6,type:"danger",color:"#909399"},{label:"暂停",value:7,type:"info",color:"#909399"}];function he(w){const B=Ye.concat([{label:"审批中",value:-1,type:"info",color:"#909399"}]);return w?B.find(d=>d.value===w):{type:"info",label:""}}const Ke=[{value:4,color:"#e95648",label:"紧急"},{value:3,color:"#ff9d28",label:"高"},{value:2,color:"#0488de",label:"中"},{value:1,color:"#5eb95e",label:"低"}];function xe(w){return w?Ke.find(B=>B.value===w)||{}:{}}function Ne(w){return w?ae(w).format("YYYY-MM-DD"):""}const wt={flex:"~ row justify-between"},xt={flex:"~ col 1","h-full":""},Tt={flex:"~ col"},$t={mb3:""},Vt={ml10:"",class:"w-450px"},It={flex:"~ row items-center"},Dt={ml2:""};const Ct={class:"flex items-center"},Lt=A({name:"my-task-form"}),Ut=A({...Lt,props:ye({workItem:{default:()=>({})},type:{},refresh:{type:Function,default:()=>{}}},{modelValue:{type:Boolean},modelModifiers:{}}),emits:["update:modelValue"],setup(w,{expose:B}){var K;const d=w,g=Ce(w,"modelValue"),D=f(ne(Ye)),{service:h}=Ee(),{user:p}=Je(),x=f(!1),o=f(!0),s=f({title:"",content:"",status:void 0,workItemType:"",responsiblePersonId:"",participantIds:[],priority:"",planStartTime:"",planEndTime:"",workDay:0}),a=((K=p.info)==null?void 0:K.id)||null,_=f([]),y=f([]);function $(c,m){at(c)||!m||c.forEach(C=>{C.id===m&&(C.disabled=!0)})}const k=f(null),I=f(),b=He({title:[{required:!0,message:"请输入标题",trigger:"blur"}],responsiblePersonId:[{required:!0,message:"请选择负责人",trigger:"submit"}],priority:[{required:!0,message:"请选择优先级",trigger:"submit"}],planStartTime:[{required:!0,message:"请选择计划开始时间",trigger:"submit"}],planEndTime:[{required:!0,message:"请选择计划完成时间",trigger:"submit"}]});async function J(){var c;return(c=I.value)==null?void 0:c.validate()}function H(c){k.value=c}function Q(){if(s.value.planEndTime&&s.value.planStartTime){const c=ae(s.value.planStartTime).format("YYYY-MM-DD"),m=ae(s.value.planEndTime).format("YYYY-MM-DD");let C=ae(m).diff(c,"day");C=Math.abs(C);let n=0;for(let v=0;v<C;v++){const L=ae(s.value.planStartTime).add(1,"day").format("YYYY-MM-DD");we(L)||n++}s.value.workDay=n}}function te(){if(s.value.planStartTime&&s.value.workDay===0&&(s.value.planEndTime=s.value.planStartTime),s.value.planStartTime&&s.value.workDay){let c=0,m=s.value.planStartTime;for(;c<s.value.workDay;)m=ae(m).add(1,"day").format("YYYY-MM-DD"),!we(m)&&c++;s.value.planEndTime=m}!s.value.workDay&&s.value.planStartTime&&s.value.planEndTime&&Q()}function le(c){let m;return m=we(c),m||s.value.planEndTime&&(m=c.getTime()>new Date(s.value.planEndTime).getTime(),m)?m:c.getTime()<Date.now()-864e5}function O(c){const m=we(c);return m||c.getTime()<Date.now()-864e5}function q(c){c.length===0&&_.value.forEach(m=>{m.disabled=!1}),c.forEach(m=>{_.value.forEach(C=>{C.disabled=C.id===m})})}const z=f(!1);function F(){var c,m;return d.workItem.id?!!(((c=d.workItem)==null?void 0:c.status)===5||a&&((m=d.workItem)==null?void 0:m.creatorId)!==a):!1}function U(){s.value={},g.value=!1}async function R(){if(await J())try{x.value=!0,s.value.id?await h.pims.workitem.update(s.value):(s.value.type=2,await h.pims.workitem.add(s.value)),W.success("保存成功"),U(),d.refresh()}catch(m){W.error(m.message)}finally{x.value=!0}}async function j(){var m;Object.keys(d.workItem).length>0&&(s.value=d.workItem,s.value.participantIds=(m=d.workItem.participants)==null?void 0:m.map(C=>C.participantId)),z.value=F();const c=await h.pims.workitem.systemUserList();_.value=c,y.value=ne(c),a&&!s.value.responsiblePersonId&&(_!=null&&_.value.some(n=>n.id===a))&&(s.value.responsiblePersonId=a),s.value.status===5&&(o.value=!1),$(y.value,a)}return Ae(async()=>{await j()}),B({callValid:J,taskForm:s}),(c,m)=>{const C=i("el-input"),n=i("el-form-item"),v=i("cl-editor-wang"),L=i("el-option"),Z=i("el-select"),N=i("el-tag"),ie=i("el-date-picker"),oe=i("el-input-number"),G=i("el-divider"),ce=i("el-form"),ve=i("el-button"),Ie=i("el-dialog");return r(),P("div",null,[e(Ie,{modelValue:g.value,"onUpdate:modelValue":m[9]||(m[9]=M=>g.value=M),"before-close":U,title:"我的任务",width:"50%","close-on-click-modal":!1},{footer:t(()=>[e(ve,{onClick:U},{default:t(()=>[S(" 取 消 ")]),_:1}),l(o)?(r(),E(ve,{key:0,type:"success",loading:l(x),onClick:R},{default:t(()=>[S(" 确 定 ")]),_:1},8,["loading"])):Y("",!0)]),default:t(()=>[g.value?(r(),E(ce,{key:0,ref_key:"addWorkItemForm",ref:I,rules:l(b),model:l(s),"label-width":"120px","label-position":"left",mt4:"","require-asterisk-position":"right"},{default:t(()=>[u("div",wt,[u("div",xt,[u("div",Tt,[u("div",$t,[e(n,{label:"",prop:"title","label-width":"0"},{default:t(()=>[e(C,{modelValue:l(s).title,"onUpdate:modelValue":m[0]||(m[0]=M=>l(s).title=M),placeholder:"请输入标题",h11:"","font-size-4":"","font-750":"",maxlength:"100",disabled:l(z)},null,8,["modelValue","disabled"])]),_:1})]),e(v,{modelValue:l(s).content,"onUpdate:modelValue":m[1]||(m[1]=M=>l(s).content=M),disabled:l(z),height:"301px",onOnCreated:H},null,8,["modelValue","disabled"])])]),u("div",Vt,[e(n,{label:"负责人",prop:"responsiblePersonId"},{default:t(()=>[e(Z,{modelValue:l(s).responsiblePersonId,"onUpdate:modelValue":m[2]||(m[2]=M=>l(s).responsiblePersonId=M),placeholder:"请选择",filterable:"",disabled:""},{default:t(()=>[(r(!0),P(re,null,ue(l(_),M=>(r(),E(L,{key:M.id,label:M.name,value:M.id,disabled:M.disabled},{default:t(()=>[u("div",It,[e(Se,{src:M.headImg,size:30,title:M.name},null,8,["src","title"]),u("span",Dt,V(M.name),1)])]),_:2},1032,["label","value","disabled"]))),128))]),_:1},8,["modelValue"])]),_:1}),Y("",!0),e(n,{label:"优先级",prop:"priority"},{default:t(()=>[e(Z,{modelValue:l(s).priority,"onUpdate:modelValue":m[4]||(m[4]=M=>l(s).priority=M),placeholder:"请选择",disabled:l(z)},{default:t(()=>[(r(!0),P(re,null,ue(l(Ke),M=>(r(),E(L,{key:M.value,label:M.label,value:M.value},{default:t(()=>[u("div",Ct,[e(N,{color:M.color,style:{"margin-right":"8px"},size:"small"},null,8,["color"]),u("span",null,V(M.label),1)])]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1}),l(s).id>0?(r(),E(n,{key:1,label:"任务状态",prop:"status",rules:[{required:!0,message:"请选择状态",trigger:"submit"}]},{default:t(()=>[e(Z,{modelValue:l(s).status,"onUpdate:modelValue":m[5]||(m[5]=M=>l(s).status=M),placeholder:"请选择状态",disabled:l(z)},{default:t(()=>[(r(!0),P(re,null,ue(l(D),M=>(r(),E(L,{key:M.value,label:M.label,value:M.value},{default:t(()=>[e(N,{type:M.type,effect:"dark"},{default:t(()=>[S(V(M.label),1)]),_:2},1032,["type"])]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})):Y("",!0),e(n,{label:"计划开始时间",prop:"planStartTime"},{default:t(()=>[e(ie,{modelValue:l(s).planStartTime,"onUpdate:modelValue":m[6]||(m[6]=M=>l(s).planStartTime=M),type:"date",disabled:l(z),format:"YYYY-MM-DD",placeholder:"请选择日期","disabled-date":le,onChange:Q},null,8,["modelValue","disabled"])]),_:1}),e(n,{label:"工期",rules:[{required:!0,message:"工期不能为空",trigger:"submit"}]},{default:t(()=>[e(oe,{modelValue:l(s).workDay,"onUpdate:modelValue":m[7]||(m[7]=M=>l(s).workDay=M),min:0,max:999,precision:0,disabled:l(z),onChange:te},null,8,["modelValue","disabled"])]),_:1}),e(n,{label:"计划完成时间",prop:"planEndTime"},{default:t(()=>[e(ie,{modelValue:l(s).planEndTime,"onUpdate:modelValue":m[8]||(m[8]=M=>l(s).planEndTime=M),type:"date",format:"YYYY-MM-DD",disabled:l(z),placeholder:"请选择日期","disabled-date":O,onChange:Q},null,8,["modelValue","disabled"])]),_:1}),e(G,{mb:"10px"})])])]),_:1},8,["rules","model"])):Y("",!0)]),_:1},8,["modelValue"])])}}}),Mt=fe(Ut,[["__scopeId","data-v-d4e3152e"]]),St=["innerHTML"],Rt=A({name:"task-progress-table"}),Ge=A({...Rt,props:{tableData:{default:()=>[]}},setup(w){const B=w;return(d,g)=>{const D=i("el-table-column"),h=i("el-table");return r(),P("div",null,[e(h,{data:B.tableData,style:{width:"100%"},class:"cl-table"},{default:t(()=>[e(D,{prop:"nodeName",label:"节点",align:"center",width:"200"},{default:t(({row:p})=>[u("span",null,V(p.nodeName),1)]),_:1}),e(D,{prop:"progress",label:"进度",align:"center","show-overflow-tooltip":"",width:"80"},{default:t(({row:p})=>[u("span",null,V(`${p.progress}%`),1)]),_:1}),e(D,{prop:"createTime",label:"创建时间",align:"center","show-overflow-tooltip":"",width:"200"},{default:t(({row:p})=>[u("span",null,V(p.createTime),1)]),_:1}),e(D,{prop:"desc",label:"描述",align:"center","show-overflow-tooltip":""},{default:t(({row:p})=>[u("span",{innerHTML:p.desc},null,8,St)]),_:1})]),_:1},8,["data"])])}}}),Et=["innerHTML"],Yt=A({name:"task-aduit-table"}),je=A({...Yt,props:{tableData:{default:()=>[]}},setup(w){const B=w,d={1:"任务",2:"日报",3:"任务抄送",4:"提交"};function g(h){return d[h.type]}function D(h){return h.type===4?"":h.agree==="1"?"同意":"驳回"}return(h,p)=>{const x=i("el-table-column"),o=i("el-table");return r(),P("div",null,[e(o,{data:B.tableData,style:{width:"100%"},class:"cl-table"},{default:t(()=>[e(x,{prop:"node",label:"类型",align:"center","show-overflow-tooltip":"",width:"100"},{default:t(({row:s})=>[u("span",null,V(g(s)),1)]),_:1}),e(x,{prop:"agree",label:"审批结果",align:"center",width:"80"},{default:t(({row:s})=>[u("span",null,V(D(s)),1)]),_:1}),e(x,{prop:"createTime",label:"审批时间",align:"center","show-overflow-tooltip":"",width:"200"},{default:t(({row:s})=>[u("span",null,V(s.createTime),1)]),_:1}),e(x,{prop:"node",label:"审批人",align:"center","show-overflow-tooltip":"",width:"80"},{default:t(({row:s})=>[e(ge,{user:s==null?void 0:s.approverInfo,"show-name":!1,flex:"~ justify-center items-center"},null,8,["user"])]),_:1}),e(x,{prop:"opinion",label:"审批意见",align:"center","show-overflow-tooltip":""},{default:t(({row:s})=>[u("span",{innerHTML:s.opinion},null,8,Et)]),_:1})]),_:1},8,["data"])])}}}),Bt=["innerHTML"],Pt=A({name:"task-report-table"}),qe=A({...Pt,props:{tableData:{default:()=>[]}},setup(w){const B=w;function d(D){if(!D.workTimeRange)return"";const h=JSON.parse(D.workTimeRange);return`${ae(h[0]).format("HH:mm")}-${ae(h[1]).format("HH:mm")} ${D.estimatedWorkNum}`}function g(D,h=60,p=60){return D.replace(/<img/g,`<img style="width: ${h}px; height: ${p}px;"`)}return(D,h)=>{const p=i("el-table-column"),x=i("el-table");return r(),P("div",null,[e(x,{data:B.tableData,style:{width:"100%"},class:"cl-table"},{default:t(()=>[e(p,{prop:"jobDescription",label:"工作事项",align:"center",width:"280","show-overflow-tooltip":""},{default:t(({row:o})=>[u("span",null,V(o.jobDescription),1)]),_:1}),e(p,{prop:"workTimeRange",label:"工时(h)",align:"center",width:"150"},{default:t(({row:o})=>[u("span",null,V(d(o)),1)]),_:1}),e(p,{prop:"createTime",label:"创建时间",align:"center","show-overflow-tooltip":"",width:"200"},{default:t(({row:o})=>[u("span",null,V(o.createTime),1)]),_:1}),e(p,{prop:"jobDetail",label:"工作详情",align:"center","show-overflow-tooltip":""},{default:t(({row:o})=>[u("span",{innerHTML:g(o.jobDetail)},null,8,Bt)]),_:1})]),_:1},8,["data"])])}}}),Xe=w=>($e("data-v-e978ee9a"),w=w(),Ve(),w),Nt=Xe(()=>u("div",null,"进展信息",-1)),zt=Xe(()=>u("div",null,"工作日记",-1)),Ft=Xe(()=>u("div",null,"审批记录",-1)),jt=A({name:"task-full-table"}),qt=A({...jt,props:ye({title:{default:""},tableData:{}},{modelValue:{type:Boolean},modelModifiers:{}}),emits:["update:modelValue"],setup(w){const B=w,d=Ce(w,"modelValue");return(g,D)=>{const h=i("el-card"),p=i("el-table-column"),x=i("el-tag"),o=i("el-table"),s=i("el-dialog");return r(),E(s,{modelValue:d.value,"onUpdate:modelValue":D[0]||(D[0]=a=>d.value=a),title:B.title,"append-to-body":"",width:"60%"},{default:t(()=>[e(o,{data:B.tableData,style:{width:"100%"}},{default:t(()=>[e(p,{type:"expand",align:"center",width:"60"},{default:t(({row:a})=>[a.progressLogList&&a.progressLogList.length>0?(r(),E(h,{key:0},{header:t(()=>[Nt]),default:t(()=>[e(Ge,{"table-data":a.progressLogList},null,8,["table-data"])]),_:2},1024)):Y("",!0),a.daliyReport&&a.daliyReport.length>0?(r(),E(h,{key:1,mt:"20px"},{header:t(()=>[zt]),default:t(()=>[e(qe,{"table-data":a.daliyReport,mt:"20px"},null,8,["table-data"])]),_:2},1024)):Y("",!0),a.auditLogList&&a.auditLogList.length>0?(r(),E(h,{key:2,mt:"20px"},{header:t(()=>[Ft]),default:t(()=>[e(je,{"table-data":a.auditLogList,mt:"20px"},null,8,["table-data"])]),_:2},1024)):Y("",!0)]),_:1}),e(p,{prop:"title",label:"任务名称","show-overflow-tooltip":"",flex:"flex"}),e(p,{prop:"participantIds",label:"进度",align:"center",width:"80"},{default:t(({row:a})=>[S(V(`${a.progress||0}%`),1)]),_:1}),e(p,{prop:"participantIds",label:"工时(h)",align:"center",width:"80"},{default:t(({row:a})=>[S(V(a.estimatedWorkNum||0),1)]),_:1}),e(p,{prop:"creatorId",label:"创建人",align:"center",width:"80"},{default:t(({row:a})=>[e(ge,{user:a==null?void 0:a.creator,"show-name":!1,flex:"~ justify-center items-center"},null,8,["user"])]),_:1}),e(p,{prop:"responsiblePersonId",label:"负责人",align:"center",width:"80"},{default:t(({row:a})=>[e(ge,{user:a==null?void 0:a.responsiblePerson,"show-name":!1,flex:"~ justify-center items-center"},null,8,["user"])]),_:1}),e(p,{prop:"priority",label:"优先级",align:"center",width:"80"},{default:t(({row:a})=>[e(x,{color:l(xe)(a.priority).color,effect:"dark",type:"info"},{default:t(()=>[S(V(l(xe)(a.priority).label),1)]),_:2},1032,["color"])]),_:1}),e(p,{prop:"status",label:"状态",align:"center",width:"80"},{default:t(({row:a})=>[e(x,{type:l(he)(a.status).type,effect:"dark"},{default:t(()=>[S(V(l(he)(a.status).label),1)]),_:2},1032,["type"])]),_:1}),e(p,{prop:"planStartTime",label:"计划开始时间",align:"center",width:"120"},{default:t(({row:a})=>[S(V(l(Ne)(a.planStartTime)),1)]),_:1}),e(p,{prop:"planEndTime",label:"计划完成时间",align:"center",width:"120"},{default:t(({row:a})=>[S(V(l(Ne)(a.planEndTime)),1)]),_:1}),e(p,{prop:"workDay",label:"工期",align:"center",width:"100"},{default:t(({row:a})=>[S(V(a.workDay||"0天"),1)]),_:1})]),_:1},8,["data"])]),_:1},8,["modelValue","title"])}}}),Ht=fe(qt,[["__scopeId","data-v-e978ee9a"]]),st=w=>($e("data-v-d95c6dfb"),w=w(),Ve(),w),At={key:0,class:"relation_graph_card",w:"400px"},Ot={ml:"4","py-2":"","text-left":""},Wt={"flex-1":""},Jt={class:"title",style:{width:"180px",color:"#000"},color:"#ccc"},Kt={w:"180px",flex:"~ items-center justify-between"},Gt={flex:"~ items-center justify-between"},Xt={"text-right":""},Zt={key:1,class:"relation_graph_card"},Qt={class:"level1"},el={"text-left":"",color:"#575757",class:"title"},tl={class:"level2"},ll={flex:"~ items-center",p:"1",h:"30px"},al=st(()=>u("span",{w:"60px","text-left":"",color:"#8b8b8b"},"负责人",-1)),ol={flex:"1 ~ items-center"},nl={flex:"~ items-center",p:"1",h:"30px"},sl=st(()=>u("span",{w:"60px","text-left":"",color:"#8b8b8b"},"状态",-1)),il={flex:"1 ~ items-center"},rl=A({name:"undefined"}),ul=A({...rl,props:{id:{type:Number,default:null}},setup(w){const B=w,{service:d}=Ee(),g=f(),D=f(),h={debug:!1,disableDragNode:!0,allowShowMiniToolBar:!0,defaultExpandHolderPosition:"bottom",backgrounImageNoRepeat:!0,layouts:[{label:"中心",layoutName:"tree",from:"top",layoutClassName:"seeks-layout-center",defaultJunctionPoint:"border",defaultNodeShape:0,defaultLineShape:1,min_per_width:"200",max_per_width:"500",levelDistance:""}],defaultLineMarker:{markerWidth:20,markerHeight:20,refX:3,refY:3,data:"M 0 0, V 6, L 4 3, Z"}};return Re(()=>{d.pims.workitem.panorama({id:B.id}).then(p=>{var o,s,a,_;console.log(p);let x={nodes:[],lines:[]};if(x.nodes.push({id:0,text:p.title,type:"parent",data:{responsiblePerson:p.responsiblePerson,status:he(p.status)}}),((o=p==null?void 0:p.children)==null?void 0:o.length)>0&&p.children.forEach((y,$)=>{const k=$+1;x.nodes.push({id:k,text:y.title,type:"child",data:{responsiblePerson:y.responsiblePerson,status:he(p.status)}}),x.lines.push({from:"0",to:k.toString()})}),((s=p==null?void 0:p.relates)==null?void 0:s.length)>0){const y=x.nodes.length,$={id:y,text:"关联项",fixed:!0,x:x.nodes.length===1?150:x.nodes.length*100,y:x.nodes.length===1?-350:-300,type:"relate",data:{relateList:((a=p.relates)==null?void 0:a.map(k=>({text:k.title,type:"relate",responsiblePerson:k.responsiblePerson||{},status:he(k.status)||{type:"info",label:""}})))||[]}};x.nodes.push($),x.lines.push({from:"0",to:y.toString()})}(_=g==null?void 0:g.value)==null||_.setJsonData(x)})}),(p,x)=>{const o=i("el-tag"),s=i("el-tooltip");return r(),P("div",null,[u("div",{ref_key:"myPage",ref:D,style:{border:"#efefef solid 1px",height:"calc(100vh - 100px)",width:"100%"}},[e(l(dt),{ref_key:"graphRef$",ref:g,options:h},{node:t(({node:a})=>{var _,y,$;return[(a==null?void 0:a.type)==="relate"?(r(),P("div",At,[u("div",Ot,[e(o,{effect:"light",type:"info"},{default:t(()=>[S(" 关联项 ")]),_:1})]),(r(!0),P(re,null,ue(a==null?void 0:a.data.relateList,k=>{var I;return r(),P("div",{style:{"border-top":"1px #ccc solid"},flex:"~ items-center justify-between",px:"4",py:"1",key:k.id},[u("div",Wt,[e(s,{effect:"dark",content:a==null?void 0:a.text,placement:"top-start"},{default:t(()=>[u("div",Jt,V(k==null?void 0:k.text),1)]),_:2},1032,["content"])]),u("div",Kt,[u("div",Gt,[e(ge,{size:30,"show-name":!1,user:k==null?void 0:k.responsiblePerson},null,8,["user"])]),u("div",Xt,[e(o,{effect:"dark",type:(I=k==null?void 0:k.status)==null?void 0:I.type,mr:"6"},{default:t(()=>{var b;return[S(V((b=k==null?void 0:k.status)==null?void 0:b.label),1)]}),_:2},1032,["type"])])])])}),128))])):(r(),P("div",Zt,[u("div",Qt,[e(o,{effect:"light",type:"primary",mr:"6"},{default:t(()=>[S(V(a.type==="parent"?"工作项":"子项"),1)]),_:2},1024),e(s,{effect:"dark",content:a==null?void 0:a.text,placement:"top-start"},{default:t(()=>[u("div",null,[u("div",el,V(a==null?void 0:a.text),1)])]),_:2},1032,["content"])]),u("div",tl,[u("div",ll,[al,u("div",ol,[e(ge,{size:30,"show-name":!1,user:(_=a==null?void 0:a.data)==null?void 0:_.responsiblePerson},null,8,["user"])])]),u("div",nl,[sl,u("div",il,[(y=a==null?void 0:a.data)!=null&&y.status?(r(),E(o,{key:0,effect:"dark",type:($=a==null?void 0:a.data)==null?void 0:$.status.type},{default:t(()=>{var k;return[S(V((k=a==null?void 0:a.data)==null?void 0:k.status.label),1)]}),_:2},1032,["type"])):Y("",!0)])])])]))]}),_:1},512)],512)])}}}),dl=fe(ul,[["__scopeId","data-v-d95c6dfb"]]),cl={key:0},pl=["onClick"],fl={key:1,flex:"~  items-center"},ml=["onClick"],_l=A({name:"workitem-nav-list"}),vl=A({..._l,props:ye({id:{},list:{},type:{default:"text"},showLastBtn:{type:Boolean,default:!0}},{modelValue:{},modelModifiers:{}}),emits:ye(["update:modelValue","change"],["update:modelValue"]),setup(w,{emit:B}){const{service:d}=Ee(),g=w,D=f(!1),h=B,p=Ce(w,"modelValue");function x(o){h("update:modelValue",o.value),h("change",o)}return(o,s)=>{const a=i("el-button"),_=i("cl-dialog"),y=Ue("permission");return r(),P("div",null,[g.type==="text"?(r(),P("div",cl,[(r(!0),P(re,null,ue(g.list,($,k)=>(r(),P("span",{key:k,style:pe(`color: ${p.value===$.value?"#2d8cf0":""}`),"cursor-pointer":"",px2:"",py1:"","text-size-14px":"","b-r":"~ 1px solid #efefef",onClick:I=>x($)},V($.label),13,pl))),128))])):(r(),P("span",fl,[u("div",null,[(r(!0),P(re,null,ue(g.list,($,k)=>(r(),P("span",{key:k,class:ct(["base",[p.value===$.value?"active":"default"]]),"cursor-pointer":"",px2:"",py1:"","text-size-14px":"",onClick:I=>x($)},V($.label),11,ml))),128))]),De((r(),E(a,{onClick:s[0]||(s[0]=$=>D.value=!0)},{default:t(()=>[S("全景图")]),_:1})),[[y,l(d).pims.workitem.permission.panorama]]),u("div",null,[Me(o.$slots,"default",{},void 0,!0)])])),e(_,{modelValue:l(D),"onUpdate:modelValue":s[1]||(s[1]=$=>de(D)?D.value=$:null),title:"全景图",width:"80%",height:"80%"},{default:t(()=>[e(dl,{id:g.id},null,8,["id"])]),_:1},8,["modelValue"])])}}}),yl=fe(vl,[["__scopeId","data-v-04d90e91"]]),bl={flex:"~",py:"3"},hl={flex:"~ col items-center"},gl=u("div",{"h-full":"",my:"1","b-l":"~ 1px solid #cfd1d7"},null,-1),kl={flex:"1"},wl={flex:"~ items-center",ml2:"","lh-30px":""},xl={"font-size-14px":""},Tl={ml2:"","font-size-3":""},$l={ml2:"","font-size-2":""},Vl={py:"2"},Il=["innerHTML"],Dl={key:0},Cl=["onClick"],Ll={"font-size-14px":""},Ul={key:2,"flex-basis":"100%",pl:"3",pt:"3",color:"#000","font-size-14px":""},Ml={key:0,mt:"2","font-size-3":""},Sl=A({name:"workitem-log-card"}),Qe=A({...Sl,props:{item:{},topText:{},showBtn:{type:Boolean,default:!0}},emits:["reply","navChange","edit","delete","toTop","refreshComment"],setup(w,{emit:B}){const d=w,g=B;function D(o){g("reply",o)}function h(o){g("toTop",o)}function p(o){ze.confirm("您确定删除这条评论吗?","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{g("delete",o)})}function x(o){o.arrowDir=!o.arrowDir}return(o,s)=>{var $,k,I,b,J;const a=i("ArrowUpBold"),_=i("el-icon"),y=i("ArrowDownBold");return r(),P("div",null,[u("div",bl,[u("div",hl,[e(ge,{user:($=d.item)==null?void 0:$.creator,size:30,"show-name":!1},null,8,["user"]),gl]),u("div",kl,[u("div",wl,[u("span",xl,V((I=(k=d.item)==null?void 0:k.creator)==null?void 0:I.name),1),u("span",Tl,V((b=d.item)==null?void 0:b.createTime),1),Me(o.$slots,"default")]),u("div",$l,[u("div",Vl,[u("div",{"text-size-14px":"",innerHTML:(J=d.item)==null?void 0:J.content},null,8,Il),d!=null&&d.item.replies?(r(),P("div",Dl,[(r(!0),P(re,null,ue(o.item.replies,H=>(r(),P("div",{key:H.id,bg:"#f2f5f7",color:"#adadad",flex:"~ row items-center wrap",py:"2","w-full":"","cursor-pointer":"","rd-2":"","font-size-4":"",my:"2",onClick:Q=>x(H)},[u("div",null,[H.arrowDir?(r(),E(_,{key:0,size:"14",px2:""},{default:t(()=>[e(a)]),_:1})):(r(),E(_,{key:1,size:"14",px2:""},{default:t(()=>[e(y)]),_:1})),u("span",Ll,V(H==null?void 0:H.creator.name)+" 回复",1),H.arrowDir?(r(),P("div",Ul,V(H.content),1)):Y("",!0)])],8,Cl))),128))])):Y("",!0)]),d.showBtn&&d.item.type===1?(r(),P("div",Ml,[d.showBtn?(r(),P("span",{key:0,color:"#526ad4","cursor-pointer":"",mr:"3",onClick:s[0]||(s[0]=H=>D(d.item))},"回复")):Y("",!0),d.showBtn?(r(),P("span",{key:1,color:"#526ad4","cursor-pointer":"",mr:"3",onClick:s[1]||(s[1]=H=>h(d.item))},V(d==null?void 0:d.topText),1)):Y("",!0),l(me)===d.item.creator.id&&d.showBtn?(r(),P("span",{key:2,color:"#526ad4","cursor-pointer":"",onClick:s[2]||(s[2]=H=>p(d.item))},"删除")):Y("",!0)])):Y("",!0)])])])])}}}),Rl={flex:"~ justify-end"},El={"w-full":"",flex:"~ justify-center"},Yl=A({name:"upload-table"}),Bl=A({...Yl,props:{workitem:{default:()=>({})},tableData:{default:()=>[]},type:{},showBtn:{type:Boolean,default:!0}},setup(w,{expose:B}){var z,F;const d=w,{user:g}=Je(),D=((z=g.info)==null?void 0:z.id)||null,{service:h}=Ee(),p=f({name:"",path:"",remark:"",workitemId:d.workitem.id}),x=f(!1),o=Pe.useCrud({service:h.pims.workitem.attachment},U=>{var j;const R=d.workitem.rootId;U.refresh({workitemId:(j=d==null?void 0:d.workitem)==null?void 0:j.id,rootId:R})}),s=f(!1);function a(U){s.value=!0,h.pims.workitem.attachment.request({url:"/download",method:"get",params:{id:U.id},responseType:"blob"}).then(R=>{yt(R)&&W.success("下载成功")}).catch(R=>{W.error(R.message||"下载失败")}).finally(()=>{s.value=!1})}function _(U){ze.confirm("此操作将永久删除该文件, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{s.value=!0,h.pims.workitem.attachment.remove({id:U.id}).then(()=>{var R;W.success("删除成功"),(R=o.value)==null||R.refresh()}).catch(R=>{W.error(R.message||"删除失败")}).finally(()=>{s.value=!1})})}const y=f({"slot-btn-download":{width:80,show:!0},"slot-btn-delete":{width:80,show:!0}}),{getOpWidth:$,getOpIsHidden:k}=ht(y),I=f(),b=f(!1);I.value=$(),b.value=k();const J=Pe.useTable({columns:[{label:"名称",prop:"name",align:"center",showOverflowTooltip:!0},{label:"备注",prop:"remark",align:"center",showOverflowTooltip:!0},{label:"创建人",prop:"creator",width:80,align:"center"},{label:"创建时间",prop:"createTime",sortable:"desc",width:200,align:"center"},{type:"op",label:"操作",width:160,hidden:!1,buttons:Object.keys(y.value)}]}),H=f(!1);function Q(U){p.value.path=U.url}async function te(){if(!p.value.path)return W.error("请上传文件");if(!p.value.name)return W.error("请输入名称");x.value=!0,p.value.rootId=d.workitem.rootId===0?d.workitem.id:d.workitem.rootId,p.value.workitemId=d.workitem.id,h.pims.workitem.attachment.upload(p.value).then(()=>{var U;W.success("保存成功"),le(),(U=o.value)==null||U.refresh()}).catch(U=>{W.error(U.messages||"保存失败")}).finally(()=>{x.value=!1})}function le(){H.value=!1,p.value={}}function O(U){return!d.workitem.id||(U==null?void 0:U.creator.id)===D?!0:([5,6,7].includes(d.workitem.status),!1)}function q(){return d.workitem.id?![5,6,7].includes(d.workitem.status):!0}return B({fileList:(F=J.value)==null?void 0:F.data}),(U,R)=>{const j=i("el-button"),K=i("cl-table"),c=i("cl-flex1"),m=i("cl-pagination"),C=i("cl-row"),n=i("cl-crud"),v=i("el-input"),L=i("el-form-item"),Z=i("cl-upload"),N=i("el-form"),ie=i("cl-dialog"),oe=Ue("loading");return r(),P("div",null,[u("div",Rl,[q()?(r(),E(j,{key:0,type:"primary",ml:"10px",onClick:R[0]||(R[0]=G=>H.value=!0)},{default:t(()=>[S(" 选择文件 ")]),_:1})):Y("",!0)]),De((r(),E(n,{ref_key:"Crud",ref:o,class:"px0!"},{default:t(()=>[e(K,{ref_key:"Table",ref:J,"auto-height":!1,"min-height":"200"},{"column-creator":t(({scope:G})=>{var ce;return[u("div",El,[e(ge,{user:(ce=G==null?void 0:G.row)==null?void 0:ce.creator,"show-name":!1},null,8,["user"])])]}),"slot-btn-download":t(({scope:G})=>[e(j,{text:"",type:"primary",loading:s.value,onClick:ce=>a(G.row)},{default:t(()=>[S(" 下载 ")]),_:2},1032,["loading","onClick"])]),"slot-btn-delete":t(({scope:G})=>[O(G.row)?(r(),E(j,{key:0,text:"",type:"danger",loading:s.value,onClick:ce=>_(G.row)},{default:t(()=>[S(" 删除 ")]),_:2},1032,["loading","onClick"])):Y("",!0)]),_:1},512),e(C,{mt:"10px"},{default:t(()=>[e(c),e(m)]),_:1})]),_:1})),[[oe,s.value]]),e(ie,{modelValue:H.value,"onUpdate:modelValue":R[3]||(R[3]=G=>H.value=G),title:"上传文件",width:"35%",controls:["close"],"close-on-click-modal":!1,"close-on-press-escape":!1},{footer:t(()=>[e(j,{onClick:le},{default:t(()=>[S(" 取消 ")]),_:1}),e(j,{loading:x.value,type:"success",onClick:te},{default:t(()=>[S(" 保存 ")]),_:1},8,["loading"])]),default:t(()=>[e(N,{model:p.value,"label-width":"100px"},{default:t(()=>[e(L,{label:"名称",required:""},{default:t(()=>[e(v,{modelValue:p.value.name,"onUpdate:modelValue":R[1]||(R[1]=G=>p.value.name=G),maxlength:255},null,8,["modelValue"])]),_:1}),e(L,{label:"备注"},{default:t(()=>[e(v,{modelValue:p.value.remark,"onUpdate:modelValue":R[2]||(R[2]=G=>p.value.remark=G),maxlength:255,type:"textarea",rows:2},null,8,["modelValue"])]),_:1}),e(L,{label:"文件",required:""},{default:t(()=>[e(Z,{type:"file",multiple:!0,"is-private":"",onSuccess:Q})]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}}),Pl={key:0},Nl={key:0,class:"card","b-b":"~ 1px solid gray-200",mb:"3"},zl={key:1,class:"card",mb:"3"},Fl={key:0,"w-full":"","text-right":""},jl={key:0,flex:"~ justify-between items-end"},ql={"w-full":""},Hl={flex:"~ justify-between items-center",pb:"1"},Al={class:"text-overflow-tooltip","w-full":"","flex-1":"","text-left":"","text-size-14px":""},Ol={"text-gray-7":""},Wl=A({name:"Comment"}),Jl=A({...Wl,props:{workItem:{},type:{},showBtn:{type:Boolean,default:!0}},setup(w,{expose:B}){const d=w,g=f({id:0,replayUserName:"",content:""});let D=null;const h=_e(()=>g.value.id>0),p=f(""),x=f(!1),o=f({page:1,total:0,size:20}),s=f([]),a=f();function _(){if(!p.value){W.error("请输入评论内容");return}x.value=!0;const q=d.workItem.rootId===0?d.workItem.id:d.workItem.rootId;ee.pims.workitem.log.add({workitemId:d.workItem.id,rootId:q,content:p.value,commentId:g.value.id}).then(()=>{W.success("评论成功"),p.value="",y(),g.value={}}).finally(()=>{x.value=!1})}function y(q=1){var F,U;const z=d.workItem.rootId;(F=d.workItem)!=null&&F.id&&ee.pims.workitem.log.page({page:q,pageSize:o.value.size,workitemId:(U=d.workItem)==null?void 0:U.id,rootId:z,type:D}).then(R=>{s.value=R.list,o.value=R.pagination})}function $(){g.value={},p.value=""}const k=f(1),I=f([{label:"全部",value:1,type:null},{label:"评论",value:2,type:1},{label:"动态",value:3,type:0},{label:"附件",value:4,type:2}]),b=_e(()=>{var q;return(q=s.value)==null?void 0:q.filter(z=>z.type===1&&z.isTop)});function J(q){p.value="",g.value={id:q.id,replayUserName:q==null?void 0:q.creator.name,content:q.content}}function H(q){D=q.type,y()}async function Q(q){await ee.pims.workitem.log.delete({ids:[q.id]}),y()}function te(q){const{id:z}=q;if(!z){W.error("请先选择评论");return}ee.pims.workitem.log.top({id:q.id}).then(()=>{W.success("操作成功"),y()}).catch(F=>{W.error(F.message)})}const le=_e(()=>500),O=_e(()=>d.workItem.status!==5);return Re(()=>{y()}),B({refresh:y,UploadTableRef:a}),(q,z)=>{var n;const F=i("el-tag"),U=i("el-empty"),R=i("el-pagination"),j=i("CircleClose"),K=i("el-icon"),c=i("el-input"),m=i("el-button"),C=Ue("loading");return r(),P("div",null,[u("div",null,[e(yl,{modelValue:l(k),"onUpdate:modelValue":z[0]||(z[0]=v=>de(k)?k.value=v:null),list:l(I),my:"4",onChange:H},null,8,["modelValue","list"]),l(k)<4?(r(),P("div",Pl,[u("div",{scrollbar:"~ rounded",style:pe(`height: ${l(le)}px`),"overflow-y":"auto"},[l(b)?(r(),P("div",Nl,[(r(!0),P(re,null,ue(l(b),(v,L)=>(r(),E(Qe,{key:L,"show-btn":d.showBtn,"top-text":"取消置顶","disable-all":l(O),item:v,onReply:J,onDelete:Q,onToTop:te},{default:t(()=>[e(F,{ml:"4",type:"primary",effect:"dark"},{default:t(()=>[S(" 置顶 ")]),_:1})]),_:2},1032,["show-btn","disable-all","item"]))),128))])):Y("",!0),l(s).length?(r(),P("div",zl,[(r(!0),P(re,null,ue(l(s),(v,L)=>(r(),E(Qe,{key:L,"show-btn":d.showBtn,"disable-all":l(O),"top-text":"置顶",item:v,onReply:J,onDelete:Q,onToTop:te},null,8,["show-btn","disable-all","item"]))),128))])):Y("",!0),l(s).length?Y("",!0):(r(),E(U,{key:2}))],4),l(o).total>20?(r(),P("div",Fl,[e(R,{layout:"prev, pager, next",total:l(o).total,"page-size":l(o).size,onChange:y},null,8,["total","page-size"])])):Y("",!0)])):Y("",!0)]),l(k)<4?(r(),P("div",jl,[u("div",ql,[De(u("div",Hl,[u("div",Al,[u("span",null,[S("回复 "),u("b",null,V(l(g).replayUserName),1),S("：")]),u("span",Ol,V(l(g).content),1)]),e(K,{size:"24",onClick:$},{default:t(()=>[e(j)]),_:1})],512),[[pt,(n=l(g))==null?void 0:n.id]]),l(O)?(r(),E(c,{key:0,modelValue:l(p),"onUpdate:modelValue":z[1]||(z[1]=v=>de(p)?p.value=v:null),type:"textarea",placeholder:`请输入${l(h)?"回复":"评论"}内容`,autosize:{minRows:1,maxRows:5}},null,8,["modelValue","placeholder"])):Y("",!0)]),l(O)?De((r(),E(m,{key:0,type:"primary",ml2:"",onClick:_},{default:t(()=>[S(V(`发表${l(h)?"回复":"评论"}`),1)]),_:1})),[[C,l(x)]]):Y("",!0)])):Y("",!0),l(k)===4?(r(),E(Bl,{key:1,ref_key:"UploadTableRef",ref:a,workitem:d.workItem,type:d.type},null,8,["workitem","type"])):Y("",!0)])}}}),Kl=u("span",{pl:"5px","font-size-18px":""},"%",-1),Gl=A({name:"progress-form"}),it=A({...Gl,props:ye({workitem:{default:()=>({})},rowData:{default:()=>({})},showHistory:{type:Boolean,default:!0}},{modelValue:{type:Boolean},modelModifiers:{}}),emits:["update:modelValue"],setup(w){const B=w,d=Pe.useCrud({service:ee.pims.progress},$=>{var k;$.refresh({workitemId:(k=B==null?void 0:B.workitem)==null?void 0:k.id})}),g=Pe.useTable({columns:[{label:"节点",prop:"nodeName",align:"center",width:300,fixed:"left",showOverflowTooltip:!0},{label:"进度",prop:"progress",align:"center",width:80,showOverflowTooltip:!0,formatter:$=>`${$.progress}%`},{label:"描述",prop:"desc",align:"center"},{label:"创建时间",prop:"createTime",sortable:"desc",width:200,align:"center"}]}),D=f(!1),h=Ce(w,"modelValue"),p=f([]),x=f(),o=f({id:0,nodeName:"",progress:0,desc:""}),s=f(!1),a=He({nodeName:[{required:!0,message:"请输入标题",trigger:"submit"}],progress:[{required:!0,message:"请输入进度",trigger:"submit"}],desc:[{required:!0,message:"请输入描述",trigger:"submit"}]});ft(()=>B.rowData,$=>{$&&(o.value=$)});function _(){x.value.validate($=>{if($){if(o.value.progress>100){W.error("进度不能大于100%");return}if(o.value.progress<=0){W.error("进度不能小于0%");return}s.value=!0;const k=o.value.id?"update":"add";o.value.workitemId=B.workitem.id,o.value.rootId=B.workitem.rootId,ee.pims.progress[k](o.value).then(()=>{W.success("保存成功"),s.value=!1,y(),Te.refresh()}).catch(I=>{W.error(I.message||"操作失败"),s.value=!1})}})}function y(){o.value={},h.value=!1}return($,k)=>{const I=i("el-input"),b=i("el-form-item"),J=i("el-col"),H=i("el-input-number"),Q=i("el-row"),te=i("el-form"),le=i("cl-table"),O=i("cl-flex1"),q=i("cl-pagination"),z=i("cl-row"),F=i("cl-crud"),U=i("el-collapse-item"),R=i("el-collapse"),j=i("el-button"),K=i("el-dialog"),c=Ue("loading");return r(),P("div",null,[e(K,{modelValue:h.value,"onUpdate:modelValue":k[5]||(k[5]=m=>h.value=m),title:"进展录入",width:"50%","append-to-body":"","close-on-press-escape":!1,"close-on-click-modal":!1},{footer:t(()=>[e(j,{onClick:y},{default:t(()=>[S(" 取 消 ")]),_:1}),$.showHistory?(r(),E(j,{key:0,type:"success",loading:l(s),onClick:k[4]||(k[4]=m=>_())},{default:t(()=>[S(" 确 定 ")]),_:1},8,["loading"])):Y("",!0)]),default:t(()=>[h.value?(r(),E(te,{key:0,ref_key:"FormRef",ref:x,rules:l(a),model:l(o),"label-width":"100px","label-position":"left",mt:"20px"},{default:t(()=>[e(Q,{gutter:20,justify:"space-between"},{default:t(()=>[e(J,{span:18},{default:t(()=>[e(b,{label:"节点",prop:"nodeName"},{default:t(()=>[e(I,{modelValue:l(o).nodeName,"onUpdate:modelValue":k[0]||(k[0]=m=>l(o).nodeName=m),disabled:!B.showHistory},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(J,{span:6},{default:t(()=>[e(b,{label:"进度",prop:"progress"},{default:t(()=>[e(H,{modelValue:l(o).progress,"onUpdate:modelValue":k[1]||(k[1]=m=>l(o).progress=m),modelModifiers:{number:!0},min:0,max:100,precision:2,controls:!1,disabled:!B.showHistory},null,8,["modelValue","disabled"]),Kl]),_:1})]),_:1})]),_:1}),e(b,{label:"描述",prop:"desc"},{default:t(()=>[e(I,{modelValue:l(o).desc,"onUpdate:modelValue":k[2]||(k[2]=m=>l(o).desc=m),type:"textarea",rows:2,disabled:!B.showHistory},null,8,["modelValue","disabled"])]),_:1})]),_:1},8,["rules","model"])):Y("",!0),B.showHistory?(r(),E(R,{key:1,modelValue:l(p),"onUpdate:modelValue":k[3]||(k[3]=m=>de(p)?p.value=m:null)},{default:t(()=>[e(U,{title:"历史进展",name:"1"},{default:t(()=>[De((r(),E(F,{ref_key:"Crud",ref:d},{default:t(()=>[e(le,{ref_key:"Table",ref:g,"auto-height":!1,"min-height":"200"},null,512),e(z,{mt:"10px"},{default:t(()=>[e(O),e(q)]),_:1})]),_:1})),[[c,l(D)]])]),_:1})]),_:1},8,["modelValue"])):Y("",!0)]),_:1},8,["modelValue"])])}}}),rt=w=>($e("data-v-2561aea1"),w=w(),Ve(),w),Xl={key:0},Zl={flex:"~"},Ql=["onClick"],ea={class:"label"},ta={"text-center":"",style:{width:"200px"},py:"10px"},la={class:"value"},aa=rt(()=>u("div",null,"任务进展",-1)),oa={color:"#48a2ff","cursor-pointer":""},na=rt(()=>u("div",null,"关联日记",-1)),sa={color:"#48a2ff","cursor-pointer":""},ia=A({name:"task-card-dynamic"}),ra=A({...ia,props:{rowData:{default(){return{status:0}}}},setup(w,{expose:B}){const d=w,g=f(!1),D=f(!1),h=f(!1),p=f([]),x=f([]),o=f(!1),s=f({}),a=f(!1),_=f({}),y=f([]);y.value=[{label:"任务总数",value:0,color:Le.panding,tableData:[]},{label:"进行中",value:0,color:Le.panding,tableData:[]},{label:"已完成",value:0,color:Le.finish,tableData:[]},{label:"逾期",value:0,color:Le.overdue,tableData:[]},{label:"暂停",value:0,color:Le.pause,tableData:[]},{label:"取消",value:0,color:Le.cancel,tableData:[]},{label:"工时(h)",value:0,type:"workTime",tableData:[]}];const $=f([]),k=f([]),I=f([]),b=_e(()=>{var j,K;const U=((j=y.value.find(c=>c.label==="已完成"))==null?void 0:j.value)||0,R=((K=y.value.find(c=>c.label==="任务总数"))==null?void 0:K.value)||0;return R===0?0:Math.round(U/R*100)}),J=f([]);async function H(U){await te(U),D.value=!0}async function Q(U){var R;if(U.value)if(U.type==="workTime")x.value=U.tableData,h.value=!0;else{const j=await ee.pims.workitem.workHourByIds({ids:(R=U==null?void 0:U.tableData)==null?void 0:R.map(K=>K.id)});j&&U.tableData.forEach(K=>{const c=j[K.id];c&&(K.estimatedWorkNum=c)}),J.value=U.tableData,g.value=!0}}async function te(U){const R=new Date().setHours(0,0,0,0),j=await ee.pims.workitem.logDetail({id:U.id,level:U.level});if(j&&j.length>0){y.value.forEach((c,m)=>{c.label=j[m].label,c.value=j[m].value,c.tableData=j[m].tableData});let K=[];j.forEach(c=>{c.tableData&&c.tableData.length>0&&c.tableData.forEach(m=>{m.daliyReport&&m.daliyReport.length>0&&m.daliyReport.forEach(C=>{const n=new Date(C.createTime).setHours(0,0,0,0),v=I.value.find(L=>L.id===C.id);n===R&&!v&&(I.value.push(C),K.push(C.workitemId))})}),c.tableData&&c.tableData.length>0&&c.tableData.forEach(m=>{m.progressLogList&&m.progressLogList.length>0&&m.progressLogList.forEach(C=>{const n=new Date(C.createTime).setHours(0,0,0,0),v=k.value.find(L=>L.id===C.id);n===R&&!v&&(K.push(C.workitemId),k.value.push(C))})})}),K.length>0&&ee.pims.workitem.listByIds({ids:K}).then(c=>{$.value=c})}}function le(U,R){const j=p.value.find(K=>K.id===U.creatorId);return j?`${ae(U.createTime).format("YYYY-MM-DD")}  ${j.name}的任务${R}`:""}function O(U){const R=$.value.find(j=>j.id===U.workitemId);return R?R.title:""}function q(U){if(!U.workTimeRange)return"";const R=JSON.parse(U.workTimeRange);return`${ae(R[0]).format("HH:mm")}-${ae(R[1]).format("HH:mm")} ${U.estimatedWorkNum}`}function z(U){s.value=U,o.value=!0}function F(U){_.value=ne(U),_.value.workTimeRange&&(_.value.workTimeRange=JSON.parse(_.value.workTimeRange)),a.value=!0}return Ae(()=>{ee.pims.workitem.systemUserList().then(U=>{p.value=U})}),B({open:H}),(U,R)=>{const j=i("el-progress"),K=i("el-card"),c=i("el-table-column"),m=i("el-table"),C=i("el-col"),n=i("el-row"),v=i("el-drawer"),L=i("el-dialog"),Z=i("el-button");return r(),P("div",null,[e(v,{modelValue:l(D),"onUpdate:modelValue":R[0]||(R[0]=N=>de(D)?D.value=N:null),title:U.rowData.title,"append-to-body":"",size:"65%"},{default:t(()=>[l(D)?(r(),P("div",Xl,[e(K,null,{default:t(()=>[u("div",Zl,[(r(!0),P(re,null,ue(l(y),(N,ie)=>(r(),P("div",{key:ie,class:"item",onClick:se(oe=>Q(N),["stop"])},[u("div",ea,V(N.label),1),u("div",{class:"value",style:pe(`color:${N.color}`)},V(N.value),5)],8,Ql))),128)),u("div",ta,[e(j,{type:"dashboard",percentage:l(b),width:90},{default:t(({percentage:N})=>[u("span",la,V(`${N}%`),1)]),_:1},8,["percentage"])])])]),_:1}),e(n,{gutter:20},{default:t(()=>[e(C,{span:12},{default:t(()=>[e(K,{mt:"20px"},{header:t(()=>[aa]),default:t(()=>[e(m,{data:l(k),style:{width:"100%"},"h-350px":"",onRowClick:z},{default:t(()=>[e(c,{prop:"",label:"标题",align:"center",width:"240",fixed:"left"},{default:t(({row:N})=>[u("span",oa,V(le(N,"进展")),1)]),_:1}),e(c,{prop:"title",label:"任务名称",align:"center","show-overflow-tooltip":""},{default:t(({row:N})=>[u("span",null,V(O(N)),1)]),_:1}),e(c,{prop:"nodeName",label:"节点",align:"center","show-overflow-tooltip":""}),e(c,{prop:"progress",label:"进度",align:"center",width:"80"},{default:t(({row:N})=>[S(V(`${N.progress}%`),1)]),_:1})]),_:1},8,["data"])]),_:1})]),_:1}),e(C,{span:12},{default:t(()=>[e(K,{mt:"20px"},{header:t(()=>[na]),default:t(()=>[e(m,{data:l(I),style:{width:"100%"},"h-350px":"",onRowClick:F},{default:t(()=>[e(c,{prop:"",label:"标题",align:"center",width:"240",fixed:"left"},{default:t(({row:N})=>[u("span",sa,V(le(N,"日记")),1)]),_:1}),e(c,{prop:"title",label:"任务名称",align:"center","show-overflow-tooltip":""},{default:t(({row:N})=>[u("span",null,V(O(N)),1)]),_:1}),e(c,{prop:"workTime",label:"工时",align:"center",width:"160"},{default:t(({row:N})=>[S(V(q(N)),1)]),_:1})]),_:1},8,["data"])]),_:1})]),_:1})]),_:1}),e(Jl,{"work-item":d.rowData},null,8,["work-item"])])):Y("",!0)]),_:1},8,["modelValue","title"]),e(Ht,{modelValue:l(g),"onUpdate:modelValue":R[1]||(R[1]=N=>de(g)?g.value=N:null),"table-data":l(J)},null,8,["modelValue","table-data"]),e(L,{modelValue:l(h),"onUpdate:modelValue":R[2]||(R[2]=N=>de(h)?h.value=N:null),title:"工时",width:"40%"},{default:t(()=>[e(qe,{"table-data":l(x),mt:"20px"},null,8,["table-data"])]),_:1},8,["modelValue"]),e(it,{modelValue:l(o),"onUpdate:modelValue":R[3]||(R[3]=N=>de(o)?o.value=N:null),"show-history":!1,"row-data":l(s)},null,8,["modelValue","row-data"]),e(L,{modelValue:l(a),"onUpdate:modelValue":R[5]||(R[5]=N=>de(a)?a.value=N:null),title:"工时"},{footer:t(()=>[e(Z,{onClick:R[4]||(R[4]=N=>a.value=!1)},{default:t(()=>[S("取消")]),_:1})]),default:t(()=>[e(nt,{form:l(_),title:"工作日记","open-type":"check"},null,8,["form"])]),_:1},8,["modelValue"])])}}}),ua=fe(ra,[["__scopeId","data-v-2561aea1"]]),da=w=>($e("data-v-821b98cd"),w=w(),Ve(),w),ca={flex:"~ row justify-between"},pa={flex:"~ col 1","h-full":""},fa={flex:"~ col"},ma={mb3:""},_a={ml10:"",class:"w-450px"},va={flex:"~ justify-between items-center","w-full":""},ya=da(()=>u("span",null,null,-1)),ba={flex:"~ row items-center"},ha={ml2:""},ga={flex:"~ row items-center"},ka={ml2:""},wa={class:"flex items-center"},xa=A({name:"workitem-form"}),Ta=A({...xa,props:{workItem:{default:()=>({})},parent:{default:()=>({})},auditInfo:{default:()=>({})},type:{}},setup(w,{expose:B}){var m;const d=w,g=f(ne(Ye)),{service:D}=Ee(),{user:h}=Je(),p=f(!1),x=f({}),o=f({title:"",content:"",status:void 0,workItemType:"",responsiblePersonId:"",participantIds:[],priority:"",planStartTime:"",planEndTime:"",workDay:0,isOrderApprove:void 0,approver:"",agree:"",opinion:""}),s=_e(()=>bt(o)),a=((m=h.info)==null?void 0:m.id)||null,_=f([]),y=f([]);function $(C,n){at(C)||!n||C.forEach(v=>{v.id===n&&(v.disabled=!0)})}const k=f(null),I=f(),b=He({title:[{required:!0,message:"请输入标题",trigger:"blur"}],responsiblePersonId:[{required:!0,message:"请选择负责人",trigger:"submit"}],priority:[{required:!0,message:"请选择优先级",trigger:"submit"}],planStartTime:[{required:!0,message:"请选择计划开始时间",trigger:"submit"}],planEndTime:[{required:!0,message:"请选择计划完成时间",trigger:"submit"}]});async function J(){var C;return(C=I.value)==null?void 0:C.validate()}function H(C){k.value=C}function Q(){if(o.value.planEndTime&&o.value.planStartTime){const C=ae(o.value.planStartTime).format("YYYY-MM-DD"),n=ae(o.value.planEndTime).format("YYYY-MM-DD");let v=ae(n).diff(C,"day");v=Math.abs(v);let L=0;for(let Z=0;Z<v;Z++){const N=ae(o.value.planStartTime).add(1,"day").format("YYYY-MM-DD");we(N)||L++}o.value.workDay=L}}function te(){if(o.value.planStartTime&&o.value.workDay===0&&(o.value.planEndTime=o.value.planStartTime),o.value.planStartTime&&o.value.workDay){let C=0,n=o.value.planStartTime;for(;C<o.value.workDay;)n=ae(n).add(1,"day").format("YYYY-MM-DD"),!we(n)&&C++;if(d.parent.planEndTime){const v=new Date(d.parent.planEndTime).setHours(0,0,0,0);if(new Date(n).setHours(0,0,0,0)>v)return o.value.workDay=0,W.error("计划结束时间不能大于父任务结束时间")}o.value.planEndTime=n}!o.value.workDay&&o.value.planStartTime&&o.value.planEndTime&&Q()}const le=_e(()=>d.parent.planStartTime?new Date(d.parent.planStartTime):null),O=_e(()=>d.parent.planEndTime?new Date(d.parent.planEndTime):null);function q(C){let n;return n=we(C),n||le.value&&(n=C.getTime()<le.value.getTime(),n)||O.value&&(n=C.getTime()>O.value.getTime(),n)||o.value.planEndTime&&(n=C.getTime()>new Date(o.value.planEndTime).getTime(),n)?n:C.getTime()<Date.now()-864e5}function z(C){var v;const n=we(C);return n||(C.getTime()<Date.now()-864e5?!0:O.value?C.getTime()>((v=O.value)==null?void 0:v.getTime()):!1)}function F(C){y.value.forEach(n=>{n.disabled=n.id===C})}function U(C){C.length===0&&_.value.forEach(n=>{n.disabled=!1}),C.forEach(n=>{_.value.forEach(v=>{v.disabled=v.id===n})})}const R=f(!1),j=f(!1);function K(){var C;return d.workItem.id?d.type&&d.type==="done"||Object.keys(d.auditInfo).length>0&&d.auditInfo.id>0||d.workItem.status===5||d.workItem.status===-1?!0:!(a&&((C=d.workItem)==null?void 0:C.creatorId)===a):!1}async function c(){var n,v,L;Object.keys(d.workItem).length>0&&(o.value=d.workItem,o.value.participantIds=(n=d.workItem.participants)==null?void 0:n.map(Z=>Z.participantId),o.value.ccUserIds=d.workItem.ccUsers?d.workItem.ccUsers.split(","):[],o.value.approver&&o.value.approver.includes(",")&&(o.value.approver=o.value.approver.split(",").map(Z=>Number.parseInt(Z))),(o.value.status>4||o.value.status===-1)&&(R.value=!0),o.value.status===-1&&(g.value=[{label:"审批中",value:-1,type:"info",color:"#909399"}])),j.value=K();const C=await D.pims.workitem.systemUserList();_.value=C,x.value.personList=_,y.value=ne(C),a&&!o.value.responsiblePersonId&&Object.keys(d.parent).length===0&&(_!=null&&_.value.some(N=>N.id===a))&&(o.value.responsiblePersonId=a),$(y.value,a),((L=(v=d==null?void 0:d.workItem)==null?void 0:v.participants)==null?void 0:L.length)>0&&d.workItem.participants.forEach(Z=>{$(_.value,Z.participantId)})}return c(),B({callValid:J,taskForm:o}),(C,n)=>{const v=i("el-input"),L=i("el-form-item"),Z=i("cl-editor-wang"),N=i("el-option"),ie=i("el-select"),oe=i("el-tag"),G=i("el-date-picker"),ce=i("el-input-number"),ve=i("el-divider"),Ie=i("el-form"),M=Ue("loading");return r(),P("div",null,[De((r(),E(Ie,{ref_key:"addWorkItemForm",ref:I,rules:l(b),model:l(o),"label-width":"120px","label-position":"left",mt4:"","require-asterisk-position":"right"},{default:t(()=>[u("div",ca,[u("div",pa,[u("div",fa,[u("div",ma,[e(L,{label:"",prop:"title","label-width":"0"},{default:t(()=>[e(v,{modelValue:l(o).title,"onUpdate:modelValue":n[0]||(n[0]=T=>l(o).title=T),placeholder:"请输入标题",h11:"","font-size-4":"","font-750":"",maxlength:"100",disabled:l(j)},null,8,["modelValue","disabled"])]),_:1})]),e(Z,{modelValue:l(o).content,"onUpdate:modelValue":n[1]||(n[1]=T=>l(o).content=T),disabled:l(j),height:"301px",onOnCreated:H},null,8,["modelValue","disabled"])])]),u("div",_a,[l(o).id&&l(o).id>0?(r(),E(L,{key:0,label:""},{default:t(()=>[u("div",va,[u("span",null,V(`进度：${l(s)} %`),1),u("span",null,V(`工时(h)：${l(o).estimatedWorkNum}`),1),ya])]),_:1})):Y("",!0),e(L,{label:"负责人",prop:"responsiblePersonId"},{default:t(()=>[e(ie,{modelValue:l(o).responsiblePersonId,"onUpdate:modelValue":n[2]||(n[2]=T=>l(o).responsiblePersonId=T),placeholder:"请选择",filterable:"",disabled:l(j),onChange:F},{default:t(()=>[(r(!0),P(re,null,ue(l(_),T=>(r(),E(N,{key:T.id,label:T.name,value:T.id,disabled:T.disabled},{default:t(()=>[u("div",ba,[e(Se,{src:T.headImg,size:30,title:T.name},null,8,["src","title"]),u("span",ha,V(T.name),1)])]),_:2},1032,["label","value","disabled"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1}),e(L,{label:"参与人",prop:"participantIds"},{default:t(()=>[e(ie,{modelValue:l(o).participantIds,"onUpdate:modelValue":n[3]||(n[3]=T=>l(o).participantIds=T),placeholder:"请选择",multiple:"",filterable:"",disabled:l(j),onChange:U},{default:t(()=>[(r(!0),P(re,null,ue(l(y),T=>(r(),E(N,{key:T.id,label:T.name,value:T.id,disabled:T.disabled},{default:t(()=>[u("div",ga,[e(Se,{src:T.headImg,size:30,title:T.name},null,8,["src","title"]),u("span",ka,V(T.name),1)])]),_:2},1032,["label","value","disabled"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1}),e(L,{label:"优先级",prop:"priority"},{default:t(()=>[e(ie,{modelValue:l(o).priority,"onUpdate:modelValue":n[4]||(n[4]=T=>l(o).priority=T),placeholder:"请选择",disabled:l(j)},{default:t(()=>[(r(!0),P(re,null,ue(l(Ke),T=>(r(),E(N,{key:T.value,label:T.label,value:T.value},{default:t(()=>[u("div",wa,[e(oe,{color:T.color,style:{"margin-right":"8px"},size:"small"},null,8,["color"]),u("span",null,V(T.label),1)])]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1}),l(o).id>0?(r(),E(L,{key:1,label:"任务状态",prop:"status",rules:[{required:!0,message:"请选择状态",trigger:"submit"}]},{default:t(()=>[e(oe,{type:l(he)(l(o).status).type,effect:"dark"},{default:t(()=>[S(V(l(he)(l(o).status).label),1)]),_:1},8,["type"])]),_:1})):Y("",!0),e(L,{label:"计划开始时间",prop:"planStartTime"},{default:t(()=>[e(G,{modelValue:l(o).planStartTime,"onUpdate:modelValue":n[5]||(n[5]=T=>l(o).planStartTime=T),type:"date",disabled:l(j),format:"YYYY-MM-DD",placeholder:"请选择日期","disabled-date":q,onChange:Q},null,8,["modelValue","disabled"])]),_:1}),e(L,{label:"工期",rules:[{required:!0,message:"工期不能为空",trigger:"submit"}]},{default:t(()=>[e(ce,{modelValue:l(o).workDay,"onUpdate:modelValue":n[6]||(n[6]=T=>l(o).workDay=T),min:0,max:999,precision:0,disabled:l(j),onChange:te},null,8,["modelValue","disabled"])]),_:1}),e(L,{label:"计划完成时间",prop:"planEndTime"},{default:t(()=>[e(G,{modelValue:l(o).planEndTime,"onUpdate:modelValue":n[7]||(n[7]=T=>l(o).planEndTime=T),type:"date",format:"YYYY-MM-DD",disabled:l(j),placeholder:"请选择日期","disabled-date":z,onChange:Q},null,8,["modelValue","disabled"])]),_:1}),e(ve,{mb:"10px"})])]),Me(C.$slots,"form-footer",{data:{personList:l(_),taskForm:l(o)}},void 0,!0)]),_:3},8,["rules","model"])),[[M,l(p)]])])}}}),et=fe(Ta,[["__scopeId","data-v-821b98cd"]]),Ze=w=>($e("data-v-f7e1c985"),w=w(),Ve(),w),$a=Ze(()=>u("div",null,"进展信息",-1)),Va=Ze(()=>u("div",null,"工作日记",-1)),Ia=Ze(()=>u("div",null,"审批记录",-1)),Da=A({name:"task-edit-table"}),Ca=A({...Da,props:ye({isEdit:{type:Boolean,default:!0}},{modelValue:{},modelModifiers:{}}),emits:ye(["removeRow","editRow"],["update:modelValue"]),setup(w,{emit:B}){const d=w,g=B,D=Ce(w,"modelValue");function h(s){g("editRow",ne(s))}async function p(s,a){var y;if(!s.id&&(D!=null&&D.value)&&((y=D==null?void 0:D.value)==null?void 0:y.length)>0){const $=D==null?void 0:D.value.splice(a,1);g("removeRow",$[0]);return}if(await ze.confirm("确认删除该任务吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}))try{await ee.pims.workitem.remove({id:s.id}),D.value&&D.value.splice(a,1),g("removeRow",s)}catch($){W.error($.message||"删除失败"),console.error($)}}function x(s){return(s==null?void 0:s.creatorId)===me}function o(s){return!d.isEdit||[5,6,-1].includes(s.status)?"查看":x(s)?"编辑":"查看"}return(s,a)=>{const _=i("el-card"),y=i("el-table-column"),$=i("el-tag"),k=i("el-button"),I=i("el-table");return r(),E(I,{data:D.value,style:{width:"100%"},class:"cl-table"},{default:t(()=>[e(y,{type:"expand",align:"center",width:"60"},{default:t(({row:b})=>[b.progressLogList&&b.progressLogList.length>0?(r(),E(_,{key:0},{header:t(()=>[$a]),default:t(()=>[e(Ge,{"table-data":b.progressLogList},null,8,["table-data"])]),_:2},1024)):Y("",!0),b.daliyReport&&b.daliyReport.length>0?(r(),E(_,{key:1,mt:"20px"},{header:t(()=>[Va]),default:t(()=>[e(qe,{"table-data":b.daliyReport,mt:"20px"},null,8,["table-data"])]),_:2},1024)):Y("",!0),b.auditLogList&&b.auditLogList.length>0?(r(),E(_,{key:2,mt:"20px"},{header:t(()=>[Ia]),default:t(()=>[e(je,{"table-data":b.auditLogList,mt:"20px"},null,8,["table-data"])]),_:2},1024)):Y("",!0)]),_:1}),e(y,{prop:"title",label:"任务标题",align:"left","show-overflow-tooltip":"",flex:"flex",width:"350"}),e(y,{prop:"creatorId",label:"创建人",align:"center",width:"80"},{default:t(({row:b})=>[e(ge,{user:b==null?void 0:b.creator,"show-name":!1,flex:"~ justify-center items-center"},null,8,["user"])]),_:1}),e(y,{prop:"responsiblePersonId",label:"负责人",align:"center",width:"80"},{default:t(({row:b})=>[e(ge,{user:b==null?void 0:b.responsiblePerson,"show-name":!1,flex:"~ justify-center items-center"},null,8,["user"])]),_:1}),e(y,{prop:"participantIds",label:"进度",align:"center",width:"80"},{default:t(({row:b})=>[S(V(`${b.progress||0}%`),1)]),_:1}),e(y,{prop:"participantIds",label:"工时(h)",align:"center",width:"80"},{default:t(({row:b})=>[S(V(b.estimatedWorkNum||0),1)]),_:1}),e(y,{prop:"priority",label:"优先级",align:"center",width:"80"},{default:t(({row:b})=>[e($,{color:l(xe)(b.priority).color,effect:"dark",type:"info"},{default:t(()=>[S(V(l(xe)(b.priority).label),1)]),_:2},1032,["color"])]),_:1}),e(y,{prop:"status",label:"状态",align:"center",width:"80"},{default:t(({row:b})=>[e($,{type:l(he)(b.status).type,effect:"dark"},{default:t(()=>[S(V(l(he)(b.status).label),1)]),_:2},1032,["type"])]),_:1}),e(y,{prop:"planStartTime",label:"计划开始时间",align:"center",width:"120"},{default:t(({row:b})=>[S(V(l(Ne)(b.planStartTime)),1)]),_:1}),e(y,{prop:"planEndTime",label:"计划完成时间",align:"center",width:"120"},{default:t(({row:b})=>[S(V(l(Ne)(b.planEndTime)),1)]),_:1}),e(y,{prop:"workDay",label:"工期",align:"center",width:"100"},{default:t(({row:b})=>[S(V(b.workDay||"0天"),1)]),_:1}),e(y,{prop:"participantIds",label:"参与人",align:"center","min-width":"160"},{default:t(({row:b})=>[e(gt,{data:b==null?void 0:b.participants,stack:"",size:30,style:{height:"30px"}},null,8,["data"])]),_:1}),e(y,{prop:"",label:"操作",align:"center",width:"160",fixed:"right"},{default:t(({row:b,$index:J})=>[e(k,{text:"",type:"primary",size:"small",onClick:H=>h(b)},{default:t(()=>[S(V(o(b)),1)]),_:2},1032,["onClick"]),x(b)&&(b==null?void 0:b.status)===4?(r(),E(k,{key:0,text:"",type:"danger",size:"small",onClick:H=>p(b,J)},{default:t(()=>[S(" 删除 ")]),_:2},1032,["onClick"])):Y("",!0)]),_:1})]),_:1},8,["data"])}}}),La=fe(Ca,[["__scopeId","data-v-f7e1c985"]]),Ua=w=>($e("data-v-e1f01f42"),w=w(),Ve(),w),Ma=Ua(()=>u("span",null,"任务列表",-1)),Sa={key:1},Ra=A({name:"task-card-form"}),Ea=A({...Ra,props:{title:{},type:{},refresh:{type:Function,default:()=>{}},reqMethod:{},openType:{}},emits:["loadedTask"],setup(w,{expose:B,emit:d}){const g=w,D=d,h=Fe(),p=mt(),x=f(["1","2"]),o=f(!1),s=f(!1),a=f({status:4,childList:[]}),_=f({status:4}),y=f(!1),$=f(!1),k=f(),I=f(),b=f([]);async function J(){x.value.find(v=>v==="2")||(x.value[1]="2");try{await(k==null?void 0:k.value.callValid())}catch{return W.error("请检查任务信息")}if(!a.value.id)try{y.value=!0,a.value.type=1,a.value.level=0,a.value=await ee.pims.workitem.add(a.value),a.value.rootId=a.value.id}catch(v){return W.error(v.message)}finally{y.value=!1}_.value.status=4,o.value=!0}async function H(n){_.value=n,o.value=!0}async function Q(){await te(a.value)}async function te(n){var v;a.value=await ee.pims.workitem.queryInfo({id:n.id,parentId:n.parentId,openType:g.openType}),D("loadedTask",a.value),a.value&&a.value.childList&&a.value.childList.length>0&&(b.value=(v=a==null?void 0:a.value)==null?void 0:v.childList)}async function le(n){await te(n),x.value=["1","2"],s.value=!0}async function O(){try{await(k==null?void 0:k.value.callValid())}catch{return W.error("请检查任务信息")}try{y.value=!0,a.value.id?await ee.pims.workitem.update(a.value):(a.value.type=1,a.value.level=0,a.value=await ee.pims.workitem.add(a.value),a.value.rootId=a.value.id),W.success("保存成功"),q(),Te.refresh()}catch(n){W.error(n.message)}finally{y.value=!1}q()}function q(){s.value=!1,a.value={status:0},b.value=[]}const z=f("");function F(n){n&&n.length>0&&(n.find(L=>L==="1")?z.value="":z.value=a.value.title||"")}function U(){x.value=["1","2"],s.value=!0}async function R(){try{await(I==null?void 0:I.value.callValid())}catch{return W.error("请检查任务信息")}try{$.value=!0,_.value.id?await ee.pims.workitem.update(_.value):(_.value.type=1,_.value.parentId=a.value.id,a.value.rootId&&a.value.rootId>0?_.value.rootId=a.value.rootId:_.value.rootId=a.value.id,a.value.level=a.value.level||0,a.value.level===0?_.value.level=1:_.value.level=a.value.level+1,await ee.pims.workitem.add(_.value)),W.success("保存成功"),j(),await te(a.value),Te.refresh()}catch(n){console.error(n),W.error(n.message)}finally{$.value=!1}}function j(){o.value=!1,_.value={status:0}}function K(n){return n.id?(n==null?void 0:n.creatorId)===me:!0}function c(n){return n.id?(n==null?void 0:n.responsiblePersonId)===me:!0}function m(){return a.value.id?a.value.status!==5:!0}function C(){return _.value.id?_.value.status===5?!1:(K(_.value),!0):!0}return B({openDialog:U,clickRow:le,cancelTask:q}),(n,v)=>{const L=i("el-collapse-item"),Z=i("el-button"),N=i("el-collapse"),ie=i("el-drawer"),oe=i("el-dialog");return r(),P("div",null,[e(ie,{modelValue:s.value,"onUpdate:modelValue":v[2]||(v[2]=G=>s.value=G),title:"任务信息","append-to-body":"",size:"65%",onClose:q},{footer:t(()=>[l(p).footer?Me(n.$slots,"footer",{key:0},void 0,!0):(r(),P("div",Sa,[e(Z,{onClick:q},{default:t(()=>[S(" 取消 ")]),_:1}),m()&&K(a.value)?(r(),E(Z,{key:0,loading:y.value,type:"success",onClick:O},{default:t(()=>[S(" 确定 ")]),_:1},8,["loading"])):Y("",!0)]))]),default:t(()=>[u("div",null,[e(N,{modelValue:x.value,"onUpdate:modelValue":v[1]||(v[1]=G=>x.value=G),onChange:F},{default:t(()=>[e(L,{title:z.value,name:"1",class:"task_info"},{default:t(()=>[s.value?(r(),E(et,be({key:0,ref_key:"workItemFormRef",ref:k},l(h),{"work-item":a.value,type:g.type}),{"form-footer":t(G=>[Me(n.$slots,"form-footer",{data:G},void 0,!0)]),_:3},16,["work-item","type"])):Y("",!0)]),_:3},8,["title"]),g.openType!=="audit"?(r(),E(L,{key:0,name:"2"},{title:t(()=>[Ma,m()&&(K(a.value)||c(a.value))?(r(),E(Z,{key:0,type:"primary",icon:"Plus","m-l-10px":"",onClick:se(J,["stop"])})):Y("",!0)]),default:t(()=>[e(La,{modelValue:b.value,"onUpdate:modelValue":v[0]||(v[0]=G=>b.value=G),onRemoveRow:Q,onEditRow:H},null,8,["modelValue"])]),_:1})):Y("",!0),a.value.auditLogList&&a.value.auditLogList.length>0?(r(),E(L,{key:1,title:"审批记录",name:"3"},{default:t(()=>[e(je,{"table-data":a.value.auditLogList},null,8,["table-data"])]),_:1})):Y("",!0),a.value.progressLogList&&a.value.progressLogList.length>0?(r(),E(L,{key:2,title:"历史进展",name:"4"},{default:t(()=>[e(Ge,{"table-data":a.value.progressLogList},null,8,["table-data"])]),_:1})):Y("",!0),a.value.daliyReport&&a.value.daliyReport.length>0?(r(),E(L,{key:3,title:"关联日记",name:"5"},{default:t(()=>[e(qe,{"table-data":a.value.daliyReport},null,8,["table-data"])]),_:1})):Y("",!0)]),_:3},8,["modelValue"])])]),_:3},8,["modelValue"]),e(oe,{modelValue:o.value,"onUpdate:modelValue":v[3]||(v[3]=G=>o.value=G),title:_.value.id?"编辑子任务":"新建子任务","append-to-body":""},{footer:t(()=>[e(Z,{onClick:j},{default:t(()=>[S(" 取消 ")]),_:1}),C()?(r(),E(Z,{key:0,loading:$.value,type:"success",onClick:R},{default:t(()=>[S(" 确定 ")]),_:1},8,["loading"])):Y("",!0)]),default:t(()=>[o.value?(r(),E(et,be({key:0,ref_key:"workItemChildFormRef",ref:I},l(h),{"work-item":_.value,type:g.type,parent:a.value}),null,16,["work-item","type","parent"])):Y("",!0)]),_:1},8,["modelValue","title"])])}}}),ut=fe(Ea,[["__scopeId","data-v-e1f01f42"]]),Ya={flex:"~ row items-center"},Ba={ml2:""},Pa=A({name:"audit"}),Na=A({...Pa,props:ye({workitem:{default:()=>({})}},{modelValue:{type:Boolean},modelModifiers:{}}),emits:ye(["submitSuccess"],["update:modelValue"]),setup(w,{emit:B}){const d=w,g=B,D=Ce(w,"modelValue"),h=d.workitem,p=f(),x=f([]);ee.pims.workitem.systemUserList().then(y=>{x.value=y});const o=f(ne(Ye)),s=f({});async function a(){if(!h.status)return W.error("请选择状态");if(!h.approver||h.approver.length===0)return W.error("请选择审批人");if(h.approver.length>1&&!h.isOrderApprove)return W.error("请选择是否按顺序审批");p.value=!0;try{const y=ne(h);h.approver&&h.approver.length>0&&(y.approver=h.approver.join(",")),await ee.pims.workitem.submit(y),_(),g("submitSuccess",h)}catch(y){W.error(y.message||"提交失败")}finally{p.value=!1}}Re(()=>{h.status===4&&(o.value=o.value.splice(1,o.value.length-1)),h.status===7&&(o.value=o.value.splice(0,o.value.length-1)),h.status=void 0});function _(){D.value=!1}return(y,$)=>{const k=i("el-form-item"),I=i("el-tag"),b=i("el-option"),J=i("el-select"),H=i("el-col"),Q=i("el-radio"),te=i("el-radio-group"),le=i("el-row"),O=i("el-form"),q=i("el-button"),z=i("el-dialog");return r(),P("div",null,[e(z,{modelValue:D.value,"onUpdate:modelValue":$[3]||($[3]=F=>D.value=F),title:"提交审批","append-to-body":"","close-on-press-escape":!1,width:"35%","close-on-click-modal":!1},{footer:t(()=>[e(q,{onClick:_},{default:t(()=>[S(" 取消 ")]),_:1}),e(q,{type:"success",loading:l(p),onClick:a},{default:t(()=>[S(" 提交 ")]),_:1},8,["loading"])]),default:t(()=>[e(O,{ref_key:"FormRef",ref:s,model:l(h),"label-width":"130px"},{default:t(()=>[e(k,{label:"任务标题:"},{default:t(()=>[u("span",null,V(l(h).title),1)]),_:1}),e(le,{gutter:20,"justify-between":""},{default:t(()=>[e(H,{span:12},{default:t(()=>[e(k,{label:"任务状态:",prop:"status",rules:[{required:!0,message:"请选择状态",trigger:"submit"}]},{default:t(()=>[e(J,{modelValue:l(h).status,"onUpdate:modelValue":$[0]||($[0]=F=>l(h).status=F),placeholder:"请选择状态"},{default:t(()=>[(r(!0),P(re,null,ue(l(o),F=>(r(),E(b,{key:F.value,label:F.label,value:F.value},{default:t(()=>[e(I,{type:F.type,effect:"dark"},{default:t(()=>[S(V(F.label),1)]),_:2},1032,["type"])]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(H,{span:12},{default:t(()=>[l(h).approver&&l(h).approver.length>1?(r(),E(k,{key:0,label:"是否按顺序审批:",rules:[{required:!0,message:"请选择是否按顺序审批",trigger:"submit"}]},{default:t(()=>[e(te,{modelValue:l(h).isOrderApprove,"onUpdate:modelValue":$[1]||($[1]=F=>l(h).isOrderApprove=F),class:"ml-4"},{default:t(()=>[e(Q,{label:1},{default:t(()=>[S(" 是 ")]),_:1}),e(Q,{label:2},{default:t(()=>[S(" 否 ")]),_:1})]),_:1},8,["modelValue"])]),_:1})):Y("",!0)]),_:1})]),_:1}),e(k,{label:"审批人:",required:"",rules:[{required:!0,message:"请选择审批人",trigger:"submit"}]},{default:t(()=>[e(J,{modelValue:l(h).approver,"onUpdate:modelValue":$[2]||($[2]=F=>l(h).approver=F),placeholder:"请选择审批人",multiple:""},{default:t(()=>[(r(!0),P(re,null,ue(l(x),F=>(r(),E(b,{key:F.id,label:F.name,value:F.id},{default:t(()=>[u("div",Ya,[e(Se,{src:F.headImg,size:30,title:F.name},null,8,["src","title"]),u("span",Ba,V(F.name),1)])]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}}),za=A({name:"undefined"}),Fa=A({...za,props:ye({workitem:{default:()=>({})}},{modelValue:{type:Boolean},modelModifiers:{}}),emits:["update:modelValue"],setup(w){const B=w,d=f(0),g=Ce(w,"modelValue"),D=f([]),h=f([]);async function p(){const x=await ee.pims.workitem.systemUserList();B.workitem.approver&&B.workitem.approver.split(",").map(a=>Number(a)).forEach(a=>{const _=x.find(y=>y.id===a);_&&D.value.push(_)});let o;B.workitem&&B.workitem.auditLogList&&B.workitem.auditLogList.length>0?o=B.workitem.auditLogList:o=await ee.pims.autid.getAutidByWorkitemId({workitemId:B.workitem.id}),o&&o.length>0&&o.filter(a=>a.type===1&&a.isRepeal===0).forEach(a=>{if(a.type===1&&a.isRepeal===0){let _=D.value.findIndex(y=>y.id===Number(a.approver));_>-1&&(_===0&&(_=1),d.value=_)}}),h.value=o}return Re(()=>{p()}),(x,o)=>{const s=i("el-step"),a=i("el-steps"),_=i("el-button"),y=i("el-dialog");return r(),P("div",null,[e(y,{modelValue:g.value,"onUpdate:modelValue":o[1]||(o[1]=$=>g.value=$),title:"审核流程"},{footer:t(()=>[e(_,{onClick:o[0]||(o[0]=$=>g.value=!1)},{default:t(()=>[S(" 取 消 ")]),_:1})]),default:t(()=>[e(a,{active:l(d),"align-center":"","finish-status":"success"},{default:t(()=>[(r(!0),P(re,null,ue(l(D),($,k)=>(r(),E(s,{key:k,title:$.name},null,8,["title"]))),128))]),_:1},8,["active"]),e(je,{"table-data":l(h),mt:"20px"},null,8,["table-data"])]),_:1},8,["modelValue"])])}}}),ja=["onClick"],qa={flex:"~ items-center","w-full":""},Ha={key:0,color:"red","font-size-16px":"","font-bold":""},Aa={class:"el-dropdown-link"},Oa=["onClick"],Wa={flex:"~ items-center","w-full":""},Ja={key:0,color:"red","font-size-16px":"","font-bold":""},Ka={class:"el-dropdown-link"},Ga=A({name:"undefined"}),Xa=A({...Ga,props:{height:{default:300},tableList:{default:()=>[]},type:{},openType:{},disabled:{type:Boolean,default:!0},reqMethod:{default:"page"},showChild:{type:Boolean,default:!0}},emits:["click-dynamic","click-row"],setup(w,{expose:B,emit:d}){const g=w,D=d,h=g.openType,p=g.type,x=g.reqMethod,o=Fe(),s=f({}),a=f(!1),_=f(!1),y=f(!1),$=f(!1),{height:k}=_t(g),I=f([]),b=f(),J=f({total:0,page:1,size:50});async function H(n){const v={...b.value,...J.value,statusType:g.type,taskType:o.tasktype};n&&(v.title=n);let L;g.reqMethod==="myList"?L=await ee.pims.workitem.myList(v):L=await ee.pims.workitem.page(v),J.value=L.pagination,I.value=L.list}function Q(){J.value.page=1,H().then()}Q();async function te(n){let v=n.id;if(n.parentId&&n.parentId>0&&(v=n.parentId),n.creatorId===me&&n.unreadNum&&n.unreadNum>0)try{await ee.pims.workitem.readLog({workitemId:v}),n.unreadNum=0}catch(L){W.error(L.message),console.error(L)}D("click-dynamic",ne(n),g.type)}function le(){return g.type==="done"?"#909399":"#48a2ff"}function O(n){return g.type==="done"?"background-color: #FFF; color: #606266":q(n)<=0?"background-color: #F87171FF":"background-color: #67c23a"}function q(n){if(n!=null&&n.status&&n.status>4)return 0;if(!n.planEndTime)return-1;const v=ae(new Date).format("YYYY-MM-DD"),L=ae(n.planEndTime).format("YYYY-MM-DD");return ae(L).diff(v,"day")}function z(n){if(!n.planStartTime)return 0;const v=new Date().setHours(0,0,0,0);if(new Date(n.planStartTime).setHours(0,0,0,0)>v)return 0;const Z=ae(new Date).format("YYYY-MM-DD"),N=ae(n.planStartTime).format("YYYY-MM-DD");return ae(Z).diff(N,"day")}async function F(n){const v=ne(I.value[n.oldIndex]),L=ne(I.value[n.newIndex]),Z=v.sortIndex;v.sortIndex=L.sortIndex,L.sortIndex=Z;try{a.value=!0,await ee.pims.workitem.updateSort({data:[{id:L.id,sortIndex:L.sortIndex},{id:v.id,sortIndex:v.sortIndex}]}),await H()}catch(N){W.error(N.message||"排序失败")}finally{a.value=!1}}function U(n){const v=ne(n);s.value=v,D("click-row",v,g.type)}function R(n){s.value=ne(n),y.value=!0}function j(n){s.value=ne(n),_.value=!0}function K(n){s.value=ne(n),$.value=!0}async function c(n){if(n.status===-1||n.status===5)return W.error("已完成或者审批中的任务不能删除");await ze.confirm("确定要删除该任务吗?","提示")&&ee.pims.workitem.remove({id:n.id}).then(async()=>{W.success("删除成功"),await Te.refresh()}).catch(L=>{W.error(L.message||"删除失败")})}function m(n){return g.type==="done"||![n.creatorId,n.responsiblePersonId].includes(me)?"查看":"编辑"}function C(n){return!(g.type==="done"||n.status===-1||n.type===2||![n.creatorId,n.responsiblePersonId].includes(me))}return B({refresh:Q,initData:H,draggableUpdate:F}),(n,v)=>{const L=i("el-table-column"),Z=i("el-tag"),N=i("Operation"),ie=i("el-icon"),oe=i("el-dropdown-item"),G=i("el-dropdown-menu"),ce=i("el-dropdown"),ve=i("el-table"),Ie=i("el-badge"),M=Ue("loading");return r(),P("div",null,[e(l(vt),{modelValue:l(I),"onUpdate:modelValue":v[0]||(v[0]=T=>de(I)?I.value=T:null),target:"tbody",animation:150,"ghost-class":"ghost",disabled:!1,onUpdate:F},{default:t(()=>[De((r(),E(ve,be({data:l(I),height:`${l(k)}px`},l(o)),{default:t(()=>[g.showChild?(r(),E(L,{key:0,type:"expand",width:"50"},{default:t(T=>[e(ve,{data:T.row.childList,"show-header":!1},{default:t(()=>[e(L,{width:"50"}),e(L,{label:"",prop:"priority",align:"left",width:"50"},{default:t(({row:X})=>[e(Z,{style:pe(`background-color: ${l(xe)(X.priority).color};color:#fff`),size:"small",round:""},{default:t(()=>[S(V(l(xe)(X.priority).label),1)]),_:2},1032,["style"])]),_:1}),e(L,{label:"事项",prop:"title","show-overflow-tooltip":"",align:"left"},{default:t(({row:X})=>[u("div",{flex:"~",onClick:se(ke=>te(X),["stop"])},[u("div",{class:"text",style:pe(`color:${le()}`),"p-r-10px":""},V(X.title),5)],8,ja)]),_:1}),e(L,{label:"倒计时",prop:"title",align:"left",width:"100"},{default:t(({row:X})=>[u("div",qa,[u("div",{class:"badge",style:pe(O(X))},V(q(X)<0?0:q(X)),5),T.type==="undo"&&q(X)<0?(r(),P("span",Ha,V(`+ ${Math.abs(q(X))}`),1)):Y("",!0)])]),_:2},1024),e(L,{label:"耗时",prop:"title",align:"center",width:"60"},{default:t(({row:X})=>[S(V(`${z(X)}天`),1)]),_:1}),e(L,{label:"操作",align:"center",width:"60"},{default:t(({row:X})=>[e(ce,null,{dropdown:t(()=>[e(G,null,{default:t(()=>{var ke;return[e(oe,{onClick:se(Be=>U(X),["stop"])},{default:t(()=>[S(V(m(X)),1)]),_:2},1032,["onClick"]),((ke=X==null?void 0:X.auditLogList)==null?void 0:ke.length)>0?(r(),E(oe,{key:0,onClick:se(Be=>K(X),["stop"])},{default:t(()=>[S(" 审批记录 ")]),_:2},1032,["onClick"])):Y("",!0),X.type===1&&l(h)==="myTask"&&(X==null?void 0:X.responsiblePersonId)===l(me)&&X.status===4?(r(),E(oe,{key:1,onClick:se(Be=>j(X),["stop"])},{default:t(()=>[S(V(T.type==="done"?"查看进展":"进展录入"),1)]),_:2},1032,["onClick"])):Y("",!0),C(X)&&l(h)==="myTask"?(r(),E(oe,{key:2,onClick:se(Be=>R(X),["stop"])},{default:t(()=>[S(" 提交审批 ")]),_:2},1032,["onClick"])):Y("",!0),T.type!=="done"&&(X==null?void 0:X.creatorId)===l(me)?(r(),E(oe,{key:3,onClick:se(Be=>c(X),["stop"])},{default:t(()=>[S(" 删除 ")]),_:2},1032,["onClick"])):Y("",!0)]}),_:2},1024)]),default:t(()=>[u("span",Aa,[e(ie,{size:"18px"},{default:t(()=>[e(N)]),_:1})])]),_:2},1024)]),_:2},1024)]),_:2},1032,["data"])]),_:1})):Y("",!0),e(L,{label:"",prop:"priority",align:"left",width:"50"},{default:t(({row:T})=>[e(Z,{style:pe(`background-color: ${l(xe)(T.priority).color};color:#fff`),size:"small",round:""},{default:t(()=>[S(V(l(xe)(T.priority).label),1)]),_:2},1032,["style"])]),_:1}),e(L,{label:"事项",prop:"title","show-overflow-tooltip":"",align:"left"},{default:t(({row:T})=>[u("div",{flex:"~",onClick:se(X=>te(T),["stop"])},[u("div",{class:"text",style:pe(`color:${le()}`),"p-r-10px":""},V(T.title),5),l(p)==="undo"&&l(x)==="page"?(r(),E(Ie,{key:0,value:T.unreadNum},null,8,["value"])):Y("",!0)],8,Oa)]),_:1}),e(L,{label:"倒计时",prop:"title",align:"left",width:"100"},{default:t(({row:T})=>[u("div",Wa,[u("div",{class:"badge",style:pe(O(T))},V(q(T)<0?0:q(T)),5),g.type==="undo"&&q(T)<0?(r(),P("span",Ja,V(`+ ${Math.abs(q(T))}`),1)):Y("",!0)])]),_:1}),e(L,{label:"耗时",prop:"title",align:"center",width:"60"},{default:t(({row:T})=>[S(V(`${z(T)}天`),1)]),_:1}),e(L,{label:"操作",align:"center",width:"60"},{default:t(({row:T})=>[e(ce,{"show-timeout":50},{dropdown:t(()=>[e(G,null,{default:t(()=>{var X;return[e(oe,{onClick:se(ke=>U(T),["stop"])},{default:t(()=>[S(V(m(T)),1)]),_:2},1032,["onClick"]),T.type===1&&l(h)==="myTask"&&(T==null?void 0:T.responsiblePersonId)===l(me)&&T.status===4?(r(),E(oe,{key:0,onClick:se(ke=>j(T),["stop"])},{default:t(()=>[S(V(g.type==="done"?"查看进展":"进展录入"),1)]),_:2},1032,["onClick"])):Y("",!0),C(T)?(r(),E(oe,{key:1,onClick:ke=>R(T)},{default:t(()=>[S(" 提交审批 ")]),_:2},1032,["onClick"])):Y("",!0),((X=T==null?void 0:T.auditLogList)==null?void 0:X.length)>0?(r(),E(oe,{key:2,onClick:se(ke=>K(T),["stop"])},{default:t(()=>[S(" 审批记录 ")]),_:2},1032,["onClick"])):Y("",!0),g.type!=="done"&&(T==null?void 0:T.creatorId)===l(me)?(r(),E(oe,{key:3,onClick:se(ke=>c(T),["stop"])},{default:t(()=>[S(" 删除 ")]),_:2},1032,["onClick"])):Y("",!0)]}),_:2},1024)]),default:t(()=>[u("span",Ka,[e(ie,{size:"20px"},{default:t(()=>[e(N)]),_:1})])]),_:2},1024)]),_:1})]),_:1},16,["data","height"])),[[M,l(a)]])]),_:1},8,["modelValue"]),l(_)?(r(),E(it,{key:0,modelValue:l(_),"onUpdate:modelValue":v[1]||(v[1]=T=>de(_)?_.value=T:null),workitem:l(s)},null,8,["modelValue","workitem"])):Y("",!0),l(y)?(r(),E(Na,{key:1,modelValue:l(y),"onUpdate:modelValue":v[2]||(v[2]=T=>de(y)?y.value=T:null),workitem:l(s),onSubmitSuccess:v[3]||(v[3]=T=>l(Te).refresh())},null,8,["modelValue","workitem"])):Y("",!0),l($)?(r(),E(Fa,{key:2,modelValue:l($),"onUpdate:modelValue":v[4]||(v[4]=T=>de($)?$.value=T:null),workitem:l(s)},null,8,["modelValue","workitem"])):Y("",!0)])}}}),tt=fe(Xa,[["__scopeId","data-v-abac0c78"]]),Za={flex:"~ items-center justify-between"},Qa={"p-r-20px":"",w:"100px"},eo={flex:"~","flex-1":""},to=A({name:"undefined"}),lo=A({...to,props:{title:{default:""},showAddBtn:{type:Boolean,default:!0},multiple:{type:Boolean,default:!1}},setup(w,{expose:B}){const d=w,g=Fe(),{height:D}=Oe(),h=_e(()=>D.value-545),p=f(""),x=f(),o=f(),s=f(),a=f(),_=f(""),y=f(!1),$=f("0"),k=f(4),I=f(""),b=f(),J=f({});ne(Ye).splice(1,0,{label:"审批中",value:-1,type:"info",color:"#909399"});function Q(){x==null||x.value.initData(p.value),o==null||o.value.initData(p.value)}function te(){var z;(z=x==null?void 0:x.value)==null||z.refresh(),o==null||o.value.refresh()}function le(){g["open-type"]==="workTask"?s.value.openDialog():g["open-type"]==="myTask"&&(J.value={},y.value=!0)}async function O(z,F){if(I.value=F,z.type&&z.type===2){J.value=z,y.value=!0;return}F==="done"&&(_.value="查看任务"),b.value=z,await s.value.clickRow(z)}async function q(z,F){if(I.value=F,z.type&&z.type===2){J.value=z,y.value=!0;return}b.value=z,a==null||a.value.open(z)}return B({refresh:te}),(z,F)=>{const U=i("el-input"),R=i("el-button"),j=i("el-option"),K=i("el-select"),c=i("el-col"),m=i("el-tag"),C=i("el-row"),n=i("el-card");return r(),P("div",null,[e(n,null,{header:t(()=>[u("div",Za,[u("span",Qa,V(d.title),1),u("div",eo,[e(U,{modelValue:p.value,"onUpdate:modelValue":F[0]||(F[0]=v=>p.value=v),placeholder:"请输入标题按回车查询",clearable:"",onKeyup:We(Q,["enter"])},null,8,["modelValue"]),e(R,{icon:"Refresh","m-l-10px":"",onClick:se(te,["stop"])}),d.showAddBtn?(r(),E(R,{key:0,type:"primary",icon:"Plus","m-l-10px":"",onClick:se(le,["stop"])})):Y("",!0)])]),Y("",!0)]),default:t(()=>[u("div",null,[e(tt,be({ref_key:"TaskCardTableRef",ref:x,height:l(h),type:"undo",disabled:!1},l(g),{onClickRow:O,onClickDynamic:q}),null,16,["height"]),e(tt,be({ref_key:"DoneTaskCardTableRef",ref:o,"show-header":!1,type:"done"},l(g),{onClickRow:O,onClickDynamic:q}),null,16)])]),_:1}),e(ut,be({ref_key:"TaskCardFormRef",ref:s},l(g),{refresh:te,type:I.value,title:_.value}),null,16,["type","title"]),e(ua,be({ref_key:"TaskCardDynamicRef",ref:a},l(g),{refresh:te,type:I.value,"row-data":b.value}),null,16,["type","row-data"]),y.value?(r(),E(Mt,be({key:0},l(g),{modelValue:y.value,"onUpdate:modelValue":F[3]||(F[3]=v=>y.value=v),refresh:te,type:I.value,"work-item":J.value}),null,16,["modelValue","type","work-item"])):Y("",!0)])}}}),lt=fe(lo,[["__scopeId","data-v-951d01ff"]]),ao=w=>($e("data-v-f29fa18f"),w=w(),Ve(),w),oo={flex:"~ justify-between items-center"},no=ao(()=>u("div",{"w-100px":""}," 流程审批 ",-1));const so={class:"text","p-r-10px":"",style:{color:"#409eff"}},io={"text-right":""},ro={key:0},uo={flex:"~ row items-center"},co={ml2:""},po={key:1},fo=A({name:"undefined"}),mo=A({...fo,setup(w){const{setDispatchMap:B}=Te,d=Fe(),{height:g}=Oe(),D=_e(()=>g.value-265),h=f([]),p=f({keyword:""}),x=f({total:0,page:1,size:10}),o=f(),s=f(!1),a=f(!1),_=f({}),y=f(),$=f({opinion:""}),k=f(""),I=f({status:0}),b=f([]);async function J(){b.value=await ee.pims.workitem.systemUserList();const c=await ee.pims.workitem.log.queryPage({...p.value,...x.value});x.value=c.pagination,h.value=c.list}J();async function H(){x.value.page=1,p.value.keyword="",await J()}async function Q(c){_.value=ne(c),c.type&&[1,3].includes(c.type)&&(c!=null&&c.businessId)&&(k.value=c.type===1?"任务审批":"任务抄送审批",_.value=ne(c),o==null||o.value.clickRow({id:c.businessId,openType:"audit"})),(c==null?void 0:c.type)===2&&(c!=null&&c.businessId)&&(y.value=Number(c==null?void 0:c.businessId),k.value="日报审批",a.value=!0)}function te(c){if(!c.submitTime)return"";const m=ae(new Date).diff(c.submitTime,"day");return m>0?`${m}天`:"0天"}function le(c){return c.title||""}function O(){$.value.opinion="",_.value={},a.value=!1}async function q(){try{s.value=!0,await ee.pims.daliyReport.readReport({...$.value,id:_.value.id,opinion:$.value.opinion}),W.success("操作成功"),O(),await H()}catch(c){console.error(c)}finally{s.value=!1}}async function z(){if(!I.value.agree)return W.error("请选择审批结果");try{s.value=!0;const c={id:_.value.id,title:_.value.title,businessId:_.value.businessId,workitemId:I.value.id,agree:I.value.agree,opinion:I.value.opinion};I.value.ccUserIds&&I.value.ccUserIds.length>0?c.ccUserIds=I.value.ccUserIds.join(","):c.ccUserIds="",await ee.pims.workitem.audit(c),W.success("审批成功"),U()}catch(c){W.error(c.message||"保存失败")}finally{s.value=!1,await H(),await Te.refresh()}}async function F(){try{s.value=!0,await ee.pims.workitem.readTask({id:_.value.id,opinion:I.value.opinion}),W.success("保存成功"),U(),await H()}catch(c){console.error(c)}finally{s.value=!1}}function U(){o.value.cancelTask()}function R(c){I.value=c}const j={1:"任务",2:"日报抄送",3:"任务抄送",4:"提交"};function K(c){return c.type?j[c.type]:""}return B("refreshTodoTable",H),(c,m)=>{const C=i("el-input"),n=i("el-button"),v=i("el-table-column"),L=i("el-table"),Z=i("el-card"),N=i("el-form-item"),ie=i("el-option"),oe=i("el-select"),G=i("el-col"),ce=i("el-row"),ve=i("el-radio"),Ie=i("el-radio-group");return r(),P("div",{style:pe(`height:${l(D)}px`)},[e(Z,null,{header:t(()=>[u("div",oo,[no,e(C,{modelValue:p.value.keyword,"onUpdate:modelValue":m[0]||(m[0]=M=>p.value.keyword=M),placeholder:"请输入标题按回车查询",clearable:"",onKeyup:We(J,["enter"]),onClear:H},null,8,["modelValue"]),e(n,{icon:"Refresh","m-l-10px":"",onClick:se(H,["stop"])})]),Y("",!0)]),default:t(()=>[e(L,{data:h.value,height:`${l(D)}px`,onRowClick:Q},{default:t(()=>[e(v,{label:"标题",prop:"title","show-overflow-tooltip":"",align:"left"},{default:t(({row:M})=>[u("div",so,V(le(M)),1)]),_:1}),e(v,{label:"类型",prop:"type","show-overflow-tooltip":"",align:"center",width:"100px"},{default:t(({row:M})=>[u("span",null,V(K(M)),1)]),_:1}),e(v,{label:"耗时",prop:"title",align:"center",width:"80"},{default:t(({row:M})=>[u("span",null,V(te(M)),1)]),_:1})]),_:1},8,["data","height"])]),_:1}),e(ot,{id:y.value,"onUpdate:id":m[2]||(m[2]=M=>y.value=M),modelValue:a.value,"onUpdate:modelValue":m[3]||(m[3]=M=>a.value=M),title:k.value,"open-type":"check",onRefresh:J},{footer:t(()=>[u("div",io,[e(n,{"ml-10px":"",onClick:O},{default:t(()=>[S(" 关闭 ")]),_:1}),e(n,{type:"success",loading:s.value,onClick:q},{default:t(()=>[S(" 已阅 ")]),_:1},8,["loading"])])]),default:t(()=>[e(N,{prop:"opinion",label:"处理意见"},{default:t(()=>[e(C,{modelValue:$.value.opinion,"onUpdate:modelValue":m[1]||(m[1]=M=>$.value.opinion=M),type:"textarea",placeholder:"请输入处理意见",maxlength:200,"show-word-limit":"",rows:2},null,8,["modelValue"])]),_:1})]),_:1},8,["id","modelValue","title"]),e(ut,be({ref_key:"TaskCardFormRef",ref:o},l(d),{refresh:H,"open-type":"audit",type:"done",title:"任务审批",onLoadedTask:R}),{"form-footer":t(()=>[e(Z,{mt:"20px"},{default:t(()=>[_.value.type===1?(r(),P("div",ro,[e(ce,{gutter:20,mt:"20px"},{default:t(()=>[e(G,{span:12},{default:t(()=>[e(N,{label:"审批人：",required:""},{default:t(()=>[e(oe,{modelValue:I.value.approver,"onUpdate:modelValue":m[4]||(m[4]=M=>I.value.approver=M),placeholder:"请选择审批人",multiple:"",disabled:""},{default:t(()=>[(r(!0),P(re,null,ue(b.value,M=>(r(),E(ie,{key:M.id,label:M.name,value:M.id},{default:t(()=>[u("div",uo,[e(Se,{src:M.headImg,size:30,title:M.name},null,8,["src","title"]),u("span",co,V(M.name),1)])]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(G,{span:12},{default:t(()=>[e(N,{label:"抄送：",prop:"ccUserIds"},{default:t(()=>[e(kt,{modelValue:I.value.ccUserIds,"onUpdate:modelValue":m[5]||(m[5]=M=>I.value.ccUserIds=M),"current-user":!1,width:"100%",multiple:""},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(ce,{gutter:20},{default:t(()=>[e(G,{span:12},{default:t(()=>[e(N,{label:"审批结果：",required:""},{default:t(()=>[e(Ie,{modelValue:I.value.agree,"onUpdate:modelValue":m[6]||(m[6]=M=>I.value.agree=M),class:"ml-4"},{default:t(()=>[e(ve,{value:"1",size:"large"},{default:t(()=>[S(" 同意 ")]),_:1}),e(ve,{value:"2",size:"large"},{default:t(()=>[S(" 驳回 ")]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(G,{span:12},{default:t(()=>[e(N,{label:"处理意见："},{default:t(()=>[e(C,{modelValue:I.value.opinion,"onUpdate:modelValue":m[7]||(m[7]=M=>I.value.opinion=M),type:"textarea",rows:2,maxlength:255,placeholder:"请输入处理意见"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})])):Y("",!0),_.value.type===3?(r(),P("div",po,[e(N,{label:"处理意见：","label-width":"100px"},{default:t(()=>[e(C,{modelValue:I.value.opinion,"onUpdate:modelValue":m[8]||(m[8]=M=>I.value.opinion=M),type:"textarea",rows:2,maxlength:255,placeholder:"请输入处理意见"},null,8,["modelValue"])]),_:1})])):Y("",!0)]),_:1})]),footer:t(()=>[e(n,{onClick:U},{default:t(()=>[S(" 取消 ")]),_:1}),_.value.type===1?(r(),E(n,{key:0,loading:s.value,type:"success",onClick:z},{default:t(()=>[S(" 确定 ")]),_:1},8,["loading"])):Y("",!0),_.value.type===3?(r(),E(n,{key:1,loading:s.value,type:"success",onClick:F},{default:t(()=>[S(" 已阅 ")]),_:1},8,["loading"])):Y("",!0)]),_:1},16)],4)}}}),_o=fe(mo,[["__scopeId","data-v-f29fa18f"]]),vo=w=>($e("data-v-63eac90e"),w=w(),Ve(),w),yo={flex:"~ items-center justify-between"},bo=vo(()=>u("span",{"p-r-20px":"",w:"80px"},"工作日记",-1)),ho={flex:"~","flex-1":""};const go=A({name:"undefined"}),ko=A({...go,setup(w){const{height:B}=Oe(),d=_e(()=>B.value-265),g=f([]),D=f({keyword:""}),h=f({total:0,page:1,size:50}),p=f(!1),x=f();async function o(){const I=await ee.pims.daliyReport.page({...D.value,...h.value});h.value=I.pagination,g.value=I.list}async function s(){h.value.page=1,D.value.keyword="",await o()}s();function a(){p.value=!0,x.value=0}function _(I){x.value=I.id,p.value=!0}function y(){return"#409eff"}function $(I){return`${ae(I.createTime).format("YYYY-MM-DD")} ${I.creator.name}的工作日记`}function k(I){const b=ne(I);return b.workTimeRange&&(b.workTimeRange=JSON.parse(b.workTimeRange)),b}return Ae(()=>{}),(I,b)=>{const J=i("el-input"),H=i("el-button"),Q=i("el-table-column"),te=i("el-table"),le=i("el-card");return r(),P("div",{style:pe(`height:${l(d)}px`)},[e(le,null,{header:t(()=>[u("div",yo,[bo,u("div",ho,[e(J,{modelValue:l(D).keyword,"onUpdate:modelValue":b[0]||(b[0]=O=>l(D).keyword=O),placeholder:"请输入标题按回车查询",clearable:"",onKeyup:We(o,["enter"]),onClear:s},null,8,["modelValue"]),e(H,{icon:"Refresh","m-l-10px":"",onClick:se(s,["stop"])}),e(H,{type:"primary",icon:"Plus","m-l-10px":"",onClick:se(a,["stop"])})])]),Y("",!0)]),default:t(()=>[e(te,{data:l(g),height:`${l(d)}px`,onRowClick:_},{default:t(()=>[e(Q,{label:"",type:"expand",align:"center",width:"60"},{default:t(({row:O})=>[e(nt,{form:k(O),title:"工作日记","open-type":"check"},null,8,["form"])]),_:1}),e(Q,{label:"标题",prop:"title","show-overflow-tooltip":"",align:"left"},{default:t(({row:O})=>[u("div",{class:"text","p-r-10px":"",style:pe(`color:${y()}`)},V($(O)),5)]),_:1}),e(Q,{label:"工时",prop:"title",align:"center",width:"80"},{default:t(({row:O})=>[S(V(`${O.estimatedWorkNum}小时`),1)]),_:1})]),_:1},8,["data","height"])]),_:1}),e(ot,{id:l(x),"onUpdate:id":b[1]||(b[1]=O=>de(x)?x.value=O:null),modelValue:l(p),"onUpdate:modelValue":b[2]||(b[2]=O=>de(p)?p.value=O:null),onRefresh:s},null,8,["id","modelValue"])],4)}}}),wo=fe(ko,[["__scopeId","data-v-63eac90e"]]),xo={"h-full":""},To=A({name:"undefined"}),Po=A({...To,setup(w){const B=f(),d=f(),{setDispatchMap:g}=Te;return Re(()=>{B.value&&g("refreshTaskTable",B.value.refresh),d.value&&g("refreshMyTaskTable",d.value.refresh)}),(D,h)=>{const p=i("el-col"),x=i("el-row");return r(),P("div",xo,[e(x,{gutter:20},{default:t(()=>[e(p,{span:6},{default:t(()=>[e(lt,{ref_key:"TaskCardRef",ref:B,title:"给别人的任务","req-method":"page","edit-type":"all","open-type":"workTask"},null,512)]),_:1}),e(p,{span:6},{default:t(()=>[e(lt,{ref_key:"TaskCardMyRef",ref:d,title:"我的任务","edit-type":"one","show-child":!1,"req-method":"myList","open-type":"myTask",multiple:""},null,512)]),_:1}),e(p,{span:6},{default:t(()=>[e(_o)]),_:1}),e(p,{span:6},{default:t(()=>[e(wo)]),_:1})]),_:1})])}}});export{Po as default};
