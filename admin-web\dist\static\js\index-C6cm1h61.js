import{u as o,a,r as i,g as f}from"./.pnpm-hVqhwuVC.js";import{u as c,a as p,s as m}from"./index-DkYL1aws.js";function l(){const t=i({});function r(s){return u=>{t[s]=u}}return{refs:t,setRefs:r}}function x(t,r){var u,n;const s=f();if(s){let e=(u=s.proxy)==null?void 0:u.$.parent;if(e){for(;e&&((n=e.type)==null?void 0:n.name)!==t;)e=e==null?void 0:e.parent;e&&e.type.name===t&&(r.value=e.exposed)}}return r}function y(){return{service:m,route:a(),router:o(),mitt:p(),...c(),...l()}}export{y as a,x as u};
