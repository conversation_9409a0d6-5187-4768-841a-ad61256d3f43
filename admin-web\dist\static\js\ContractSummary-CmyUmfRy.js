import{c as G,b as w,K as A,A as yt,aa as R,q as v,w as n,h as l,y as e,i as h,j as k,f as y,s as ft,F as bt,L as gt,G as B,H as j,B as x,v as O,ab as vt,t as r,E as f,T as S,o as m}from"./.pnpm-hVqhwuVC.js";import{i as U,e as H}from"./index-DkYL1aws.js";import{g as xt}from"./index-BFVs8cCE.js";import{u as kt}from"./table-ops-CrFIfhgA.js";import{a as Dt}from"./index-C6cm1h61.js";const Tt={style:{"margin-right":"20px"}},Pt={style:{"margin-right":"20px",width:"300px"}},Ft={key:0,class:"no-data-container"},qt=e("p",{class:"no-data-text"}," 该供应商在当前筛选条件下暂无合同数据 ",-1),At=e("p",{class:"no-data-subtext"}," 您可以尝试调整日期范围或搜索条件 ",-1),Ot={key:2,class:"deduct-section contract-section"},Et=e("h3",null,"合同统计信息",-1),Wt={class:"deduct-summary contract-summary"},Ct={class:"summary-row"},Mt={class:"summary-item"},It=e("span",{class:"summary-label"},"合同总数量：",-1),Vt={class:"summary-value"},Bt={class:"summary-item"},Ht=e("span",{class:"summary-label"},"已收总数量：",-1),Lt={class:"summary-value"},Qt={class:"summary-item"},Nt=e("span",{class:"summary-label"},"在途总数量：",-1),Yt={class:"summary-value"},$t={class:"summary-row"},zt={class:"summary-item"},Kt=e("span",{class:"summary-label"},"本期入库总数量：",-1),Rt={class:"summary-value"},jt={class:"summary-item"},St=e("span",{class:"summary-label"},"本期出库总数量：",-1),Ut={class:"summary-value"},Gt={class:"summary-item"},Jt=e("span",{class:"summary-label"},"本期合同总金额：",-1),Xt={class:"summary-value",style:{color:"red","font-weight":"bold"}},Zt={key:3,class:"deduct-section"},te=e("h3",null,"工时扣款信息",-1),ee={key:1},ae={class:"ellipsis-text"},ne={class:"deduct-summary"},le=e("span",null,"工时扣款总金额：",-1),oe={class:"deduct-amount"},re={key:4,class:"deduct-section"},se=e("h3",null,"物料扣款信息",-1),ie={key:1},ue={class:"ellipsis-text"},de={class:"deduct-summary"},ce=e("span",null,"物料扣款总金额：",-1),_e={class:"deduct-amount"},me={key:5,class:"deduct-section return-policy-section"},pe=e("h3",null,"补退货信息",-1),he={class:"deduct-summary"},we=e("span",null,"补退货总数量：",-1),ye={class:"deduct-amount"},fe={class:"deduct-summary"},be=e("span",null,"补退货总金额：",-1),ge={class:"deduct-amount"},ve={key:6,class:"deduct-section total-section"},xe={class:"deduct-summary total"},ke=e("span",null,"本期合同总金额：",-1),De={class:"deduct-amount"},Te={class:"deduct-summary total"},Pe=e("span",null,"扣款总金额：",-1),Fe={class:"deduct-amount"},qe={key:0,class:"deduct-summary total"},Ae=e("span",null,"补退货总金额：",-1),Oe={class:"deduct-amount"},Ee={class:"deduct-summary total"},We=e("span",null,"应付金额：",-1),Ce={class:"deduct-amount"},Me=G({name:"pms-contract-summary"}),Ne=G({...Me,setup(Ie){const L=w([]),Q=w([]),E=w(!1),D=w(!1),{service:b}=Dt(),T=w(!1),W=w(!1),C=w(!1),p=w({date:[A().startOf("month").toDate(),A().endOf("month").toDate()],keyWord:"",status:void 0}),N=w({"slot-btn-export":{width:220,show:!0},"slot-btn-updateStatus":{width:220,show:!0}});async function J(){try{const o=await b.pms.material.request({url:"/list",method:"POST"});Q.value=o.map(s=>({...s,value:s.id,label:`${s.name} / ${s.code}`}))}catch(o){console.error(o)}}async function X(){try{L.value=await b.pms.supplier.request({url:"/list",method:"POST"})}catch(o){console.error(o)}}yt(()=>{J(),X()});const{getOpWidth:Z}=kt(N);w(Z()+120);const Y=U.useTable({columns:[{label:"展开",prop:"contract",type:"expand",width:60},{label:"供应商名",prop:"name",minWidth:260},{label:"联系人",prop:"contact",minWidth:160},{label:"当前合同总金额",prop:"contractTotalAmount",minWidth:260},{label:"当前工时扣款金额",prop:"manHourDeductAmount",minWidth:260},{label:"当前物料扣款金额",prop:"materialDeductAmount",minWidth:260},{label:"当前扣款总金额",minWidth:160,formatter(o){return o.manHourDeductAmount+o.materialDeductAmount}},{label:"当前补退货金额",minWidth:160,formatter(o){return!o.return_policy||o.return_policy.length===0?0:o.return_policy.reduce((s,u)=>s+u.quantity*u.unitPrice,0).toFixed(2)}},{label:"应付金额",minWidth:160,formatter(o){const s=o.return_policy?o.return_policy.reduce((u,c)=>u+c.quantity*c.unitPrice,0):0;return(o.contractTotalAmount-o.manHourDeductAmount-o.materialDeductAmount+s).toFixed(2)}},{type:"op",label:"操作",width:320,buttons:Object.keys(N.value)}]}),_=U.useCrud({dict:{api:{page:"financePaymentPage"}},service:b.pms.payment,async onRefresh(o,{next:s,done:u,render:c}){D.value=!0,u(),M(o),o.with=["contract","man_hour_deduct","material_deduct","return_policy"];try{const{list:g,pagination:F}=await s(o);c(g,F)}catch(g){console.error("加载数据失败",g)}finally{D.value=!1}}},o=>{o.refresh()});function M(o){if(p.value.date&&p.value.date.length>0){const s=R(p.value.date[0]).startOf("day").format("YYYY-MM-DD HH:mm:ss"),u=R(p.value.date[1]).endOf("day").format("YYYY-MM-DD HH:mm:ss");o.date=`${s},${u}`}return p.value.keyWord&&(o.keyWord=p.value.keyWord),p.value.supplierId&&(o.supplierId=p.value.supplierId),o}function I(){var o,s;p.value.keyWord="",p.value.supplierId=void 0,p.value.date=[A().startOf("month").toDate(),A().endOf("month").toDate()],(o=_==null?void 0:_.value)!=null&&o.params&&(_.value.params.page=1,_.value.params.size=20),(s=_==null?void 0:_.value)==null||s.refresh()}async function P(){var o,s,u;try{(s=(o=_==null?void 0:_.value)==null?void 0:o.params)!=null&&s.page&&(_.value.params.page=1),E.value=!0,D.value=!0,(u=_==null?void 0:_.value)==null||u.refresh()}catch(c){console.error(c)}finally{E.value=!1}}function tt(o){o?P():I()}const et=w([]);function at(o,s){var u;(s==null?void 0:s.type)==="expand"||(s==null?void 0:s.type)==="op"||(u=Y.value)==null||u.toggleRowExpansion(o)}function nt(o){T.value=!0;const s={url:"/export",method:"POST",responseType:"blob",data:{finance_payment_vo:o}};b.pms.payment.request(s).then(u=>{var c;H(u)&&f.success("导出成功"),(c=_.value)==null||c.refresh()}).catch(u=>{f.error(u.message||"导出失败")}).finally(()=>{T.value=!1})}function lt(){S.confirm("数据量过大，请耐心等待导出。是否继续？","导出提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{const o={};M(o),o.with=["contract","man_hour_deduct","material_deduct","return_policy"],o.page=1,o.size=1e6,C.value=!0;const s={url:"/summaryExport",method:"POST",responseType:"blob",data:o};b.pms.payment.request(s).then(u=>{var c;H(u)&&f.success("导出成功"),(c=_.value)==null||c.refresh()}).catch(u=>{f.error(u.message||"导出失败")}).finally(()=>{C.value=!1})}).catch(()=>{f.info("已取消导出")})}function ot(){const o={};M(o),o.with=["contract","man_hour_deduct","material_deduct","return_policy"],o.page=1,o.size=1e6,W.value=!0;const s={url:"/batchExport",method:"POST",responseType:"blob",data:o};b.pms.payment.request(s).then(u=>{var c;H(u)&&f.success("导出成功"),(c=_.value)==null||c.refresh()}).catch(u=>{f.error(u.message||"导出失败")}).finally(()=>{W.value=!1})}function rt(o){S.prompt(`将对供应商"${o.name}"当前日期范围的合同添加付款状态，请输入付款状态描述：`,"更新付款状态",{confirmButtonText:"确认",cancelButtonText:"取消",inputPattern:/\S+/,inputErrorMessage:"付款状态描述不能为空",inputPlaceholder:"请输入付款状态描述",inputType:"textarea"}).then(({value:s})=>{let u=o.contract.map(c=>c.id);b.pms.purchase.contract.updateContractStatus({ids:u,payment_status:s}).then(c=>{var g;f.success("完成入库成功"),(g=_.value)==null||g.refresh()}).catch(c=>{f.error(c.message)})}).catch(()=>{f.info("已取消操作")})}return(o,s)=>{const u=h("el-button"),c=h("cl-flex1"),g=h("el-date-picker"),F=h("el-option"),st=h("el-select"),it=h("el-input"),V=h("el-row"),ut=h("el-icon"),dt=h("el-empty"),i=h("el-table-column"),q=h("el-table"),$=h("el-image"),z=h("el-tooltip"),ct=h("el-tag"),_t=h("cl-table"),mt=h("cl-pagination"),pt=h("cl-crud"),K=j("permission"),ht=j("loading");return m(),v(pt,{ref_key:"Crud",ref:_},{default:n(()=>[l(V,null,{default:n(()=>[l(u,{onClick:I},{default:n(()=>[k(" 刷新 ")]),_:1}),l(c),e("div",Tt,[l(g,{modelValue:p.value.date,"onUpdate:modelValue":s[0]||(s[0]=a=>p.value.date=a),type:"daterange",clearable:"","range-separator":"~","start-placeholder":"开始日期","end-placeholder":"结束日期",onChange:tt},null,8,["modelValue"])]),e("div",Pt,[l(st,{modelValue:p.value.supplierId,"onUpdate:modelValue":s[1]||(s[1]=a=>p.value.supplierId=a),placeholder:"请选择供应商",filterable:"",onChange:P},{default:n(()=>[l(F,{label:"全部",value:""}),(m(!0),y(bt,null,ft(L.value,a=>(m(),v(F,{key:a.id,label:a.supplierName,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),l(it,{modelValue:p.value.keyWord,"onUpdate:modelValue":s[2]||(s[2]=a=>p.value.keyWord=a),placeholder:"请输入物料编号",style:{width:"200px"},clearable:"",onClear:I,onKeyup:gt(P,["enter"])},null,8,["modelValue"]),l(u,{type:"primary",mx:"10px",loading:E.value,onClick:P},{default:n(()=>[k(" 搜索 ")]),_:1},8,["loading"]),l(u,{type:"warning",mx:"10px",loading:W.value,onClick:ot},{default:n(()=>[k(" 批量导出 ")]),_:1},8,["loading"]),l(u,{type:"success",mx:"10px",loading:C.value,onClick:lt},{default:n(()=>[k(" 导出汇总 ")]),_:1},8,["loading"])]),_:1}),l(V,null,{default:n(()=>[B((m(),v(_t,{ref_key:"Table",ref:Y,"row-key":"id","expand-row-keys":et.value,class:"table-row-pointer",onRowClick:at},{"slot-btn-export":n(({scope:a})=>[B((m(),v(u,{type:"success",loading:T.value,onClick:t=>nt(a.row)},{default:n(()=>[k(" 导出 ")]),_:2},1032,["loading","onClick"])),[[K,O(b).pms.payment.permission.export]])]),"slot-btn-updateStatus":n(({scope:a})=>[B((m(),v(u,{type:"primary",disabled:a.row.contract==null,loading:T.value,onClick:t=>rt(a.row)},{default:n(()=>[k(" 更新付款状态 ")]),_:2},1032,["disabled","loading","onClick"])),[[K,O(b).pms.purchase.contract.permission.updateContractStatus]])]),"column-contract":n(({scope:a})=>[(!a.row.contract||a.row.contract.length===0)&&(!a.row.man_hour_deduct||a.row.man_hour_deduct.length===0)&&(!a.row.material_deduct||a.row.material_deduct.length===0)&&(!a.row.return_policy||a.row.return_policy.length===0)?(m(),y("div",Ft,[l(dt,{description:"暂无合同数据","image-size":120},{image:n(()=>[l(ut,{class:"no-data-icon"},{default:n(()=>[l(O(vt))]),_:1})]),description:n(()=>[qt,At]),_:1})])):x("",!0),a.row.contract&&a.row.contract.length>0?(m(),v(q,{key:1,data:a.row.contract,style:{width:"100%"},border:""},{default:n(()=>[l(i,{label:"PO号",align:"center","min-width":"220"},{default:n(t=>[e("span",null,r(t.row.po),1)]),_:1}),l(i,{label:"物料编码",align:"center","min-width":"220"},{default:n(t=>[e("span",null,r(t.row.material.code),1)]),_:1}),l(i,{label:"物料名称",align:"center","min-width":"120"},{default:n(t=>[e("span",null,r(t.row.material.name),1)]),_:1}),l(i,{label:"合同单价","min-width":"120",align:"center"},{default:n(t=>[e("span",null,r(t.row.unitPrice),1)]),_:1}),l(i,{label:"合同数量","min-width":"120",align:"center"},{default:n(t=>[e("span",null,r(t.row.quantity),1)]),_:1}),l(i,{label:"已收数量","min-width":"120",align:"center"},{default:n(t=>[e("span",null,r(t.row.receivedQuantity),1)]),_:1}),l(i,{label:"合同金额","min-width":"120",align:"center"},{default:n(t=>[e("span",null,r(t.row.subtotal),1)]),_:1}),l(i,{label:"本期入库数量","min-width":"120",align:"center"},{default:n(t=>[e("span",null,r(t.row.curInboundQuantity),1)]),_:1}),l(i,{label:"本期出库数量","min-width":"120",align:"center"},{default:n(t=>[e("span",null,r(t.row.curOutboundQuantity),1)]),_:1})]),_:2},1032,["data"])):x("",!0),a.row.contract&&a.row.contract.length>0?(m(),y("div",Ot,[Et,e("div",Wt,[e("div",Ct,[e("div",Mt,[It,e("span",Vt,r(a.row.contract.reduce((t,d)=>t+d.quantity,0).toFixed(2)),1)]),e("div",Bt,[Ht,e("span",Lt,r(a.row.contract.reduce((t,d)=>t+d.receivedQuantity,0).toFixed(2)),1)]),e("div",Qt,[Nt,e("span",Yt,r(a.row.contract.reduce((t,d)=>t+d.expectedQuantity,0).toFixed(2)),1)])]),e("div",$t,[e("div",zt,[Kt,e("span",Rt,r(a.row.contract.reduce((t,d)=>t+d.curInboundQuantity,0).toFixed(2)),1)]),e("div",jt,[St,e("span",Ut,r(a.row.contract.reduce((t,d)=>t+d.curOutboundQuantity,0).toFixed(2)),1)]),e("div",Gt,[Jt,e("span",Xt,r(a.row.contractTotalAmount.toFixed(2))+" 元",1)])])])])):x("",!0),a.row.man_hour_deduct&&a.row.man_hour_deduct.length>0?(m(),y("div",Zt,[te,l(q,{data:a.row.man_hour_deduct,style:{width:"100%"},border:""},{default:n(()=>[l(i,{label:"事故时间","min-width":"180",align:"center"},{default:n(t=>[e("span",null,r(t.row.accident_time),1)]),_:1}),l(i,{label:"扣款号","min-width":"220",align:"center"},{default:n(t=>[e("span",null,r(t.row.no),1)]),_:1}),l(i,{label:"不良物料名称","min-width":"220",align:"center"},{default:n(t=>[e("span",null,r(t.row.badMaterialName),1)]),_:1}),l(i,{label:"凭证","min-width":"120",align:"center"},{default:n(t=>[t.row.voucher?(m(),v($,{key:0,src:t.row.voucher,"preview-src-list":[t.row.voucher],fit:"cover",style:{width:"80px",height:"80px",cursor:"zoom-in","border-radius":"4px","box-shadow":"0 2px 6px rgba(0, 0, 0, 0.1)",transition:"transform 0.3s ease"},"preview-teleported":!0,onMouseenter:s[3]||(s[3]=d=>d.target.style.transform="scale(1.05)"),onMouseleave:s[4]||(s[4]=d=>d.target.style.transform="scale(1)")},null,8,["src","preview-src-list"])):(m(),y("span",ee,"无"))]),_:1}),l(i,{label:"人数","min-width":"120",align:"center"},{default:n(t=>[e("span",null,r(t.row.number_of_people),1)]),_:1}),l(i,{label:"工时总计","min-width":"120",align:"center"},{default:n(t=>[e("span",null,r(t.row.man_hour),1)]),_:1}),l(i,{label:"人工费率(元/h)",width:"220",align:"center"},{default:n(t=>[e("span",null,r(t.row.labor_rate),1)]),_:1}),l(i,{label:"人均工时","min-width":"120",align:"center"},{default:n(t=>[e("span",null,r((t.row.man_hour/t.row.number_of_people).toFixed(2)),1)]),_:1}),l(i,{label:"扣款金额","min-width":"120",align:"center"},{default:n(t=>[e("span",null,r(t.row.total_amount),1)]),_:1}),l(i,{label:"描述",align:"center"},{default:n(t=>[l(z,{content:t.row.description,placement:"top","show-after":200,enterable:!1},{default:n(()=>[e("span",ae,r(t.row.description),1)]),_:2},1032,["content"])]),_:1})]),_:2},1032,["data"]),e("div",ne,[le,e("span",oe,r(a.row.manHourDeductAmount.toFixed(2))+" 元",1)])])):x("",!0),a.row.material_deduct&&a.row.material_deduct.length>0?(m(),y("div",re,[se,l(q,{data:a.row.material_deduct,style:{width:"100%"},border:""},{default:n(()=>[l(i,{label:"事故时间","min-width":"180",align:"center"},{default:n(t=>[e("span",null,r(t.row.accident_time),1)]),_:1}),l(i,{label:"扣款号","min-width":"220",align:"center"},{default:n(t=>[e("span",null,r(t.row.no),1)]),_:1}),l(i,{label:"不良物料名称","min-width":"220",align:"center"},{default:n(t=>[e("span",null,r(t.row.badMaterialName),1)]),_:1}),l(i,{label:"报废物料名称","min-width":"220",align:"center"},{default:n(t=>[e("span",null,r(t.row.materialName),1)]),_:1}),l(i,{label:"凭证","min-width":"120",align:"center"},{default:n(t=>[t.row.voucher?(m(),v($,{key:0,src:t.row.voucher,"preview-src-list":[t.row.voucher],fit:"cover",style:{width:"80px",height:"80px",cursor:"zoom-in","border-radius":"4px","box-shadow":"0 2px 6px rgba(0, 0, 0, 0.1)",transition:"transform 0.3s ease"},"preview-teleported":!0,onMouseenter:s[5]||(s[5]=d=>d.target.style.transform="scale(1.05)"),onMouseleave:s[6]||(s[6]=d=>d.target.style.transform="scale(1)")},null,8,["src","preview-src-list"])):(m(),y("span",ie,"无"))]),_:1}),l(i,{label:"数量","min-width":"120",align:"center"},{default:n(t=>[e("span",null,r(t.row.quantity),1)]),_:1}),l(i,{label:"扣款单价","min-width":"120",align:"center"},{default:n(t=>[e("span",null,r(t.row.unitPrice),1)]),_:1}),l(i,{label:"扣款金额","min-width":"120",align:"center"},{default:n(t=>[e("span",null,r(t.row.total_amount),1)]),_:1}),l(i,{label:"描述",align:"center"},{default:n(t=>[l(z,{content:t.row.description,placement:"top","show-after":200,enterable:!1},{default:n(()=>[e("span",ue,r(t.row.description),1)]),_:2},1032,["content"])]),_:1})]),_:2},1032,["data"]),e("div",de,[ce,e("span",_e,r(a.row.materialDeductAmount.toFixed(2))+" 元",1)])])):x("",!0),a.row.return_policy&&a.row.return_policy.length>0?(m(),y("div",me,[pe,l(q,{data:a.row.return_policy,style:{width:"100%"},border:""},{default:n(()=>[l(i,{label:"物料名称",align:"center","min-width":"180"},{default:n(t=>{var d;return[e("span",null,r((d=Q.value.find(wt=>wt.id===t.row.materialId))==null?void 0:d.label),1)]}),_:1}),l(i,{label:"仓位","min-width":"180",align:"center"},{default:n(t=>[e("span",null,r(t.row.address),1)]),_:1}),l(i,{label:"合同ID","min-width":"220",align:"center"},{default:n(t=>[e("span",null,r(t.row.contractId),1)]),_:1}),l(i,{label:"入库ID","min-width":"220",align:"center"},{default:n(t=>[e("span",null,r(t.row.inboundId),1)]),_:1}),l(i,{label:"关键字","min-width":"120",align:"center"},{default:n(t=>[l(ct,{type:t.row.inbound_outbound_key===0?"success":"warning"},{default:n(()=>[k(r(t.row.inbound_outbound_key===0?"无":O(xt)("inbound_outbound_key",t.row.inbound_outbound_key)),1)]),_:2},1032,["type"])]),_:1}),l(i,{label:"PO号","min-width":"180",align:"center"},{default:n(t=>[e("span",null,r(t.row.po||"-"),1)]),_:1}),l(i,{label:"数量","min-width":"120",align:"center"},{default:n(t=>[e("span",null,r(t.row.quantity),1)]),_:1}),l(i,{label:"单价","min-width":"120",align:"center"},{default:n(t=>[e("span",null,r(t.row.unitPrice),1)]),_:1}),l(i,{label:"金额","min-width":"120",align:"center"},{default:n(t=>[e("span",null,r((t.row.quantity*t.row.unitPrice).toFixed(2)),1)]),_:1})]),_:2},1032,["data"]),e("div",he,[we,e("span",ye,r(a.row.return_policy.reduce((t,d)=>t+d.quantity,0).toFixed(2)),1)]),e("div",fe,[be,e("span",ge,r(a.row.return_policy.reduce((t,d)=>t+d.quantity*d.unitPrice,0).toFixed(2))+" 元",1)])])):x("",!0),a.row.contract&&a.row.contract.length>0||a.row.man_hour_deduct&&a.row.man_hour_deduct.length>0||a.row.material_deduct&&a.row.material_deduct.length>0||a.row.return_policy&&a.row.return_policy.length>0?(m(),y("div",ve,[e("div",xe,[ke,e("span",De,r(a.row.contractTotalAmount.toFixed(2))+" 元",1)]),e("div",Te,[Pe,e("span",Fe,r((a.row.manHourDeductAmount+a.row.materialDeductAmount).toFixed(2))+" 元",1)]),a.row.return_policy&&a.row.return_policy.length>0?(m(),y("div",qe,[Ae,e("span",Oe,r(a.row.return_policy.reduce((t,d)=>t+d.quantity*d.unitPrice,0).toFixed(2))+" 元",1)])):x("",!0),e("div",Ee,[We,e("span",Ce,r((a.row.contractTotalAmount-a.row.manHourDeductAmount-a.row.materialDeductAmount+(a.row.return_policy?a.row.return_policy.reduce((t,d)=>t+d.quantity*d.unitPrice,0):0)).toFixed(2))+" 元",1)])])):x("",!0)]),_:1},8,["expand-row-keys"])),[[ht,D.value]])]),_:1}),l(V,null,{default:n(()=>[l(c),l(mt)]),_:1})]),_:1},512)}}});export{Ne as default};
