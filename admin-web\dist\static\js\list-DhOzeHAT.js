import{_ as t}from"./space-inner.vue_vue_type_style_index_0_lang-DXy-9KmJ.js";import{c as o,q as r,o as e}from"./.pnpm-hVqhwuVC.js";import"./index-BtOcqcNl.js";import"./index-D95m1iJL.js";import"./hook-BXXLBjrX.js";import"./viewer.vue_vue_type_script_setup_true_name_item-viewer_lang-B08Tkdj3.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";const p=o({name:"upload-list"}),d=o({...p,setup(a){return(m,_)=>(e(),r(t))}});export{d as default};
