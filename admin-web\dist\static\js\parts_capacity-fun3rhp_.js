import{i as m}from"./index-DkYL1aws.js";import{c as S,b as c,A as G,q as W,w as l,h as a,i as p,j as y,f as C,s as x,F as q,o as d}from"./.pnpm-hVqhwuVC.js";import{a as z}from"./index-C6cm1h61.js";import{u as D}from"./table-ops-CrFIfhgA.js";const J=S({name:"pms-product-group"}),Z=S({...J,setup(M){const u=c([]),s=c([]),{service:i}=z(),v=m.useCrud({service:i.pms.PartsCapacity},e=>{e.refresh()});m.useSearch({items:[{label:"sku",prop:"keyWord",props:{labelWidth:"120px"},component:{name:"el-input",props:{clearable:!1,onChange(e){var t;(t=v.value)==null||t.refresh({keyWord:e.trim(),page:1})}}}}]});async function T(){try{const e=await i.pms.product.request({url:"/getAllProduct",method:"GET"});s.value=e==null?void 0:e.map(t=>({group_id:t.groupId,value:t.id,sku:t.sku,label:`${t.sku} ${t.name}`,name:t.name})),u.value=s.value}catch(e){console.error(e)}}G(()=>{T()});const k=c({edit:{width:80,permission:i.pms.PartsCapacity.permission.update,show:!0},delete:{width:80,permission:i.pms.PartsCapacity.permission.delete,show:!0}}),{getOpWidth:O,getOpIsHidden:P}=D(k),B=c(O()),L=c(P()),V=[{label:"加工段",value:1,name:"-",nameEn:"-",type:"info"},{label:"组装段",value:2,name:"-",nameEn:"-",type:"info"},{label:"老化段",value:3,name:"-",nameEn:"-",type:"info"},{label:"包装段",value:4,name:"-",nameEn:"-",type:"info"},{label:"加工段一",value:5,name:"-",nameEn:"-",type:"info"},{label:"加工段二",value:6,name:"-",nameEn:"-",type:"info"},{label:"加工段三",value:7,name:"-",nameEn:"-",type:"info"},{label:"芯子配件加工段一",value:8,name:"-",nameEn:"-",type:"info"},{label:"芯子配件加工段二",value:9,name:"-",nameEn:"-",type:"info"},{label:"芯子配件组装段一",value:10,name:"-",nameEn:"-",type:"info"},{label:"芯子配件组装段二",value:11,name:"-",nameEn:"-",type:"info"},{label:"芯子配件组装段三",value:12,name:"-",nameEn:"-",type:"info"},{label:"芯子配件组装段四",value:13,name:"-",nameEn:"-",type:"info"},{label:"芯子配件组装段五",value:14,name:"-",nameEn:"-",type:"info"},{label:"芯子配件包装段",value:15,name:"-",nameEn:"-",type:"info"}],w=m.useUpsert({props:{labelWidth:"120px",class:"product-form"},items:[{label:"选择产品",prop:"sku",required:!0,component:{name:"slot-product-select"}},{label:"配件名称",prop:"product_name",required:!0,component:{name:"el-input",props:{disabled:!0}}},{label:"描述",prop:"description",required:!0,component:{name:"el-input"}},{label:"工段",prop:"workshop_section",required:!0,component:{name:"slot-workshop-section-select"}},{label:"试产产能",prop:"pilot_production_capacity",required:!0,component:{name:"slot-input-pilot-production"}},{label:"首次量产产能",prop:"first_mass_production_capacity",required:!0,component:{name:"slot-input-first-production"}},{label:"量产产能",prop:"mass_production_capacity",required:!0,component:{name:"slot-input-mass-production"}},{label:"备注",prop:"re_mark",required:!0,component:{name:"el-input"}}]}),K=m.useTable({columns:[{label:"创建日期",prop:"createTime",minWidth:120},{label:"SKU",prop:"sku",minWidth:120},{label:"配件名称",prop:"product_name",minWidth:120},{label:"工段",prop:"workshop_section",minWidth:120,formatter(e){const t=V.find(r=>r.value===e.workshop_section);return t?t.label:""}},{label:"描述",prop:"description",minWidth:120},{label:"备注",prop:"re_mark",minWidth:120},{label:"试产产能",prop:"pilot_production_capacity",minWidth:120},{label:"首次量产产能",prop:"first_mass_production_capacity",minWidth:80},{label:"量产产能",prop:"mass_production_capacity",minWidth:80},{type:"op",label:"操作",width:B,hidden:L,buttons:Object.keys(k.value)}]});function F(e){e=e.trim(),e||(u.value=s.value),u.value=s.value.filter(t=>t.sku.toLowerCase().includes(e.toLowerCase()))}function _(e){return/^\d+(?:\.\d{0,2})?$/.test(e)?e:e.slice(0,-1)}function I(e){var r;console.log(e);const t=s.value.find(f=>f.sku===e);console.log("product",t),t&&((r=w.value)==null||r.setForm("product_name",t.name))}return(e,t)=>{const r=p("cl-add-btn"),f=p("cl-refresh-btn"),g=p("cl-flex1"),N=p("cl-search-key"),b=p("el-row"),$=p("cl-table"),j=p("cl-pagination"),E=p("el-option"),U=p("el-select"),h=p("el-input"),A=p("cl-upsert"),H=p("cl-crud");return d(),W(H,{ref_key:"Crud",ref:v},{default:l(()=>[a(b,null,{default:l(()=>[a(r),a(f),a(g),a(N,{placeholder:"请输入SKU查询"})]),_:1}),a(b,null,{default:l(()=>[a($,{ref_key:"Table",ref:K},null,512)]),_:1}),a(b,null,{default:l(()=>[a(g),a(j)]),_:1}),a(A,{ref_key:"Upsert",ref:w},{"slot-product-select":l(({scope:o})=>[a(U,{modelValue:o.sku,"onUpdate:modelValue":n=>o.sku=n,filterable:"",remote:"",placeholder:"输入配件SKU查询","remote-method":F,onChange:n=>{o.sku=n,o.description="",o.workshop_section=void 0,o.pilot_production_capacity=void 0,o.first_mass_production_capacity=void 0,o.mass_production_capacity=void 0,o.re_mark="",I(n)}},{default:l(()=>[(d(!0),C(q,null,x(u.value,n=>(d(),W(E,{key:n.value,label:n.label,value:n.sku},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])]),"slot-workshop-section-select":l(({scope:o})=>[a(U,{modelValue:o.workshop_section,"onUpdate:modelValue":n=>o.workshop_section=n,placeholder:"请选择工段"},{default:l(()=>[(d(),C(q,null,x(V,n=>a(E,{key:n.value,label:n.label,value:n.value},null,8,["label","value"])),64))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),"slot-input-pilot-production":l(({scope:o})=>[a(h,{modelValue:o.pilot_production_capacity,"onUpdate:modelValue":n=>o.pilot_production_capacity=n,type:"number",placeholder:"请输入试产产能",formatter:_,step:"0.01"},{append:l(()=>[y(" pcs/h ")]),_:2},1032,["modelValue","onUpdate:modelValue"])]),"slot-input-first-production":l(({scope:o})=>[a(h,{modelValue:o.first_mass_production_capacity,"onUpdate:modelValue":n=>o.first_mass_production_capacity=n,type:"number",placeholder:"请输入首次量产产能",formatter:_,step:"0.01"},{append:l(()=>[y(" pcs/h ")]),_:2},1032,["modelValue","onUpdate:modelValue"])]),"slot-input-mass-production":l(({scope:o})=>[a(h,{modelValue:o.mass_production_capacity,"onUpdate:modelValue":n=>o.mass_production_capacity=n,type:"number",placeholder:"请输入量产产能",formatter:_,step:"0.01"},{append:l(()=>[y(" pcs/h ")]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:1},512)]),_:1},512)}}});export{Z as default};
