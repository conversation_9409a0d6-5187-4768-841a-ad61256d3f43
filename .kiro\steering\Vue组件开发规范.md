# Vue 组件开发规范

## 组件结构规范

### 单文件组件结构
```vue
<script lang="ts" name="component-name" setup>
// 1. 导入依赖
import { ref, computed, watchEffect } from 'vue'
import { ElMessage } from 'element-plus'

// 2. 类型定义
interface ComponentProps {
  // 属性定义
}

// 3. 组件属性
const props = defineProps<ComponentProps>()

// 4. 响应式数据
const loading = ref(false)
const formData = ref({})

// 5. 计算属性
const computedValue = computed(() => {
  // 计算逻辑
})

// 6. 方法定义
function handleSubmit() {
  // 处理逻辑
}

// 7. 生命周期
watchEffect(() => {
  // 副作用逻辑
})
</script>

<template>
  <!-- 模板内容 -->
</template>

<style lang="scss" scoped>
/* 样式定义 */
</style>
```

## Cool-Admin-Vue 框架规范

### CRUD 组件使用规范
```typescript
// 使用 useCrud 钩子
const Crud = useCrud(
  {
    service: service.moduleName.entityName,
  },
  (app) => {
    app.refresh()
  },
)

// 使用 useSearch 钩子
const Search = useSearch({
  items: [
    {
      label: '搜索标签',
      prop: 'searchField',
      component: {
        name: 'el-input',
        props: {
          placeholder: '请输入搜索内容',
          clearable: true,
        },
      },
    },
  ],
})

// 使用 useTable 钩子
const Table = useTable({
  columns: [
    { type: 'selection' },
    {
      label: '列标题',
      prop: 'fieldName',
      minWidth: 120,
      formatter: (row) => {
        // 格式化逻辑
        return row.fieldName
      },
    },
  ],
})

// 使用 useUpsert 钩子
const Upsert = useUpsert({
  items: [
    {
      label: '字段标签',
      prop: 'fieldName',
      required: true,
      component: {
        name: 'el-input',
        props: {
          placeholder: '请输入内容',
        },
      },
    },
  ],
})
```

## 表单处理规范

### 表单验证
```typescript
// 定义验证规则
const rules = {
  fieldName: [
    { required: true, message: '请输入字段名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' },
  ],
}

// 表单提交处理
async function handleSubmit() {
  try {
    loading.value = true
    await service.moduleName.entityName.save(formData.value)
    ElMessage.success('保存成功')
    Crud.value?.refresh()
  } catch (error: any) {
    ElMessage.error(error.message || '保存失败')
  } finally {
    loading.value = false
  }
}
```

## 数据处理规范

### 日期处理
```typescript
import dayjs from 'dayjs'
import moment from 'moment'

// 使用 dayjs 进行日期格式化
const formatDate = (date: string) => {
  return dayjs(date).format('YYYY-MM-DD')
}

// 日期范围处理
const dateRange = ref<[string, string]>([])
```

### Excel 导入导出
```typescript
import * as XLSX from 'xlsx'
import { downloadBlob } from '/@/cool/utils'

// Excel 导出
function handleExport() {
  const params = {
    url: '/export',
    method: 'GET',
    responseType: 'blob',
    params: searchParams,
  }
  
  service.moduleName.entityName
    .request(params)
    .then((res: any) => {
      if (downloadBlob(res)) {
        ElMessage.success('导出成功')
      }
    })
    .catch((err: any) => {
      ElMessage.error(err.message || '导出失败')
    })
}

// Excel 导入
function handleImport(file: File) {
  const reader = new FileReader()
  reader.onload = (e: ProgressEvent<FileReader>) => {
    const data = new Uint8Array(e.target?.result as ArrayBuffer)
    const workbook = XLSX.read(data, { type: 'array' })
    const worksheet = workbook.Sheets[workbook.SheetNames[0]]
    const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })
    
    // 处理导入数据
    processImportData(jsonData)
  }
  reader.readAsArrayBuffer(file)
}
```

## 权限控制规范

### 按钮权限
```vue
<template>
  <el-button 
    v-permission="service.moduleName.entityName.permission.create"
    type="primary"
    @click="handleCreate"
  >
    新增
  </el-button>
</template>
```

### 操作按钮配置
```typescript
const opButtons = ref({
  edit: {
    width: 80,
    permission: service.moduleName.entityName.permission.update,
    show: true,
  },
  delete: {
    width: 80,
    permission: service.moduleName.entityName.permission.delete,
    show: true,
  },
})
```

## 样式规范

### CSS 类命名
```scss
// 使用 BEM 命名规范
.component-name {
  &__element {
    // 元素样式
  }
  
  &--modifier {
    // 修饰符样式
  }
}

// 表单样式
.form-container {
  .el-form-item {
    margin-bottom: 20px;
  }
  
  .el-input-number {
    :deep(.el-input__wrapper) {
      padding-left: 11px;
      input {
        text-align: left;
      }
    }
  }
}
```

## 错误处理规范

### 统一错误处理
```typescript
// API 调用错误处理
async function fetchData() {
  try {
    const result = await service.moduleName.entityName.list()
    return result
  } catch (error: any) {
    console.error('获取数据失败:', error)
    ElMessage.error(error.message || '获取数据失败')
    throw error
  }
}

// 表单验证错误处理
function validateForm() {
  if (!formData.value.requiredField) {
    ElMessage.warning('请填写必填字段')
    return false
  }
  return true
}
```

## 性能优化规范

### 列表渲染优化
```vue
<template>
  <!-- 使用 key 优化列表渲染 -->
  <el-table :data="tableData" row-key="id">
    <el-table-column 
      v-for="column in columns" 
      :key="column.prop"
      :prop="column.prop"
      :label="column.label"
    />
  </el-table>
</template>
```

### 计算属性优化
```typescript
// 使用计算属性缓存复杂计算
const filteredData = computed(() => {
  return tableData.value.filter(item => 
    item.name.includes(searchKeyword.value)
  )
})
```