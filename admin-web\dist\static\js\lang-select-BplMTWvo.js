import{y as l,z as r,A as y,r as h}from"./index-BtOcqcNl.js";import{c as m,e as v,q as _,B as w,v as d,w as s,i as n,o as a,h as L,f as x,F as C,s as b,j as B,t as E,E as N}from"./.pnpm-hVqhwuVC.js";import{_ as V}from"./_plugin-vue_export-helper-DlAUqK2U.js";const q="/logo.png",z=m({name:"lang-select"}),A=m({...z,setup(F){const t=l,p=v(()=>l.find(e=>e.key.includes(r().locale.value))),{t:i}=r();function u(e){y(e).then(()=>{N.success(i("base.Language change success")),h.go(0)})}return(e,I)=>{const g=n("cl-svg"),f=n("el-dropdown-item"),k=n("el-dropdown");return d(t).length>1?(a(),_(k,{key:0,class:"lang-select",trigger:"click","hide-on-click":"",onCommand:u},{dropdown:s(()=>[(a(!0),x(C,null,b(d(t),o=>{var c;return a(),_(f,{key:o.key,disabled:((c=p.value)==null?void 0:c.key)===o.key,command:o.key},{default:s(()=>[B(E(o.name),1)]),_:2},1032,["disabled","command"])}),128))]),default:s(()=>[L(g,{name:"icon-lang-select",class:"lang-select__icon",size:"20"})]),_:1})):w("",!0)}}}),D=V(A,[["__scopeId","data-v-c00087b3"]]);export{D as L,q as _};
