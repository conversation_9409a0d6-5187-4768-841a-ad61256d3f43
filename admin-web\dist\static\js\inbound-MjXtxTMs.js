import{c as N,b as Y,q as d,w as a,h as o,i as s,f as A,s as J,F as X,y as f,t as V,v as m,G as x,B as D,H as Z,Y as v,j as k,o as p,T as E,E as r}from"./.pnpm-hVqhwuVC.js";import{g as ee,i as S,j as te,e as ne}from"./index-BtOcqcNl.js";import{a as oe}from"./index-D95m1iJL.js";/* empty css              */const ae={class:"table-summary"},se={class:"table-summary-container"},le=f("span",{class:"cl-table__expand-footer-title"},"数量总计：",-1),re=N({name:"pms-warehouse-inbound"}),me=N({...re,setup(ce){const{dict:$}=ee(),c=Y(0),{service:l}=oe(),R=$.get("color"),C=Y([{label:"待确认",value:0,count:0,type:"primary"},{label:"待入库",value:1,count:0,type:"danger"},{label:"入库中",value:2,count:0,type:"warning"},{label:"已完成",value:3,count:0,type:"success"}]),M=S.useTable({columns:[{label:"#",prop:"products",type:"expand"},{label:"关联订单",prop:"order.orderSn"},{label:"需要入库总数量",prop:"totalQuantity"},{label:"入库状态",prop:"status",dict:C},{label:"创建时间",prop:"createTime"},{label:"完成时间",prop:"completeTime",component:{name:"cl-date-text",props:{format:"YYYY-MM-DD HH:mm:ss"}}},{type:"op",width:250,buttons:["slot-btn-confirm","slot-btn-start","slot-btn-finish","slot-btn-export"]}]}),T=S.useCrud({service:l.pms.warehouse.destination.inbound,async onRefresh(t,{next:e,render:i}){const{count:h,list:B,pagination:b}=await e(t);C.value.forEach(w=>{w.count=h[w.value]||0}),i(B,b)}},t=>{t.refresh({status:c})});function L(t){var e;c.value=t,(e=T.value)==null||e.refresh()}function P(t,e){var i;(e==null?void 0:e.type)==="expand"||(e==null?void 0:e.type)==="op"||(i=M.value)==null||i.toggleRowExpansion(t)}function F(t){E.confirm("确认已经收到货了吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{l.pms.warehouse.destination.inbound.confirm({id:t.id}).then(e=>{r.success("确认收货成功"),c.value=(e==null?void 0:e.status)||0}).catch(e=>{r.error(e.message)})})}function H(t){E.confirm("确认开始入库吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{l.pms.warehouse.destination.inbound.start({id:t.id}).then(e=>{r.success("开始入库成功"),c.value=(e==null?void 0:e.status)||0}).catch(e=>{r.error(e.message)})})}function U(t){E.confirm("确认完成入库吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{l.pms.warehouse.destination.inbound.finish({id:t.id}).then(e=>{r.success("完成入库成功"),c.value=(e==null?void 0:e.status)||0}).catch(e=>{r.error(e.message)})})}function j(){return"primary-row"}const q=Y([]),K=S.useSearch({items:[{label:"下单时间",prop:"dateRange",props:{labelWidth:"100px"},component:{name:"el-date-picker",props:{type:"daterange","value-format":"YYYY-MM-DD",format:"YYYY-MM-DD",rangeSeparator:"至",startPlaceholder:"开始日期",endPlaceholder:"结束日期",clearable:!0,onChange(t){var e;(e=T.value)==null||e.refresh({dateRange:t})}}}}]});function Q(t){l.pms.warehouse.destination.inbound.request({url:"/export",data:{id:t==null?void 0:t.id},method:"POST",responseType:"blob"}).then(e=>{ne(e,"入库单.xlsx")&&r.success("导出成功")}).catch(e=>{r.error(e.message||"导出失败")})}return(t,e)=>{const i=s("cl-refresh-btn"),h=s("cl-flex1"),B=s("cl-search"),b=s("el-row"),w=s("el-tab-pane"),y=s("el-button"),u=s("el-table-column"),G=s("el-table"),I=s("cl-table"),O=s("cl-pagination"),W=s("el-tabs"),z=s("cl-crud"),g=Z("permission");return p(),d(z,{ref_key:"Crud",ref:T},{default:a(()=>[o(b,null,{default:a(()=>[o(i),o(h),o(B,{ref_key:"Search",ref:K},null,512)]),_:1}),o(W,{modelValue:c.value,"onUpdate:modelValue":e[0]||(e[0]=n=>c.value=n),type:"border-card",onTabChange:L},{default:a(()=>[(p(!0),A(X,null,J(C.value,n=>(p(),d(w,{key:n.value,label:`${n.label}(${n.count})`,name:n.value},null,8,["label","name"]))),128)),o(b,null,{default:a(()=>[o(I,{ref_key:"Table",ref:M,"row-key":"id","expand-row-keys":q.value,class:"table-row-pointer",onRowClick:P},{"slot-btn-export":a(({scope:n})=>[x((p(),d(y,{text:"",bg:"",type:"primary",onClick:v(_=>Q(n.row),["stop"])},{default:a(()=>[k(" 导出入库单 ")]),_:2},1032,["onClick"])),[[g,m(l).pms.warehouse.destination.inbound.permission.export]])]),"slot-btn-confirm":a(({scope:n})=>[n.row.status===0?x((p(),d(y,{key:0,text:"",bg:"",type:"success",onClick:v(_=>F(n.row),["stop"])},{default:a(()=>[k(" 确认收货 ")]),_:2},1032,["onClick"])),[[g,m(l).pms.warehouse.destination.inbound.permission.confirm]]):D("",!0)]),"slot-btn-start":a(({scope:n})=>[n.row.status===1?x((p(),d(y,{key:0,text:"",bg:"",type:"success",onClick:v(_=>H(n.row),["stop"])},{default:a(()=>[k(" 开始入库 ")]),_:2},1032,["onClick"])),[[g,m(l).pms.warehouse.destination.inbound.permission.start]]):D("",!0)]),"slot-btn-finish":a(({scope:n})=>[n.row.status===2?x((p(),d(y,{key:0,text:"",bg:"",type:"success",onClick:v(_=>U(n.row),["stop"])},{default:a(()=>[k(" 完成入库 ")]),_:2},1032,["onClick"])),[[g,m(l).pms.warehouse.destination.inbound.permission.finish]]):D("",!0)]),"column-products":a(({scope:n})=>[o(G,{data:n.row.products,style:{width:"100%"},border:"","row-class-name":j},{default:a(()=>[o(u,{label:"产品名称","min-width":"500",align:"center"},{default:a(()=>[o(u,{prop:"product.name",label:"中文名",align:"center"}),o(u,{prop:"product.nameEn",label:"英文名",align:"center"})]),_:1}),o(u,{prop:"product.sku",label:"SKU",width:"130",align:"center"}),o(u,{prop:"product.upc",label:"UPC",width:"130",align:"center"}),o(u,{prop:"product.color",label:"颜色",width:"120",align:"center"},{default:a(_=>[f("span",null,V(m(te)(m(R),parseInt(_.row.product.color))),1)]),_:2},1024),o(u,{prop:"piece",label:"订单数量",width:"100",align:"center"})]),_:2},1032,["data"]),f("div",ae,[f("div",se,[le,f("span",null,V(n.row.totalQuantity),1)])])]),_:1},8,["expand-row-keys"])]),_:1}),o(b,null,{default:a(()=>[o(h),o(O)]),_:1})]),_:1},8,["modelValue"])]),_:1},512)}}});export{me as default};
