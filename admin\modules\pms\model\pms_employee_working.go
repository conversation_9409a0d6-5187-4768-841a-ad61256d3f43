package model

import (
	"github.com/gogf/gf/v2/os/gtime"
)

const TableNamePmsEmployeeWorking = "pms_employee_working"

// PmsEmployeeWorking mapped from table <pms_employee_working>
type PmsEmployeeWorking struct {
	ID                 int64       `json:"id"       gorm:"column:id;type:bigint(20);not null;primary_key;auto_increment;comment:ID;"` // ID
	StaffName          string      `json:"staffName"     gorm:"column:staff_name;type:varchar(15);not null;default:'';comment:员工名称;"` // 员工名称
	GroupId            int         `json:"group_id" gorm:"column:group_id;type:bigint(20);not null;default:0;comment:数据组;"`           // 数据组
	TotalManHour       float32     `json:"total_man_hour" gorm:"column:total_man_hour;type:decimal(10,4);not null;default:0.0000;comment:合计工时;"`
	Beat               float32     `json:"beat" gorm:"column:beat;type:decimal(10,4);not null;default:0.0000;comment:节拍;"`
	ManHour            float32     `json:"man_hour" gorm:"column:man_hour;type:decimal(10,4);not null;default:0.0000;comment:工时;"`
	WorkshopId         int64       `json:"workshop_id" gorm:"column:workshop_id;type:int(10);not null;default:0;comment:生产车间;"`
	ProductId          int64       `json:"product_id" gorm:"column:product_id;type:bigint(20);not null;default:0;comment:ProductId;"` // ProductId
	WorkshopSection    int         `json:"workshop_section" gorm:"column:workshop_section;type:int(10);not null;default:0;comment:工序;"`
	Station            string      `json:"station" gorm:"column:station;type:varchar(20);not null;default:'';comment:工位;"`
	StandardCapacity   float32     `json:"standard_capacity" gorm:"column:standard_capacity;type:decimal(10,4);not null;default:0.0000;comment:标准人均产能;"`
	ProductionCapacity float32     `json:"production_capacity" gorm:"column:production_capacity;type:decimal(10,4);not null;default:0.0000;comment:实际产能;"`
	CapacityDiff       float32     `json:"capacity_diff" gorm:"column:capacity_diff;type:decimal(10,4);not null;default:0.0000;comment:产能差异;"`
	ManHourDiff        float32     `json:"man_hour_diff" gorm:"column:man_hour_diff;type:decimal(10,4);not null;default:0.0000;comment:工时差异;"`
	Remark             string      `json:"remark" gorm:"column:remark;type:varchar(100);not null;default:'';comment:备注;"`
	ProducedDate       *gtime.Time `json:"produced_date" gorm:"column:produced_date;type:date;comment:生产日期;"`                         // 生产日期
	CreateTime         *gtime.Time `json:"createTime" gorm:"column:createTime;not null;index,priority:1;autoCreateTime;comment:创建时间"` // 创建时间
	DeleteTime         *gtime.Time `json:"-" gorm:"column:deleteTime;index;comment:删除时间"`                                             // 删除时间
}

// GroupName 返回分组名
func (m *PmsEmployeeWorking) GroupName() string {
	return ""
}

// TableName PmsSupplier's table name
func (*PmsEmployeeWorking) TableName() string {
	return TableNamePmsEmployeeWorking
}

// NewPmsEmployeeWorking 创建实例
func NewPmsEmployeeWorking() *PmsEmployeeWorking {
	return &PmsEmployeeWorking{}
}

func init() {
	//_ = yc.CreateTable(NewPmsEmployeeWorking())
}
