import{g as re,j as se}from"./index-DkYL1aws.js";import{a as ce}from"./index-C6cm1h61.js";import{c as $,b as f,e as ie,z as E,A as T,q as w,i as c,w as o,h as n,y as m,B as pe,f as z,s as j,F as K,W as L,j as y,t as k,v as A,E as V,o as b,af as me,ag as fe}from"./.pnpm-hVqhwuVC.js";import{_ as _e}from"./_plugin-vue_export-helper-DlAUqK2U.js";const q=g=>(me("data-v-0900e1d6"),g=g(),fe(),g),ve={class:"schedule-base-form-row"},he=q(()=>m("span",{style:{color:"var(--el-color-danger)"}},"*",-1)),ye=q(()=>m("span",null,"计划总数量 ",-1)),be=q(()=>m("span",{style:{color:"var(--el-color-danger)"}},"*",-1)),ge=$({name:"undefined"}),Ie=$({...ge,props:{modelValue:{type:Boolean,default:!1},scheduleData:{type:Object,default:()=>null},scheduleDate:{type:String,default:""}},emits:["update:modelValue","close"],setup(g,{emit:W}){const _=g,Q=W,C=f(!1),{dict:G}=re(),{service:P}=ce(),H=G.get("color"),x=f(0),r=f({scheduleId:void 0,scheduleDate:"",products:[]}),S=f([]),U=f(),i=f([]),I=f(!1),v=f(),B=f([]),Y=ie(()=>{var t;return!!((t=_.scheduleData)!=null&&t.id)});E(()=>_.scheduleData,t=>{v.value=t}),T(()=>{var t;_.modelValue&&(r.value.scheduleDate=_.scheduleDate,r.value.scheduleId=(t=_.scheduleData)==null?void 0:t.id,Y.value||P.pms.production.schedule.list({status:1}).then(a=>{const d=a||[];B.value=d.map(e=>({id:e.id,name:e.sn}))}))});function J(t){const a=S.value.find(d=>d.productId===t.productId);a&&(t.quantity=a==null?void 0:a.quantity,t.planQuantity=null,t.disabled=!0)}function M(t){var d;if(!t)return"";const a=((d=v.value)==null?void 0:d.schedulePlan)||[];return R(t,a)}function R(t,a){return(a==null?void 0:a.filter(e=>e.products.some(p=>p.pieceProductId===t)).reduce((e,p)=>e+p.products.reduce((h,u)=>u.pieceProductId===t?h+(u==null?void 0:u.piece)||0:h,0),0))||0}function X(t){const a=t.index;i.value=i.value.filter(d=>d.index!==a)}function Z(){i.value.push({index:new Date().getTime()+Math.floor(Math.random()*1e3),productId:null,quantity:null,planQuantity:null})}async function ee(){r.value&&await U.value.validate(t=>{if(t){if(I.value=!0,i.value.length===0){V.error("请添加产品");return}if(i.value.find(e=>e.productId===null||e.productId===void 0)){V.error({message:"请选择正确的产品"});return}if(i.value.find(e=>e.planQuantity===null||e.planQuantity===void 0||e.planQuantity<=0)){V.error({message:"请填写正确的产品数量"});return}I.value=!I.value,r.value.products=i.value.map(e=>({productId:e.productId,quantity:e.planQuantity,workOrderNo:e.workOrderNo})),P.pms.production.schedule.plan.create(r.value).then(()=>{V.success("计划添加成功"),Q("update:modelValue",!1),Q("close")}).catch(e=>{V.error(e.message)})}})}function le(){const t=r.value.scheduleId;if(!t)return!1;P.pms.production.schedule.info({id:t}).then(a=>{v.value=a})}function F(){i.value=[],r.value={scheduleId:void 0,scheduleDate:_.scheduleDate,products:[]},v.value=null,Q("update:modelValue",!1)}return E(()=>_.modelValue,t=>{C.value=t},{immediate:!0}),T(()=>{var d;const t=i.value.map(e=>e.productId),a=((d=v.value)==null?void 0:d.products)||[];S.value=a.map(e=>(t.includes(e.productId)?e.disabled=!0:e.disabled=!1,{productId:e.productId,sku:e.sku,quantity:e.quantity,planQuantity:null,disabled:e.disabled})),a.forEach(e=>{e.scheduledQuantity=i.value.filter(p=>p.productId===e.productId).reduce((p,h)=>p+h.quantity,0)})}),(t,a)=>{const d=c("el-option"),e=c("el-select"),p=c("el-form-item"),h=c("el-date-picker"),u=c("el-table-column"),te=c("cl-svg"),ae=c("el-tooltip"),oe=c("el-input-number"),D=c("el-button"),N=c("el-table"),ne=c("el-form"),O=c("el-tab-pane"),de=c("el-tabs"),ue=c("cl-dialog");return b(),w(ue,{modelValue:C.value,"onUpdate:modelValue":a[3]||(a[3]=l=>C.value=l),width:"70%",controls:["close"],title:"填写排产计划信息",onClose:F},{footer:o(()=>[n(D,{type:"success",onClick:ee},{default:o(()=>[y(" 添加计划 ")]),_:1}),n(D,{onClick:F},{default:o(()=>[y(" 取 消 ")]),_:1})]),default:o(()=>[n(de,{modelValue:x.value,"onUpdate:modelValue":a[2]||(a[2]=l=>x.value=l),"tab-position":"right"},{default:o(()=>[n(O,{label:"排产计划",name:0},{default:o(()=>[n(ne,{ref_key:"productionPlanForm",ref:U,"label-width":"88px",model:r.value,size:"large"},{default:o(()=>[m("div",ve,[Y.value?pe("",!0):(b(),w(p,{key:0,label:"生产订单",prop:"scheduleId",rules:{required:!0,message:"请选择生产订单"}},{default:o(()=>[n(e,{modelValue:r.value.scheduleId,"onUpdate:modelValue":a[0]||(a[0]=l=>r.value.scheduleId=l),style:{"min-width":"250px"},placeholder:"请选择生产订单",onChange:le},{default:o(()=>[(b(!0),z(K,null,j(B.value,l=>(b(),w(d,{key:l.id,label:l.name,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})),n(p,{label:"排产日期",prop:"scheduleDate",rules:{required:!0,message:"请选择排产日期"}},{default:o(()=>[n(h,{modelValue:r.value.scheduleDate,"onUpdate:modelValue":a[1]||(a[1]=l=>r.value.scheduleDate=l),style:{"min-width":"250px"},disabled:_.scheduleDate!=="",type:"date",placeholder:"请选择排产日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD","disabled-date":l=>l.getTime()<Date.now()-864e5},null,8,["modelValue","disabled","disabled-date"])]),_:1})]),n(N,{data:i.value},{default:o(()=>[n(u,{prop:"sku",label:"*SKU"},{header:o(()=>[he,y(" SKU ")]),default:o(l=>[m("div",{style:{display:"flex","align-items":"center"},class:L(I.value&&!(l.row.productId>0)?"sku-select-error":"")},[n(e,{modelValue:l.row.productId,"onUpdate:modelValue":s=>l.row.productId=s,prop:"productId",filterable:"","reserve-keyword":"",placeholder:"请选择产品",onChange:s=>J(l.row)},{default:o(()=>[(b(!0),z(K,null,j(S.value,s=>(b(),w(d,{key:s.productId,label:s.sku,value:s.productId,disabled:s.disabled},null,8,["label","value","disabled"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])],2)]),_:1}),n(u,{prop:"orderQuantity",label:"排产数量"},{default:o(l=>[m("span",null,k(l.row.quantity),1)]),_:1}),n(u,{prop:"scheduledQuantity",label:"已计划数量"},{header:o(()=>[ye,n(ae,{effect:"dark",content:"计算此产品在该生产订单所有计划的累计数量(不包括正在输入的数量)",placement:"top"},{default:o(()=>[n(te,{name:"icon-question",class:"tool-tip-icon"})]),_:1})]),default:o(l=>[m("span",null,k(M(l.row.productId)),1)]),_:1}),n(u,{prop:"quantity",label:"*数量"},{header:o(()=>[be,y(" 数量 ")]),default:o(l=>[m("div",{style:{display:"flex","align-items":"center"},class:L(I.value&&!(l.row.quantity>0)?"quantity-input-error":"")},[n(oe,{modelValue:l.row.planQuantity,"onUpdate:modelValue":s=>l.row.planQuantity=s,prop:"quantity",min:1,placeholder:"数量"},null,8,["modelValue","onUpdate:modelValue"])],2)]),_:1}),n(u,{label:"操作",width:"100",align:"center"},{default:o(l=>[n(D,{type:"danger",size:"small",onClick:s=>X(l.row)},{default:o(()=>[y(" 删除 ")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"]),n(D,{style:{width:"100%"},class:"btn-data-add",onClick:Z},{default:o(()=>[y(" + 选择产品 ")]),_:1})]),_:1},8,["model"])]),_:1}),n(O,{label:"订单信息",name:1,disabled:!(r.value.scheduleId&&r.value.scheduleId>0)},{default:o(()=>{var l;return[n(N,{data:(l=v.value)==null?void 0:l.products,style:{width:"100%"},border:""},{default:o(()=>[n(u,{prop:"sku",label:"SKU",align:"center"}),n(u,{prop:"quantity",label:"排产数量",align:"center"}),n(u,{prop:"color",label:"颜色",align:"center"},{default:o(s=>[m("span",null,k(A(se)(A(H),parseInt(s.row.color))),1)]),_:1}),n(u,{label:"计划中数量",align:"center"},{default:o(s=>[m("span",null,k(M(s.row.productId)),1)]),_:1}),n(u,{label:"当前计划数量",prop:"scheduledQuantity",align:"center"})]),_:1},8,["data"])]}),_:1},8,["disabled"])]),_:1},8,["modelValue"])]),_:1},8,["modelValue"])}}}),Qe=_e(Ie,[["__scopeId","data-v-0900e1d6"]]);export{Qe as S};
