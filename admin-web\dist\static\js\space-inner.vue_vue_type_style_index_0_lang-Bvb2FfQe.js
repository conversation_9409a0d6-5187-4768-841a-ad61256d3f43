import{az as ce,c as D,b as q,e as $,z as ee,f as c,o as a,y as f,_ as re,i as b,B as F,t as C,F as j,q as V,w as _,v as n,V as ue,h as d,aV as pe,Y as M,aW as de,aL as fe,aM as ve,W as te,E as J,r as _e,G as X,j as Y,H as Z,s as me,be as ge,T as ye,p as he}from"./.pnpm-hVqhwuVC.js";import{o as ke,i as be}from"./index-DkYL1aws.js";import{a as ae}from"./index-C6cm1h61.js";import{u as xe}from"./hook-CIEgpwJn.js";import{c as we,f as Ce,_ as $e}from"./viewer.vue_vue_type_script_setup_true_name_item-viewer_lang-6n4_YlHr.js";import{_ as se}from"./_plugin-vue_export-helper-DlAUqK2U.js";function le(){return{space:ce("upload-space")}}const Ve={class:"item-video"},Ee=["src"],De=D({name:"item-video"}),Ie=D({...De,props:{data:Object,list:Array},setup(m,{expose:I}){const S=m,{space:h}=le(),g=q(),v=$(()=>S.data||{}),z=$(()=>v.value.progress===void 0||v.value.progress===100);function k(){var r;h.list.value.forEach(u=>{u.isPlay=v.value.id==u.id}),(r=g.value)==null||r.play()}function t(){var u;const r=h.list.value.find(s=>s.id==v.value.id);r&&(r.isPlay=!1),(u=g.value)==null||u.pause()}return ee(()=>v.value.isPlay,r=>{r?k():t()}),I({play:k,pause:t,loaded:z}),(r,u)=>(a(),c("div",Ve,[f("video",{ref_key:"Video",ref:g,src:v.value.url},null,8,Ee)]))}}),Se=se(Ie,[["__scopeId","data-v-356dc887"]]),ze={class:"item-file__wrap"},Be={key:0,class:"item-file__error"},Me={class:"image-error"},je={key:2,class:"item-file__name"},Ne={class:"item-file__actions"},Pe={class:"item-file__progress-bar"},Ge={class:"item-file__progress-value"},Oe={key:2,class:"item-file__index"},Re=D({name:"item-file"}),Te=D({...Re,props:{data:Object,list:Array},emits:["select","remove","confirm"],setup(m,{emit:I}){const S=m,h=I,{refs:g,setRefs:v}=ae(),{copy:z}=re(),{space:k}=le(),t=$(()=>S.data||{}),r=$(()=>k.selection.value.findIndex(y=>y.id===t.value.id)),u=$(()=>r.value>=0),s=$(()=>t.value.preload||t.value.url),p=$(()=>we(t.value.type||""));function x(){h("select",t.value)}function A(){}function N(){h("remove",t.value)}function P(){k.preview(t.value)}function E(y){be.ContextMenu.open(y,{hover:{target:"item-file__wrap"},list:[{label:"预览",callback(l){P(),l()}},{label:"复制链接",callback(l){t.value.url&&(z(t.value.url),J.success("复制成功")),l()}},{label:u.value?"取消选中":"选中",callback(l){x(),l()}},{label:"删除",callback(l){N(),l()}}]})}return(y,l)=>{var G,O,R;const L=b("el-image"),w=b("el-icon"),W=b("el-progress");return a(),c("div",ze,[f("div",{class:te(["item-file",[`is-${t.value.type}`]]),onClick:l[2]||(l[2]=e=>x()),onDblclick:l[3]||(l[3]=e=>void 0),onContextmenu:M(E,["stop","prevent"])},[t.value.error?(a(),c("div",Be," 上传失败："+C(t.value.error),1)):(a(),c(j,{key:1},[t.value.type==="image"?(a(),V(L,{key:0,fit:"contain",src:s.value,lazy:""},{error:_(()=>[f("div",Me,[f("span",null,C(s.value),1)])]),_:1},8,["src"])):t.value.type==="video"?(a(),V(Se,{key:1,ref:n(v)("video"),data:t.value,list:m.list},null,8,["data","list"])):(a(),c("span",je,C(n(Ce)(s.value))+"."+C(n(ke)(s.value)),1)),f("span",{class:"item-file__type",style:ue({backgroundColor:(G=p.value)==null?void 0:G.color})},C((O=p.value)==null?void 0:O.label),5),f("div",Ne,[t.value.type=="video"?(a(),c(j,{key:0},[(R=n(g).video)!=null&&R.loaded?(a(),c(j,{key:0},[t.value.isPlay?(a(),V(w,{key:0,onClick:l[0]||(l[0]=M(e=>n(g).video.pause(),["stop"]))},{default:_(()=>[d(n(pe))]),_:1})):(a(),V(w,{key:1,onClick:l[1]||(l[1]=M(e=>{var o;return(o=n(g).video)==null?void 0:o.play()},["stop"]))},{default:_(()=>[d(n(de))]),_:1}))],64)):F("",!0)],64)):(a(),V(w,{key:1,onClick:M(P,["stop"])},{default:_(()=>[d(n(fe))]),_:1})),d(w,{onClick:M(N,["stop"])},{default:_(()=>[d(n(ve))]),_:1})]),t.value.progress>0&&t.value.progress<100&&!t.value.url?(a(),c(j,{key:3},[f("div",Pe,[d(W,{percentage:t.value.progress,"show-text":!1},null,8,["percentage"])]),f("span",Ge,C(t.value.progress)+"%",1)],64)):F("",!0)],64)),u.value?(a(),c("div",Oe,[f("span",null,C(r.value+1),1)])):F("",!0)],34)])}}}),qe=se(Te,[["__scopeId","data-v-f17e19f9"]]),Fe={class:"cl-upload-space-inner__right"},Ae={class:"cl-upload-space-inner__header"},Le={style:{margin:"0px 10px"}},We={"infinite-scroll-immediate":!1},He={key:1,class:"empty"},Ue=f("p",null,"将文件拖到此处，或点击按钮上传",-1),Ye=D({name:"cl-upload-space-inner"}),tt=D({...Ye,props:{limit:{type:Number,default:99},accept:String,selectable:Boolean},emits:["selectionChange","confirm"],setup(m,{expose:I,emit:S}){const h=m,g=S,{service:v,browser:z,refs:k,setRefs:t}=ae(),{ViewGroup:r}=xe({label:"分类",title:"文件列表",service:v.space.type,onEdit(){return{width:"400px",props:{labelPosition:"top"},dialog:{controls:["close"]},items:[{label:"名称",prop:"name",value:"",required:!0,component:{name:"el-input",props:{maxlength:20,clearable:!0}}}]}},onSelect(e){y({classifyId:e.id,page:1})}}),u=q(!1),s=q([]),p=q([]),x=_e({page:1,size:50,total:0});function A(){s.value=[]}function N(e){}function P(e){p.value.unshift(e)}const E={page:1};async function y(e){var o;A(),Object.assign(E,{type:((o=h.accept)==null?void 0:o.split("/")[0].replace("*",""))||void 0,...x,...e}),E.page===1&&(u.value=!0),await v.space.info.page(E).then(i=>{Object.assign(x,i.pagination),E.page===1&&(p.value=[]),p.value.push(...i.list)}),u.value=!1}function l(e){const o=s.value.findIndex(i=>i.id===e.id);o>=0?s.value.splice(o,1):h.limit===1?s.value=[e]:s.value.length<h.limit&&s.value.push(e)}function L(e){g("confirm",[e])}function w(e){const o=e?[e.id]:s.value.map(i=>i.id);ye.confirm("此操作将删除文件, 是否继续?","提示",{type:"warning"}).then(()=>{J.success("删除成功"),o.forEach(i=>{[p.value,s.value].forEach(T=>{const H=T.findIndex(U=>U.id===i);T.splice(H,1)})}),v.space.info.delete({ids:o}).catch(i=>{J.error(i.message)})}).catch(()=>null)}function W(e){k.viewer.open(e,p.value)}ee(s,e=>{g("selectionChange",e)},{deep:!0});function G(){p.value.length&&p.value.length<x.total&&y({page:x.page+1})}function O(e){e.preventDefault()}function R(e){e.preventDefault(),e.dataTransfer.files.forEach((o,i)=>{setTimeout(()=>{k.upload.upload(o)},i*10)})}return he("upload-space",{selection:s,refresh:y,loading:u,list:p,preview:W}),I({selection:s,open,close,clear:A,refresh:y}),(e,o)=>{const i=b("el-button"),T=b("cl-upload"),H=b("el-icon"),U=b("el-scrollbar"),oe=b("cl-view-group"),ie=Z("infinite-scroll"),ne=Z("loading");return a(),c("div",{class:"cl-upload-space-inner",onDragover:O,onDrop:R},[d(oe,{ref_key:"ViewGroup",ref:r},{right:_(()=>{var K,Q;return[f("div",Fe,[f("div",Ae,[d(i,{onClick:o[0]||(o[0]=B=>y({page:1}))},{default:_(()=>[Y(" 刷新 ")]),_:1}),f("div",Le,[d(T,{ref:n(t)("upload"),menu:"space",type:"file","show-file-list":!1,limit:m.limit,"limit-upload":!1,accept:m.accept,multiple:"","classify-id":(Q=(K=n(r))==null?void 0:K.selected)==null?void 0:Q.id,onSuccess:N,onUpload:P},{default:_(()=>[d(i,{type:"primary"},{default:_(()=>[Y(" 点击上传 ")]),_:1})]),_:1},8,["limit","accept","classify-id"])]),m.selectable?F("",!0):(a(),V(i,{key:0,type:"danger",disabled:s.value.length===0,onClick:o[1]||(o[1]=B=>w())},{default:_(()=>[Y(" 删除选中文件 ")]),_:1},8,["disabled"]))]),X((a(),V(U,{class:"cl-upload-space-inner__file"},{default:_(()=>[X((a(),c("div",We,[p.value.length>0?(a(),c("div",{key:0,class:te(["list",{"is-mini":n(z).isMini}])},[(a(!0),c(j,null,me(p.value,B=>(a(),c("div",{key:B.preload||B.url,class:"item"},[d(qe,{data:B,list:p.value,onConfirm:L,onSelect:l,onRemove:w},null,8,["data","list"])]))),128))],2)):(a(),c("div",He,[d(H,{class:"el-icon--upload"},{default:_(()=>[d(n(ge))]),_:1}),Ue]))])),[[ie,G]])]),_:1})),[[ne,u.value]])])]}),_:1},512),d($e,{ref:n(t)("viewer")},null,512)],32)}}});export{tt as _};
