import{s as Q}from"./index-DkYL1aws.js";import{c as g,n as T,b as w,A as Y,f as v,h as o,y,w as r,q as i,B as n,v as t,j as p,t as c,i as d,K as C,F as I,s as L,o as a}from"./.pnpm-hVqhwuVC.js";import{g as M}from"./index-BFVs8cCE.js";import{OUTBOUND_TYPE as A}from"./constant-C2dsBPRR.js";import{_ as E}from"./_plugin-vue_export-helper-DlAUqK2U.js";const P={class:"mt2 b-1px b-gray-400 b-dashed"},j=g({name:"AuditView"}),z=g({...j,props:{modelValue:{},modelModifiers:{}},emits:["update:modelValue"],setup(k){const m=T(k,"modelValue"),x=w(A);function _(b){return x.value.find(h=>h.value===b)||{}}const f=w([]),e=w({});return Y(async()=>{m.value&&m.value>0&&(e.value=await Q.pms.material.inbound.detail({id:m.value}),e.value.voucher&&(f.value=e.value.voucher.split(",")))}),(b,h)=>{const s=d("el-descriptions-item"),N=d("el-tag"),D=d("el-image"),V=d("el-descriptions"),l=d("el-table-column"),B=d("el-table");return a(),v("div",null,[o(V,{size:"small",title:"",border:"",class:"purchase-order-info"},{default:r(()=>[t(e).orderNo?(a(),i(s,{key:0,label:"订单号",align:"center"},{default:r(()=>[p(c(t(e).orderNo),1)]),_:1})):n("",!0),o(s,{label:"创建时间",align:"center"},{default:r(()=>[p(c(t(e).createTime),1)]),_:1}),o(s,{label:"入库单号",align:"center"},{default:r(()=>[p(c(t(e).no),1)]),_:1}),o(s,{label:"入库类型",align:"center"},{default:r(()=>[o(N,{type:_(t(e).type).type},{default:r(()=>[p(c(_(t(e).type).label),1)]),_:1},8,["type"])]),_:1}),o(s,{label:"入库（总）数量",align:"center"},{default:r(()=>[p(c(t(e).totalQuantity),1)]),_:1}),o(s,{label:"入库日期",align:"center"},{default:r(()=>[p(c(t(C)(t(e).inboundTime).format("YYYY-MM-DD")),1)]),_:1}),o(s,{label:"入库凭证",align:"center","class-name":"voucher_image"},{default:r(()=>[(a(!0),v(I,null,L(t(f),(u,O)=>(a(),i(D,{key:O,style:{width:"60px",height:"60px"},src:u,"preview-src-list":[u],"zoom-rate":1.2,"max-scale":7,"min-scale":.2,"initial-index":4,fit:"cover"},null,8,["src","preview-src-list"]))),128))]),_:1})]),_:1}),y("div",P,[o(B,{data:t(e).products||[],stripe:"",border:"",class:"cl-table"},{default:r(()=>[t(e).type===1?(a(),i(l,{key:0,prop:"Po",label:"Po",align:"center","show-overflow-tooltip":""})):n("",!0),t(e).type===3?(a(),i(l,{key:1,prop:"workOrderDetail.workOrderNo",label:"工单号",align:"center",width:"200","show-overflow-tooltip":""})):n("",!0),o(l,{prop:"inboundQut",label:"入库数量",align:"center",width:"100","show-overflow-tooltip":""}),t(e).type===1?(a(),i(l,{key:2,prop:"orderQuantity",label:"采购数量",align:"center",width:"100","show-overflow-tooltip":""})):n("",!0),t(e).type===1?(a(),i(l,{key:3,prop:"receivedQuantity",label:"已收数量",align:"center",width:"100","show-overflow-tooltip":""})):n("",!0),t(e).type===1?(a(),i(l,{key:4,prop:"transfer",label:"转单数量",align:"center",width:"100","show-overflow-tooltip":""})):n("",!0),t(e).type===3?(a(),i(l,{key:5,prop:"workOrderDetail.calcBomQuantity",label:"Bom用量",align:"center",width:"120","show-overflow-tooltip":""})):n("",!0),t(e).type===3?(a(),i(l,{key:6,prop:"workOrderDetail.outboundQuantity",label:"已领数量",align:"center",width:"120","show-overflow-tooltip":""})):n("",!0),o(l,{prop:"code",label:"物料编号",width:"180",align:"center","show-overflow-tooltip":""}),o(l,{prop:"materialName",label:"物料名称",align:"center","show-overflow-tooltip":""}),o(l,{prop:"model",label:"型号",align:"center","show-overflow-tooltip":""}),o(l,{prop:"unit",label:"单位",align:"center",width:"70","show-overflow-tooltip":""}),t(e).type===1?(a(),i(l,{key:7,prop:"supplierName",label:"供应商",align:"center","show-overflow-tooltip":""})):n("",!0),o(l,{prop:"warehouseId",label:"仓位",align:"center",width:"120"},{default:r(({row:u})=>[y("span",null,c(t(M)("warehouse_name",u.warehouseId)),1)]),_:1})]),_:1},8,["data"])])])}}}),$=E(z,[["__scopeId","data-v-009b34ed"]]);export{$ as default};
