---
inclusion: always
---

# 技术栈规范

## 前端技术栈

### 核心框架
- **Vue 3.4+**: 使用 Composition API，禁用 Options API
- **TypeScript 5.5+**: 严格模式，完整类型定义
- **Vite 5.4+**: 构建工具，开发服务器
- **pnpm**: 包管理器，统一使用 pnpm

### UI 框架
- **Element Plus 2.8+**: 主要 UI 组件库
- **UnoCSS 0.58+**: 原子化 CSS 框架
- **@element-plus/icons-vue**: 图标库

### 状态管理
- **Pinia 2.2+**: 状态管理，替代 Vuex
- **@vueuse/core**: Vue 组合式工具库

### 路由和导航
- **Vue Router 4.4+**: 路由管理
- **nprogress**: 页面加载进度条

### 数据处理
- **Axios 1.7+**: HTTP 客户端
- **dayjs 1.11+**: 日期处理库（优先使用）
- **moment 2.30+**: 日期处理库（兼容性使用）
- **lodash-es 4.17+**: 工具函数库

### 表格和表单
- **@cool-vue-p/crud**: Cool-Admin 的 CRUD 组件
- **xlsx 0.18+**: Excel 文件处理
- **file-saver 2.0+**: 文件下载

### 图表和可视化
- **echarts 5.5+**: 图表库
- **vue-echarts 6.7+**: Vue 图表组件

### 编辑器
- **@wangeditor/editor 5.1+**: 富文本编辑器
- **monaco-editor 0.36**: 代码编辑器
- **quill 1.3+**: 轻量级富文本编辑器

### 开发工具
- **@antfu/eslint-config**: ESLint 配置
- **prettier**: 代码格式化
- **sass**: CSS 预处理器

## 后端技术栈

### 核心框架
- **Go 1.21+**: 编程语言
- **GoFrame v2.7+**: Web 框架
- **GORM 1.25+**: ORM 框架

### 数据库
- **MySQL 8.0+**: 主要数据库（生产环境）
- **SQL Server**: 企业级数据库支持
- **SQLite**: 开发和测试环境
- **Redis**: 缓存和会话存储

### 认证和安全
- **JWT (golang-jwt/jwt/v4)**: 身份认证
- **golang.org/x/crypto**: 密码加密
- **base64Captcha**: 验证码

### 文件存储
- **MinIO**: 对象存储
- **阿里云 OSS**: 云存储支持

### 工具库
- **excelize/v2**: Excel 文件处理
- **uuid**: UUID 生成
- **cron**: 定时任务
- **geoip2**: IP 地理位置

## 开发环境要求

### Node.js 环境
- **Node.js**: 18.0+ 或 20.0+
- **pnpm**: 8.0+
- **TypeScript**: 5.0+

### Go 环境
- **Go**: 1.21+
- **Go Modules**: 启用模块管理

### 数据库环境
- **MySQL**: 8.0+
- **Redis**: 6.0+

### 开发工具
- **VS Code**: 推荐编辑器
- **Git**: 版本控制
- **Postman**: API 测试

## 版本管理策略

### 依赖版本
- **主要依赖**: 锁定大版本，允许小版本更新
- **开发依赖**: 可以使用最新稳定版本
- **安全更新**: 及时更新有安全漏洞的依赖

### 版本升级原则
1. **向后兼容**: 优先选择向后兼容的版本
2. **稳定性**: 选择稳定版本，避免 beta 版本
3. **社区支持**: 选择社区活跃、文档完善的版本
4. **性能考虑**: 新版本有明显性能提升时考虑升级

## 浏览器兼容性

### 支持的浏览器
- **Chrome**: 90+
- **Firefox**: 88+
- **Safari**: 14+
- **Edge**: 90+

### 不支持的浏览器
- **IE**: 所有版本
- **Chrome**: < 90
- **移动端**: 基本支持，但不作为主要目标

## 性能要求

### 前端性能
- **首屏加载**: < 2秒
- **路由切换**: < 500ms
- **API 响应**: < 1秒
- **内存使用**: < 100MB

### 后端性能
- **API 响应**: < 200ms
- **数据库查询**: < 100ms
- **并发处理**: 500+ 用户
- **内存使用**: < 512MB

## 代码质量要求

### 前端代码质量
- **TypeScript 覆盖率**: 100%
- **ESLint 检查**: 0 错误
- **组件复用率**: > 80%
- **代码重复率**: < 5%

### 后端代码质量
- **Go fmt**: 100% 格式化
- **Go vet**: 0 警告
- **单元测试覆盖率**: > 70%
- **API 文档覆盖率**: 100%

## 安全要求

### 前端安全
- **XSS 防护**: 所有用户输入转义
- **CSRF 防护**: 使用 CSRF Token
- **敏感信息**: 不在前端存储敏感信息

### 后端安全
- **SQL 注入防护**: 使用参数化查询
- **权限验证**: 所有 API 进行权限检查
- **数据加密**: 敏感数据加密存储
- **日志记录**: 完整的操作日志