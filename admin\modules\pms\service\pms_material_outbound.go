package service

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"net/url"

	"github.com/gogf/gf/v2/container/gmap"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/text/gstr"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/imhuso/lookah-erp/admin/modules/pms/model"
	"github.com/imhuso/lookah-erp/admin/modules/pms/utils"
	"github.com/imhuso/lookah-erp/admin/yc"
	"github.com/xuri/excelize/v2"
)

type PmsMaterialOutboundService struct {
	*yc.Service
	*AuditService
}

// MaterialOutboundStatus 状态枚举
type MaterialOutboundStatus int

// 0:草稿 1:待处理 2:出库中 3:已完成 4:待审核
const (
	MaterialOutboundStatusDraft MaterialOutboundStatus = iota
	MaterialOutboundStatusPending
	MaterialOutboundStatusOutbound
	MaterialOutboundStatusCompleted
	MaterialOutboundStatusPendingReview
)

func (s *PmsMaterialOutboundService) ServiceUpdate(ctx context.Context, _ *yc.UpdateReq) (data interface{}, err error) {
	// 订单更新直接调用Add方法
	return s.ServiceAdd(ctx, nil)
}

func (s *PmsMaterialOutboundService) ServiceAdd(ctx context.Context, _ *yc.AddReq) (data interface{}, err error) {
	r := g.RequestFromCtx(ctx)
	rMap := r.GetMap()
	isUpdate := false

	outboundType := r.Get("type").Int()
	orderId := r.Get("orderId").Int64()
	workOrderId := gconv.Int64(rMap["workOrderId"])
	productId := gconv.Int64(rMap["productId"])
	// 获取出库单ID
	outboundId := gconv.Int64(rMap["id"])

	// 获取出库单信息
	var outbound *model.PmsMaterialOutbound
	if outboundId > 0 {
		err = yc.DBM(s.Model).Where("id", outboundId).WithAll().Scan(&outbound)
		if err != nil {
			return nil, err
		}

		if outbound == nil {
			return nil, gerror.New(yc.T(ctx, "outboundOrderDoesNotExist"))
		}

		if outbound.Status != int(MaterialOutboundStatusDraft) {
			return nil, gerror.New(yc.T(ctx, "onlyDraftOutboundOrderCanBeModified"))
		}

		orderId = outbound.OrderId
		outboundType = outbound.Type

		isUpdate = true
	} else {
		isUpdate = false
	}
	// 获取产品
	products := make([]*model.PmsMaterialOutboundProduct, 0)
	extras := make([]*model.PmsPurchaseOrderWithSummaryExtraOutput, 0)
	// 获取产品
	materialOutboundProductsInput := make([]*model.MaterialOutboundProductType, 0)
	err = gconv.Scan(rMap["materials"], &materialOutboundProductsInput)
	if err != nil {
		return
	}

	if len(materialOutboundProductsInput) == 0 {
		return nil, gerror.New("出库物料不能为空")
	}

	// 遍历产品
	for _, v := range materialOutboundProductsInput {
		// 退货记录增加备注
		// 退货记录增加物料总量 2025/07/03
		products = append(products, &model.PmsMaterialOutboundProduct{
			MaterialId:         v.MaterialId,
			Quantity:           v.Quantity,
			ContractId:         v.ContractId,
			Remark:             v.MateriaLdetail.Remark,
			ProductGroupId:     v.ProductGroupId,
			HandlingMethod:     v.HandlingMethod,
			Responsible:        v.Responsible,
			ScrapDate:          v.ScrapDate,
			Address:            v.Address,
			InboundOutboundKey: v.InboundOutboundKey,
			TotalQuantity:      v.TotalQuantity,
		})

		extras = append(extras, v.MateriaLdetail)
	}

	err = s.validateOutbound(ctx, orderId, outboundType, products)
	if err != nil {
		return nil, err
	}
	if (outboundType == int(model.MaterialOutboundTypePick) || outboundType == int(model.MaterialOutboundTypeSubcontract)) && workOrderId == 0 {
		return nil, gerror.New("工单号不能为空")
	}

	// 开启事务
	err = yc.DB(s.Model.GroupName()).Ctx(ctx).Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		outboundId, err = s.CreateOutbound(ctx, &model.PmsMaterialOutbound{
			ID:          outboundId,
			Status:      int(MaterialOutboundStatusDraft),
			Remark:      r.Get("remark").String(),
			Type:        outboundType,
			OrderId:     orderId,
			ProductId:   productId,
			WorkOrderId: workOrderId,
			Products:    products,
		}, isUpdate)
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return
	}
	return
}

// 验证出库单
func (s *PmsMaterialOutboundService) validateOutbound(ctx context.Context, orderId int64, outboundType int, products []*model.PmsMaterialOutboundProduct) error {
	// 验证报废总量
	if outboundType == int(model.MaterialOutboundTypeScrap) {
		for _, product := range products {
			if product.TotalQuantity < product.Quantity {
				return gerror.New("报废数量不能大于物料总量")
			}
		}
	}
	// 如果是自定义或报废或库存退货出库出库， 需要验证出库数量是否大于0并且不超过库存数量 MaterialOutboundTypeReturnInventory
	if outboundType == int(model.MaterialOutboundTypeScrap) || outboundType == int(model.MaterialOutboundTypeCustom) || outboundType == int(model.MaterialOutboundTypeReturnInventory) {
		// 验证出库产品数量是否大于0并且不超过库存数量
		for _, product := range products {
			if product.Quantity <= 0 {
				return gerror.New("出库数量必须大于0")
			}

			// 获取可出库库存
			material, _, err := NewPmsMaterialService().GetAvailableInventory(ctx, product.MaterialId)
			if err != nil {
				return err
			}
			//if availableInventory < product.Quantity {
			//	return gerror.New(fmt.Sprintf("出库数量错误，物料编码：%s，可出库数量：%f，出库数量：%f", material.Code, availableInventory, product.Quantity))
			//}
			if product.Quantity > material.Inventory {
				return gerror.New(fmt.Sprintf("出库数量错误，物料编码：%s，库存数量：%f，出库数量：%f", material.Code, material.Inventory, product.Quantity))
			}
		}
	} else if outboundType == int(model.MaterialOutboundTypeReturn) {
		purchaseOrderId := orderId
		if purchaseOrderId == 0 {
			return gerror.New("当前订单不是采购退货订单，无法退货")
		}

		// 获取采购订单信息
		purchaseOrder := &model.PmsPurchaseOrder{}
		err := yc.DBM(model.NewPmsPurchaseOrder()).Ctx(ctx).Where("id", purchaseOrderId).Scan(&purchaseOrder)
		if err != nil && !errors.Is(err, sql.ErrNoRows) {
			return err
		}

		if purchaseOrder == nil {
			return gerror.New("采购订单不存在")
		}

		// 判断采购单是否可以退货，如果超过3个月，不允许退货,2024年8月9日取消这个逻辑
		//if purchaseOrder.CreateTime.AddDate(0, 3, 0).Before(gtime.Now()) {
		//	return gerror.New("采购订单已超过3个月，无法退货")
		//}

		contractReceiveQuantityMap := make(map[int64]float64)
		contractIds := make([]int64, 0)
		for _, product := range products {
			contractIds = append(contractIds, product.ContractId)
		}
		// 获取采购订单合同
		contracts := make([]*model.PmsPurchaseContract, 0)
		err = yc.DBM(model.NewPmsPurchaseContract()).WhereIn("id", contractIds).Scan(&contracts)
		if err != nil && !errors.Is(err, sql.ErrNoRows) {
			return err
		}

		// 遍历合同
		for _, contract := range contracts {
			contractReceiveQuantityMap[contract.ID] = contract.ReceivedQuantity
		}

		// 遍历产品判断是否超过了相应的已收货数量
		for _, product := range products {
			if product.Quantity > contractReceiveQuantityMap[product.ContractId] {
				return gerror.New("出库数量不能大于已收货数量")
			}
		}

		err = s.validateReturnOutboundData(ctx, orderId, products)
		if err != nil {
			return err
		}
	} else if outboundType == int(model.MaterialOutboundTypePick) || outboundType == int(model.MaterialOutboundTypeSubcontract) {
		// 领料出库获取订单ID
		//ps := make([]*model.MaterialOutboundDataPick, 0)
		// 遍历产品
		//for _, product := range products {
		//	ps = append(ps, &model.MaterialOutboundDataPick{
		//		MaterialId: product.MaterialId,
		//		Quantity:   product.Quantity,
		//	})
		//}

		//err := s.validatePickOutboundData(ctx, orderId, ps)
		//if err != nil {
		//	return err
		//}

		if len(products) == 0 {
			return gerror.New("出库物料不能为空")
		}

		// 如果是领料出库，先要判断是否存在对应的生产订单和采购订单
		if orderId == 0 {
			return gerror.New("领料出库必须关联生产订单")
		}

		// 获取生产订单
		productionOrder := &model.PmsProductionSchedule{}
		err := yc.DBM(model.NewPmsProductionSchedule()).Where("id", orderId).Scan(&productionOrder)
		if err != nil && !errors.Is(err, sql.ErrNoRows) {
			return err
		}
		if productionOrder.Status != int(ProductionStatusProducing) {
			return gerror.New("生产订单状态不是生产中")
		}

		for _, product := range products {
			material := model.PmsMaterial{}
			err = yc.DBM(model.NewPmsMaterial()).Ctx(ctx).Where("id", product.MaterialId).Scan(&material)
			if err != nil {
				return err
			}
			if material.ID == 0 {
				return gerror.New("物料不存在")
			}
			if product.Quantity > material.Inventory {
				return gerror.New("物料库存不足")
			}
		}
	}

	if len(products) == 0 {
		return gerror.New("出库物料不能为空")
	}

	return nil
}

func (s *PmsMaterialOutboundService) CreateOutbound(ctx context.Context, outbound *model.PmsMaterialOutbound, isUpdate bool) (id int64, err error) {
	// 计算出库单总数量
	totalQuantity := 0.0000
	for _, product := range outbound.Products {
		totalQuantity += product.Quantity
	}

	outbound.TotalQuantity = totalQuantity
	outbound.CreateTime = gtime.Now()
	id = outbound.ID

	if isUpdate {
		// 更新出库单
		_, err = yc.DBM(s.Model).Ctx(ctx).Data(outbound).OmitEmptyData().Where("id", id).Update()
		if err != nil {
			return 0, err
		}
		// 删除产品
		_, err = yc.DBM(model.NewPmsMaterialOutboundProduct()).Ctx(ctx).Where("outbound_id", id).Delete()
		if err != nil {
			return 0, err
		}
	} else {
		todayOrderCount := 0
		todayStart := gtime.Now().StartOfDay()
		todayOrderCount, err = yc.DBM(s.Model).Ctx(ctx).WhereGT("createTime", todayStart).Unscoped().Count()
		if err != nil {
			return 0, err
		}
		todayOrderCountStr := fmt.Sprintf("%d", todayOrderCount+1)
		// 订单数量不足3位数，前面补0
		if len(todayOrderCountStr) < 3 {
			todayOrderCountStr = fmt.Sprintf("%03s", todayOrderCountStr)
		}
		// 出库单号规则：oSN+20230102131456+出库仓库ID+出库人ID+随机数
		outboundNo := fmt.Sprintf("OSN%s%s", gconv.String(gtime.Now().Format("Ymd")), todayOrderCountStr)

		outbound.No = outboundNo

		id, err = yc.DBM(s.Model).Ctx(ctx).Data(outbound).InsertAndGetId()
		if err != nil {
			return 0, err
		}
	}

	if id == 0 {
		return 0, gerror.New("创建出库单失败")
	}

	// 创建产品
	products := outbound.Products
	if len(products) > 0 {
		// 遍历添加id
		for _, product := range products {
			product.OutboundId = id
			if outbound.WorkOrderId > 0 {
				product.WorkOrderId = outbound.WorkOrderId
			}
		}

		// 添加产品
		_, err = yc.DBM(model.NewPmsMaterialOutboundProduct()).Ctx(ctx).Data(products).Insert()
		if err != nil {
			return 0, err
		}
	}

	return
}

func (s *PmsMaterialOutboundService) GetAuditData(ctx context.Context, auditId int64) (data interface{}, err error) {
	auditData, err := s.GetAuditDataByAuditId(ctx, auditId)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return nil, err
	}

	if auditData.DataId == 0 {
		return nil, gerror.New("审核数据不存在")
	}

	pmsMaterialOutbound := &model.PmsMaterialOutbound{}

	err = yc.DBM(s.Model).Ctx(ctx).Where("id", auditData.DataId).Scan(&pmsMaterialOutbound)

	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return nil, err
	}

	if pmsMaterialOutbound == nil {
		return nil, gerror.New("物料出库单不存在")
	}

	return pmsMaterialOutbound, nil
}

func (s *PmsMaterialOutboundService) Submit(ctx context.Context, outboundId int64) (data interface{}, err error) {
	if outboundId == 0 {
		return nil, gerror.New("id不能为空")
	}
	r := g.RequestFromCtx(ctx)
	materialOutbound := model.NewPmsMaterialOutbound()
	err = gconv.Scan(r.GetMap(), materialOutbound)
	if err != nil {
		return nil, err
	}
	if materialOutbound.OutboundTime == nil {
		return nil, gerror.New("出库时间不能为空")
	}
	if materialOutbound.Voucher == "" {
		return nil, gerror.New("出库凭证不能为空")
	}
	outbound := &model.PmsMaterialOutbound{}
	err = yc.DBM(s.Model).Ctx(ctx).Where("id", outboundId).With(&model.PmsMaterialOutboundProduct{}).Scan(&outbound)
	if err != nil {
		return
	}

	if outbound == nil {
		return nil, gerror.New("出库单不存在")
	}

	//委外出库和领料出库需要关联工单
	if (outbound.Type == int(model.MaterialOutboundTypePick) || outbound.Type == int(model.MaterialOutboundTypeSubcontract)) && outbound.WorkOrderId == 0 {
		return nil, gerror.New("工单号不能为空")
	}

	if outbound.Status != int(MaterialOutboundStatusDraft) {
		return nil, gerror.New("只有草稿状态的出库单才能提交审批")
	}
	targetStatus := 0
	// 开启事务
	err = yc.DB(s.Model.GroupName()).Ctx(ctx).Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 触发采购订单的审核流程
		isSkipAudit, err2 := s.AddAudit(ctx, outboundId)
		if err2 != nil {
			return err2
		}
		auditLog := &model.PmsPurchaseOrderAuditLog{
			PurchaseId:   outboundId,
			Source:       3,
			OperatorId:   yc.GetAdmin(ctx).UserId,
			OperatorName: yc.GetAdmin(ctx).UserName,
			Type:         0,
			OperatorNote: "提交物料出库单审批",
		}

		if isSkipAudit {
			result, err := s.AuditPass(ctx, outboundId)
			if err != nil {
				return err
			}
			// result 转map
			resultMap := gconv.Map(&result)
			targetStatus = gconv.Int(resultMap["status"])
			auditLog.OperatorNote = "提交出库单审核, 并自动跳过审核"
		} else {
			auditLog.OperatorNote = "提交出库单审核"
			targetStatus = int(MaterialOutboundStatusPendingReview)
			// 更新入库单状态
			updateData := g.Map{"status": targetStatus, "is_submit": 1}
			updateData["outbound_time"] = materialOutbound.OutboundTime
			updateData["voucher"] = materialOutbound.Voucher
			_, err = yc.DBM(s.Model).Ctx(ctx).Data(updateData).Where("id", outboundId).Update()
			if err != nil {
				return err
			}
		}
		// 增加审核记录
		NewPmsPurchaseOrderAuditLogService().AddLog(ctx, auditLog)
		return nil
	})
	if err != nil {
		return nil, err
	}
	return g.Map{"status": targetStatus}, nil
}

func (s *PmsMaterialOutboundService) AuditPass(ctx context.Context, outboundId int64) (data interface{}, err error) {
	result, err := s.OutboundConfirmV2(ctx, outboundId)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func (s *PmsMaterialOutboundService) ProcessAuditResult(ctx context.Context, isEnd bool, isPass bool, audit *model.PmsAudit, auditProcessorNode *model.PmsAuditProcessorNode) (err error) {
	if audit == nil {
		return gerror.New("审核记录不存在")
	}

	outbound := &model.PmsMaterialOutbound{}

	data, err := s.GetAuditData(ctx, audit.ID)
	if err != nil {
		return err
	}

	err = gconv.Scan(data, outbound)
	if err != nil {
		return err
	}
	// 判断数据是否存在
	if outbound.ID == 0 {
		return gerror.New("物料出库单不存在")
	}

	auditLog := &model.PmsPurchaseOrderAuditLog{
		PurchaseId:   outbound.ID,
		OperatorId:   auditProcessorNode.UserId,
		OperatorName: auditProcessorNode.UserName,
		CreateTime:   gtime.Now(),
		Source:       3,
	}

	// 同意
	if isPass {
		auditLog.Type = int(PurchaseOrderAuditTypePass)
		auditLog.OperatorNote = fmt.Sprintf("物料出库单【%s】审核通过，备注：%s", auditProcessorNode.NodeName, auditProcessorNode.Remark)
	} else { // 驳回
		auditLog.Type = int(PurchaseOrderAuditTypeReject)
		auditLog.OperatorNote = fmt.Sprintf("物料出库单【%s】审核不通过，原因：%s", auditProcessorNode.NodeName, auditProcessorNode.Remark)
		_, err = yc.DBM(s.Model).Ctx(ctx).Data(g.Map{"status": int(MaterialOutboundStatusDraft)}).Where("id", outbound.ID).Update()
		if err != nil {
			return err
		}
	}
	// 开启事务
	err = yc.DB(s.Model.GroupName()).Ctx(ctx).Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		if isPass && isEnd {
			_, err := s.AuditPass(ctx, outbound.ID)
			if err != nil {
				return err
			}
		}
		NewPmsPurchaseOrderAuditLogService().AddLog(ctx, auditLog)
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

func (s *PmsMaterialOutboundService) OutboundConfirmV2(ctx context.Context, outboundId int64) (data interface{}, err error) {
	// 获取出库单信息
	var outbound *model.PmsMaterialOutbound
	err = yc.DBM(s.Model).Ctx(ctx).Where("id", outboundId).With(&model.PmsMaterialOutboundProduct{}).Scan(&outbound)
	if outbound == nil {
		return nil, gerror.New("出库单不存在")
	}
	if err != nil {
		return
	}
	r := g.RequestFromCtx(ctx)
	materialOutbound := model.NewPmsMaterialOutbound()
	err = gconv.Scan(r.GetMap(), materialOutbound)
	if err != nil {
		return nil, err
	}

	if outbound.Status != int(MaterialOutboundStatusPendingReview) && outbound.Status != int(MaterialOutboundStatusDraft) {
		return nil, gerror.New("只有草稿或者待审批状态的出库单才能确认")
	}

	err = s.validateOutbound(ctx, outbound.OrderId, outbound.Type, outbound.Products)
	if err != nil {
		return nil, err
	}

	actionLogs := make([]*model.PmsActionLog, 0)
	updateData := g.Map{"status": MaterialOutboundStatusCompleted}
	if materialOutbound.OutboundTime != nil {
		updateData["outbound_time"] = materialOutbound.OutboundTime
	}
	if materialOutbound.Voucher != "" {
		updateData["voucher"] = materialOutbound.Voucher
	}
	// 更新出库单状态
	_, err = yc.DBM(s.Model).Ctx(ctx).Data(updateData).Where("id", outboundId).Update()

	if err != nil {
		return nil, err
	}

	// 获取产品
	var products []*model.PmsMaterialOutboundProduct
	err = yc.DBM(model.NewPmsMaterialOutboundProduct()).Ctx(ctx).Where("outbound_id", outboundId).Scan(&products)
	if err != nil {
		return nil, err
	}

	ms := make([]*model.MaterialStock, 0)
	extras := make([]*model.PmsPurchaseOrderWithSummaryExtraOutput, 0)
	// 如果是退货订单，还需要更新采购订单合同的退货的退货记录和已收货数量
	if outbound.Type == int(model.MaterialOutboundTypeReturn) {
		purchaseOrderId := outbound.OrderId
		if purchaseOrderId == 0 {
			return nil, gerror.New("当前订单不是采购退货订单，无法退货")
		}

		err = NewPmsPurchaseContractService().UpdateReturnOrRevertV2(ctx, outbound, products, false)
		if err != nil {
			return nil, err
		}
	} else if outbound.Type == int(model.MaterialOutboundTypePick) || outbound.Type == int(model.MaterialOutboundTypeSubcontract) { // 领料出库和委外出库
		productionOrderId := outbound.OrderId
		if productionOrderId == 0 {
			return nil, gerror.New("当前出库单没有关联生产订单，无法领料")
		}
		if outbound.WorkOrderId == 0 {
			return nil, gerror.New("工单号不能为空")
		}
		// 获取生产订单信息
		productionOrder := &model.PmsProductionSchedule{}
		err = yc.DBM(model.NewPmsProductionSchedule()).Ctx(ctx).Where("id", productionOrderId).Scan(&productionOrder)
		if err != nil && !errors.Is(err, sql.ErrNoRows) {
			return nil, err
		}

		//_, err = s.PickOutboundOrRevoke(ctx, productionOrderId, products, fmt.Sprintf("出库单号：%s 出库", outbound.No), false)
		//if err != nil {
		//	return nil, err
		//}
		for _, product := range products {
			material := model.NewPmsMaterial()
			err = yc.DBM(model.NewPmsMaterial()).Ctx(ctx).Data(g.Map{"status": 1}).Where("id", product.MaterialId).Scan(&material)
			if err != nil {
				return nil, err
			}
			if material.ID == 0 {
				return nil, gerror.New("物料不存在")
			}
			if product.Quantity > material.Inventory {
				return nil, gerror.New("物料库存不足")
			}

			msItem := &model.MaterialStock{}
			msItem.MaterialId = product.MaterialId
			msItem.UsedQuantity = -product.Quantity
			msItem.WorkOrderId = outbound.WorkOrderId
			msItem.WorkOrderNo = outbound.WorkOrderNo
			msItem.OutboundSn = outbound.No
			msItem.OutboundId = outbound.ID
			msItem.ProductionScheduleId = productionOrder.ID
			msItem.ProductionScheduleSn = productionOrder.Sn
			msItem.Remark = fmt.Sprintf("出库单号：%s 出库", outbound.No)
			msItem.OperationType = model.OperationTypeOutboundPick
			ms = append(ms, msItem)
			extra := &model.PmsPurchaseOrderWithSummaryExtraOutput{MaterialId: product.MaterialId}
			extra.OutQty = product.Quantity
			extras = append(extras, extra)
		}
		if err != nil {
			return nil, err
		}

	} else if outbound.Type == int(model.MaterialOutboundTypeScrap) || outbound.Type == int(model.MaterialOutboundTypeCustom) {
		// 遍历产品
		for _, product := range products {
			ms = append(ms, &model.MaterialStock{
				MaterialId:      product.MaterialId,
				UsedQuantity:    -product.Quantity,
				OutboundSn:      outbound.No,
				OutboundId:      outbound.ID,
				OperationType:   StatusConvert(outbound.Type),
				ExpectedInbound: 0,
				Remark:          fmt.Sprintf("出库单号：%s 出库", outbound.No),
			})
		}
	} else if outbound.Type == int(model.MaterialOutboundTypeReturnInventory) { // 库存退货
		outbound.Products = products
		_, err := NewPmsMaterialService().ReturnInventory(ctx, outbound, false)
		if err != nil {
			return nil, err
		}
	}

	_, err = yc.DBM(s.Model).Ctx(ctx).Data(g.MapStrAny{"complete_time": gtime.Now()}).Where("id", outboundId).Update()
	if err != nil {
		return nil, err
	}

	// 更新库存
	err = NewPmsMaterialService().UpdateMaterialStockBatchV2(ctx, ms)
	if err != nil {
		g.Log().Error(ctx, "更新库存失败", err)
		return nil, err
	}
	// 更新工单详情
	if outbound.WorkOrderId > 0 && (outbound.Type == int(model.MaterialOutboundTypePick) || outbound.Type == int(model.MaterialOutboundTypeSubcontract)) {
		pmsWorkOrderVo := &model.PmsWorkOrderVo{
			PmsWorkOrder: &model.PmsWorkOrder{ID: outbound.WorkOrderId},
			Extras:       extras,
		}
		_, err = NewPmsWorkOrderService().UpdateDetailBatch(ctx, pmsWorkOrderVo)
		if err != nil {
			g.Log().Error(ctx, "更新工单失败", err)
			return nil, err
		}
	}

	// 添加操作日志
	actionLogs = append(actionLogs, &model.PmsActionLog{
		UserId:   yc.GetAdmin(ctx).UserId,
		Type:     int(ActionLogTypeWarehouseSourceOutbound),
		ObjectId: outboundId,
		Content:  fmt.Sprintf("确认出库单，更新库存，出库单ID：%d", outboundId),
	})

	NewPmsActionLogService().CreateLog(ctx, actionLogs)

	return g.Map{"status": MaterialOutboundStatusCompleted}, nil

}

func (s *PmsMaterialOutboundService) OutboundConfirm(ctx context.Context, outboundId int64) (data interface{}, err error) {
	// 获取出库单信息
	var outbound *model.PmsMaterialOutbound
	err = yc.DBM(s.Model).Ctx(ctx).Where("id", outboundId).With(&model.PmsMaterialOutboundProduct{}).Scan(&outbound)
	if outbound == nil {
		return nil, gerror.New("出库单不存在")
	}
	if err != nil {
		return
	}
	r := g.RequestFromCtx(ctx)
	materialOutbound := model.NewPmsMaterialOutbound()
	err = gconv.Scan(r.GetMap(), materialOutbound)
	if err != nil {
		return nil, err
	}

	if outbound.Status != int(MaterialOutboundStatusPendingReview) && outbound.Status != int(MaterialOutboundStatusDraft) {
		return nil, gerror.New("只有草稿或者待审批状态的出库单才能确认")
	}

	err = s.validateOutbound(ctx, outbound.OrderId, outbound.Type, outbound.Products)
	if err != nil {
		return nil, err
	}

	actionLogs := make([]*model.PmsActionLog, 0)
	// 开启事务
	err = yc.DB(s.Model.GroupName()).Ctx(ctx).Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		updateData := g.Map{"status": MaterialOutboundStatusOutbound}
		if materialOutbound.OutboundTime != nil {
			updateData["outbound_time"] = materialOutbound.OutboundTime
		}
		if materialOutbound.Voucher != "" {
			updateData["voucher"] = materialOutbound.Voucher
		}
		// 更新出库单状态
		_, err = yc.DBM(s.Model).Ctx(ctx).Data(updateData).Where("id", outboundId).Update()

		if err != nil {
			return err
		}

		// 获取产品
		var products []*model.PmsMaterialOutboundProduct
		err = yc.DBM(model.NewPmsMaterialOutboundProduct()).Ctx(ctx).Where("outbound_id", outboundId).Scan(&products)
		if err != nil {
			return err
		}

		ms := make([]*model.MaterialStock, 0)
		// 如果是退货订单，还需要更新采购订单合同的退货的退货记录和已收货数量
		if outbound.Type == int(model.MaterialOutboundTypeReturn) {
			purchaseOrderId := outbound.OrderId
			if purchaseOrderId == 0 {
				return gerror.New("当前订单不是采购退货订单，无法退货")
			}

			err = NewPmsPurchaseContractService().UpdateReturnOrRevert(ctx, outbound, products, false)
			if err != nil {
				return err
			}
		} else if outbound.Type == int(model.MaterialOutboundTypePick) || outbound.Type == int(model.MaterialOutboundTypeSubcontract) {
			ps := make([]*model.MaterialOutboundDataPick, 0)
			// 遍历产品
			for _, product := range products {
				ps = append(ps, &model.MaterialOutboundDataPick{
					MaterialId: product.MaterialId,
					Quantity:   product.Quantity,
				})
			}

			err = s.validatePickOutboundData(ctx, outbound.OrderId, ps)
			if err != nil {
				return err
			}

			productionOrderId := outbound.OrderId
			if productionOrderId == 0 {
				return gerror.New("当前出库单没有关联生产订单，无法领料")
			}

			// 获取生产订单信息
			productionOrder := &model.PmsProductionSchedule{}
			err = yc.DBM(model.NewPmsProductionSchedule()).Ctx(ctx).Where("id", productionOrderId).Scan(&productionOrder)
			if err != nil && !errors.Is(err, sql.ErrNoRows) {
				return err
			}

			ms, err = s.PickOutboundOrRevoke(ctx, productionOrderId, products, fmt.Sprintf("出库单号：%s 出库", outbound.No), false)
			if err != nil {
				return err
			}

		} else if outbound.Type == int(model.MaterialOutboundTypeScrap) || outbound.Type == int(model.MaterialOutboundTypeCustom) {
			// 遍历产品
			for _, product := range products {
				ms = append(ms, &model.MaterialStock{
					MaterialId:        product.MaterialId,
					Stock:             -product.Quantity,
					LockedStock:       0,
					ExpectedInbound:   0,
					OccupiedInventory: 0,
					Remark:            fmt.Sprintf("出库单号：%s 出库", outbound.No),
				})
			}
		}

		// 更新库存
		err = NewPmsMaterialService().UpdateMaterialStocks(ctx, ms)
		if err != nil {
			g.Log().Error(ctx, "更新库存失败", err)
			return err
		}

		// 添加操作日志
		actionLogs = append(actionLogs, &model.PmsActionLog{
			UserId:   yc.GetAdmin(ctx).UserId,
			Type:     int(ActionLogTypeWarehouseSourceOutbound),
			ObjectId: outboundId,
			Content:  fmt.Sprintf("确认出库单，更新库存，出库单ID：%d", outboundId),
		})

		return nil
	})

	if err != nil {
		return
	}

	NewPmsActionLogService().CreateLog(ctx, actionLogs)

	return g.Map{"status": MaterialOutboundStatusPending}, nil

}

func (s *PmsMaterialOutboundService) OutboundStart(ctx context.Context, outboundId int64, outboundTime *gtime.Time, voucher string) (res interface{}, err error) {
	// 获取出库单信息
	var outbound *model.PmsMaterialOutbound
	err = yc.DBM(s.Model).Ctx(ctx).Where("id", outboundId).Scan(&outbound)
	if err != nil {
		return
	}

	if outbound == nil {
		return nil, gerror.New("出库单不存在")
	}

	if outbound.Status != int(MaterialOutboundStatusPending) {
		return nil, gerror.New("只有待处理状态的出库单才能开始")
	}

	// 开启事务
	err = yc.DB(s.Model.GroupName()).Ctx(ctx).Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 更新出库单状态
		_, err = yc.DBM(s.Model).Ctx(ctx).Data(g.Map{
			"status":        MaterialOutboundStatusOutbound,
			"outbound_time": outboundTime,
			"voucher":       voucher,
		}).Where("id", outboundId).Update()

		if err != nil {
			return err
		}

		// 获取产品 (这里看不到)
		var products []*model.PmsMaterialOutboundProduct
		err = yc.DBM(model.NewPmsMaterialOutboundProduct()).Ctx(ctx).Where("outbound_id", outboundId).Scan(&products)
		if err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return
	}

	return g.Map{"status": MaterialOutboundStatusOutbound}, nil
}

func (s *PmsMaterialOutboundService) OutboundComplete(ctx context.Context, outboundId int64) (res interface{}, err error) {
	// 获取出库单信息
	var outbound *model.PmsMaterialOutbound
	err = yc.DBM(s.Model).Ctx(ctx).Where("id", outboundId).Scan(&outbound)
	if err != nil {
		return
	}

	if outbound == nil {
		return nil, gerror.New("出库单不存在")
	}

	if outbound.Status != int(MaterialOutboundStatusOutbound) {
		return nil, gerror.New("只有出库中状态的出库单才能完成")
	}

	// 开启事务
	err = yc.DB(s.Model.GroupName()).Ctx(ctx).Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		dateMap := g.Map{
			"status":        MaterialOutboundStatusCompleted,
			"complete_time": gtime.Now(),
		}

		if outbound.OutboundTime.Timestamp() <= 0 {
			dateMap["outbound_time"] = gtime.Now()
		}

		// 更新出库单状态
		_, err = yc.DBM(s.Model).Ctx(ctx).Data(dateMap).Where("id", outboundId).Update()

		if err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return
	}

	return g.Map{"status": MaterialOutboundStatusCompleted}, nil
}

// Revoke 撤销出库单。
func (s *PmsMaterialOutboundService) Revoke(ctx context.Context, id int64) (res interface{}, err error) {
	// 获取出库单信息
	var outbound *model.PmsMaterialOutbound
	err = yc.DBM(s.Model).Ctx(ctx).Where("id", id).Scan(&outbound)
	if err != nil {
		return
	}

	if (outbound.Type == int(model.MaterialOutboundTypePick) || outbound.Type == int(model.MaterialOutboundTypeSubcontract)) && outbound.WorkOrderId == 0 {
		return nil, gerror.New("工单号不能为空")
	}

	if outbound == nil {
		return nil, gerror.New("出库单不存在")
	}

	if outbound.Status == int(MaterialOutboundStatusDraft) {
		return nil, gerror.New("草稿状态的出库单不能撤销")
	}

	if utils.V1_DISABLED(outbound.CreateTime) {
		return nil, gerror.New("V1版本之前的数据不能撤回")
	}

	// 开启事务
	err = yc.DB(s.Model.GroupName()).Ctx(ctx).Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 更新出库单状态
		_, err = yc.DBM(s.Model).Ctx(ctx).Data(g.Map{
			"status":        MaterialOutboundStatusDraft,
			"complete_time": nil,
			"outbound_time": nil,
			"voucher":       "",
		}).Where("id", id).Update()

		if err != nil {
			return err
		}

		actionLogs := make([]*model.PmsActionLog, 0)

		// 获取产品
		var products []*model.PmsMaterialOutboundProduct
		err = yc.DBM(model.NewPmsMaterialOutboundProduct()).Ctx(ctx).Where("outbound_id", id).Scan(&products)
		if err != nil {
			return err
		}
		// 如果出库单状态为已完成，则需要更新库存
		if outbound.Status == int(MaterialOutboundStatusCompleted) {
			materialStocks := make([]*model.MaterialStock, 0)
			// 如果是退货订单，还需要更新采购订单合同的退货的退货记录和已收货数量
			if outbound.Type == int(model.MaterialOutboundTypeReturn) {
				purchaseOrderId := outbound.OrderId
				if purchaseOrderId == 0 {
					return gerror.New("当前订单不是采购退货订单，无法撤销退货")
				}

				// 更新采购订单合同的退货的退货记录和已收货数量
				err = NewPmsPurchaseContractService().UpdateReturnOrRevertV2(ctx, outbound, products, true)
				if err != nil {
					return err
				}
			} else if outbound.Type == int(model.MaterialOutboundTypeReturnInventory) {
				outbound.Products = products
				_, err := NewPmsMaterialService().ReturnInventory(ctx, outbound, true)
				if err != nil {
					return err
				}
			} else {
				extras := make([]*model.PmsPurchaseOrderWithSummaryExtraOutput, 0)
				for _, product := range products {
					materialStocks = append(materialStocks, &model.MaterialStock{
						OutboundSn:      outbound.No,
						OutboundId:      outbound.ID,
						MaterialId:      product.MaterialId,
						Stock:           product.Quantity,
						OperationType:   -StatusConvert(outbound.Type),
						UsedQuantity:    0,
						ExpectedInbound: 0,
						Remark:          fmt.Sprintf("出库单号：%s 撤销", outbound.No),
					})
					if outbound.Type == int(model.MaterialOutboundTypePick) || outbound.Type == int(model.MaterialOutboundTypeSubcontract) {
						extra := &model.PmsPurchaseOrderWithSummaryExtraOutput{MaterialId: product.MaterialId}
						extra.OutQty = -product.Quantity
						extras = append(extras, extra)
					}
				}
				if len(extras) > 0 {
					pmsWorkOrderVo := &model.PmsWorkOrderVo{
						PmsWorkOrder: &model.PmsWorkOrder{ID: outbound.WorkOrderId},
						Extras:       extras,
						Revoke:       true,
					}
					_, err = NewPmsWorkOrderService().UpdateDetailBatch(ctx, pmsWorkOrderVo)
					if err != nil {
						return err
					}
				}
			}
			err = NewPmsMaterialService().UpdateMaterialStockBatchV2(ctx, materialStocks)
			if err != nil {
				g.Log().Error(ctx, "更新库存失败, err: %v", err)
				return err
			}

		}
		// 添加操作日志
		actionLogs = append(actionLogs, &model.PmsActionLog{
			UserId:   yc.GetAdmin(ctx).UserId,
			Type:     int(ActionLogTypeWarehouseSourceOutbound),
			ObjectId: id,
			Content:  fmt.Sprintf("撤销出库单，出库单ID：%d", id),
		})

		if err != nil {
			return err
		}

		NewPmsActionLogService().CreateLog(ctx, actionLogs)

		// 结束流程
		err2 := s.Cancel(ctx, id)
		if err2 != nil {
			return err2
		}
		return nil
	})

	if err != nil {
		return
	}

	return
}

// PickOutboundOrRevoke 撤销领料出库实际操作
func (s *PmsMaterialOutboundService) PickOutboundOrRevoke(ctx context.Context, productionPurchaseOrderId int64, products []*model.PmsMaterialOutboundProduct, remark string, isRevoke bool) (materialStocks []*model.MaterialStock, err error) {
	// 获取生产订单关联的所有采购订单
	purchaseIds, err := yc.DBM(model.NewPmsProductionPurchaseOrder()).Ctx(ctx).Fields("purchase_id").Where("order_id", productionPurchaseOrderId).Array()
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return nil, err
	}

	if len(purchaseIds) == 0 {
		return nil, gerror.New("没有找到关联的采购订单")
	}

	sign := 1.00
	if isRevoke {
		sign = -1.00
	}

	for _, product := range products {
		// 如果是领料出库，还需要更新采购扩展信息中的已出库数量
		orderExtras := make([]*model.PmsPurchaseOrderDetailExtra, 0)
		err = yc.DBM(model.NewPmsPurchaseOrderDetailExtra()).Ctx(ctx).WhereIn("purchase_id", purchaseIds).Where("material_id", product.MaterialId).Scan(&orderExtras)
		if err != nil && !errors.Is(err, sql.ErrNoRows) {
			return nil, err
		}

		if len(orderExtras) == 0 {
			return nil, gerror.New("没有找到采购订单扩展信息")
		}
		// 记录每个Extra最大已出库数量，如果当前Extra不够，将剩余的数量添加到下一个Extra
		type extraOutbound struct {
			Extra    *model.PmsPurchaseOrderDetailExtra
			Quantity float64
		}

		extraOutboundMap := make(map[int64]*extraOutbound)
		remainQuantity := product.Quantity

		// 填充实际出库数量
		for _, extra := range orderExtras {
			if remainQuantity <= 0 {
				break
			}

			maxOutboundQuantity := extra.PurchaseTotal - extra.OutboundQuantity
			if isRevoke {
				// 计算当前Extra最大可出库数量
				maxOutboundQuantity = extra.OutboundQuantity
			}

			if maxOutboundQuantity <= 0 {
				continue
			}

			// 如果剩余数量小于当前Extra最大可出库数量，直接出库
			if remainQuantity <= maxOutboundQuantity {
				extraOutboundMap[extra.MaterialId] = &extraOutbound{
					Extra:    extra,
					Quantity: remainQuantity,
				}
				remainQuantity = 0
				break
			}

			// 剩余数量大于当前Extra最大可出库数量，出库数量为当前Extra最大可出库数量
			extraOutboundMap[extra.MaterialId] = &extraOutbound{
				Extra:    extra,
				Quantity: maxOutboundQuantity,
			}

			remainQuantity -= maxOutboundQuantity
		}

		// 如果还有剩余数量，说明超出了可出库数量
		if remainQuantity > 0.00000001 {
			g.Log().Debugf(ctx, "物料ID为%d的物料超出了可出库数量，超出数量为%f", product.MaterialId, remainQuantity)
			errString := "出库数量不能大于可出库数量"
			if isRevoke {
				errString = "可出库数量不足以撤销出库"
			}
			return nil, gerror.New(errString)
		}

		// 过滤掉没有出库数量的Extra
		for mid, eo := range extraOutboundMap {
			if eo.Quantity <= 0 {
				delete(extraOutboundMap, mid)
			}
		}

		// 如果没有可出库数量，说明出库数量为0
		if len(extraOutboundMap) == 0 {
			return nil, gerror.New("出库数量有误")
		}

		// 更新采购订单扩展信息
		for mid, eo := range extraOutboundMap {
			newOutboundQuantity := eo.Extra.OutboundQuantity + eo.Quantity*sign
			if newOutboundQuantity < 0 || newOutboundQuantity > eo.Extra.PurchaseTotal {
				return nil, gerror.New("更新订单数据失败")
			}

			affected, err := yc.DBM(model.NewPmsPurchaseOrderDetailExtra()).Ctx(ctx).Data(g.Map{
				"outbound_quantity": newOutboundQuantity,
			}).
				Where("purchase_id", eo.Extra.PurchaseId).
				Where("material_id", mid).
				Where("outbound_quantity", eo.Extra.OutboundQuantity).
				UpdateAndGetAffected()

			if err != nil {
				return nil, err
			}

			if affected == 0 {
				return nil, gerror.New("更新采购订单信息失败")
			}
		}

		materialStocks = append(materialStocks, &model.MaterialStock{
			MaterialId:        product.MaterialId,
			UsedQuantity:      -product.Quantity * sign,
			Stock:             0,
			LockedStock:       0,
			OccupiedInventory: -product.Quantity * sign,
			ExpectedInbound:   0,
			Remark:            remark,
		})
	}

	return
}

// 验证领料出库数据
func (s *PmsMaterialOutboundService) validatePickOutboundData(ctx context.Context, productionOrderId int64, products []*model.MaterialOutboundDataPick) (err error) {
	if len(products) == 0 {
		return gerror.New("出库物料不能为空")
	}

	// 如果是领料出库，先要判断是否存在对应的生产订单和采购订单
	if productionOrderId == 0 {
		return gerror.New("领料出库必须关联生产订单")
	}

	// 获取生产订单
	productionOrder := &model.PmsProductionSchedule{}
	err = yc.DBM(model.NewPmsProductionSchedule()).Where("id", productionOrderId).Scan(&productionOrder)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return err
	}

	if productionOrder.Status != int(ProductionStatusProducing) {
		return gerror.New("生产订单状态不是生产中")
	}

	// 获取关联生产采购订单
	purchaseOrderIds := make([]int64, 0)
	productionPurchaseOrder := make([]*model.PmsProductionPurchaseOrder, 0)
	err = yc.DBM(model.NewPmsProductionPurchaseOrder()).WhereIn("order_id", productionOrderId).Scan(&productionPurchaseOrder)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return err
	}

	// 遍历关联生产采购订单
	for _, order := range productionPurchaseOrder {
		purchaseOrderIds = append(purchaseOrderIds, order.PurchaseId)
	}

	if len(purchaseOrderIds) == 0 {
		return gerror.New("没有找到关联的采购订单")
	}

	// 获取订单的扩展信息
	purchaseOrderDetailExtra := make([]*model.PmsPurchaseOrderDetailExtra, 0)
	purchaseOrderDetailRemainQuantityMap := make(map[int64]float64)
	err = yc.DBM(model.NewPmsPurchaseOrderDetailExtra()).WhereIn("purchase_id", purchaseOrderIds).Scan(&purchaseOrderDetailExtra)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return err
	}

	if len(purchaseOrderDetailExtra) == 0 {
		return gerror.New("采购订单数据异常")
	}

	// 遍历扩展信息，生成Map,由于一个采购订单中可能有多个相同的物料，所以需要将相同的物料的数量相加
	extraMap := make(map[int64]*model.PmsPurchaseOrderDetailExtra)
	for _, extra := range purchaseOrderDetailExtra {
		if _, exist := extraMap[extra.MaterialId]; exist {
			extraMap[extra.MaterialId].AvailableQuantity += extra.AvailableQuantity
			extraMap[extra.MaterialId].OutboundQuantity += extra.OutboundQuantity
		} else {
			extraMap[extra.MaterialId] = extra
		}
	}
	// 遍历Map,生成可出库数量Map
	for _, extra := range extraMap {
		purchaseOrderDetailRemainQuantityMap[extra.MaterialId] = extra.AvailableQuantity - extra.OutboundQuantity
	}

	// 遍历产品，判断是否在扩展信息中，并且数量正常
	for _, product := range products {
		if product.Quantity <= 0 {
			return gerror.New("出库数量必须大于0")
		}

		// 从purchaseOrderDetailRemainQuantityMap中获取可出库数量，如果不存在则报错
		remainQuantity, exist := purchaseOrderDetailRemainQuantityMap[product.MaterialId]
		if !exist {
			return gerror.New("出库物料不在采购订单中")
		}

		if remainQuantity <= 0 || (product.Quantity-remainQuantity > 0.000001) {
			g.Log().Debugf(ctx, "物料ID：%d, 可出库数量：%f, 出库数量：%f", product.MaterialId, remainQuantity, product.Quantity)
			return gerror.New("出库数量不能大于可出库数量")
		}
	}

	return nil
}

// 验证退货出库数据
func (s *PmsMaterialOutboundService) validateReturnOutboundData(ctx context.Context, orderId int64, products []*model.PmsMaterialOutboundProduct) (err error) {
	if len(products) == 0 {
		return gerror.New("出库物料不能为空")
	}

	// 如果是退货出库，先要判断是否存在对应的采购订单
	if orderId == 0 {
		return gerror.New("退货出库必须关联采购订单")
	}

	// 获取采购订单
	purchaseOrder := &model.PmsPurchaseOrder{}
	err = yc.DBM(model.NewPmsPurchaseOrder()).Ctx(ctx).Where("id", orderId).Scan(&purchaseOrder)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return err
	}

	if purchaseOrder.ID == 0 {
		return gerror.New("采购订单不存在")
	}

	return nil
}

func (s *PmsMaterialOutboundService) Detail(ctx context.Context, id int64) (result *model.PmsMaterialOutbound, err error) {
	if id == 0 {
		return nil, gerror.New("id不能为空")
	}

	err = yc.DBM(s.Model).Ctx(ctx).Where("id", id).Scan(&result)
	if err != nil {
		return nil, err
	}
	if result.ID == 0 {
		return nil, gerror.New("物料出库单不存在")
	}
	productList := make([]*model.MaterialOutboundProductOutput, 0)
	fields := `
		outbound.id,
		outbound.outbound_id,
		outbound.contract_id,
		outbound.material_id,
		outbound.outbound_quantity,
		outbound.quantity,
		material.*
	`
	err = yc.DBM(model.NewPmsMaterialOutboundProduct()).Ctx(ctx).As("outbound").LeftJoin("pms_material AS material", "outbound.material_id = material.id ").
		Where("outbound.outbound_id", result.ID).Fields(fields).Scan(&productList)
	if err != nil {
		return nil, err
	}
	result.ProductsOutput = productList
	return result, nil
}

func (s *PmsMaterialOutboundService) QueryPage(ctx context.Context, reqVo *model.QueryVo) (result model.PageResult[*model.MaterialInboundVo], err error) {
	result = model.NewPageResult[*model.MaterialInboundVo](reqVo)
	reqVo.KeyWord = gstr.Trim(reqVo.KeyWord)

	result.Pagination.Page = reqVo.Page
	result.Pagination.Size = reqVo.Size
	// 注意这里的《右连接》 pms_material_outbound_product
	queryWrap := `
		WITH query_data AS 
		(
			SELECT main.*,t1.deleteTime FROM pms_material_outbound_product AS main LEFT JOIN pms_material_outbound AS t1 ON main.outbound_id = t1.id WHERE t1.deleteTime IS NULL
		)
		SELECT #{Fields} FROM
			pms_material_outbound AS main
			RIGHT JOIN query_data AS t1 ON ( main.id = t1.outbound_id )
			LEFT JOIN pms_material AS t2 ON ( t2.id = t1.material_id )
			LEFT JOIN pms_purchase_contract AS t3 ON ( t3.id = t1.contract_id )
		    LEFT JOIN pms_work_order_detail AS t_wo ON ( t_wo.work_order_id = main.work_order_id AND t_wo.material_id = t1.material_id )  
			LEFT JOIN pms_product AS product ON t_wo.product_id = product.id 
			LEFT JOIN 
			(
				SELECT pro1.id, pro1.sn 
				FROM pms_production_schedule AS pro1
				LEFT JOIN pms_production_schedule_product AS pro2 ON pro1.id = pro2.id
				WHERE pro1.deleteTime IS NULL 
			) AS tmp ON ( tmp.id = main.order_id AND main.type = 0)             
		WHERE main.deleteTime IS NULL 
	`
	if reqVo.UseStatus {
		queryWrap += fmt.Sprintf(" AND main.status = %d", reqVo.Status)
	}
	if reqVo.UseType {
		queryWrap += fmt.Sprintf(" AND main.type = %d", reqVo.Type)
	}
	if reqVo.KeyWord != "" {
		queryWrap += fmt.Sprintf(" AND (main.no LIKE '%%%s%%' OR t2.code LIKE '%%%s%%' OR t2.name LIKE '%%%s%%' OR tmp.sn LIKE '%%%s%%' OR product.name LIKE '%%%s%%')",
			reqVo.KeyWord, reqVo.KeyWord, reqVo.KeyWord, reqVo.KeyWord, reqVo.KeyWord)
	}
	if reqVo.Date != "" {
		split := gstr.Split(reqVo.Date, ",")
		queryWrap += fmt.Sprintf(` AND DATE_FORMAT(main.createTime,'%%Y-%%m-%%d') BETWEEN '%s' AND '%s'`, split[0], split[1])
	}
	// 查询总数 fmt.Sprintf 会格式化  LIKE '%1%'
	countQuery := gstr.Replace(queryWrap, "#{Fields}", "COUNT(1)")
	count, err := g.DB().Query(ctx, countQuery)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return result, err
	}
	result.Pagination.Total = gconv.Int64(count[0]["COUNT(1)"])

	// 查询列表
	reqVo.CalcOffset()
	queryWrap += fmt.Sprintf(" ORDER BY t1.outbound_id DESC LIMIT %d,%d", reqVo.Page, reqVo.Size)
	Fields := fmt.Sprintf(`main.*,t1.quantity AS quantity, t1.material_id,%s,t_wo.work_order_no,t_wo.calc_bom_quantity,t_wo.product_id,product.name AS ProductName,product.sku,tmp.sn,%s`, utils.MATERIAL_FIEIDS, utils.CONTRACT_FIEIDS)
	queryWrap = gstr.Replace(queryWrap, "#{Fields}", Fields)
	queryResult, err := g.DB().Query(ctx, queryWrap)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return result, err
	}
	err = queryResult.Structs(&result.List)
	if err != nil {
		return result, err
	}
	if len(result.List) > 0 {
		MaterialOutboundStatusMap := g.MapIntStr{int(MaterialOutboundStatusDraft): "草稿", int(MaterialOutboundStatusPending): "待处理", int(MaterialOutboundStatusOutbound): "出库中", int(MaterialOutboundStatusCompleted): "已完成", int(MaterialOutboundStatusPendingReview): "待审批"}
		MaterialOutboundTypeMap := g.MapIntStr{
			int(model.MaterialOutboundTypePick):            "领料出库",
			int(model.MaterialOutboundTypeReturn):          "退货出库",
			int(model.MaterialOutboundTypeReturnInventory): "库存退货出库",
			int(model.MaterialOutboundTypeCustom):          "自定义出库",
			int(model.MaterialOutboundTypeScrap):           "报废",
			int(model.MaterialOutboundTypeSubcontract):     "委外出库",
		}
		for i, item := range result.List {
			if result.MapData[item.MaterialId] == nil {
				result.MapData[item.MaterialId] = make([]*model.MaterialInboundVo, 0)
			}
			result.MapData[item.MaterialId] = append(result.MapData[item.MaterialId], item)
			result.List[i].StatusLabel = MaterialOutboundStatusMap[item.Status]
			result.List[i].TypeLabel = MaterialOutboundTypeMap[item.Type]
		}
	}
	return result, nil
}

func (s *PmsMaterialOutboundService) ExportExcel(ctx context.Context, reqVo *model.QueryVo) error {
	reqVo.Page = 1
	reqVo.Size = utils.MAX_PAGE_SIZE
	result, err := s.QueryPage(ctx, reqVo)
	if err != nil {
		return err
	}
	if result.List == nil || len(result.List) == 0 {
		return errors.New("没有数据")
	}
	sheetName := "物料出库"
	file := excelize.NewFile()
	_ = file.SetSheetName("Sheet1", sheetName)
	_, _ = file.NewSheet(sheetName)
	columnHead := []string{"出库单号", "生产单号", "Po", "产品名称", "物料编码", "物料名称", "规格/型号", "单位", "出库数量", "出库类型", "状态", "创建时间", "备注"}
	err = utils.CreateExcelHead(file, sheetName, columnHead)
	if err != nil {
		return err
	}
	row := 2
	for _, item := range result.List {
		_ = file.SetCellValue(sheetName, fmt.Sprintf("A%d", row), item.No)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("B%d", row), item.SN)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("C%d", row), item.Po)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("D%d", row), item.ProductName)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("E%d", row), item.Code)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("F%d", row), item.MaterialName)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("G%d", row), item.Model)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("H%d", row), item.Unit)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("I%d", row), item.Quantity)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("J%d", row), item.TypeLabel)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("K%d", row), item.StatusLabel)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("L%d", row), item.CreateTime)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("M%d", row), item.Remark)
		row++
	}
	sheetName = "物料汇总(不包含生产退料)"
	file.NewSheet(sheetName)
	columnHead = []string{"物料编码", "物料名称", "规格/型号", "单位", "数量"}
	err = utils.CreateExcelHead(file, sheetName, columnHead)
	if err != nil {
		return err
	}
	row = 2
	for _, v := range result.MapData {
		tmp := &model.MaterialInboundVo{}
		qty := 0.0
		if len(v) > 0 {
			tmp = v[0]
		}
		for _, item := range v {
			qty += item.Quantity
		}
		_ = file.SetCellValue(sheetName, fmt.Sprintf("A%d", row), tmp.Code)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("B%d", row), tmp.MaterialName)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("C%d", row), tmp.Model)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("D%d", row), tmp.Unit)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("E%d", row), qty)
		row++
	}
	// 写出到文件
	fileName := fmt.Sprintf("物料出库-%s.xlsx", gtime.Now().Format("YmdHis"))
	fileName = url.QueryEscape(fileName)

	// 返回文件流
	buffer, err := file.WriteToBuffer()
	if err != nil {
		return err
	}

	utils.SetResponseToStream(ctx, fileName, buffer.Bytes())
	return nil
}

func getMaterialOutboundCount(ctx context.Context, m *gdb.Model, whereMap *gmap.StrAnyMap) (res interface{}, err error) {
	request := g.RequestFromCtx(ctx)
	key := request.Get("inbound_outbound_key").Int()
	data := gmap.NewIntIntMap()
	// 遍历whereMap,删除status条件
	for key := range whereMap.Map() {
		// 如果key等于status或者是带表前缀的status
		if key == "status" || gstr.HasSuffix(key, ".status") {
			whereMap.Remove(key)
		}
	}
	// 获取每种状态的订单数量
	// 读取 MaterialOutboundStatus 所有枚举值
	statusList := []MaterialOutboundStatus{
		MaterialOutboundStatusDraft,
		MaterialOutboundStatusPending,
		MaterialOutboundStatusOutbound,
		MaterialOutboundStatusCompleted,
		MaterialOutboundStatusPendingReview,
	}

	// 获取订单数量
	countModel := m.Clone().Ctx(ctx).Where(whereMap.Map())
	for _, status := range statusList {
		if key > 0 {
			// 判断whereBuilder是否存在status条件
			count, err := countModel.Clone().Where("status", status).
				LeftJoin("pms_material_outbound_product", "pms_material_outbound_product.outbound_id = pms_material_outbound.id").
				Where("pms_material_outbound_product.inbound_outbound_key", key).Count()
			if err != nil {
				return nil, err
			}
			// 向map中添加数据
			data.Set(int(status), count)
		} else {
			// 判断whereBuilder是否存在status条件
			count, err := countModel.Clone().Where("status", status).Count()
			if err != nil {
				return nil, err
			}
			// 向map中添加数据
			data.Set(int(status), count)
		}
	}

	return data, nil
}

// 获取报废出库数据
func getScrapData(ctx context.Context) (res []*model.PmsMaterialOutboundPrint, err error) {
	request := g.RequestFromCtx(ctx)
	// 判断是否传入dateRange
	dateRange := request.Get("dateRange").Array()
	// 判断是否传入了productGroupId
	productGroupId := request.Get("productGroupId").Int64()
	// 判断是否传入了关键字
	keyWord := request.Get("keyWord").String()
	inboundOutboundKey := request.Get("inbound_outbound_key").Int()
	outboundList := make([]*model.PmsMaterialOutboundPrint, 0)
	m := yc.DBM(model.NewPmsMaterialOutboundProduct()).Ctx(ctx).As("main").
		LeftJoin("pms_material_outbound as pmo", "main.outbound_id = pmo.id").
		LeftJoin("pms_material as pm", "main.material_id = pm.id").
		LeftJoin("pms_product_group as pp", "pp.id = main.product_group_id").
		Where("pmo.type", 3)

	if inboundOutboundKey > 0 {
		m = m.Where("main.inbound_outbound_key", inboundOutboundKey)
	}

	if len(dateRange) > 0 {
		m = m.WhereBetween("pmo.createTime", dateRange[0], dateRange[1])
	}

	if keyWord != "" {
		m = m.Where("pmo.no like ?", "%"+keyWord+"%")
	}

	if productGroupId > 0 {
		m = m.Where("main.product_group_id", productGroupId)
	}

	err = m.Fields("main.total_quantity,pmo.no,main.product_group_id,main.voucher,main.handling_method,main.scrap_date,pm.name,pm.code,pm.model," +
		"pm.unit,pp.name as product_group_name,main.outbound_id as outbound_id,main.material_id as materialId,main.quantity,main.id," +
		"pmo.outbound_time as outboundTime,pmo.createTime as createTime,main.remark,main.responsible").
		OrderDesc("main.product_group_id").
		Scan(&outboundList)
	if err != nil {
		return nil, err
	}
	return outboundList, nil
}

// GetScrapPrintData 获取报废打印数据
func (s *PmsMaterialOutboundService) GetScrapPrintData(ctx context.Context) (res []*model.PmsMaterialOutboundPrint, err error) {
	res, err = getScrapData(ctx)
	if err != nil {
		return nil, err
	}
	return res, err
}

// ExportScrap 导出报废数据
func (s *PmsMaterialOutboundService) ExportScrap(ctx context.Context) error {
	res, err := getScrapData(ctx)
	if err != nil {
		return err
	}

	if len(res) == 0 {
		return errors.New("没有数据")
	}

	// 创建一个新的Excel文件
	file := excelize.NewFile()
	sheetName := "报废单"
	file.SetSheetName("Sheet1", sheetName)

	// 设置表头
	headers := []string{
		"序号", "报废单号", "物料编码", "品名", "规格/型号",
		"单位", "数量", "报废日期", "责任人", "处理方式",
		"机型", "报废原因",
	}

	// 创建表头样式 - 黑体、居中对齐、背景色、边框
	headerStyle, err := file.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold: true,
			Size: 11,
		},
		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "center",
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Color:   []string{"#E0E6F1"},
			Pattern: 1,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "#000000", Style: 1},
			{Type: "top", Color: "#000000", Style: 1},
			{Type: "right", Color: "#000000", Style: 1},
			{Type: "bottom", Color: "#000000", Style: 1},
		},
	})
	if err != nil {
		return err
	}

	// 创建内容样式 - 居中对齐、边框
	contentStyle, err := file.NewStyle(&excelize.Style{
		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "center",
		},
		Border: []excelize.Border{
			{Type: "left", Color: "#000000", Style: 1},
			{Type: "top", Color: "#000000", Style: 1},
			{Type: "right", Color: "#000000", Style: 1},
			{Type: "bottom", Color: "#000000", Style: 1},
		},
	})
	if err != nil {
		return err
	}

	// 写入表头并应用样式
	for i, header := range headers {
		cell := fmt.Sprintf("%s1", string(rune('A'+i)))
		_ = file.SetCellValue(sheetName, cell, header)
		_ = file.SetCellStyle(sheetName, cell, cell, headerStyle)
	}

	// 写入数据并应用样式
	row := 2
	for i, item := range res {
		// 处理报废日期格式，只显示到日期
		var scrapDate string
		if item.ScrapDate != nil {
			scrapDate = item.ScrapDate.Format("Y-m-d")
		}

		_ = file.SetCellValue(sheetName, fmt.Sprintf("A%d", row), i+1)                   // 序号
		_ = file.SetCellValue(sheetName, fmt.Sprintf("B%d", row), item.No)               // 报废单号
		_ = file.SetCellValue(sheetName, fmt.Sprintf("C%d", row), item.Code)             // 物料编码
		_ = file.SetCellValue(sheetName, fmt.Sprintf("D%d", row), item.Name)             // 品名
		_ = file.SetCellValue(sheetName, fmt.Sprintf("E%d", row), item.Model)            // 规格/型号
		_ = file.SetCellValue(sheetName, fmt.Sprintf("F%d", row), item.Unit)             // 单位
		_ = file.SetCellValue(sheetName, fmt.Sprintf("G%d", row), item.Quantity)         // 数量
		_ = file.SetCellValue(sheetName, fmt.Sprintf("H%d", row), scrapDate)             // 报废日期
		_ = file.SetCellValue(sheetName, fmt.Sprintf("I%d", row), item.Responsible)      // 责任人
		_ = file.SetCellValue(sheetName, fmt.Sprintf("J%d", row), item.HandlingMethod)   // 处理方式
		_ = file.SetCellValue(sheetName, fmt.Sprintf("K%d", row), item.ProductGroupName) // 机型
		_ = file.SetCellValue(sheetName, fmt.Sprintf("L%d", row), item.Remark)           // 报废原因

		// 为每个单元格应用内容样式
		for j := 0; j < len(headers); j++ {
			cell := fmt.Sprintf("%s%d", string(rune('A'+j)), row)
			_ = file.SetCellStyle(sheetName, cell, cell, contentStyle)
		}

		row++
	}

	// 设置列宽
	_ = file.SetColWidth(sheetName, "A", "A", 6)  // 序号
	_ = file.SetColWidth(sheetName, "B", "B", 15) // 报废单号
	_ = file.SetColWidth(sheetName, "C", "C", 15) // 物料编码
	_ = file.SetColWidth(sheetName, "D", "D", 20) // 品名
	_ = file.SetColWidth(sheetName, "E", "E", 30) // 规格/型号
	_ = file.SetColWidth(sheetName, "F", "F", 8)  // 单位
	_ = file.SetColWidth(sheetName, "G", "G", 8)  // 数量
	_ = file.SetColWidth(sheetName, "H", "H", 15) // 报废日期
	_ = file.SetColWidth(sheetName, "I", "I", 15) // 责任人
	_ = file.SetColWidth(sheetName, "J", "J", 20) // 处理方式
	_ = file.SetColWidth(sheetName, "K", "K", 15) // 机型
	_ = file.SetColWidth(sheetName, "L", "L", 25) // 报废原因

	// 设置行高
	_ = file.SetRowHeight(sheetName, 1, 25) // 表头行高

	// 冻结首行
	_ = file.SetPanes(sheetName, &excelize.Panes{
		Freeze:      true,
		Split:       false,
		XSplit:      0,
		YSplit:      1,
		TopLeftCell: "A2",
		ActivePane:  "bottomLeft",
	})

	// 设置文件名
	fileName := fmt.Sprintf("报废数据-%s.xlsx", gtime.Now().Format("YmdHis"))
	fileName = url.QueryEscape(fileName)

	// 写入buffer并返回
	buffer, err := file.WriteToBuffer()
	if err != nil {
		return err
	}

	// 将文件写入响应
	utils.SetResponseToStream(ctx, fileName, buffer.Bytes())
	return nil
}

// addOutboundQueryConditions 添加查询条件
func addMaterialOutboundQueryConditions(ctx g.Ctx, builder *gdb.WhereBuilder) *gdb.WhereBuilder {
	request := g.RequestFromCtx(ctx)
	// 判断是否传入dateRange
	dateRange := request.Get("dateRange").Array()
	// finished := request.Get("finished").Bool()
	if len(dateRange) > 0 {
		t1 := gconv.GTime(dateRange[0], "Y-m-d")
		t2 := gconv.GTime(dateRange[1], "Y-m-d")
		if t1.Unix() < t2.Unix() {
			builder = builder.Where("createTime between ? and ?", t1, t2)
		} else if t1.Unix() > t2.Unix() {
			builder = builder.Where("createTime between ? and ?", t2, t1)
		} else if t1.Unix() == t2.Unix() {
			builder = builder.Where("createTime", t1)
		}
	}
	supplierId := request.Get("supplierId").Int64()
	// 如果传入了供应商ID，则查询和供应商绑定的出库单
	if supplierId > 0 {
		purchaseIds := make([]int64, 0)
		contractIds := make([]int64, 0)
		contracts := make([]*model.PmsPurchaseContract, 0)
		// 查询和供应商绑定的采购合同
		err := yc.DBM(model.NewPmsPurchaseContract()).Ctx(ctx).
			Where("supplier_id", supplierId).
			Scan(&contracts)
		if err != nil {
			return builder
		}
		for _, contract := range contracts {
			purchaseIds = append(purchaseIds, contract.PurchaseId)
			contractIds = append(contractIds, contract.ID)
		}
		purchaseIds = RemoveDuplicateInt64(purchaseIds)
		contractIds = RemoveDuplicateInt64(contractIds)
		builder = builder.WhereIn("order_id", purchaseIds)
		builder = builder.WhereIn("id", yc.DBM(model.NewPmsMaterialOutboundProduct()).Ctx(ctx).WhereIn("contract_id", contractIds).
			Fields("outbound_id"))
	}
	return builder
}

// 状态转换
func StatusConvert(status int) int {
	statusMap := g.MapIntInt{
		int(model.MaterialOutboundTypePick):        model.OperationTypeOutboundPick,   // 领料出库
		int(model.MaterialOutboundTypeSubcontract): model.OperationTypeOutboundPick,   // 领料出库
		int(model.MaterialOutboundTypeReturn):      model.OperationTypeOutboundReturn, // 退货出库
		int(model.MaterialOutboundTypeCustom):      model.OperationTypeOutboundCustom, // 自定义出库
		int(model.MaterialOutboundTypeScrap):       model.OperationTypeScrap,          //报废
	}
	return statusMap[status]
}

func NewPmsMaterialOutboundService() *PmsMaterialOutboundService {
	return &PmsMaterialOutboundService{
		&yc.Service{
			Model: model.NewPmsMaterialOutbound(),
			PageQueryOp: &yc.QueryOp{
				FieldEQ:      []string{"status", "type"},
				KeyWordField: []string{"no"},
				AdditionFields: []*yc.AdditionField{
					{
						FieldName: "count",
						FieldFunc: getMaterialOutboundCount,
					},
				},
				Where: addMaterialOutboundQueryConditions,
				With: []interface{}{
					&model.MaterialOutboundProductOutput{},
					&model.PmsMaterialOutput{},
					&model.PmsSupplierSecretOutput{},
					&model.PmsPurchaseOrderContractInboundOutput{},
				},
				ResultModify: func(ctx context.Context, data interface{}) (res interface{}, err error) {
					outboundList := make([]*model.PmsMaterialOutbound, 0)
					err = gconv.Structs(data, &outboundList)
					if err != nil {
						return nil, err
					}
					result, err := NewPmsProductGroupService().GetProductGroupList(ctx)
					if err != nil {
						return nil, err
					}
					if len(outboundList) > 0 {
						for i, item := range outboundList {
							// 如果是领料出库，需要查询工单号
							if item.Type == int(model.MaterialOutboundTypePick) || item.Type == int(model.MaterialOutboundTypeSubcontract) {
								workOrderId := item.WorkOrderId
								mMaterialData := make(map[int64]*model.PmsWorkOrderDetail, 0)
								if workOrderId > 0 {
									workOrderDetails := make([]*model.PmsWorkOrderDetail, 0)
									err = yc.DBM(model.NewPmsWorkOrderDetail()).Ctx(ctx).Where("work_order_id", workOrderId).Scan(&workOrderDetails)
									if err != nil {
										return nil, err
									}
									var workOrder *model.PmsWorkOrder
									//查询工单号
									err = yc.DBM(model.NewPmsWorkOrder()).Ctx(ctx).Where("id", workOrderId).Scan(&workOrder)
									if err != nil {
										return nil, err
									}
									outboundList[i].WorkOrderNo = workOrder.WorkOrderNo
									outboundList[i].ProductionScheduleSn = workOrder.ProductionScheduleSn
									for _, v := range workOrderDetails {
										mMaterialData[v.MaterialId] = v
									}
								}
								if len(item.ProductsOutput) > 0 {
									for j, row := range item.ProductsOutput {
										if val, ok := mMaterialData[row.MaterialId]; ok {
											outboundList[i].ProductsOutput[j].CalcBomQuantity = val.CalcBomQuantity
											outboundList[i].ProductsOutput[j].OutboundQuantity = val.OutboundQuantity
										}
									}
								}
							}
							if item.Type == int(model.MaterialOutboundTypeScrap) {
								if len(item.ProductsOutput) > 0 {
									for j, row := range item.ProductsOutput {
										if product, ok := result.IdMap[row.ProductGroupId]; ok {
											outboundList[i].ProductsOutput[j].ProductGroupName = product.Name
										}
									}
								}
							}
							//request := g.RequestFromCtx(ctx)
							//supplierId := request.Get("supplierId").Int64()
							//// 如果是退货出库
							//if item.Type == int(model.MaterialOutboundTypeReturn) && supplierId > 0 {
							//	contract := &model.PmsPurchaseContract{}
							//	err = yc.DBM(model.NewPmsPurchaseContract()).Ctx(ctx).
							//		Where("id", item.ContractId).
							//}
						}
					}

					r := g.RequestFromCtx(ctx)
					inboundOutboundKey := r.Get("inbound_outbound_key").Int()
					if inboundOutboundKey > 0 {
						tmpOutbounds := make([]*model.PmsMaterialOutbound, 0)
						if inboundOutboundKey > 0 {
							for _, item := range outboundList {
								if len(item.ProductsOutput) > 0 {
									tmp := make([]*model.MaterialOutboundProductOutput, 0)
									for _, row := range item.ProductsOutput {
										if row.InboundOutboundKey == inboundOutboundKey {
											tmp = append(tmp, row)
										}
									}
									item.ProductsOutput = tmp
								}
								if len(item.ProductsOutput) > 0 {
									tmpOutbounds = append(tmpOutbounds, item)
								}
							}
						}
						return tmpOutbounds, err
					} else {
						return outboundList, err
					}
				},
			},
			InfoQueryOp: &yc.QueryOp{
				With: []interface{}{
					&model.MaterialOutboundProductOutput{},
					&model.PmsMaterialOutput{},
					&model.PmsPurchaseOrderContractInboundOutput{},
				},
				ResultModify: func(ctx context.Context, data interface{}) (res interface{}, err error) {
					tmp := &model.PmsMaterialOutboundInfo{}
					err = gconv.Struct(data, tmp)
					if err != nil {
						return nil, err
					}
					if tmp.WorkOrderId > 0 {
						WorkOrder := &model.PmsWorkOrder{}
						err = yc.DBM(model.NewPmsWorkOrder()).Ctx(ctx).Where("id", tmp.WorkOrderId).Scan(&WorkOrder)
						if err != nil {
							return nil, err
						}
						if WorkOrder.ProductId > 0 {
							Product := &model.PmsProduct{}
							err = yc.DBM(model.NewPmsProduct()).Ctx(ctx).Where("id", WorkOrder.ProductId).Scan(&Product)
							if err != nil {
								return nil, err
							}
							WorkOrder.ProductName = Product.Name
						}
						tmp.WorkOrder = WorkOrder
						if tmp.WorkOrder != nil {
							// 查询生产单Sn
							productionSchedule := &model.PmsProductionSchedule{}
							err = yc.DBM(model.NewPmsProductionSchedule()).Ctx(ctx).Where("id", tmp.WorkOrder.ProductionScheduleId).Scan(&productionSchedule)
							if err != nil {
								return nil, err
							}
							tmp.WorkOrder.ProductionScheduleSn = productionSchedule.Sn
						}
						workOrderDetailList := make([]*model.PmsWorkOrderDetail, 0)
						err = yc.DBM(model.NewPmsWorkOrderDetail()).Ctx(ctx).Where("work_order_id", tmp.WorkOrderId).OrderDesc("id").Scan(&workOrderDetailList)
						if err != nil {
							return nil, err
						}
						tmp.Extras = workOrderDetailList
						if len(tmp.Extras) > 0 {
							materialMap, err := NewPmsMaterialService().QueryAllMaterial(ctx)
							if err != nil {
								return nil, err
							}
							// 兼容旧版前端
							for i, item := range tmp.Extras {
								// 主要这里的赋值顺序不能变
								tmp.Extras[i].WorkOrderDetailID = item.ID
								tmp.Extras[i].ID = item.MaterialId
								if materialMap.MaterialIdMap[item.MaterialId] != nil {
									el := materialMap.MaterialIdMap[item.MaterialId]
									tmp.Extras[i].Inventory = el.Inventory
									tmp.Extras[i].ExpectedInbound = el.ExpectedInbound
									tmp.Extras[i].Code = el.Code
									tmp.Extras[i].Name = el.Name
									tmp.Extras[i].Model = el.Model
									tmp.Extras[i].Unit = el.Unit
									tmp.Extras[i].Material = el.Material
									tmp.Extras[i].CoverColor = el.CoverColor
									tmp.Extras[i].Size = el.Size
									tmp.Extras[i].AddressName = el.AddressName
								}
							}
						}
						if len(tmp.ProductsOutput) > 0 {
							for i, item := range tmp.ProductsOutput {
								if len(tmp.Extras) > 0 {
									for _, el := range tmp.Extras {
										if el.MaterialId == item.MaterialId {
											tmp.ProductsOutput[i].ID = el.MaterialId
											tmp.ProductsOutput[i].WorkOrderDetailID = el.WorkOrderDetailID
											tmp.ProductsOutput[i].WorkOrderID = el.WorkOrderID
											tmp.ProductsOutput[i].BomQuantity = el.BomQuantity
											tmp.ProductsOutput[i].OutboundQuantity = el.OutboundQuantity
											tmp.ProductsOutput[i].OutQty = el.OutboundQuantity
											tmp.ProductsOutput[i].CalcBomQuantity = el.CalcBomQuantity
											//if WorkOrder.Quantity > 0 && el.BomQuantity > 0 {
											//	tmp.ProductsOutput[i].CalcBomQuantity = el.BomQuantity * WorkOrder.Quantity
											//}
										}
									}
								}

							}
						}
					}

					if tmp.Type == int(model.MaterialOutboundTypeReturnInventory) {
						for i := range tmp.ProductsOutput {
							// 兼容前端
							tmp.ProductsOutput[i].ID = tmp.ProductsOutput[i].MaterialId
						}
					}
					return tmp, nil
				},
				ScanStruct: &model.PmsMaterialOutboundInfo{},
			},
		},
		&AuditService{
			Key: AuditMaterialOutboundKey,
		},
	}
}
func init() {
	RegisterAuditService(AuditMaterialOutboundKey, NewPmsMaterialOutboundService())
}
