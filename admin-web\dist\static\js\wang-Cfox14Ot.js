import{c as I,aI as M,aJ as R,aK as j,b as N,e as P,z as V,a1 as $,i as d,f as D,o as g,h,G as K,B as w,q as B,V as A,W as J,I as L}from"./.pnpm-hVqhwuVC.js";import{p as W,l as q}from"./index-BtOcqcNl.js";import{a as G}from"./index-D95m1iJL.js";import Q from"./upload-C9JKmO0R.js";import{_ as X}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./viewer.vue_vue_type_script_setup_true_name_item-viewer_lang-B08Tkdj3.js";const Y=I({name:"ClEditorWang",components:{Editor:R,Toolbar:M,Upload:Q},props:{modelValue:String,mode:{type:String,default:"simple"},uploadType:{type:String,default:"default"},height:{type:[String,Number],default:400},disabled:Boolean},emits:["update:modelValue","change","focus","blur","onCreated"],setup(e,{emit:l}){const{refs:r,setRefs:v}=G(),t=j(),s=N(),f=P(()=>({height:W(e.height)}));V(()=>e.modelValue,o=>{s.value=o||""},{immediate:!0});const a={insertFn:null},m={toolbarKeys:["bold","underline","italic","color","bgColor","clearStyle","|","fontSize","fontFamily","lineHeight","|",{key:"group-justify",iconSvg:'<svg viewBox="0 0 1024 1024"><path d="M768 793.6v102.4H51.2v-102.4h716.8z m204.8-230.4v102.4H51.2v-102.4h921.6z m-204.8-230.4v102.4H51.2v-102.4h716.8zM972.8 102.4v102.4H51.2V102.4h921.6z"></path></svg>',title:"对齐",menuKeys:["justifyLeft","justifyRight","justifyCenter","justifyJustify"]},"|","emotion",{key:"group-image",title:"图片",iconSvg:'<svg viewBox="0 0 1024 1024"><path d="M959.877 128l0.123 0.123v767.775l-0.123 0.122H64.102l-0.122-0.122V128.123l0.122-0.123h895.775zM960 64H64C28.795 64 0 92.795 0 128v768c0 35.205 28.795 64 64 64h896c35.205 0 64-28.795 64-64V128c0-35.205-28.795-64-64-64zM832 288.01c0 53.023-42.988 96.01-96.01 96.01s-96.01-42.987-96.01-96.01S682.***********.99 ***********.***********.01zM896 832H128V704l224.01-384 256 320h64l224.01-192z"></path></svg>',menuKeys:["uploadImage"]},"divider","|","undo","redo","|","fullScreen"]},u={placeholder:"请输入",MENU_CONF:{uploadImage:{customBrowseAndUpload(o){a.insertFn=o,r.default.handleOpen()}},uploadVideo:{customBrowseAndUpload(o){a.insertFn=o,r.video.open()}}}};function c(o){t.value=o,l("onCreated",o),C()}function C(){var o,n;e.disabled?(o=t.value)==null||o.disable():(n=t.value)==null||n.enable()}function z(o){l("focus",o)}function E(o){l("blur",o)}function O(){l("update:modelValue",s.value),l("change",s.value)}const H=async(o,n,y)=>{let b=[];const k=i=>{b.push(i)};let F=!1,S=(n.clipboardData||n.originalEvent.clipboardData).items;for(let i in S){let p=S[i];p.kind==="file"&&p.type.indexOf("image/")!==-1&&(y(!1),await q({file:p.getAsFile()},{type:"image"},k),F=!0)}if(F){for(let i of b)o.dangerouslyInsertHtml(`<img src="${i.url}" alt=""/>`);return}y(!0)};function T(o){o.length>0&&o.forEach(n=>{a.insertFn&&a.insertFn(n.url)})}$(()=>{const o=t.value;o!=null&&o.destroy()}),V(()=>e.disabled,C);function U(o){var n;(n=a.insertFn)==null||n.call(a,o.url)}return{refs:r,setRefs:v,Editor:t,value:s,style:f,onCreated:c,onFocus:z,onBlur:E,onChange:O,onCustomPaste:H,editorConfig:u,onFileConfirm:T,toolbarConfig:m,uploadType:e.uploadType,uploadSuccess:U}}});function Z(e,l,r,v,t,s){const f=d("Toolbar"),a=d("Editor"),m=d("Upload"),u=d("cl-upload-space");return g(),D("div",{class:J(["cl-editor-wang",{disabled:e.disabled}])},[h(f,{editor:e.Editor,mode:e.mode,"default-config":e.toolbarConfig},null,8,["editor","mode","default-config"]),h(a,{modelValue:e.value,"onUpdate:modelValue":l[0]||(l[0]=c=>e.value=c),"default-config":e.editorConfig,mode:e.mode,style:A(e.style),onOnCreated:e.onCreated,onOnFocus:e.onFocus,onOnBlur:e.onBlur,onOnChange:e.onChange,onCustomPaste:e.onCustomPaste},null,8,["modelValue","default-config","mode","style","onOnCreated","onOnFocus","onOnBlur","onOnChange","onCustomPaste"]),e.uploadType==="default"?K((g(),B(m,{key:0,ref:e.setRefs("default"),type:"file","show-file-list":!1,accept:"image/*",onSuccess:e.uploadSuccess},null,8,["onSuccess"])),[[L,!1]]):w("",!0),e.uploadType==="image"?(g(),B(u,{key:1,ref:e.setRefs("image"),accept:"image/*","show-btn":!1,onConfirm:e.onFileConfirm},null,8,["onConfirm"])):w("",!0),h(u,{ref:e.setRefs("video"),accept:"video/*","show-btn":!1,onConfirm:e.onFileConfirm},null,8,["onConfirm"])],2)}const le=X(Y,[["render",Z],["__scopeId","data-v-f6ce6bbe"]]);export{le as default};
