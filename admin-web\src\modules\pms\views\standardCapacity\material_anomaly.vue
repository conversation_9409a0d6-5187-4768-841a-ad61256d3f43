<script lang="ts" name="pms-job-instruction" setup>
import {useCrud, useSearch, useTable, useUpsert} from '@cool-vue-p/crud'
import {computed, ref} from 'vue'
import { useCool } from '/@/cool'
import {useTableOps} from "/$/pms/hooks/table-ops";
import {ElMessage, ElMessageBox} from "element-plus";
import {downloadBlob} from "/@/cool/utils";
import {useDict} from "/$/dict";
import * as XLSX from 'xlsx'
const orderList = ref<any[]>([])
const orderOption = ref<any[]>([])
const supplierList = ref<any[]>([])
const supplierOption = ref<any[]>([])
const productOptions = ref<any[]>([])
const productList = ref<any[]>([])
const materialList = ref<any[]>([])
const materialOptions = ref<any[]>([])
const { service } = useCool()
import Dayjs from 'dayjs'
import row from "../../../../../packages/crud/src/components/row";
import moment from "moment";
const { dict } = useDict()
// 导入文件
const fileInputRef = ref<HTMLInputElement | null>(null)
const isLoading = ref(false)
const colorList = dict.get('color')
if (!colorList.value?.find(item => item.value === 0))
  colorList.value?.unshift({ label: '无', value: 0 })

// 获取生产订单列表
async function getOrder() {
  try {
    const res = await service.pms.production.schedule.request({
      url: '/list',
      method: 'POST',
    })
    orderList.value = res

    orderOption.value = res?.map((e: any) => {
      return {
        value: e.id,
        label: e.sn,
      }
    })
  }
  catch (e: any) {
    console.error(e)
  }
}

// 根据生产订单获取产品列表
async function getProductList(id: any) {
  try {
    productOptions.value = await service.pms.production.schedule.request({
      url: '/getProductListByOrderId',
      method: 'POST',
      data: { order_id: id },
    })
    productOptions.value = productOptions.value?.map((e: any) => {
      return {
        ...e,
        value: e.sku,
        label: `${e.sku} ${e.name}`,
      }
    })
  }
  catch (e: any) {
    console.error(e)
  }
}

// 获取产品列表
async function getAllProductList() {
  try {
    const res = await service.pms.product.request({
      url: '/getAllProduct',
      method: 'GET',
    })
    productList.value = res?.map((e: any) => {
      return {
        ...e,
        group_id: e.groupId,
        value: e.id,
        sku: e.sku,
        label: `${e.sku} ${e.name}`,
      }
    })
  }
  catch (e: any) {
    console.error(e)
  }
}


// 获取物料列表
async function getMaterialByName(keyword:string) {
  try {
    const res = await service.pms.material.request({
      url: "/getMaterialByName",
      method: "GET",
      params: {
        keyword,
      },
    });
    materialOptions.value = res.map((e: any) => {
      return {
        ...e,
        value: e.id,
        label: `${e.name} / ${e.code}`,
      }
    })
  } catch (e: any) {
    console.error(e);
  }
}

// 获取物料列表
async function getMaterialById(id: number) {
  try {
    const res = await service.pms.material.request({
      url: "/getMaterialById",
      method: "GET",
      params: {
        id,
      },
    });
    materialOptions.value = [res].map((e: any) => {
      return {
        ...e,
        value: e.id,
        label: `${e.name} / ${e.code}`,
      }
    })
  } catch (e: any) {
    console.error(e);
  }
}

// 获取供应商列表
async function getSupplier() {
  try {
    supplierList.value = await service.pms.supplier.request({
      url: '/list',
      method: 'POST',
    })
    supplierOption.value = supplierList.value.map((item) => {
      return {
        label: item.supplierName,
        value: item.id,
      }
    })
  }
  catch (e: any) {
    console.error(e)
  }
}
// 获取物料列表
async function getMaterial() {
  try {
    const res = await service.pms.material.request({
      url: "/list",
      method: "POST",
    });
    materialList.value = res.map((e =>{
      return {
        ...e,
        value: e.id,
        label: `${e.name} / ${e.code}`,
      }
    }));
  } catch (e: any) {
    console.error(e);
  }
}
watchEffect(() => {
  getSupplier()
  getOrder()
  getMaterial()
  getAllProductList()
})

const WorkshopSectionOptions = [
  {
    label: '加工段',
    value: 1,
    name: '-',
    nameEn: '-',
    type: 'info',
  },
  {
    label: '组装段',
    value: 2,
    name: '-',
    nameEn: '-',
    type: 'info',
  },
  {
    label: '老化段',
    value: 3,
    name: '-',
    nameEn: '-',
    type: 'info',
  },
  {
    label: '包装段',
    value: 4,
    name: '-',
    nameEn: '-',
    type: 'info',
  },
  {
    label: '加工段一',
    value: 5,
    name: '-',
    nameEn: '-',
    type: 'info',
  },
  {
    label: '加工段二',
    value: 6,
    name: '-',
    nameEn: '-',
    type: 'info',
  },
  {
    label: '加工段三',
    value: 7,
    name: '-',
    nameEn: '-',
    type: 'info',
  },
  {
    label: '芯子配件加工段一',
    value: 8,
    name: '-',
    nameEn: '-',
    type: 'info',
  },
  {
    label: '芯子配件加工段二',
    value: 9,
    name: '-',
    nameEn: '-',
    type: 'info',
  },
  {
    label: '芯子配件组装段一',
    value: 10,
    name: '-',
    nameEn: '-',
    type: 'info',
  },
  {
    label: '芯子配件组装段二',
    value: 11,
    name: '-',
    nameEn: '-',
    type: 'info',
  },
  {
    label: '芯子配件组装段三',
    value: 12,
    name: '-',
    nameEn: '-',
    type: 'info',
  },
  {
    label: '芯子配件组装段四',
    value: 13,
    name: '-',
    nameEn: '-',
    type: 'info',
  },
  {
    label: '芯子配件组装段五',
    value: 14,
    name: '-',
    nameEn: '-',
    type: 'info',
  },
  {
    label: '芯子配件包装段',
    value: 15,
    name: '-',
    nameEn: '-',
    type: 'info',
  },
]
// cl-upsert 配置
const Upsert = useUpsert({
  props: {
    class: 'material-anomaly-form',
    labelWidth: '120px',
  },
  items: [
    {
      label: '异常日期',
      prop: 'abnormal_date',
      required: true,
      component: {
        name: 'el-date-picker',
        props: {
          'type': 'date',
          'placeholder': '选择日期',
          'value-format': 'YYYY-MM-DD',
          'format': 'YYYY-MM-DD',
          'clearable': true,
          // 不能超过今天
          'disabledDate': (time: any) => {
            return time.getTime() > Date.now()
          },
        },
      },
    },
    {
      label: '生产订单',
      prop: 'order_id',
      required: true,

      component: {
        options: orderOption,
        name: 'el-select',
        props: {
          clearable: true,
          filterable: true,
          onChange(order_id: number) {
            Upsert.value?.setForm('sku', undefined)
            getProductList(order_id)
          },
        }
      },
    },
    {
      label: '机型',
      prop: 'sku',
      required: true,
      component: {
        options: productOptions,
        name: 'el-select',
        props: {
          clearable: true,
          filterable: true,
        }
      },
    },
    {
      label: '物料',
      prop: 'material_id',
      required: true,
      component: {
        options: materialOptions,
        name: 'el-select',
        props: {
          clearable: true,
          filterable: true,
          'filter-method': (keyword: string) => {
            if(keyword === ''){
              return
            }
            getMaterialByName(keyword)
          },
        }
      },
    },
    {
      label: '供应商',
      prop: 'supplier_id',
      required: true,
      component: {
        options: supplierOption,
        name: 'el-select',
        props: {
          clearable: true,
          filterable: true,
        }
      },
    },
    {
      label: '工段',
      prop: 'workshop_section',
      required: true,
      component: {
        name: 'el-select',
        props: {
          filterable: true,
        },
        options: WorkshopSectionOptions,
      },
    },
    {
      label: "损耗数量",
      prop: "quantity",
      required: true,
      component: {  name: 'el-input-number', props: { min: 1 }  },
    },
    {
      label: '描述',
      prop: 'description',
      required: true,
      component: {
        name: 'el-input',
      },
    },
    {
      label: '责任单位',
      prop: 'accountability_unit',
      required: true,
      component: {
        name: 'el-input',
      },
    },
    {
      label: '备注',
      prop: 'remark',
      required: true,
      component: {
        name: 'el-input',
      },
    },
  ],
  async onInfo(data, { done }) {
    console.log('data',data)
    getMaterialById(data.material_id)
    done(data)
  },
})

// cl-table 配置
const Table = useTable({
  columns: [
    { label: 'ID', prop: 'id', width: 60 },
    // { label: '创建时间', prop: 'createTime', width: 180 },
    { label: '异常日期',
      prop: 'abnormal_date', width: 100,
      formatter: (row: any) => {
        return (
          Dayjs(new Date(row.abnormal_date)).format('YYYY-MM-DD')
        )
      },
    },
    { label: '订单号', prop: 'order_id', width: 120,dict: orderOption },
    { label: '订单数量', prop: 'order_quantity', width: 120 },
    { label: '产品名', prop: 'product_name', width: 120 },
    { label: 'SKU', prop: 'sku', width: 120 },
    { label: '颜色', prop: 'color', width: 120,dict: colorList  },
    {
      label: '工段',
      prop: 'workshop_section',
      width: 150 ,
      dict: WorkshopSectionOptions,
    },
    { label: '异常描述', prop: 'description' },
    { label: '责任单位', prop: 'accountability_unit' },
    {
      label: '实际生产数据',
      align: 'center',
      children: [
        {label: "物料编码", prop: "material_code", width: 150},
        {label: "物料名称", prop: "material_name", width: 180},
        {label: '供应商', prop: 'supplier_id', width: 220, dict: supplierOption},
        {label: "损耗数(pcs)", prop: "quantity", width: 100},
        {
          label: "物料单价（元/pcs)", prop: "unit_price", width: 100,
          formatter(row: any) {
            return row.unit_price.toFixed(3)
          },
        },
        {
          label: "损耗金额（元）",
          prop: "subtotal",
          width: 100,
          formatter(row: any) {
            return row.subtotal.toFixed(3)
          },},
      ]
    },
    { label: '备注', prop: 'remark' },
    {
      type: 'op',
      label: '操作',
      buttons: ['edit', 'delete'],
      width:300,
    },
  ],
})

// cl-crud 配置
const Crud = useCrud(
  {
    service: service.pms.material_anomaly,
    async onRefresh(params, { next, render }) {
      // 1 默认调用
      const { list, pagination } = await next(params)
      render(list, pagination)
    },
  },
  (app) => {
    app.refresh()
  },
)



const Search = useSearch({
  items: [
    {
      label: '异常时间',
      prop: 'dateRange',
      props: {
        labelWidth: '100px',
      },
      component: {
        name: 'el-date-picker',
        props: {
          'type': 'daterange',
          'value-format': 'YYYY-MM-DD',
          'format': 'YYYY-MM-DD',
          'rangeSeparator': '至',
          'startPlaceholder': '开始日期',
          'endPlaceholder': '结束日期',
          'clearable': true,

          onChange(dateRange) {
            Crud.value?.refresh({
              dateRange,
            })
          },
        },
      },
    },
    {
      label: 'sku',
      prop: 'keyword',
      props: {
        labelWidth: '120px',
      },
      component: {
        name: 'el-input',
        props: {
          clearable: true,
          onChange(keyword: string) {
            Crud.value?.refresh({ keyword, page: 1 })
          },
        },
      },
    },
    {
      label: '订单号',
      prop: 'order_id',
      props: { labelWidth: '80px' },
      component: {
        name: 'el-select',
        props: {
          style: 'width: 200px',
          clearable: true,
          filterable: true,
          onChange(order_id) {
            Crud.value?.refresh({
              order_id,
            })
          },
        },
        options: orderOption,
      },
    },
  ],
})

function handleDownloadAttachment(row: any) {
  // 下载附件
  // 请求downloadAttachment接口，返回blob文件
  service.pms.jobInstruction
    .request({
      url: '/download',
      data: { id: row.id },
      method: 'POST',
      responseType: 'blob',
    })
    .then((res: any) => {

      // 如果是blob，下载文件
      if (downloadBlob(res, `测试名称.zip`))
        ElMessage.success('附件下载成功')
    })
    .catch((err: any) => {
      // 提示错误消息
      ElMessage.error(err.message)
    })
}

// 下载excel模板
function downloadExcelTemplate() {
  const fileName = '物料异常表_模板.xlsx'
  const filePath = '/material_abnormal_template.xlsx'

  // 发起下载请求
  fetch(filePath)
    .then(response => response.blob())
    .then((blob) => {
      // 保存文件
      downloadBlob(blob, fileName)
    })
    .catch(() => {
      ElMessage.error({
        message: '下载模板文件失败',
      })
    })
}

function openFileInput() {
  const fileInput = fileInputRef.value
  if (fileInput)
    fileInput.click()
}
function clearInput(fileInput: { value: string }) {
  // 清空文件输入的值
  if (fileInput)
    fileInput.value = ''
}
// 处理文件输入框的change事件
async function handleFileInputChange(event: Event) {
  const fileInput = event.target as HTMLInputElement
  const files = fileInput.files
  const WorkshopSectionMap: any = {}
  if (WorkshopSectionOptions && WorkshopSectionOptions.length > 0) {
    WorkshopSectionOptions.forEach((item: any) => {
      WorkshopSectionMap[item.label] = item.value
    })
  }

  if (files && files.length > 0) {
    isLoading.value = true
    const file = files[0]
    const reader = new FileReader()

    reader.onload = (e: ProgressEvent<FileReader>) => {
      const data = new Uint8Array(e.target?.result as ArrayBuffer)
      const workbook = XLSX.read(data, { type: 'array' })

      // 这里可以根据需要处理读取到的Excel数据
      const worksheet = workbook.Sheets[workbook.SheetNames[0]]
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })
      // 定义非法值
      const illegalValue = [undefined, null, '', 'undefined', 'null', 'NaN']
      // 定义列
      const columns: string[] = [
        'abnormal_date',
        'order_id',
        'sku',
        'workshop_section',
        'description',
        'accountability_unit',
        'material_code',
        'supplier_name',
        'quantity',
        're_mark',
      ]
      const result: any = []
      if (jsonData && jsonData.length > 0) {
        for (let i = 4; i < jsonData.length; i++) {

          const row = jsonData[i] as string[]
          console.log('row',row)
          const cell: any = {}
          for (let j = 0; j < row.length; j++) {
            const columnName = columns[j]
            cell[columnName] = (row[j].toString() || '').trim()

            if (columnName === 'abnormal_date' && cell.abnormal_date) {
              cell.abnormal_date = moment(cell.abnormal_date).format('YYYY-MM-DD')
            }
            // 获取工序
            if (columnName === 'workshop_section') {
              cell.workshop_section = WorkshopSectionMap[cell.workshop_section]
                ? WorkshopSectionMap[cell.workshop_section]
                : 0
            }

            // 获取订单号
            if (columnName === 'order_id') {
              cell.order_id = orderList.value.find(item =>
                item.sn.toLowerCase().includes(cell.order_id.toLowerCase()),
              )?.id
              if (cell.order_id === undefined || cell.order_id === 0) {
                continue
              }
            }
            cell.quantity = Number.parseInt(cell.quantity)
          }

          cell.supplier_id = supplierList.value.find(
            item => item.supplierName === cell.supplier_name,
          )?.id
          cell.material_id = materialList.value.find(
            item => item.code === cell.material_code,
          )?.id
          if (illegalValue.includes(cell.abnormal_date)) {
            clearInput(fileInput)
            isLoading.value = false
            break
          }
          if (
            illegalValue.includes(cell.workshop_section)
            || Number.isNaN(cell.workshop_section)
            || cell.workshop_section === 0
          ) {
            clearInput(fileInput)
            isLoading.value = false
            break
          }
          if (
            illegalValue.includes(cell.quantity)
            || Number.isNaN(cell.quantity)
            || cell.quantity === 0
          ) {
            clearInput(fileInput)
            isLoading.value = false
            break
          }
          if (
            illegalValue.includes(cell.order_id)
            || Number.isNaN(cell.order_id)
            || cell.order_id === 0
          ) {
            clearInput(fileInput)
            isLoading.value = false
            break
          }
          if (
            illegalValue.includes(cell.supplier_id)
            || Number.isNaN(cell.supplier_id)
            || cell.order_id === 0
          ) {
            clearInput(fileInput)
            isLoading.value = false
            break
          }
          if (illegalValue.includes(cell.sku) || cell.sku === '') {
            clearInput(fileInput)
            isLoading.value = false
            break
          }
          result.push(cell)
        }
        // 校验物料编码是否重复
        if (result.length > 0) {
          service.pms.material_anomaly.ImportMaterialAnomaly({
            material_anomaly: result,
          })
            .then(() => {
              Crud.value?.refresh()
              ElMessage.success(`导入成功`)
            })
            .catch((e: any) => {
              ElMessage.error(e.message || '导入失败')
            })
            .finally(() => {
              isLoading.value = false
            })
        }
        else {
          isLoading.value = false
          ElMessage.error('导入有效数据为空')
        }
        clearInput(fileInput)
      }
    }
    reader.readAsArrayBuffer(file)
  }
  else {
    isLoading.value = false
    ElMessage.error('请选择文件')
  }
}

// 导出数据
function handleExport() {
  const sku = Search.value?.getForm("sku")
  const order_id = Search.value?.getForm("order_id")
  const dateRange = Search.value?.getForm("dateRange")

  const params = {
    url: '/export',
    method: 'GET',
    responseType: 'blob',
    params: {
      sku,
      order_id,
      dateRange,
    },
  }
  service.pms.material_anomaly.request(params)
    .then((res: any) => {
      if (downloadBlob(res))
        ElMessage.success('导出成功')
      Crud.value?.refresh()
    })
    .catch((err: any) => {
      // 提示错误消息
      ElMessage.error(err.message || '导出失败')
    })
}

</script>

<template>
  <cl-crud ref="Crud">
    <el-row>
      <!-- 刷新按钮 -->
      <cl-refresh-btn />
      <!-- 新增按钮 -->
      <cl-add-btn v-if="service.pms.material_anomaly.permission.add"/>
      <!-- 下载excel模板 -->
      <el-button
        v-permission="
          service.pms.material_anomaly.permission.ImportMaterialAnomaly
        "
        style="margin-left: 10px"
        type="info"
        class="mb-10px mr-10px"
        ml="10px"
        size="default"
        @click="downloadExcelTemplate"
      >
        下载Excel模板
      </el-button>
      <input
        ref="fileInputRef"
        type="file"
        style="display: none"
        accept=".xlsx, .xls"
        @change="handleFileInputChange"
      >
      <el-button
        size="default"
        :loading="isLoading"
        type="warning"
        class="mb-10px mr-10px"
        @click="openFileInput"
      >
        Excel导入
      </el-button>
      <el-button type="success" @click="handleExport">
        导出
      </el-button>
      <cl-flex1 />

      <!-- 关键字搜索 -->
      <cl-search ref="Search" />

    </el-row>
    <el-row style="margin-top: 10px">
      <!-- 数据表格 -->
      <cl-table ref="Table">
<!--        &lt;!&ndash; 下载附件按钮 &ndash;&gt;-->
<!--        <template #slot-btn-download="{ scope }">-->
<!--          <el-button-->
<!--            text bg-->
<!--            type="primary"-->
<!--            @click.stop="handleDownloadAttachment(scope.row)"-->
<!--          >-->
<!--            下载-->
<!--          </el-button>-->
<!--        </template>-->
      </cl-table>
    </el-row>

    <el-row>
      <cl-flex1 />
      <!-- 分页控件 -->
      <cl-pagination />
    </el-row>
    <!-- 新增、编辑 -->
    <cl-upsert ref="Upsert">
    </cl-upsert>
  </cl-crud>
</template>

<style lang="scss">
.material-anomaly-form {
  .cl-form__items {
    .el-row {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      .full-line {
        grid-column: 1 / -1;
      }
    }

    // 让input-number的提示语靠左显示
    .el-input-number {
      :deep(.el-input__wrapper) {
        padding-left: 11px;
        input {
          text-align: left;
        }
      }
    }
  }
}
</style>
