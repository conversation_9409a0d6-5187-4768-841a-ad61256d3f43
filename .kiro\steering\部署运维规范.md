---
inclusion: manual
---

# 部署运维规范

## 环境管理

### 环境分类
- **开发环境 (dev)**: 开发人员本地开发和联调
- **测试环境 (test)**: 功能测试和集成测试
- **预生产环境 (staging)**: 生产前最后验证
- **生产环境 (prod)**: 正式对外提供服务

### 环境配置管理
```yaml
# config/config.dev.yaml - 开发环境
server:
  address: ":8080"
  serverRoot: "resource/public"

database:
  default:
    link: "mysql:root:123456@tcp(127.0.0.1:3306)/lookah_erp_dev?charset=utf8mb4&parseTime=True&loc=Local"
    debug: true
    maxIdle: 5
    maxOpen: 10

redis:
  default:
    address: "127.0.0.1:6379"
    db: 0

logger:
  level: "all"
  stdout: true
```

```yaml
# config/config.prod.yaml - 生产环境
server:
  address: ":8080"
  serverRoot: "resource/public"

database:
  default:
    link: "mysql:${DB_USER}:${DB_PASS}@tcp(${DB_HOST}:${DB_PORT})/${DB_NAME}?charset=utf8mb4&parseTime=True&loc=Local"
    debug: false
    maxIdle: 20
    maxOpen: 100
    maxLifetime: "30s"

redis:
  default:
    address: "${REDIS_HOST}:${REDIS_PORT}"
    db: 0
    pass: "${REDIS_PASS}"

logger:
  level: "warn"
  stdout: false
  file: "logs/app.log"
```

## Docker 容器化

### 后端 Dockerfile
```dockerfile
# admin/Dockerfile
FROM golang:1.21-alpine AS builder

WORKDIR /app
COPY go.mod go.sum ./
RUN go mod download

COPY . .
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o main .

FROM alpine:latest
RUN apk --no-cache add ca-certificates tzdata
WORKDIR /root/

# 设置时区
ENV TZ=Asia/Shanghai

COPY --from=builder /app/main .
COPY --from=builder /app/manifest ./manifest
COPY --from=builder /app/resource ./resource

EXPOSE 8080

CMD ["./main"]
```

### 前端 Dockerfile
```dockerfile
# admin-web/Dockerfile
FROM node:18-alpine AS builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
```

### Nginx 配置
```nginx
# admin-web/nginx.conf
user nginx;
worker_processes auto;

error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;

    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;

    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

    server {
        listen 80;
        server_name localhost;
        root /usr/share/nginx/html;
        index index.html;

        # 前端路由支持
        location / {
            try_files $uri $uri/ /index.html;
        }

        # API 代理
        location /api/ {
            proxy_pass http://backend:8080;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # 静态资源缓存
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
}
```

### Docker Compose
```yaml
# docker-compose.yml
version: '3.8'

services:
  mysql:
    image: mysql:8.0
    container_name: lookah-mysql
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD}
      MYSQL_DATABASE: ${DB_NAME}
      MYSQL_USER: ${DB_USER}
      MYSQL_PASSWORD: ${DB_PASS}
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./scripts/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - lookah-network

  redis:
    image: redis:7-alpine
    container_name: lookah-redis
    command: redis-server --requirepass ${REDIS_PASS}
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - lookah-network

  backend:
    build:
      context: ./admin
      dockerfile: Dockerfile
    container_name: lookah-backend
    environment:
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USER=${DB_USER}
      - DB_PASS=${DB_PASS}
      - DB_NAME=${DB_NAME}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASS=${REDIS_PASS}
    ports:
      - "8080:8080"
    depends_on:
      - mysql
      - redis
    networks:
      - lookah-network
    restart: unless-stopped

  frontend:
    build:
      context: ./admin-web
      dockerfile: Dockerfile
    container_name: lookah-frontend
    ports:
      - "80:80"
    depends_on:
      - backend
    networks:
      - lookah-network
    restart: unless-stopped

volumes:
  mysql_data:
  redis_data:

networks:
  lookah-network:
    driver: bridge
```

### 环境变量配置
```bash
# .env
# 数据库配置
DB_ROOT_PASSWORD=rootpassword123
DB_USER=lookah_user
DB_PASS=lookah_pass123
DB_NAME=lookah_erp

# Redis 配置
REDIS_PASS=redis_pass123

# 应用配置
APP_ENV=production
APP_DEBUG=false
```

## CI/CD 流水线

### GitHub Actions
```yaml
# .github/workflows/deploy.yml
name: Deploy

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Go
        uses: actions/setup-go@v3
        with:
          go-version: '1.21'
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
      
      - name: Test Backend
        run: |
          cd admin
          go test ./...
      
      - name: Test Frontend
        run: |
          cd admin-web
          npm ci
          npm run test

  build:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Log in to Container Registry
        uses: docker/login-action@v2
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
      
      - name: Build and push Backend image
        uses: docker/build-push-action@v3
        with:
          context: ./admin
          push: true
          tags: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-backend:latest
      
      - name: Build and push Frontend image
        uses: docker/build-push-action@v3
        with:
          context: ./admin-web
          push: true
          tags: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-frontend:latest

  deploy:
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Deploy to Production
        uses: appleboy/ssh-action@v0.1.5
        with:
          host: ${{ secrets.HOST }}
          username: ${{ secrets.USERNAME }}
          key: ${{ secrets.SSH_KEY }}
          script: |
            cd /opt/lookah-erp
            docker-compose pull
            docker-compose up -d
            docker system prune -f
```

## 监控和日志

### 应用监控
```go
// internal/middleware/metrics.go
package middleware

import (
    "time"
    "github.com/gogf/gf/v2/net/ghttp"
    "github.com/prometheus/client_golang/prometheus"
    "github.com/prometheus/client_golang/prometheus/promauto"
)

var (
    httpRequestsTotal = promauto.NewCounterVec(
        prometheus.CounterOpts{
            Name: "http_requests_total",
            Help: "Total number of HTTP requests",
        },
        []string{"method", "endpoint", "status"},
    )
    
    httpRequestDuration = promauto.NewHistogramVec(
        prometheus.HistogramOpts{
            Name: "http_request_duration_seconds",
            Help: "HTTP request duration in seconds",
        },
        []string{"method", "endpoint"},
    )
)

func Metrics(r *ghttp.Request) {
    start := time.Now()
    
    r.Middleware.Next()
    
    duration := time.Since(start).Seconds()
    status := r.Response.Status
    
    httpRequestsTotal.WithLabelValues(
        r.Method,
        r.URL.Path,
        fmt.Sprintf("%d", status),
    ).Inc()
    
    httpRequestDuration.WithLabelValues(
        r.Method,
        r.URL.Path,
    ).Observe(duration)
}
```

### 日志配置
```yaml
# config/config.yaml
logger:
  level: "info"
  stdout: false
  file: "logs/app.log"
  rotateSize: "100MB"
  rotateBackupLimit: 10
  rotateBackupExpire: "30d"
  format: "json"
  
  # 结构化日志字段
  fields:
    app: "lookah-erp"
    version: "1.0.0"
```

### 健康检查
```go
// internal/controller/health.go
package controller

import (
    "context"
    "github.com/gogf/gf/v2/frame/g"
    "github.com/gogf/gf/v2/net/ghttp"
)

type cHealth struct{}

var Health = cHealth{}

// Check 健康检查
func (c *cHealth) Check(ctx context.Context, r *ghttp.Request) {
    // 检查数据库连接
    _, err := g.DB().Ctx(ctx).Query("SELECT 1")
    if err != nil {
        r.Response.WriteJsonExit(g.Map{
            "status": "unhealthy",
            "error":  "database connection failed",
        })
        return
    }
    
    // 检查 Redis 连接
    _, err = g.Redis().Ctx(ctx).Do("PING")
    if err != nil {
        r.Response.WriteJsonExit(g.Map{
            "status": "unhealthy",
            "error":  "redis connection failed",
        })
        return
    }
    
    r.Response.WriteJsonExit(g.Map{
        "status":    "healthy",
        "timestamp": gtime.Now(),
        "version":   "1.0.0",
    })
}
```

## 备份和恢复

### 数据库备份脚本
```bash
#!/bin/bash
# scripts/backup.sh

# 配置
DB_HOST="localhost"
DB_PORT="3306"
DB_USER="root"
DB_PASS="password"
DB_NAME="lookah_erp"
BACKUP_DIR="/opt/backups"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p $BACKUP_DIR

# 数据库备份
mysqldump -h$DB_HOST -P$DB_PORT -u$DB_USER -p$DB_PASS \
  --single-transaction \
  --routines \
  --triggers \
  $DB_NAME > $BACKUP_DIR/db_backup_$DATE.sql

# 压缩备份文件
gzip $BACKUP_DIR/db_backup_$DATE.sql

# 删除7天前的备份
find $BACKUP_DIR -name "db_backup_*.sql.gz" -mtime +7 -delete

echo "Database backup completed: db_backup_$DATE.sql.gz"
```

### 自动备份定时任务
```bash
# 添加到 crontab
# 每天凌晨2点执行备份
0 2 * * * /opt/scripts/backup.sh >> /var/log/backup.log 2>&1
```

## 性能优化

### 数据库优化
```sql
-- 慢查询日志配置
SET GLOBAL slow_query_log = 'ON';
SET GLOBAL long_query_time = 2;
SET GLOBAL slow_query_log_file = '/var/log/mysql/slow.log';

-- 索引优化建议
-- 分析慢查询
SELECT * FROM mysql.slow_log ORDER BY start_time DESC LIMIT 10;

-- 查看索引使用情况
SHOW INDEX FROM table_name;
```

### 缓存策略
```go
// 应用级缓存
func (s *sUser) GetUserWithCache(ctx context.Context, id uint64) (*entity.User, error) {
    cacheKey := fmt.Sprintf("user:%d", id)
    
    // 尝试从缓存获取
    var user *entity.User
    err := g.Redis().Get(ctx, cacheKey, &user)
    if err == nil && user != nil {
        return user, nil
    }
    
    // 缓存未命中，从数据库获取
    user, err = s.getUserFromDB(ctx, id)
    if err != nil {
        return nil, err
    }
    
    // 写入缓存，过期时间1小时
    g.Redis().Set(ctx, cacheKey, user, time.Hour)
    
    return user, nil
}
```

## 安全配置

### SSL/TLS 配置
```nginx
server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    ssl_certificate /etc/ssl/certs/your-domain.crt;
    ssl_certificate_key /etc/ssl/private/your-domain.key;
    
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    
    # HSTS
    add_header Strict-Transport-Security "max-age=63072000" always;
    
    # 其他安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
}
```

### 防火墙配置
```bash
# UFW 防火墙配置
ufw default deny incoming
ufw default allow outgoing

# 允许 SSH
ufw allow ssh

# 允许 HTTP/HTTPS
ufw allow 80
ufw allow 443

# 允许数据库（仅内网）
ufw allow from 10.0.0.0/8 to any port 3306

# 启用防火墙
ufw enable
```

## 故障排查

### 常见问题排查
```bash
# 查看应用日志
docker logs lookah-backend
docker logs lookah-frontend

# 查看系统资源使用
top
htop
df -h
free -h

# 查看网络连接
netstat -tulpn
ss -tulpn

# 查看数据库状态
mysql -e "SHOW PROCESSLIST;"
mysql -e "SHOW ENGINE INNODB STATUS\G"

# 查看 Redis 状态
redis-cli info
redis-cli monitor
```

### 应急响应流程
1. **问题发现**: 监控告警或用户反馈
2. **问题确认**: 验证问题范围和影响
3. **紧急处理**: 快速恢复服务
4. **根因分析**: 分析问题根本原因
5. **永久修复**: 实施长期解决方案
6. **总结改进**: 更新运维文档和流程