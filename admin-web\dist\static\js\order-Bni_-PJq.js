import{c as L,b as m,e as T,z as oe,A as ne,q as f,w as o,h as a,i as l,j as x,f as E,s as le,F as re,y as p,t as v,v as w,B as O,Y as V,o as i,T as M,E as C}from"./.pnpm-hVqhwuVC.js";import{g as se,i as Y,j as ce}from"./index-DkYL1aws.js";import{a as I}from"./index-C6cm1h61.js";/* empty css              */import{u as ie}from"./table-ops-CrFIfhgA.js";const pe={key:0},de={key:1},ue={class:"ellipsis"},_e={class:"ellipsis"},be={class:"cl-table__expand-footer"},me={class:"table-summary-container"},he=p("span",{class:"cl-table__expand-footer-title"},"总数量：",-1),fe={class:"cl-table__expand-footer-value"},ve=L({name:"pms-sale-delivery-order"}),Be=L({...ve,setup(we){const{service:d}=I(),{router:u}=I(),{dict:P}=se(),j=P.get("color"),r=m(0),q=m([{label:"草稿",value:0,count:0},{label:"待发货",value:1,count:0},{label:"已发货",value:2,count:0},{label:"已完成",value:3,count:0}]),D=m([]),R=m({"slot-btn-confirm":{width:80,permission:d.pms.sale.delivery.order.permission.confirm,show:T(()=>r.value===0)},"slot-btn-edit":{width:80,permission:d.pms.sale.delivery.order.permission.update,show:T(()=>r.value===0)},"slot-btn-delete":{width:80,permission:d.pms.sale.delivery.order.permission.delete,show:T(()=>r.value===0)}}),{getOpWidth:K,checkOpButtonIsAvaliable:B,getOpIsHidden:A}=ie(R),S=m(),U=m(!1);oe(r,()=>{S.value=K(),U.value=A()},{immediate:!0});const N=Y.useTable({columns:[{label:"#",prop:"products",type:"expand",width:50},{label:"订单号",prop:"orderSn",width:200},{label:"派送地址",prop:"consignee",width:200},{label:"是否付款",prop:"isPaid",width:120,dict:[{label:"未付款",value:0,type:"danger"},{label:"已付款",value:1,type:"success"}]},{label:"运费",prop:"trackingCost",width:80},{label:"承运商",prop:"carrier",width:100},{label:"跟踪号码",prop:"pro",width:150},{label:"跟踪网址",prop:"trackingUrl",width:150},{label:"创建时间",prop:"createTime",width:180},{label:"发货日期",prop:"dispatchDate",width:160},{label:"预计到货时间",prop:"eta",width:120,component:{name:"cl-date-text",props:{format:"YYYY-MM-DD"}}},{label:"完成时间",prop:"completeTime",width:180},{type:"op",label:"操作",width:S,hidden:U,buttons:Object.keys(R.value)}]}),g=Y.useCrud({service:d.pms.sale.delivery.order,async onRefresh(n,{next:e,render:s}){const{count:_,list:y,pagination:h}=await e(n);q.value.forEach(k=>{k.count=_[k.value]||0}),s(y,h)}},n=>{n.refresh({status:r})});function F(n){var e;r.value=n,(e=g.value)==null||e.refresh()}function H(){u.push("/pms/sale/delivery/order/add")}function W(n,e){var s;(e==null?void 0:e.type)==="expand"||(e==null?void 0:e.type)==="op"||(s=N.value)==null||s.toggleRowExpansion(n)}function z(n){if(!n)return!1;u.push(`/pms/sale/delivery/order/add?id=${n}`)}function G(n){if(!n)return!1;M.confirm("确认删除订单吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{d.pms.sale.delivery.order.delete({ids:[n]}).then(()=>{var e;C.success("订单删除成功"),(e=g.value)==null||e.refresh()}).catch(e=>{C.error(e.message)})}).catch(()=>{})}function J(n){if(!n)return!1;M.confirm("确认提交订单吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{d.pms.sale.delivery.order.confirm({orderId:n}).then(e=>{C.success("订单提交成功"),r.value=(e==null?void 0:e.status)||0}).catch(e=>{C.error(e.message)})}).catch(()=>{})}return ne(()=>{var s;const n=u.currentRoute.value.query.tab;n&&(r.value=Number.parseInt(n.toString()),(s=g.value)==null||s.refresh(),u.replace({query:{tab:void 0}}));const e=u.currentRoute.value.query.expand;e&&(D.value=[Number.parseInt(e.toString())],u.replace({query:{expand:void 0}}))}),(n,e)=>{const s=l("cl-refresh-btn"),_=l("el-button"),y=l("cl-flex1"),h=l("el-row"),k=l("el-tab-pane"),$=l("el-popover"),c=l("el-table-column"),Q=l("el-table"),X=l("cl-table"),Z=l("el-tabs"),ee=l("cl-pagination"),te=l("cl-crud");return i(),f(te,{ref_key:"Crud",ref:g},{default:o(()=>[a(h,null,{default:o(()=>[a(s),a(_,{type:"primary",onClick:H},{default:o(()=>[x(" 创建派送单 ")]),_:1}),a(y)]),_:1}),a(Z,{modelValue:r.value,"onUpdate:modelValue":e[0]||(e[0]=t=>r.value=t),type:"border-card",onTabChange:F},{default:o(()=>[(i(!0),E(re,null,le(q.value,t=>(i(),f(k,{key:t.value,label:`${t.label}(${t.count})`,name:t.value},null,8,["label","name"]))),128)),a(h,null,{default:o(()=>[a(X,{ref_key:"Table",ref:N,"row-key":"id","expand-row-keys":D.value,class:"table-row-pointer",onRowClick:W},{"slot-btn-edit":o(({scope:t})=>[w(B)("slot-btn-edit")?(i(),f(_,{key:0,text:"",bg:"",type:"primary",onClick:V(b=>z(t.row.id),["stop"])},{default:o(()=>[x(" 编辑 ")]),_:2},1032,["onClick"])):O("",!0)]),"slot-btn-delete":o(({scope:t})=>[w(B)("slot-btn-delete")?(i(),f(_,{key:0,text:"",bg:"",type:"danger",onClick:V(b=>G(t.row.id),["stop"])},{default:o(()=>[x(" 删除 ")]),_:2},1032,["onClick"])):O("",!0)]),"slot-btn-confirm":o(({scope:t})=>[w(B)("slot-btn-confirm")?(i(),f(_,{key:0,text:"",bg:"",type:"success",onClick:V(b=>J(t.row.id),["stop"])},{default:o(()=>[x(" 提交 ")]),_:2},1032,["onClick"])):O("",!0)]),"column-trackingCost":o(({scope:t})=>[t.row.trackingCost>0?(i(),E("span",pe,v(t.row.trackingCost),1)):(i(),E("span",de))]),"column-trackingUrl":o(({scope:t})=>[a($,{placement:"top-start",width:300,trigger:"hover",content:t.row.trackingUrl},{reference:o(()=>[p("span",ue,v(t.row.trackingUrl),1)]),_:2},1032,["content"])]),"column-consignee":o(({scope:t})=>[a($,{placement:"top-start",width:300,trigger:"hover",content:t.row.consignee},{reference:o(()=>[p("span",_e,v(t.row.consignee),1)]),_:2},1032,["content"])]),"column-products":o(({scope:t})=>[a(Q,{data:t.row.products,style:{width:"100%"},border:""},{default:o(()=>[a(c,{label:"产品名称",align:"center"},{default:o(()=>[a(c,{prop:"name",label:"中文名",align:"center"}),a(c,{prop:"nameEn",label:"英文名",align:"center"})]),_:1}),a(c,{prop:"cartonPo",label:"外箱PO#",width:"150",align:"center"}),a(c,{prop:"airway",label:"空运提单号",width:"150",align:"center"}),a(c,{prop:"carton",label:"箱号",width:"150",align:"center"}),a(c,{prop:"sku",label:"SKU",width:"150",align:"center"}),a(c,{prop:"upc",label:"UPC",width:"150",align:"center"}),a(c,{prop:"color",label:"颜色",width:"120",align:"center"},{default:o(b=>[p("span",null,v(w(ce)(w(j),parseInt(b.row.color))),1)]),_:2},1024),a(c,{prop:"quantity",label:"订单数量",width:"100",align:"center"})]),_:2},1032,["data"]),p("div",be,[p("div",me,[p("div",null,[he,p("span",fe,v(t.row.products.reduce((b,ae)=>b+ae.quantity,0)),1)])])])]),_:1},8,["expand-row-keys"])]),_:1})]),_:1},8,["modelValue"]),a(h,null,{default:o(()=>[a(y),a(ee)]),_:1})]),_:1},512)}}});export{Be as default};
