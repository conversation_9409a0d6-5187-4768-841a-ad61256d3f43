package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/imhuso/lookah-erp/admin/yc"
)

type MaterialStockType int

// 0: 可用库存 1: 在途库存 2: 冻结库存 3: 占用库存 4: 可用在途 5: 已使用在途
const (
	MaterialStockTypeAvailable MaterialStockType = iota
	MaterialStockTypeExpected
	MaterialStockTypeLocked
	MaterialStockTypeOccupied
	MaterialStockTypeDeductibleExpected
	MaterialStockTypeUsedExpected
)

// 1采购下单，2入库-库存调整，3入库-生产退料，4自定义入库，5采购单入库，6领料出库，7退货出库，8自定义出库，9报废 10补退货
const (
	OperationTypePurchaseOrder = iota + 1
	OperationTypeInboundAdjust
	OperationTypeInboundReturn
	OperationTypeInboundCustom
	OperationTypeInboundPurchase
	OperationTypeOutboundPick
	OperationTypeOutboundReturn
	OperationTypeOutboundCustom
	OperationTypeScrap
	OperationTypeReturnPolicy
)

const TableNamePmsMaterialStockLog = "pms_material_stock_log"

// PmsMaterialStockLog mapped from table <pms_material_stock_log>
type PmsMaterialStockLog struct {
	ID         int64       `json:"id"       gorm:"column:id;type:bigint(20);not null;primary_key;auto_increment;comment:ID;"`                       // ID
	ContractId int64       `json:"contractId" gorm:"column:contract_id;type:bigint(20);not null;default:0;index:idx_contract_id;comment:合同ID;"`     // 合同ID
	MaterialId int64       `json:"materialId" gorm:"column:material_id;type:bigint(20);not null;default:0;index:idx_material_id;comment:物料ID;"`     // 物料ID
	Before     float64     `json:"before"    gorm:"column:before;type:decimal(14,4);not null;default:0.0000;comment:变更前数量;"`                        // 变更前数量
	After      float64     `json:"after"     gorm:"column:after;type:decimal(14,4);not null;default:0.0000;comment:变更后数量;"`                         // 变更后数量
	Type       int         `json:"type"      gorm:"column:type;type:tinyint(1);not null;default:0;comment:变更类型 0: 可用库存 1: 在途库存 2: 冻结库存 ,3在途，4库存，;"` // 变更类型
	Quantity   float64     `json:"quantity"  gorm:"column:quantity;type:decimal(14,4);not null;default:0.0000;comment:变更数量;"`                       // 变更数量
	Remark     string      `json:"remark"   gorm:"column:remark;type:varchar(500);not null;default:'';comment:备注;"`                                 // 备注
	CreateTime *gtime.Time `json:"createTime" gorm:"column:createTime;not null;index,priority:1;autoCreateTime;comment:创建时间"`                       // 创建时间
	// 创建人id
	CreatorId int64 `json:"creatorId"              gorm:"column:creator_id;type:bigint(20);not null;comment:创建人ID;"` // 创建人ID
	// 生产单 pms_production_schedule （生产表）
	ProductionScheduleId int64 `json:"productionScheduleId" gorm:"column:production_schedule_id;type:bigint(20);not null;default:0;comment:生产单;"` // 生产单
	// 生产单SN
	ProductionScheduleSn string `json:"productionScheduleSn" gorm:"column:production_schedule_sn;type:varchar(100);not null;default:'';comment:生产单SN;"` // 生产单SN
	// 采购单
	PurchaseOrderId int64 `json:"purchaseOrderId" gorm:"column:purchase_order_id;type:bigint(20);not null;default:0;comment:采购单;"` // 采购单
	// 采购单SN
	PurchaseOrderSn string `json:"purchaseOrderSn" gorm:"column:purchase_order_sn;type:varchar(100);not null;default:'';comment:采购单SN;"` // 采购单SN
	// 入库单
	InboundId int64 `json:"warehouseInboundId" gorm:"column:warehouse_inbound_id;type:bigint(20);not null;default:0;comment:入库单;"` // 入库单
	// 入库单SN
	InboundSn string `json:"warehouseInboundSn" gorm:"column:warehouse_inbound_sn;type:varchar(100);not null;default:'';comment:入库单SN;"` // 入库单SN
	// 出库单
	OutboundId int64 `json:"warehouseOutboundId" gorm:"column:warehouse_outbound_id;type:bigint(20);not null;default:0;comment:出库单;"` // 出库单
	// 出库单SN
	OutboundSn string `json:"warehouseOutboundSn" gorm:"column:warehouse_outbound_sn;type:varchar(100);not null;default:'';comment:出库单SN;"` // 出库单SN
	// 工单id
	WorkOrderId int64 `json:"workOrderId" gorm:"column:work_order_id;type:bigint(20);not null;default:0;comment:工单id;"` // 工单id
	// 工单号
	WorkOrderNo string `json:"workOrderNo" gorm:"column:work_order_no;type:varchar(100);not null;default:'';comment:工单号;"` // 工单号
	// 操作类型 1采购下单，2入库-库存调整，3入库-生成退料，4自定义入库，5采购单入库，6领料出库，7退货出库，8自定义出库，9报废
	OperationType int `json:"operationType" gorm:"column:operation_type;type:tinyint(1);not null;default:0;comment:操作类型 1采购下单，2入库-库存调整，3入库-生成退料，4自定义入库，5采购单入库，6领料出库，7退货出库，8自定义出库，9报废"` // 操作类型
	// 其他备注
	OtherRemark string `json:"otherRemark" gorm:"column:other_remark;type:varchar(1000);not null;default:'';comment:其他备注;"` // 其他备注
}

type PmsMaterialStockLogVo struct {
	*PmsMaterial
	*PmsMaterialStockLog
	CreateTime *gtime.Time `json:"createTime"`
}

// GroupName 返回分组名
func (m *PmsMaterialStockLog) GroupName() string {
	return ""
}

// TableName PmsMaterialStockLog's table name
func (*PmsMaterialStockLog) TableName() string {
	return TableNamePmsMaterialStockLog
}

// NewPmsMaterialStockLog 创建实例
func NewPmsMaterialStockLog() *PmsMaterialStockLog {
	return &PmsMaterialStockLog{}
}

func init() {
	_ = yc.CreateTable(NewPmsMaterialStockLog())
}
