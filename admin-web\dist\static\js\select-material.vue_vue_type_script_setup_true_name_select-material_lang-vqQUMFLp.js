import{s as v}from"./index-BtOcqcNl.js";import{c as i,k as c,l as h,n as V,e as _,b,E as k,q as x,i as M,m as w,v as d,x as y,o as C}from"./.pnpm-hVqhwuVC.js";const E=i({name:"select-material"}),q=i({...E,props:c({width:{default:"150px"},options:{default:()=>[]}},{modelValue:{},modelModifiers:{}}),emits:c(["update:modelValue","change"],["update:modelValue"]),setup(r,{emit:m}){const t=r,l=m,p=h(),s=V(r,"modelValue"),n=_({get:()=>{if(![0,"0"].includes(s.value))return s.value},set:e=>{s.value=e,l("update:modelValue",e)}}),a=b([]);t.options.length===0?v.pms.productionData.incoming.materialList({keyWord:""}).then(e=>{e.forEach(o=>{o.label=`${o.name}，(料号：${o.code})`,o._label=o.name,o.value=o.code}),a.value=e}).catch(e=>{k.error(e.message||"获取用户列表失败")}):a.value=t.options;function f(e){if(e===void 0){l("change",e);return}const o=a.value.find(u=>u.code===e);l("change",o)}return(e,o)=>{const u=M("el-select-v2");return C(),x(u,w({modelValue:d(n),"onUpdate:modelValue":o[0]||(o[0]=g=>y(n)?n.value=g:null),filterable:""},d(p),{clearable:"",style:`width:${t.width}`,options:a.value,onChange:f}),null,16,["modelValue","style","options"])}}});export{q as _};
