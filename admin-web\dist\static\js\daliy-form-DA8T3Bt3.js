import{c as N,b as f,e as R,S as G,a1 as Q,f as U,h as a,w as s,q as P,B as X,J as Z,i as d,y as j,v as o,t as ee,F as te,s as le,L as B,Y as oe,a2 as ae,E as ie,o as _}from"./.pnpm-hVqhwuVC.js";import{_ as ne}from"./select-user.vue_vue_type_script_setup_true_name_select-user_lang-Dw22Jqfu.js";import{s as H,l as re}from"./index-BtOcqcNl.js";import{U as se}from"./user-avatar-DTjmVWm6.js";import{_ as de}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index.vue_vue_type_script_setup_true_name_cl-avatar_lang-Dwbve7L9.js";import"./text-overflow-tooltip.vue_vue_type_script_setup_true_name_text-overflow-tooltip_lang-D_kIBTxD.js";const ue={"ml-5px":"","w-4xl":"200px"},me={"p-l-10px":""},pe=N({name:"undefined"}),fe=N({...pe,props:{showBtn:{type:Boolean,default:!0},form:{default:()=>({})},openType:{default:""}},setup($,{expose:C}){const E=$,k=f(0),l=R({get:()=>E.form,set:()=>{}}),c=f(300),h=f(),g=f([]),w=f();function F(t){if(!t.workTimeRange){t.estimatedWorkNum=0;return}if(!t.workTimeRange||t.workTimeRange.length===0){t.estimatedWorkNum=0,t.workTimeRange=[];return}const[e,r]=t.workTimeRange;let n=(r-e)/1e3/60/60;e.getHours()<=12&&r.getHours()>=13&&(n-=1.5),t.estimatedWorkNum=n.toFixed(1)}function L(){return[0,1,2,3,4,5,6,7]}function y(t,e){const r=[];for(let n=t;n<=e;n++)r.push(n);return r}function W(t){if(t===12)return y(1,59);if(t===8||t===13)return y(0,29)}function A(t){const e=ae(l.value);if(t===void 0){e.jobDescription="",e.workitemId=0,e.rootId=0;return}const r=g.value.find(n=>n.value===t);r&&(e.jobDescription=r.label,r.rootId===0?e.rootId=r.id:e.rootId=r.rootId)}async function S(){const t=await H.pims.workitem.listByUser();if(l.value.workitemId&&(!t||t.length===0)){const e=await H.pims.workitem.listByIds({ids:[l.value.workitemId]});e&&(t.list=e)}g.value=t.map(e=>(e.value=e.id,e.label=e.title,e.rootId=e.rootId||e.id,e))}S();function q(t){w.value=t}function I(t){if(w.value){const e=w.value.getSelectionPosition();if(e!=null&&e.bottom&&(e!=null&&e.bottom)){const r=Number(e.bottom.substring(0,e.bottom.length-2));t==="enter"?r<70&&(c.value+=36):c.value>300&&(c.value-=36)}}}async function M(){const t=await h.value.validate();return t||(ie.error("请填写日报"),t)}async function V(t){if(t.target.id!=="DaliyReportPasteInput")return;const e=[],r=m=>{e.push(m)},n=(t.clipboardData||t.originalEvent.clipboardData).items;for(const m in n){const u=n[m];let b="";u.type&&u.type.includes("/")&&(b=u.type.split("/")[0]),u.kind==="file"&&await re({file:u.getAsFile()},{type:b},r)}if(e.length>0)if(Array.isArray(l.value.filePath)){const m=e.map(u=>u.url);l.value.filePath=l.value.filePath.concat(m)}else{const m=e.map(u=>u.url).join(",");l.value.filePath=`${l.value.filePath},${m}`}}const p=R(()=>l.value.isTemp===1);return G(()=>{window.addEventListener("paste",V)}),Q(()=>{window.removeEventListener("paste",V)}),window.onbeforeunload=function(t){const e=new Date().getTime();k.value&&e-k.value>15*60*1e3?t.returnValue="true":t.returnValue=""},C({validate:M}),(t,e)=>{const r=d("el-time-picker"),n=d("el-form-item"),m=d("el-col"),u=d("el-option"),b=d("el-select"),K=d("el-row"),T=d("el-input"),J=d("cl-editor-wang"),O=d("cl-upload"),v=d("el-table-column"),Y=d("el-table"),z=d("el-form");return _(),U("div",null,[a(z,{ref_key:"registeredHoursFormRef",ref:h,model:o(l),ml2:"",mr2:"",mt4:"","label-width":"80"},{default:s(()=>{var D,x;return[a(K,{gutter:10},{default:s(()=>[a(m,{span:12},{default:s(()=>[a(n,{label:"工时",prop:"workTimeRange",class:"work_time_range",rules:[{required:!0,message:"请选择工时",trigger:"submit"}]},{default:s(()=>[a(r,{modelValue:o(l).workTimeRange,"onUpdate:modelValue":e[0]||(e[0]=i=>o(l).workTimeRange=i),"default-value":[void 0,void 0],disabled:o(p),"is-range":"",w:"100px",format:"HH:mm","range-separator":"~","disabled-hours":L,"disabled-minutes":W,"start-placeholder":"开始时间","end-placeholder":"结束时间",onChange:e[1]||(e[1]=i=>F(o(l)))},null,8,["modelValue","disabled"]),j("span",ue,ee(o(l).estimatedWorkNum>0?`${o(l).estimatedWorkNum}小时`:"小时"),1)]),_:1})]),_:1}),a(m,{span:12},{default:s(()=>[a(n,{prop:"workitemId",label:"任务"},{default:s(()=>[a(b,{modelValue:o(l).workitemId,"onUpdate:modelValue":e[2]||(e[2]=i=>o(l).workitemId=i),filterable:"",clearable:"",disabled:o(p),placeholder:"请输入任务名称","w-full":"",onChange:A},{default:s(()=>[(_(!0),U(te,null,le(g.value,i=>(_(),P(u,{key:i.value,label:i.label,disabled:i.disabled,value:i.value},null,8,["label","disabled","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),a(n,{label:"工作事项",prop:"jobDescription",rules:[{required:!0,message:"请输入工作事项",trigger:"submit"}]},{default:s(()=>[a(T,{modelValue:o(l).jobDescription,"onUpdate:modelValue":e[3]||(e[3]=i=>o(l).jobDescription=i),disabled:o(p),placeholder:"请输入工作事项","show-word-limit":"",maxlength:"100"},null,8,["modelValue","disabled"])]),_:1}),a(n,{label:"工作详情",prop:"jobDetail",rules:[{required:!0,message:"请输入工作详情",trigger:"submit"}]},{default:s(()=>[a(J,{modelValue:o(l).jobDetail,"onUpdate:modelValue":e[4]||(e[4]=i=>o(l).jobDetail=i),disabled:o(p),height:`${c.value}px`,onOnCreated:q,onKeydown:[e[5]||(e[5]=B(i=>I("delete"),["delete"])),e[6]||(e[6]=B(i=>I("enter"),["enter"]))]},null,8,["modelValue","disabled","height"])]),_:1}),a(n,{prop:"filePath",label:"上传文件"},{default:s(()=>[a(O,{modelValue:o(l).filePath,"onUpdate:modelValue":e[7]||(e[7]=i=>o(l).filePath=i),type:"file",multiple:!0,disabled:o(p),limit:3,"is-private":""},{uploadSlot:s(()=>[j("div",me,[a(T,{id:"DaliyReportPasteInput",disabled:o(p),placeholder:"点击后按粘贴上传",onClick:oe(()=>{},["stop"])},null,8,["disabled"])])]),_:1},8,["modelValue","disabled"])]),_:1}),a(n,{label:"抄送",prop:"ccUserIds"},{default:s(()=>[a(ne,{modelValue:o(l).ccUserIds,"onUpdate:modelValue":e[8]||(e[8]=i=>o(l).ccUserIds=i),disabled:o(p),"current-user":!1,width:"100%",multiple:""},null,8,["modelValue","disabled"])]),_:1}),((x=(D=o(l))==null?void 0:D.autidLogs)==null?void 0:x.length)>0?(_(),P(n,{key:0,label:""},{default:s(()=>[a(Y,{data:o(l).autidLogs,style:{width:"100%"},border:""},{default:s(()=>[a(v,{prop:"opinion",label:"阅读意见",align:"left","show-overflow-tooltip":""}),a(v,{prop:"creator",label:"处理人",align:"center",width:"80"},{default:s(({row:i})=>[a(se,{user:i.creator,"show-name":!1,flex:"~ justify-center items-center"},null,8,["user"])]),_:1}),a(v,{prop:"updateTime",label:"审批时间",align:"center",width:"180"})]),_:1},8,["data"])]),_:1})):X("",!0),Z(t.$slots,"default",{},void 0,!0)]}),_:3},8,["model"])])}}}),he=de(fe,[["__scopeId","data-v-0e8d1bfe"]]);export{he as default};
