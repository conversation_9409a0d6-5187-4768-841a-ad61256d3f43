import{e as We,g as dt,i as de,s as x,d as Ne}from"./index-BtOcqcNl.js";import{c as Y,b as p,f as E,F as Fe,h as l,y as r,i as v,w as a,j as i,M as mt,N as pt,E as c,o as h,af as Ae,ag as je,A as He,q as I,ae as ft,e as vt,S as _t,G as Q,H as Oe,B as F,v as B,t as X,W as De,s as ht,T as Re}from"./.pnpm-hVqhwuVC.js";import{a as bt}from"./index-D95m1iJL.js";import{n as Pe}from"./index-CBanFtSc.js";import{_ as Ke}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{u as gt,_ as yt}from"./bom-DOuAfwoo.js";import{_ as wt}from"./material-bundle.vue_vue_type_style_index_0_lang-DWkV_0UE.js";import{_ as xt}from"./material-selector.vue_vue_type_script_name_product-selector_setup_true_lang-DvMmZ9VE.js";import{u as Bt}from"./material-CnT3-AWx.js";import"./table-ops-mcGHjph4.js";import"./material-table-columns.vue_vue_type_script_setup_true_lang-BFX8eStE.js";const se=S=>(Ae("data-v-49493b6f"),S=S(),je(),S),kt={class:"title"},Ct=se(()=>r("div",{class:"title"}," 2. 在表格中按照模板格式填写数据 ",-1)),Mt={class:"title"},It={class:"title mb20px!"},Vt=se(()=>r("br",null,null,-1)),St=se(()=>r("br",null,null,-1)),Ut=se(()=>r("br",null,null,-1)),$t=se(()=>r("br",null,null,-1)),Lt={class:"import-error-dialog"},Tt=Y({name:"undefined"}),Et=Y({...Tt,emits:["success"],setup(S,{emit:ee}){const q=ee,{service:g}=bt(),d=p(null),n=p(!1),U=p([]),V=p(!1),A=p(!1),j=p(!0);function ne(){A.value=!0}function me(){const k=d.value;k&&k.click()}function pe(k){const m=k.target,C=m.files;if(C&&C.length>0){const N=C[0],O=new FileReader;O.onload=K=>{var le;const D=new Uint8Array((le=K.target)==null?void 0:le.result),$=mt(D,{type:"array"}),M=$.Sheets[$.SheetNames[0]],te=pt.sheet_to_json(M,{header:1}),R=te[0];if(!R||!R[0]){c.error({message:"SKU不能为空"}),z(m);return}const re=R[1].toString().trim().replace(/[\r\n]/g,"");n.value=!0,g.pms.product.list({sku:re,unit:0}).then(o=>{o||c.error({message:"导入的SKU不存在"})}).catch(o=>{c.error({message:o.message||"SKU不存在"})}).finally(()=>{n.value=!1,z(m)});const P=te.slice(2).filter(o=>o.length>0),ie=P.filter(o=>{try{const T=o[8],ce=Number(T);return Number.isNaN(ce)?!0:o.length!==10||!o[1]||!o[2]||Pe(ce)<=0||!o[9]}catch{return!0}}).map(o=>({row:P.indexOf(o)+2,message:"数据格式不正确"}));if(ie.length>0){U.value=ie,V.value=!0;return}const G=P.map(o=>{const T=Pe(Number(o[8]));return{code:o[1],name:o[2],model:o[3],size:o[4],material:o[5],process:o[6],coverColor:o[7],quantity:T,unit:o[9]}}).filter(o=>o.code&&o.name&&o.quantity&&o.unit),fe={sku:re,materials:G,isOverrideMaterial:j.value},ue=G.filter(o=>G.filter(T=>T.code===o.code).length>1).map(o=>({row:G.indexOf(o)+4,message:"物料编码重复"}));if(ue.length>0){U.value=ue,V.value=!0,z(m);return}n.value=!0,g.pms.bom.importBom(fe).then(o=>{o&&(o==null?void 0:o.bomId)>0?(c.success({message:"导入成功"}),A.value=!1,q("success",o)):c.error({message:"导入失败"})}).catch(o=>{c.error({message:o.message||"导入失败"})}).finally(()=>{n.value=!1,z(m)})},O.readAsArrayBuffer(N)}}function z(k){k&&(k.value="")}function H(){const k="BOM导入模板.xlsx";fetch("/bom_template.xlsx").then(C=>C.blob()).then(C=>{We(C,k)}).catch(()=>{c.error({message:"下载模板文件失败"})})}return(k,m)=>{const C=v("el-button"),N=v("el-switch"),O=v("el-text"),K=v("cl-dialog"),D=v("el-table-column"),$=v("el-table");return h(),E(Fe,null,[l(C,{type:"success",onClick:ne},{default:a(()=>[i(" 导入 ")]),_:1}),r("input",{ref_key:"fileInputRef",ref:d,type:"file",style:{display:"none"},accept:".xlsx, .xls",onChange:pe},null,544),l(K,{modelValue:A.value,"onUpdate:modelValue":m[1]||(m[1]=M=>A.value=M),title:"导入BOM","close-on-click-modal":!1,controls:["close"],width:"800",class:"import-step-dialog"},{default:a(()=>[r("div",kt,[i(" 1. "),l(C,{type:"primary",link:"",onClick:H},{default:a(()=>[i(" 下载Excel模板 ")]),_:1})]),Ct,r("div",Mt,[i(" 3. 是否同时覆盖物料信息 "),l(N,{modelValue:j.value,"onUpdate:modelValue":m[0]||(m[0]=M=>j.value=M),class:"ml-10px","active-text":"是","inactive-text":"否","active-value":!0,"inactive-value":!1},null,8,["modelValue"])]),r("div",It,[i(" 4. "),l(C,{loading:n.value,link:"",type:"success",onClick:me},{default:a(()=>[i(" 点击选择文件导入数据 ")]),_:1},8,["loading"])]),l(O,{type:"danger"},{default:a(()=>[i(" 注意事项： "),Vt,i(" 1. 如果导入的产品已经存在BOM，将会清空旧的BOM数据 "),St,i(" 2. 如果系统中不存在导入的物料，将会自动创建物料 "),Ut,i(" 3. 如果选择覆盖物料信息，将会更新系统中已经存在导入的物料的名称、型号、尺寸/封装、材质、工艺、颜色、单位 "),$t,i(" 4. 如果选择不覆盖物料信息，不会更新物料信息 ")]),_:1})]),_:1},8,["modelValue"]),l(K,{modelValue:V.value,"onUpdate:modelValue":m[2]||(m[2]=M=>V.value=M),controls:["close"],title:"导入错误信息",width:"400"},{default:a(()=>[r("div",Lt,[l($,{data:U.value,style:{width:"100%"},border:""},{default:a(()=>[l(D,{prop:"row",label:"行号",width:"100"}),l(D,{prop:"message",label:"错误信息"})]),_:1},8,["data"])])]),_:1},8,["modelValue"])],64)}}}),qt=Ke(Et,[["__scopeId","data-v-49493b6f"]]),zt=Y({name:"undefined"}),Nt=Y({...zt,props:{modelValue:Array},emits:["update:modelValue"],setup(S,{emit:ee}){const q=S,g=ee,d=p(q.modelValue);return He(()=>{g("update:modelValue",d.value)}),(n,U)=>(h(),I(wt,{modelValue:d.value,"onUpdate:modelValue":U[0]||(U[0]=V=>d.value=V),selector:!0},null,8,["modelValue"]))}}),Z=S=>(Ae("data-v-f6effde5"),S=S(),je(),S),Ot={class:"bom"},Dt={class:"bom-product"},Rt={flex:"~ row nowrap justify-between","overflow-x-scroll":""},Pt={class:"bom-material"},Wt={key:0,class:"bom-material-container"},Ft={class:"nowrap w-full flex overflow-x-scroll py5px",scrollbar:"~ rounded"},At={class:"bom-material-title-btn",flex:"~ nowrap items-center","w-full":""},jt={flex:"~ col items-center"},Ht=Z(()=>r("span",{class:"i-clarity:copy-solid mr-2 text-size-lg"},null,-1)),Kt=Z(()=>r("span",{class:"i-clarity:paste-solid mr-2 text-size-lg"},null,-1)),Gt=Z(()=>r("span",{class:"i-clarity:trash-solid mr-2 text-size-lg"},null,-1)),Jt={class:"cleanBomMaterialList",flex:"~ nowrap justify-end items-center","w-full":""},Qt={flex:"~ justify-between items-center"},Xt=Z(()=>r("div",{class:"i-material-symbols:help mr-1"},null,-1)),Yt=Z(()=>r("div",null,[r("p",null,"未审核：未提交过审核，或提交后还未处理，不可下单"),r("p",null,"审核中：正在审核中，不可下单"),r("p",null,"已审核：已审核通过，可以正常下单")],-1)),Zt={key:0,class:"bom-material-list"},el={key:1},tl={flex:"~ justify-between items-center h30px"},ll={class:"row flex items-center"},al=Z(()=>r("div",{class:"i-material-symbols:package-2-outline-sharp mr10px"},null,-1)),ol={key:1},sl={key:1},nl={class:"dialog-footer"},rl={class:"dialog-footer"},il={flex:"~ items-center"},ul=Y({name:"undefined"}),cl=Y({...ul,setup(S){var Ce,Me;const{dict:ee}=dt(),q=ee.get("color");(Ce=q.value)!=null&&Ce.find(e=>e.value===0)||(Me=q.value)==null||Me.unshift({label:"无",value:0});const g=p(),d=p(),n=p([]),U=p({}),V=p(!0),{getAllUsedColors:A,getBomMaterialRowStyle:j,getBomMaterialCellStyle:ne}=gt(U),me=p([]),pe=p([]),{height:z}=ft(),H=p(!1),k=p(!1),m=p(!1),C=de.useTable({columns:[{label:"SKU",prop:"sku",width:120},{label:"名称",prop:"name",showOverflowTooltip:!0},{label:"颜色",prop:"color",width:150,showOverflowTooltip:!0,formatter:e=>{var t,u;return((u=(t=q.value)==null?void 0:t.find(f=>f.value===Number.parseInt(e.color)))==null?void 0:u.label)||"-"}}],contextMenu:["refresh"]}),N=de.useCrud({service:x.pms.product,async onRefresh(e,{next:t,render:u}){e.keyWord&&(e.keyWord=e.keyWord.trim());const{list:f,pagination:b}=await t(e);u(f,b)}},e=>{e.refresh({unit:0})}),O=de.useSearch({items:[{prop:"keyWord",props:{labelWidth:"0px"},component:{name:"el-input",props:{placeholder:"请输入SKU/UPC/名称",clearable:!1,onChange:e=>{var t;(t=N.value)==null||t.refresh({keyWord:e.trim(),page:1})}}}}]});async function K(e){g.value=e,n.value=[],U.value=[],await D(g.value.id),m.value=!1}function D(e){k.value=!0,x.pms.bom.GetBomMaterialByProductId({productId:e}).then(t=>{d.value=t,n.value=(t==null?void 0:t.materials.filter(u=>(u==null?void 0:u.isBundle)===!1))||[],U.value[t.id]=t.changeLog||[]}).catch(()=>{}).finally(()=>{k.value=!1})}const $=p(!1),M=p(!1),te=p(null),R=p([]),re=p(null),P=de.useForm(),{materialUpsertItems:ie,getMaterialUpsertItemsWithMaterial:G}=Bt();function fe(){var e;(e=P.value)==null||e.open({title:"添加物料",dialog:{controls:["close"],closeOnPressEscape:!1},width:"600px",op:{saveButtonText:"添加"},items:ie,on:{submit(t,{close:u,done:f}){x.pms.material.add(t).then(async b=>{const{id:y}=b;if(!y){c.error("添加失败");return}c.success("添加成功"),u();const ae=await x.pms.material.info({id:y});le(ae)}).catch(b=>{c.error(b.message||"添加失败")}).finally(()=>{f()})}}})}async function ue(e){var f;const t=await x.pms.material.info({id:e});if(!t){c.error("物料不存在");return}const u=G(t);(f=P.value)==null||f.open({title:"编辑物料",dialog:{controls:["close"],closeOnPressEscape:!1},width:"600px",op:{saveButtonText:"保存"},items:u,on:{submit(b,{close:y,done:ae}){b.id=e,x.pms.material.update(b).then(()=>{c.success("更新成功"),n.value=n.value.map(W=>W.materialId===e?{...W,...b}:W),y()}).catch(W=>{c.error(W.message||"更新失败")}).finally(()=>{ae()})}}})}function le(e){n.value.unshift({bomId:0,materialId:e.id,code:e.code,name:e.name,model:e.model,size:e.size,material:e.material,process:e.process,coverColor:e.coverColor,quantity:1,unit:e.unit})}function o(){var e;if(!g.value||g.value.id<=0)return c.warning("请先选择产品");$.value=!0,(e=te.value)==null||e.resetData()}function T(){const e=R.value;if(e.length===0){c.warning("请选择物料");return}e.map(t=>le(t)),n.value=n.value.reduce((t,u)=>(t.findIndex(b=>b.materialId===u.materialId)===-1&&t.push(u),t),[]),m.value=!0,c.success("已添加到物料清单，保存后才会生效！！！"),$.value=!1}function ce(){x.pms.material.list().then(e=>{H.value=!1,me.value=e==null?void 0:e.map(t=>({...t})),pe.value=e==null?void 0:e.map(t=>({label:`${t.name} - ${t.model}`,value:t.id}))}).catch(e=>{c.error(e.message||"获取物料列表失败")})}function Ge(){m.value=!0,c.success("列表清空成功，保存后才会生效！！！"),n.value=[],d.value.materialBundles=[]}function we(e,t){if(e===0&&t===0){Re.confirm('确定要清空物料清单吗？ <br /> <b style="color: red">清空后需要点击保存清单才会生效！！！</b>',"提示",{confirmButtonText:"确定",cancelButtonText:"取消",dangerouslyUseHTMLString:!0,type:"warning"}).then(()=>{Ge()}).catch(()=>{});return}Re.confirm('确定要删除该物料吗？ <br /> <b style="color: red">删除后需要点击保存清单才会生效！！！</b>',"提示",{confirmButtonText:"确定",cancelButtonText:"取消",dangerouslyUseHTMLString:!0,type:"warning"}).then(()=>{m.value=!0,n.value=n.value.filter(f=>f.bomId!==e||f.materialId!==t),c.success("删除成功，保存后才会生效！！！")}).catch(()=>{})}const ve=p(),J=p(!1),_e=p(0);function Je(){ve.value=n.value,J.value=!0,_e.value=g.value.id,c.success("已复制，可以选择其他产品进行粘贴")}function xe(){var f,b;if(H.value)return;H.value=!0;const e=g.value.id,t=n.value.map(y=>({materialId:y.materialId,quantity:y.quantity}));if(!m.value&&(e<=0||t.length===0)){c.warning("请先选择产品并添加物料");return}const u=((b=(f=d.value)==null?void 0:f.materialBundles)==null?void 0:b.map(y=>y.id))||[];x.pms.bom.saveBom({productId:e,materials:t,materialBundleIds:u}).then(()=>{c.success("保存成功"),m.value=!1,D(e)}).catch(y=>{c.error(y.message||"保存失败")}).finally(()=>{H.value=!1})}function Be(){ve.value=[],J.value=!1,_e.value=0}function Qe(){n.value||(n.value=[]),n.value=ve.value.concat(n.value),n.value=n.value.reduce((e,t)=>(e.findIndex(f=>f.materialId===t.materialId)===-1&&e.push(t),e),[]),n.value=n.value.sort((e,t)=>e.sort-t.sort),Be(),xe()}function Xe(e){var u,f;const{sku:t}=e;(u=O.value)==null||u.setForm("keyWord",t),(f=N.value)==null||f.refresh({keyWord:t,page:1})}const he=p(!1);function Ye(){if(!g.value||g.value.id<=0)return c.warning("请先选择产品");const e=g.value.id;he.value=!0,x.pms.bom.request({url:"/export",method:"get",params:{productId:e},responseType:"blob"}).then(t=>{We(t)&&c.success("导出成功")}).catch(()=>{c.error("导出失败")}).finally(()=>{he.value=!1})}const be=p([]);function Ze(){M.value=!0}function et(){const e=be.value;e.length===0&&c.warning("请选择物料集合"),d.value.materialBundles=e,m.value=!0,M.value=!1,c.success("已将物料集合添加到BOM中，保存后才会生效！！！")}function tt(e){d.value.materialBundles=d.value.materialBundles.filter(t=>t.id!==e),c.success("删除成功，保存后才会生效！！！"),m.value=!0}function lt(){return n.value.map(t=>t.materialId)}const ge=vt(()=>{var e,t,u;return!(n.value&&((e=n.value)==null?void 0:e.length)>0||(t=d.value)!=null&&t.materialBundles&&((u=d.value)==null?void 0:u.materialBundles.length)>0)});function ke(e){switch(e){case 0:return{text:"未审核",color:"danger"};case 1:return{text:"审核中",color:"warning"};case 2:return{text:"已审核",color:"success"};default:return{text:"未知",color:"info"}}}return _t(()=>{setTimeout(()=>{ce()},500)}),He(()=>{var e,t,u;(e=d.value)!=null&&e.materialBundles&&(d.value.materialBundles=((u=(t=d.value)==null?void 0:t.materialBundles)==null?void 0:u.map(f=>({...f,materials:f.materials.map(b=>{var y;return{...b,bomId:(y=d.value)==null?void 0:y.id}})})))||[])}),(e,t)=>{var Ue,$e,Le,Te,Ee;const u=v("cl-refresh-btn"),f=v("cl-search"),b=v("cl-table"),y=v("el-row"),ae=v("cl-flex1"),W=v("cl-pagination"),at=v("cl-crud"),w=v("el-button"),ot=v("el-popover"),ye=v("el-tag"),st=v("el-tooltip"),_=v("el-table-column"),nt=v("el-input-number"),Ie=v("el-table"),rt=v("el-card"),Ve=v("el-empty"),Se=v("el-dialog"),it=v("el-switch"),ut=v("cl-form"),oe=Oe("permission"),ct=Oe("loading");return h(),E("div",Ot,[r("div",Dt,[l(at,{ref_key:"Crud",ref:N,scrollbar:"~ rounded"},{default:a(()=>[r("div",Rt,[r("div",null,[l(u),l(qt,{onSuccess:Xe})]),r("div",null,[l(f,{ref_key:"Search",ref:O},null,512)])]),l(y,null,{default:a(()=>[l(b,{ref_key:"Table",ref:C,onRowClick:K},null,512)]),_:1}),l(y,null,{default:a(()=>[l(ae),l(W,{layout:"prev, pager, next","page-size":20})]),_:1})]),_:1},512)]),r("div",Pt,[g.value&&g.value.id>0?Q((h(),E("div",Wt,[r("div",Ft,[r("div",At,[Q((h(),I(w,{type:"success",onClick:fe},{default:a(()=>[i(" 添加物料 ")]),_:1})),[[oe,B(x).pms.material.permission.add]]),Q((h(),I(w,{type:"primary",onClick:o},{default:a(()=>[i(" 选择物料 ")]),_:1})),[[oe,B(x).pms.material.permission.page]]),Q((h(),I(w,{type:"danger",onClick:Ze},{default:a(()=>[i(" 选择集合 ")]),_:1})),[[oe,B(x).pms.material.bundle.permission.page]]),Q((h(),I(w,{loading:he.value,disabled:!((Ue=d.value)!=null&&Ue.materials),type:"warning",onClick:Ye},{default:a(()=>[i(" 导出BOM ")]),_:1},8,["loading","disabled"])),[[oe,B(x).pms.bom.permission.export]]),l(ot,{trigger:"click"},{reference:a(()=>[l(w,{mr2:""},{default:a(()=>[i(" 更多 ")]),_:1})]),default:a(()=>[r("div",jt,[l(w,{link:"",class:"my-1 p-0 mx-0!",type:"primary",disabled:J.value||!(n.value&&n.value.length>0),onClick:Je},{default:a(()=>[Ht,i(X(`${J.value?"已复制":"复制"}清单`),1)]),_:1},8,["disabled"]),l(w,{link:"",class:"my-1 p-0 mx-0!",type:"danger",disabled:_e.value===g.value.id||!J.value,onClick:Qe},{default:a(()=>[Kt,i(" 粘贴清单 ")]),_:1},8,["disabled"]),l(w,{type:"warning",link:"",class:"my-1 p-0 mx-0!",disabled:!J.value,onClick:Be},{default:a(()=>[Gt,i(" 清空复制 ")]),_:1},8,["disabled"])])]),_:1}),m.value?(h(),I(ye,{key:0,type:"danger",m2:""},{default:a(()=>[i(" 清单未保存 ")]),_:1})):F("",!0)]),r("div",Jt,[l(yt,{modelValue:V.value,"onUpdate:modelValue":t[0]||(t[0]=s=>V.value=s),flex:"~ justify-end",colors:B(A)()},null,8,["modelValue","colors"]),(($e=d.value)==null?void 0:$e.status)!==void 0?(h(),I(st,{key:0,placement:"top"},{content:a(()=>[Yt]),default:a(()=>{var s,L;return[l(ye,{"mr-10px":"",type:(L=ke((s=d.value)==null?void 0:s.status))==null?void 0:L.color},{default:a(()=>{var qe,ze;return[r("div",Qt,[Xt,i(" "+X((ze=ke((qe=d.value)==null?void 0:qe.status))==null?void 0:ze.text),1)])]}),_:1},8,["type"])]}),_:1})):F("",!0),n.value.length&&((Le=d.value)!=null&&Le.version)?(h(),I(ye,{key:1,"mr-10px":""},{default:a(()=>{var s;return[i(" 版本：v"+X((s=d.value)==null?void 0:s.version),1)]}),_:1})):F("",!0),Q((h(),I(w,{type:"success",disabled:!m.value,onClick:xe},{default:a(()=>[i(" 保存清单 ")]),_:1},8,["disabled"])),[[oe,B(x).pms.bom.permission.saveBom]]),l(w,{type:"danger",disabled:ge.value,onClick:t[1]||(t[1]=s=>we(0,0))},{default:a(()=>[i(" 清空清单 ")]),_:1},8,["disabled"])])]),ge.value?F("",!0):(h(),E("div",Zt,[r("div",{class:De(m.value?"b b-red b-dashed":"")},[n.value&&n.value.length>0?(h(),I(Ie,{key:0,data:n.value,class:De((m.value,"bom-material-table")),flexible:"",border:"","max-height":(Te=d.value)!=null&&Te.materialBundles?B(z)-300:B(z)-220,"row-style":V.value?B(j):()=>{},"cell-style":V.value?B(ne):()=>{}},{default:a(()=>[l(_,{type:"index",label:"序号",width:"70",align:"center"}),l(_,{prop:"code",label:"编码",width:"150","show-overflow-tooltip":""}),l(_,{prop:"name",label:"名称","show-overflow-tooltip":""}),l(_,{prop:"model",label:"型号","show-overflow-tooltip":""}),l(_,{prop:"size",label:"尺寸/封装",width:"150","show-overflow-tooltip":""}),l(_,{prop:"material",label:"材质",width:"150","show-overflow-tooltip":""}),l(_,{prop:"process",label:"工艺",width:"150","show-overflow-tooltip":""}),l(_,{prop:"coverColor",label:"颜色",width:"100","show-overflow-tooltip":""}),l(_,{prop:"quantity",label:"用量",width:"150",align:"center"},{default:a(({row:s})=>[B(Ne)(B(x).pms.bom.permission.saveBom)?(h(),I(nt,{key:0,modelValue:s.quantity,"onUpdate:modelValue":L=>s.quantity=L,class:"bom-quantity",min:0,max:999999,size:"small",onChange:t[2]||(t[2]=()=>{m.value=!0})},null,8,["modelValue","onUpdate:modelValue"])):(h(),E("span",el,X(s.quantity),1))]),_:1}),l(_,{prop:"unit",label:"单位",width:"80",align:"center"}),B(Ne)(B(x).pms.bom.permission.saveBom)?(h(),I(_,{key:0,label:"操作",width:"140",align:"center",fixed:"right"},{default:a(({row:s})=>[l(w,{type:"warning",size:"small",onClick:L=>ue(s.materialId)},{default:a(()=>[i(" 编辑 ")]),_:2},1032,["onClick"]),l(w,{type:"danger",size:"small",onClick:L=>we(s.bomId,s.materialId)},{default:a(()=>[i(" 删除 ")]),_:2},1032,["onClick"])]),_:1})):F("",!0)]),_:1},8,["data","class","max-height","row-style","cell-style"])):F("",!0),(h(!0),E(Fe,null,ht((Ee=d.value)==null?void 0:Ee.materialBundles,s=>(h(),I(rt,{key:s.id,shadow:"never",class:"material-bundle-card"},{header:a(()=>[r("div",tl,[r("div",ll,[al,i(X(s.name)+" - "+X(s.code),1)]),l(w,{class:"button",type:"danger",size:"small",mr5px:"",onClick:L=>tt(s.id)},{default:a(()=>[i(" 删除 ")]),_:2},1032,["onClick"])])]),default:a(()=>[l(Ie,{border:"","row-style":V.value?B(j):()=>{},"cell-style":B(ne),data:s.materials,"max-height":500,style:{width:"100%"},class:"bom-material-table"},{default:a(()=>[l(_,{type:"index",label:"序号",width:"70",align:"center"}),l(_,{prop:"code",label:"编码",width:"150","show-overflow-tooltip":""}),l(_,{prop:"name",label:"名称","show-overflow-tooltip":""}),l(_,{prop:"model",label:"型号","show-overflow-tooltip":""}),l(_,{prop:"size",label:"尺寸/封装",width:"150","show-overflow-tooltip":""}),l(_,{prop:"material",label:"材质",width:"150","show-overflow-tooltip":""}),l(_,{prop:"process",label:"工艺",width:"150","show-overflow-tooltip":""}),l(_,{prop:"coverColor",label:"颜色",width:"100","show-overflow-tooltip":""}),l(_,{prop:"quantity",label:"用量",width:"150",align:"center"}),l(_,{prop:"unit",label:"单位",width:"80",align:"center"})]),_:2},1032,["row-style","cell-style","data"])]),_:2},1024))),128))],2)])),ge.value?(h(),E("div",ol,[l(Ve,{description:"暂无数据"})])):F("",!0)])),[[ct,k.value]]):(h(),E("div",sl,[l(Ve,{description:"请先在左侧选择产品"})]))]),l(Se,{modelValue:M.value,"onUpdate:modelValue":t[5]||(t[5]=s=>M.value=s),title:"选择物料集合",width:"80%","append-to-body":!1},{footer:a(()=>[r("span",nl,[l(w,{onClick:t[4]||(t[4]=s=>M.value=!1)},{default:a(()=>[i("取消")]),_:1}),l(w,{type:"primary",onClick:et},{default:a(()=>[i(" 确认选择 ")]),_:1})])]),default:a(()=>[l(Nt,{ref_key:"materialBundleSelector",ref:re,modelValue:be.value,"onUpdate:modelValue":t[3]||(t[3]=s=>be.value=s)},null,8,["modelValue"])]),_:1},8,["modelValue"]),l(Se,{modelValue:$.value,"onUpdate:modelValue":t[8]||(t[8]=s=>$.value=s),title:"选择物料",width:"80%","append-to-body":""},{footer:a(()=>[r("span",rl,[l(w,{onClick:t[7]||(t[7]=s=>$.value=!1)},{default:a(()=>[i("取消")]),_:1}),l(w,{type:"primary",onClick:T},{default:a(()=>[i(" 确认选择 ")]),_:1})])]),default:a(()=>[l(xt,{ref_key:"materialSelector",ref:te,modelValue:R.value,"onUpdate:modelValue":t[6]||(t[6]=s=>R.value=s),"can-add":"","has-stock":!1,"disabled-materials":lt()},null,8,["modelValue","disabled-materials"])]),_:1},8,["modelValue"]),l(ut,{ref_key:"MaterialAddForm",ref:P},{"slot-bind-user":a(({scope:s})=>[r("div",il,[l(it,{modelValue:s.isBindUser,"onUpdate:modelValue":L=>s.isBindUser=L,"active-text":(s==null?void 0:s.bindUserId)>0?"更换绑定":"是","inactive-text":"否","active-color":"#13ce66"},null,8,["modelValue","onUpdate:modelValue","active-text"])])]),_:1},512)])}}}),xl=Ke(cl,[["__scopeId","data-v-f6effde5"]]);export{xl as default};
