import{c as g,b as h,aP as N,r as V,i as n,f,o as r,y as c,h as o,q as S,B as F,w as s,v as i,aQ as I,aR as L,F as b,s as M,V as T,G as $,am as j,I as q,t as O,E as P}from"./.pnpm-hVqhwuVC.js";import{k as Q,v as R,w as A,x as v}from"./index-DkYL1aws.js";import{_ as H}from"./_plugin-vue_export-helper-DlAUqK2U.js";const J={class:"cl-theme"},K={class:"cl-theme__drawer"},W={class:"cl-theme__comd"},X=["onClick"],Y=g({name:"cl-theme"}),Z=g({...Y,setup(ee){const{menu:k}=Q(),u=h(N()),m=V(R.get("theme")),a=V({color:m.color||"",theme:m}),d=h(!1);function w(){d.value=!0}function y(){u.value=!1}function C(t){Object.assign(a.theme,t),a.color=t.color,v(t),y(),P.success(`切换主题：${t.label}`)}function x(t){v({isGroup:t}),k.setMenu()}function B(t){v({transition:t})}return(t,l)=>{const D=n("cl-svg"),G=n("el-badge"),_=n("el-switch"),p=n("el-form-item"),U=n("el-form"),z=n("el-drawer");return r(),f(b,null,[c("div",J,[o(G,{type:"primary","is-dot":"",onClick:w},{default:s(()=>[o(D,{name:"icon-discover",size:15})]),_:1}),m.name==="default"?(r(),S(_,{key:0,modelValue:u.value,"onUpdate:modelValue":l[0]||(l[0]=e=>u.value=e),style:{marginLeft:"15px"},"inline-prompt":"","active-icon":i(L),"inactive-icon":i(I)},null,8,["modelValue","active-icon","inactive-icon"])):F("",!0)]),o(z,{modelValue:d.value,"onUpdate:modelValue":l[3]||(l[3]=e=>d.value=e),title:"设置主题",size:"350px","append-to-body":""},{default:s(()=>[c("div",K,[o(U,{"label-position":"top"},{default:s(()=>[o(p,{label:"推荐"},{default:s(()=>[c("ul",W,[(r(!0),f(b,null,M(i(A),(e,E)=>(r(),f("li",{key:E,onClick:oe=>C(e)},[c("div",{class:"w",style:T({backgroundColor:e.color})},[$(o(i(j),null,null,512),[[q,e.color===a.theme.color]])],4),c("span",null,O(e.label),1)],8,X))),128))])]),_:1}),o(p,{label:"菜单分组显示"},{default:s(()=>[o(_,{modelValue:a.theme.isGroup,"onUpdate:modelValue":l[1]||(l[1]=e=>a.theme.isGroup=e),onChange:x},null,8,["modelValue"])]),_:1}),o(p,{label:"转场动画"},{default:s(()=>[o(_,{modelValue:a.theme.transition,"onUpdate:modelValue":l[2]||(l[2]=e=>a.theme.transition=e),"active-value":"slide","inactive-value":"none",onChange:B},null,8,["modelValue"])]),_:1})]),_:1})])]),_:1},8,["modelValue"])],64)}}}),ne=H(Z,[["__scopeId","data-v-6b087692"]]);export{ne as default};
