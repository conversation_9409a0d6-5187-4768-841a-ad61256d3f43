import{i as p}from"./index-BtOcqcNl.js";import{c as b,q as I,w as l,h as e,i as s,f as m,s as q,F as V,o as u,j as B,y as T,t as f}from"./.pnpm-hVqhwuVC.js";import{a as N}from"./index-D95m1iJL.js";const U=T("br",null,null,-1),D=b({name:"pms-freight-forwarder"}),L=b({...D,setup(E){const{service:_}=N(),d=p.useUpsert({items:[{label:"公司名",prop:"name",required:!0,component:{name:"el-input"}},{label:"地址",prop:"address",required:!0,component:{name:"el-input"}},{label:"状态",prop:"status",flex:!1,value:1,component:{name:"el-switch",props:{activeValue:1,inactiveValue:0}},required:!0},{prop:"userIds",label:"联系人",value:[],required:!0,component:{name:"el-select",options:[],props:{multiple:!0,"multiple-limit":3}}}],async onOpen(){var n;const t=await _.base.sys.user.list({departmentId:14});(n=d.value)==null||n.setOptions("userIds",(t==null?void 0:t.map(r=>({label:r.name||"",value:r.id})))||[])},async onInfo(t,{done:n}){var r;t.userIds=(r=t.users)==null?void 0:r.map(o=>o.id),n(t)}}),h=p.useTable({columns:[{label:"ID",prop:"id",width:80},{label:"公司名",prop:"name",width:350},{label:"地址",prop:"address"},{label:"管理员",prop:"users",width:200},{label:"状态",prop:"status",dict:[{label:"启用",value:!0,type:"success"},{label:"禁用",value:!1,type:"danger"}],width:150},{type:"op",buttons:["edit","delete"]}]}),y=p.useCrud({service:_.pms.freight.forwarder},t=>{t.refresh()});return(t,n)=>{const r=s("cl-refresh-btn"),o=s("cl-add-btn"),i=s("cl-flex1"),w=s("cl-search-key"),a=s("el-row"),v=s("cl-table"),k=s("cl-pagination"),x=s("cl-upsert"),g=s("cl-crud");return u(),I(g,{ref_key:"Crud",ref:y},{default:l(()=>[e(a,null,{default:l(()=>[e(r),e(o),e(i),e(w)]),_:1}),e(a,null,{default:l(()=>[e(v,{ref_key:"Table",ref:h},{"column-users":l(({scope:C})=>[(u(!0),m(V,null,q(C.row.users,c=>(u(),m("span",{key:c.id},[B(f(c.name)+" "+f(c.phone),1),U]))),128))]),_:1},512)]),_:1}),e(a,null,{default:l(()=>[e(i),e(k)]),_:1}),e(x,{ref_key:"Upsert",ref:d},null,512)]),_:1},512)}}});export{L as default};
