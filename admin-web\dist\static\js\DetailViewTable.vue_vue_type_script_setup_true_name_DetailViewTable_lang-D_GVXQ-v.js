import{c as x,b as i,K as y,q as B,w as s,h as t,y as k,i as n,j as w,B as L,L as C,v as h,o as D}from"./.pnpm-hVqhwuVC.js";import{c as M,i as g,s as P}from"./index-BtOcqcNl.js";import{U}from"./user-avatar-DTjmVWm6.js";const N={style:{"margin-right":"20px"}},$={flex:"~ justify-center"},j=x({name:"DetailViewTable"}),Q=x({...j,setup(I){const V=M().user.getUserList(),p=i(!1),o=i({date:[],keyWord:""}),a=g.useCrud({dict:{api:{page:"queryMaterialStockLogPage"}},service:P.pms.materialStockLog,async onRefresh(e,{next:l,done:r,render:u}){r(),T(e);const{list:m,pagination:v}=await l(e);m.forEach(c=>{c.user=V.find(b=>b.id===c.creatorId)||{}}),u(m,v)}},e=>{e.refresh()});function T(e){if(o.value.date&&o.value.date.length>0){const l=y(o.value.date[0]).format("YYYY-MM-DD"),r=y(o.value.date[1]).format("YYYY-MM-DD");e.date=`${l},${r}`}return o.value.keyWord&&(e.keyWord=o.value.keyWord),e}i([{label:"采购下单",value:1},{label:"入库-库存调整",value:2},{label:"生产退料",value:3},{label:"生产退良品",value:4},{label:"采购单入库",value:5},{label:"领料出库",value:6},{label:"退货出库",value:7},{label:"其他领用",value:8},{label:"报废",value:9}]),i([{label:"可用库存",value:0},{label:"在途库存",value:1},{label:"冻结库存",value:2},{label:"占用库存",value:3},{label:"可用在途",value:4},{label:"已使用在途",value:5}]);const Y=g.useTable({columns:[{label:"物料编码",prop:"code",width:160},{label:"物料名称",prop:"name",width:300,showOverflowTooltip:!0},{label:"规格/型号",prop:"model",width:300,showOverflowTooltip:!0},{label:"数量",prop:"quantity",width:100},{label:"修改前数量",prop:"before",width:140},{label:"修改后数量",prop:"after",width:140},{label:"备注",prop:"remark"},{label:"创建时间",prop:"createTime",width:160}]});function _(){var e,l,r;try{(l=(e=a==null?void 0:a.value)==null?void 0:e.params)!=null&&l.page&&(a.value.params.page=1),p.value=!0,(r=a==null?void 0:a.value)==null||r.refresh()}catch(u){console.error(u)}finally{p.value=!1}}function f(){var e,l;o.value.keyWord="",o.value.date=[],(e=a==null?void 0:a.value)!=null&&e.params&&(a.value.params.page=1,a.value.params.size=20),(l=a==null?void 0:a.value)==null||l.refresh()}function K(e){e?_():f()}return(e,l)=>{const r=n("el-button"),u=n("cl-flex1"),m=n("el-date-picker"),v=n("el-input"),c=n("el-row"),b=n("cl-table"),q=n("cl-pagination"),W=n("cl-crud");return D(),B(W,{ref_key:"Crud",ref:a},{default:s(()=>[t(c,null,{default:s(()=>[t(r,{onClick:f},{default:s(()=>[w(" 刷新 ")]),_:1}),t(u),k("div",N,[L("",!0)]),t(v,{modelValue:h(o).keyWord,"onUpdate:modelValue":l[1]||(l[1]=d=>h(o).keyWord=d),placeholder:"请输入",style:{width:"500px"},clearable:"",onClear:f,onKeyup:C(_,["enter"])},null,8,["modelValue"]),t(r,{type:"primary",mx:"10px",loading:h(p),onClick:_},{default:s(()=>[w(" 搜索 ")]),_:1},8,["loading"])]),_:1}),t(c,null,{default:s(()=>[t(b,{ref_key:"Table",ref:Y,"row-key":"rowIndex"},{"column-createId":s(({scope:d})=>[k("div",$,[t(U,{user:d.row.user,"show-name":!1},null,8,["user"])])]),_:1},512)]),_:1}),t(c,null,{default:s(()=>[t(u),t(q)]),_:1})]),_:1},512)}}});export{Q as _};
