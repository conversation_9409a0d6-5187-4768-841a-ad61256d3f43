import{i as m}from"./index-DkYL1aws.js";import{c as r,b as f,z as V,h,i as g,Q as v,R as p,E as i}from"./.pnpm-hVqhwuVC.js";const b=r({name:"ClSwitch",props:{scope:null,column:null,modelValue:[Number,String,Boolean],api:Function,activeValue:{type:[Number,String,Boolean],default:1},inactiveValue:{type:[Number,String,Boolean],default:0}},emits:["update:modelValue","change"],setup(a,{emit:n}){const c=m.useCrud(),u=f();V(()=>a.modelValue,e=>{v(a.activeValue)?u.value=!!e:u.value=e},{immediate:!0});function s(e){var t;if(a.column&&a.scope){if(e!==void 0){const l={...a.scope,[a.column.property]:e},o=p(a.api)?a.api(l):(t=c.value)==null?void 0:t.service.update(l);o&&o.then(()=>{i.success("更新成功"),n("update:modelValue",e),n("change",e)}).catch(d=>{i.error(d.message)})}}else n("update:modelValue",e),n("change",e)}return()=>h(g("el-switch"),{modelValue:u.value,"onUpdate:modelValue":e=>u.value=e,"active-value":a.activeValue,"inactive-value":a.inactiveValue,onChange:s},null)}});export{b as default};
