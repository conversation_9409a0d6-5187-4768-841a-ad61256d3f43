import{i as O}from"./index-BtOcqcNl.js";import{c as C,b as k,f as E,h as c,i,w as a,y as f,t as b,W as U,j as y,F as z,T as q,E as m,o as N,af as Q,ag as W}from"./.pnpm-hVqhwuVC.js";import{a as j}from"./index-D95m1iJL.js";import{_ as K}from"./_plugin-vue_export-helper-DlAUqK2U.js";const A=v=>(Q("data-v-63ff1127"),v=v(),W(),v),G=A(()=>f("span",{style:{color:"var(--el-color-danger)"}},"*",-1)),H=C({name:"undefined"}),J=C({...H,setup(v,{expose:P}){const{service:x}=j(),w=O.useForm(),_=k(!1),p=k(),B=k(null),L=k(!1),M=k(),I=k(0),T=k();function Y(u,S,h){var n;S==="outbound"?q.confirm("订单库存充足，将直接生成出库单到仓库","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{x.pms.production.sale.order.outbound({orderId:u.id}).then(e=>{e.message="处理成功，生成出库单到仓库",h(e)}).catch(e=>{m.error(e.message||"处理失败")})}).catch(()=>{}):S==="schedule"?(n=w.value)==null||n.open({title:"填写生产订单信息",width:"400px",dialog:{controls:["close"]},props:{labelWidth:"120px"},items:[{label:"生产订单号",required:!0,prop:"sn",component:{name:"el-input",props:{placeholder:"请输入生产订单号",clearable:!0}}},{label:"预计交货日期",prop:"expectedDeliveryTime",required:!0,component:{name:"el-date-picker",props:{placeholder:"请选择预计交货日期",clearable:!0,format:"YYYY-MM-DD","value-format":"YYYY-MM-DD","disabled-date":e=>e.getTime()<Date.now()-864e5}}}],on:{submit:async(e,{done:s,close:d})=>{const r=e.expectedDeliveryTime>u.requiredShipDate?"预计交货日期超过了订单的要求出货日期，是否继续？":"确定排产吗？";q.confirm(r,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{x.pms.production.sale.order.schedule({orderId:u.id,...e}).then(l=>{d(),l.message="排产成功",h(l)}).catch(l=>{m.error(l.message||"排产失败"),s()})}).catch(()=>{s()})}}}):S==="lockStock"&&(_.value=!0,I.value=u.id,p.value=u.products.map(e=>{var s,d,r,l;return{index:new Date().getTime()+Math.floor(Math.random()*1e3),productId:e.pieceProductId,sku:e.pieceProduct.sku,quantity:e.piece,lockStock:e.lockStock,lockedStock:e.lockStock||0,inStock:((d=(s=e==null?void 0:e.pieceProduct)==null?void 0:s.stock)==null?void 0:d.inStock)-((l=(r=e==null?void 0:e.pieceProduct)==null?void 0:r.stock)==null?void 0:l.freezeStock)||0}}),M.value=u.products.map(e=>({productId:e.productId,sku:e.sku})),T.value=()=>{var l,o,g,V;if(L.value=!0,!((l=p.value)==null?void 0:l.every(t=>t.lockStock!==null&&t.lockStock>=0)))return m.error("请填写锁定库存"),!1;if(!((o=p.value)==null?void 0:o.every(t=>t.lockStock<=t.inStock+t.lockedStock)))return m.error("锁定库存不能大于可用库存"),!1;if(!((g=p.value)==null?void 0:g.every(t=>t.lockStock<=t.quantity)))return m.error("锁定库存不能大于订单数量"),!1;const r=(V=p.value)==null?void 0:V.map(t=>({productId:t.productId,lockStock:t.lockStock}));x.pms.production.sale.order.lockStock({orderId:I.value,lockStockProducts:r}).then(t=>{t.message="锁定库存成功",_.value=!1,h(t)}).catch(t=>{m.error(t.message||"锁定库存失败")})})}function D(){_.value=!1}function F(){return T.value()}return P({processOrder:Y}),(u,S)=>{const h=i("cl-form"),n=i("el-table-column"),e=i("el-input-number"),s=i("el-table"),d=i("el-form"),r=i("el-button"),l=i("cl-dialog");return N(),E(z,null,[c(h,{ref_key:"ProductionScheduleForm",ref:w},null,512),c(l,{modelValue:_.value,"onUpdate:modelValue":S[0]||(S[0]=o=>_.value=o),width:"50%",controls:["close"],title:"请填写锁定库存信息","modal-class":"schedule-order-lock-stock-dialog",onClose:D},{footer:a(()=>[c(r,{type:"success",onClick:F},{default:a(()=>[y(" 提交锁定库存 ")]),_:1}),c(r,{onClick:D},{default:a(()=>[y(" 取 消 ")]),_:1})]),default:a(()=>[c(d,{"label-width":"88px",model:B.value,size:"large"},{default:a(()=>[c(s,{data:p.value},{default:a(()=>[c(n,{prop:"sku",label:"产品SKU"},{default:a(o=>[f("span",null,b(o.row.sku),1)]),_:1}),c(n,{prop:"quantity",label:"订单数量",align:"center"},{default:a(o=>[f("span",null,b(o.row.quantity),1)]),_:1}),c(n,{prop:"inStock",label:"可用库存",align:"center"},{default:a(o=>[f("span",null,b(o.row.inStock),1)]),_:1}),c(n,{prop:"lockedStock",label:"锁定库存",align:"center"},{default:a(o=>[f("span",null,b(o.row.lockedStock),1)]),_:1}),c(n,{prop:"lockStock",label:"*数量",align:"center"},{header:a(()=>[G,y(" 数量 ")]),default:a(o=>[f("div",{style:{display:"flex","align-items":"center"},class:U(L.value&&!(o.row.lockStock!==null&&o.row.lockStock>=0)?"quantity-input-error":"")},[c(e,{modelValue:o.row.lockStock,"onUpdate:modelValue":g=>o.row.lockStock=g,prop:"lockStock",min:0,max:o.row.inStock+o.row.lockedStock>o.row.quantity?o.row.quantity:o.row.inStock+o.row.lockedStock,placeholder:"数量"},null,8,["modelValue","onUpdate:modelValue","max"])],2)]),_:1})]),_:1},8,["data"])]),_:1},8,["model"])]),_:1},8,["modelValue"])],64)}}}),ee=K(J,[["__scopeId","data-v-63ff1127"]]);export{ee as S};
