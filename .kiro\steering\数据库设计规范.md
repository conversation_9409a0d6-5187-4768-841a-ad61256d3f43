---
inclusion: fileMatch
fileMatchPattern: "**/*{model,entity,dao}*"
---

# 数据库设计规范

## 数据库命名规范

### 表命名规范
- **表名**: 使用小写字母和下划线，复数形式
- **前缀**: 按模块使用前缀，如 `pms_`, `inv_`, `ord_`
- **示例**: `pms_production_orders`, `inv_materials`, `ord_sales_orders`

### 字段命名规范
- **字段名**: 使用小写字母和下划线
- **主键**: 统一使用 `id`
- **外键**: 使用 `关联表名_id`，如 `user_id`, `order_id`
- **时间字段**: `created_at`, `updated_at`, `deleted_at`
- **状态字段**: `status`, `is_active`, `is_deleted`

### 索引命名规范
```sql
-- 主键索引
PRIMARY KEY (`id`)

-- 唯一索引
UNIQUE KEY `uk_users_email` (`email`)

-- 普通索引
KEY `idx_users_status` (`status`)
KEY `idx_users_created_at` (`created_at`)

-- 复合索引
KEY `idx_orders_user_status` (`user_id`, `status`)

-- 外键索引
KEY `fk_orders_user_id` (`user_id`)
```

## 表结构设计规范

### 基础表结构
```sql
CREATE TABLE `base_table_template` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  `created_by` bigint(20) unsigned DEFAULT NULL COMMENT '创建人ID',
  `updated_by` bigint(20) unsigned DEFAULT NULL COMMENT '更新人ID',
  PRIMARY KEY (`id`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='表注释';
```

### 用户表设计
```sql
CREATE TABLE `sys_users` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `email` varchar(100) NOT NULL COMMENT '邮箱',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `real_name` varchar(50) DEFAULT NULL COMMENT '真实姓名',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像URL',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `last_login_at` datetime DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(45) DEFAULT NULL COMMENT '最后登录IP',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_users_username` (`username`),
  UNIQUE KEY `uk_users_email` (`email`),
  KEY `idx_users_status` (`status`),
  KEY `idx_users_created_at` (`created_at`),
  KEY `idx_users_deleted_at` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';
```

### 生产订单表设计
```sql
CREATE TABLE `pms_production_orders` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '生产订单ID',
  `order_no` varchar(50) NOT NULL COMMENT '订单编号',
  `sales_order_id` bigint(20) unsigned DEFAULT NULL COMMENT '销售订单ID',
  `product_id` bigint(20) unsigned NOT NULL COMMENT '产品ID',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `product_spec` varchar(200) DEFAULT NULL COMMENT '产品规格',
  `quantity` decimal(10,2) NOT NULL COMMENT '生产数量',
  `unit` varchar(20) NOT NULL COMMENT '单位',
  `plan_start_date` date NOT NULL COMMENT '计划开始日期',
  `plan_end_date` date NOT NULL COMMENT '计划结束日期',
  `actual_start_date` date DEFAULT NULL COMMENT '实际开始日期',
  `actual_end_date` date DEFAULT NULL COMMENT '实际结束日期',
  `status` tinyint(2) NOT NULL DEFAULT '1' COMMENT '状态：1-待生产，2-生产中，3-已完成，4-已取消',
  `priority` tinyint(1) NOT NULL DEFAULT '3' COMMENT '优先级：1-高，2-中，3-低',
  `workshop_id` bigint(20) unsigned DEFAULT NULL COMMENT '车间ID',
  `responsible_user_id` bigint(20) unsigned DEFAULT NULL COMMENT '负责人ID',
  `remark` text COMMENT '备注',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  `created_by` bigint(20) unsigned DEFAULT NULL COMMENT '创建人ID',
  `updated_by` bigint(20) unsigned DEFAULT NULL COMMENT '更新人ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_production_orders_order_no` (`order_no`),
  KEY `idx_production_orders_sales_order_id` (`sales_order_id`),
  KEY `idx_production_orders_product_id` (`product_id`),
  KEY `idx_production_orders_status` (`status`),
  KEY `idx_production_orders_plan_dates` (`plan_start_date`, `plan_end_date`),
  KEY `idx_production_orders_workshop_id` (`workshop_id`),
  KEY `idx_production_orders_created_at` (`created_at`),
  KEY `idx_production_orders_deleted_at` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='生产订单表';
```

## GoFrame 模型定义规范

### 实体模型定义
```go
// internal/model/entity/user.go
package entity

import (
    "github.com/gogf/gf/v2/os/gtime"
)

// User 用户实体
type User struct {
    Id           uint64      `json:"id"           description:"用户ID"`
    Username     string      `json:"username"     description:"用户名"`
    Email        string      `json:"email"        description:"邮箱"`
    Password     string      `json:"password"     description:"密码"`
    RealName     string      `json:"realName"     description:"真实姓名"`
    Phone        string      `json:"phone"        description:"手机号"`
    Avatar       string      `json:"avatar"       description:"头像URL"`
    Status       int         `json:"status"       description:"状态：0-禁用，1-启用"`
    LastLoginAt  *gtime.Time `json:"lastLoginAt"  description:"最后登录时间"`
    LastLoginIp  string      `json:"lastLoginIp"  description:"最后登录IP"`
    CreatedAt    *gtime.Time `json:"createdAt"    description:"创建时间"`
    UpdatedAt    *gtime.Time `json:"updatedAt"    description:"更新时间"`
    DeletedAt    *gtime.Time `json:"deletedAt"    description:"删除时间"`
}
```

### 数据对象定义
```go
// internal/model/do/user.go
package do

import (
    "github.com/gogf/gf/v2/frame/g"
    "github.com/gogf/gf/v2/os/gtime"
)

// User 用户数据对象，用于数据库操作
type User struct {
    g.Meta      `orm:"table:sys_users, do:true"`
    Id          interface{} // 用户ID
    Username    interface{} // 用户名
    Email       interface{} // 邮箱
    Password    interface{} // 密码
    RealName    interface{} // 真实姓名
    Phone       interface{} // 手机号
    Avatar      interface{} // 头像URL
    Status      interface{} // 状态：0-禁用，1-启用
    LastLoginAt interface{} // 最后登录时间
    LastLoginIp interface{} // 最后登录IP
    CreatedAt   interface{} // 创建时间
    UpdatedAt   interface{} // 更新时间
    DeletedAt   interface{} // 删除时间
}
```

### DAO 层定义
```go
// internal/dao/user.go
package dao

import (
    "github.com/gogf/gf/v2/database/gdb"
    "github.com/gogf/gf/v2/frame/g"
)

// internalUserDao is internal type for wrapping dao logic.
type internalUserDao struct {
    table   string      // table is the underlying table name of the DAO.
    group   string      // group is the database configuration group name of current DAO.
    columns UserColumns // columns contains all the column names of Table for convenient usage.
}

// UserColumns defines and stores column names for table sys_users.
type UserColumns struct {
    Id          string
    Username    string
    Email       string
    Password    string
    RealName    string
    Phone       string
    Avatar      string
    Status      string
    LastLoginAt string
    LastLoginIp string
    CreatedAt   string
    UpdatedAt   string
    DeletedAt   string
}

var (
    // User is globally public accessible object for table sys_users operations.
    User = internalUserDao{
        table:   "sys_users",
        group:   "default",
        columns: userColumns,
    }
    
    // userColumns holds the columns for table sys_users.
    userColumns = UserColumns{
        Id:          "id",
        Username:    "username",
        Email:       "email",
        Password:    "password",
        RealName:    "real_name",
        Phone:       "phone",
        Avatar:      "avatar",
        Status:      "status",
        LastLoginAt: "last_login_at",
        LastLoginIp: "last_login_ip",
        CreatedAt:   "created_at",
        UpdatedAt:   "updated_at",
        DeletedAt:   "deleted_at",
    }
)

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *internalUserDao) DB() gdb.DB {
    return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *internalUserDao) Table() string {
    return dao.table
}

// Columns returns the columns of current dao.
func (dao *internalUserDao) Columns() UserColumns {
    return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *internalUserDao) Group() string {
    return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *internalUserDao) Ctx(ctx context.Context) *gdb.Model {
    return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}
```

## 数据库查询优化规范

### 索引设计原则
1. **主键索引**: 每个表必须有主键
2. **唯一索引**: 业务唯一字段添加唯一索引
3. **查询索引**: 经常用于 WHERE 条件的字段
4. **排序索引**: 经常用于 ORDER BY 的字段
5. **复合索引**: 多字段组合查询的场景

### 查询优化示例
```go
// 避免 SELECT *，明确指定字段
func (s *sUser) GetUserInfo(ctx context.Context, id uint64) (*entity.User, error) {
    var user *entity.User
    err := dao.User.Ctx(ctx).
        Fields("id, username, email, real_name, status").
        Where("id = ? AND deleted_at IS NULL", id).
        Scan(&user)
    return user, err
}

// 使用索引优化分页查询
func (s *sUser) ListUsers(ctx context.Context, req *model.UserListInput) (*model.UserListOutput, error) {
    query := dao.User.Ctx(ctx).Where("deleted_at IS NULL")
    
    // 添加筛选条件
    if req.Status != 0 {
        query = query.Where("status = ?", req.Status)
    }
    if req.Username != "" {
        query = query.Where("username LIKE ?", "%"+req.Username+"%")
    }
    
    // 统计总数
    total, err := query.Count()
    if err != nil {
        return nil, err
    }
    
    // 分页查询
    var users []*entity.User
    err = query.
        OrderBy("created_at DESC").
        Page(req.Page, req.Size).
        Scan(&users)
    if err != nil {
        return nil, err
    }
    
    return &model.UserListOutput{
        List:  users,
        Total: total,
    }, nil
}

// 批量操作优化
func (s *sUser) BatchUpdateStatus(ctx context.Context, ids []uint64, status int) error {
    _, err := dao.User.Ctx(ctx).
        Where("id IN (?)", ids).
        Data(g.Map{"status": status, "updated_at": gtime.Now()}).
        Update()
    return err
}
```

## 数据库事务规范

### 事务使用示例
```go
// 使用事务确保数据一致性
func (s *sOrder) CreateOrderWithItems(ctx context.Context, req *model.CreateOrderInput) error {
    return dao.Order.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
        // 创建订单
        orderResult, err := dao.Order.Ctx(ctx).Data(do.Order{
            OrderNo:    req.OrderNo,
            CustomerId: req.CustomerId,
            TotalAmount: req.TotalAmount,
            Status:     1,
        }).Insert()
        if err != nil {
            return err
        }
        
        orderId, err := orderResult.LastInsertId()
        if err != nil {
            return err
        }
        
        // 创建订单明细
        var items []do.OrderItem
        for _, item := range req.Items {
            items = append(items, do.OrderItem{
                OrderId:   orderId,
                ProductId: item.ProductId,
                Quantity:  item.Quantity,
                Price:     item.Price,
            })
        }
        
        _, err = dao.OrderItem.Ctx(ctx).Data(items).Insert()
        if err != nil {
            return err
        }
        
        // 更新库存
        for _, item := range req.Items {
            _, err = dao.Inventory.Ctx(ctx).
                Where("product_id = ?", item.ProductId).
                Decrement("quantity", item.Quantity)
            if err != nil {
                return err
            }
        }
        
        return nil
    })
}
```

## 数据迁移规范

### 迁移文件命名
```
migrations/
├── 20240101_120000_create_users_table.sql
├── 20240101_120001_create_roles_table.sql
├── 20240101_120002_add_index_to_users.sql
└── 20240102_090000_alter_users_add_phone.sql
```

### 迁移文件示例
```sql
-- 20240101_120000_create_users_table.sql
-- +migrate Up
CREATE TABLE `sys_users` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `email` varchar(100) NOT NULL COMMENT '邮箱',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_users_username` (`username`),
  UNIQUE KEY `uk_users_email` (`email`),
  KEY `idx_users_status` (`status`),
  KEY `idx_users_deleted_at` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- +migrate Down
DROP TABLE IF EXISTS `sys_users`;
```

## 数据库配置规范

### 配置文件示例
```yaml
# config.yaml
database:
  default:
    link: "mysql:root:password@tcp(127.0.0.1:3306)/lookah_erp?charset=utf8mb4&parseTime=True&loc=Local"
    debug: true
    maxIdle: 10
    maxOpen: 100
    maxLifetime: "30s"
  
  # 读写分离配置
  master:
    link: "mysql:root:password@tcp(master.db:3306)/lookah_erp?charset=utf8mb4&parseTime=True&loc=Local"
    maxIdle: 10
    maxOpen: 100
  
  slave:
    link: "mysql:root:password@tcp(slave.db:3306)/lookah_erp?charset=utf8mb4&parseTime=True&loc=Local"
    maxIdle: 10
    maxOpen: 100
```

## 数据安全规范

### 敏感数据处理
```go
// 密码加密存储
func (s *sUser) HashPassword(password string) (string, error) {
    bytes, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
    return string(bytes), err
}

// 密码验证
func (s *sUser) CheckPassword(password, hash string) bool {
    err := bcrypt.CompareHashAndPassword([]byte(hash), []byte(password))
    return err == nil
}

// 数据脱敏
func (s *sUser) MaskSensitiveData(user *entity.User) *entity.User {
    if user == nil {
        return nil
    }
    
    // 手机号脱敏
    if len(user.Phone) > 7 {
        user.Phone = user.Phone[:3] + "****" + user.Phone[7:]
    }
    
    // 邮箱脱敏
    if strings.Contains(user.Email, "@") {
        parts := strings.Split(user.Email, "@")
        if len(parts[0]) > 2 {
            parts[0] = parts[0][:2] + "***"
        }
        user.Email = strings.Join(parts, "@")
    }
    
    // 清除密码
    user.Password = ""
    
    return user
}
```