import{c as ue,b as I,A as Ve,r as we,U as qe,E as u,f as w,y,h as n,q as k,B as x,t as z,w as i,i as h,F as re,s as ie,j as O,v as X,an as Pe,ao as Me,a0 as Se,T as de,o as v,af as Ae,ag as Ee}from"./.pnpm-hVqhwuVC.js";import{c as Le,s as V}from"./index-DkYL1aws.js";import{_ as Be}from"./OutboundTable.vue_vue_type_script_setup_true_name_OutboundTable_lang-DQuvyBaK.js";import{a as Ne}from"./index-C6cm1h61.js";import{_ as Ue}from"./_plugin-vue_export-helper-DlAUqK2U.js";const Y=G=>(Ae("data-v-da4368ec"),G=G(),Ee(),G),$e={class:"delivery-note-add"},De={class:"header-container"},Fe={class:"form-section"},Ge=Y(()=>y("div",{class:"section-title"}," 基本信息 ",-1)),Re={key:0,style:{color:"#999","font-size":"12px","margin-top":"5px"}},je={key:1,style:{color:"#67c23a","font-size":"12px","margin-top":"5px"}},Je={key:0,style:{color:"#999","font-size":"12px","margin-top":"5px"}},Ke={key:1,style:{color:"#67c23a","font-size":"12px","margin-top":"5px"}},Ze={class:"form-section"},He={class:"section-header"},We=Y(()=>y("div",{class:"section-title"}," 物料信息 ",-1)),Xe={key:0,class:"empty-material-tip"},Ye=Y(()=>y("span",{class:"required-field"},"数量",-1)),ea={class:"ellipsis-cell"},aa={class:"ellipsis-cell"},ta={class:"ellipsis-cell"},la={class:"ellipsis-cell"},oa={class:"ellipsis-cell"},na={key:2,class:"add-material-button-container"},ra={class:"form-actions"},ia={class:"mode-dialog-content"},da={class:"dialog-icon"},ua={class:"dialog-message"},sa={class:"dialog-buttons"},ca=ue({name:"undefined"}),pa=ue({...ca,setup(G){const A=I([]),b=I([]),m=I([]),R=I(!1),Q=I(!1),T=I(0),{router:M}=Ne(),{user:q}=Le(),C=I(!1),B=I(!1);Ve(()=>{ce();const t=M.currentRoute.value.query.id;t&&(C.value=!0,se(Number(t)))});function se(t){console.log("fetchById 开始加载数据"),B.value=!0,V.pms.delivery_note.getDeliveryInfo({id:t}).then(async e=>{var d;console.log("fetchById 收到数据:",{inboundType:e.inboundType,po:e.po}),l.inboundType=e.inboundType||1,console.log("设置入库类型:",l.inboundType),l.id=e.id,l.supplierId=e.supplier_id,l.supplierOrderNo=e.supplier_order_no,l.inboundType===1?(console.log("采购入库，设置PO字段:",e.po),l.po=e.po):console.log("补退货入库，不设置PO字段"),l.voucher=e.voucher,l.remark=e.remark,l.orderId=e.orderId,l.no=e.no,l.inboundType===1&&e.po&&T.value&&await Qe(e.po);const a=((d=e.products)==null?void 0:d.map(s=>{var p,S,$,L,g,J,f,K,P,Z,D,H,F,r,_,te,le,oe,ne;const o=qe(s);return l.inboundType===1?{quantity:o.quantity,contractId:o.contractId,materialId:o.materialId,createTime:o.createTime||"",status:o.status||0,remark:o.remark||"",expectedInbound:((p=o.contract)==null?void 0:p.expectedQuantity)||0,receivedQuantity:((S=o.contract)==null?void 0:S.receivedQuantity)||0,name:((L=($=o.contract)==null?void 0:$.material)==null?void 0:L.name)||o.name,model:((J=(g=o.contract)==null?void 0:g.material)==null?void 0:J.model)||o.model,size:((K=(f=o.contract)==null?void 0:f.material)==null?void 0:K.size)||o.size,material:((Z=(P=o.contract)==null?void 0:P.material)==null?void 0:Z.material)||o.material,unit:((H=(D=o.contract)==null?void 0:D.material)==null?void 0:H.unit)||o.unit,process:((r=(F=o.contract)==null?void 0:F.material)==null?void 0:r.process)||o.process,code:((te=(_=o.contract)==null?void 0:_.material)==null?void 0:te.code)||o.code,coverColor:((oe=(le=o.contract)==null?void 0:le.material)==null?void 0:oe.coverColor)||o.coverColor||"",orderId:((ne=o.contract)==null?void 0:ne.purchaseId)||o.orderId}:{quantity:o.quantity,contractId:o.contractId||0,materialId:o.materialId,createTime:o.createTime||"",status:o.status||0,remark:o.remark||"",expectedInbound:o.expectedInbound||0,receivedQuantity:o.receivedQty||0,name:o.name||"",model:o.model||"",size:o.size||"",material:o.material||"",unit:o.unit||"",process:o.process||"",code:o.code||"",coverColor:o.coverColor||"",orderId:o.orderId||0,returnOrderQuantity:o.expectedInbound||0,restockingQty:o.receivedQty||0}}))||[];if(l.inboundType===1){const o=[...a.map(p=>({id:p.materialId,code:p.code,name:p.name,model:p.model,size:p.size,material:p.material,unit:p.unit,process:p.process,coverColor:p.coverColor,contractId:p.contractId,expectedQuantity:p.expectedInbound,receivedQuantity:p.receivedQuantity,purchaseId:p.orderId}))];m.value.forEach(p=>{o.some(S=>S.id===p.id)||o.push(p)}),m.value=o}else m.value=a.map(s=>({id:s.materialId,code:s.code,name:s.name,model:s.model,size:s.size,material:s.material,unit:s.unit,process:s.process,coverColor:s.coverColor,returnOrderQuantity:s.returnOrderQuantity,restockingQty:s.restockingQty,orderId:s.orderId}));c.value=a}).catch(e=>{u.error({message:e.message||"获取送货单信息失败"})}).finally(()=>{B.value=!1})}async function ce(){var e;q.info=q.info||{};const t=(e=q==null?void 0:q.info)==null?void 0:e.id;V.pms.supplier_account.request({url:"/getUserRole",method:"POST",data:{user_id:t}}).then(a=>{const d=[];a&&a.forEach(s=>{d.push(s.name)}),d.includes("供应商")?pe():(u.error("本页面只供供应商账号访问！"),M.push("/pms/delivery_note"))}).catch(a=>{u.error("查询角色信息失败！"),M.push("/pms/delivery_note")})}function pe(){var t;V.pms.supplier_account.request({url:"/getUserBindSupplier",method:"GET",params:{user_id:(t=q==null?void 0:q.info)==null?void 0:t.id}}).then(e=>{T.value=e.supplier_id,l.supplierId=T.value,me(e.supplier_id)}).catch(e=>{M.push("/pms/delivery_note")})}function me(t){if(l.inboundType!==1){console.log("非采购入库，跳过PO列表获取");return}console.log("采购入库，开始获取PO列表"),R.value=!0,V.pms.purchase.contract.request({url:"/getUnfinishedPo",method:"GET",params:{supplier_id:t}}).then(e=>{if(e){if(Array.isArray(e))A.value=e.map(a=>{if(typeof a=="string")return{po:a};if(a&&typeof a.po=="string")return a;if(a&&a.po_number)return{po:a.po_number};for(const d in a)if(typeof a[d]=="string"&&/^[A-Z0-9]+$/i.test(a[d]))return{po:a[d]};return{po:JSON.stringify(a)}});else if(typeof e=="object"){for(const a in e)if(Array.isArray(e[a])){A.value=e[a].map(d=>typeof d=="string"?{po:d}:d&&typeof d.po=="string"?d:{po:JSON.stringify(d)});break}}u.success(`已加载${A.value.length}个PO号`)}R.value=!1}).catch(e=>{u.error("获取PO列表失败"),R.value=!1})}const l=we({id:"",no:"",po:"",remark:"",voucher:"",orderId:"",supplierId:T.value,supplierOrderNo:"",inboundType:1}),c=I([]),N=I(!1),E=I(""),j=I(!1),ee=I(0);function ve(t){c.value.length>0&&c.value.splice(t,1)}function U(){const t={quantity:void 0,contractId:void 0,materialId:void 0,createTime:"",status:0,remark:"",expectedInbound:void 0,receivedQuantity:void 0,name:"",model:"",size:"",material:"",unit:"",process:"",coverColor:"",orderId:0,code:"",returnOrderQuantity:void 0,restockingQty:void 0};c.value.push(t)}function fe(t,e){return c.value.some(a=>a!==e&&a.materialId===t)}function ye(t){if(l.inboundType===1){const e=Number(t.expectedInbound)||0;return Math.max(e,0)}else if(l.inboundType===9){const e=(t.returnOrderQuantity||0)-(t.restockingQty||0);return Math.max(e,0)}return 999999}function _e(t,e){const a=m.value.find(d=>d.id===t);a&&(e.name=a.name,e.model=a.model,e.size=a.size,e.material=a.material,e.unit=a.unit,e.process=a.process,e.code=a.code,e.coverColor=a.coverColor||"",l.inboundType===1?a.contractId&&(e.contractId=a.contractId,e.expectedInbound=a.expectedQuantity,e.receivedQuantity=a.receivedQuantity,e.orderId=a.purchaseId):l.inboundType===9&&(e.returnOrderQuantity=a.returnOrderQuantity,e.restockingQty=a.restockingQty||0,e.orderId=a.orderId))}function he(t){!t||!T.value||(Q.value=!0,V.pms.purchase.contract.request({url:"/getContractListByPoAndSupplierId",method:"GET",params:{po:t,supplier_id:T.value}}).then(e=>{e&&Array.isArray(e)?(b.value=e,m.value=e.map(a=>({id:a.materialId,code:a.material.code,name:a.material.name,model:a.material.model,size:a.material.size,material:a.material.material,unit:a.material.unit,process:a.material.process,coverColor:a.material.coverColor||"",contractId:a.id,expectedQuantity:a.expectedQuantity,receivedQuantity:a.receivedQuantity,purchaseId:a.purchaseId})),u.success(`已加载${m.value.length}个可选物料`)):(b.value=[],m.value=[],u.warning("未找到相关物料")),Q.value=!1}).catch(e=>{u.error("获取物料列表失败"),b.value=[],m.value=[],Q.value=!1}))}function ae(t){if(N.value=!1,t==="auto")W(E.value);else if(C.value)Te(E.value);else{c.value=[];for(let e=0;e<10;e++)U();he(E.value)}}function be(){N.value=!1,l.po="",E.value=""}function ge(){if(l.inboundType===1&&!l.po){u.error("请选择送货单PO号");return}if(l.inboundType===9&&c.value.length===0){u.error("请选择退货出库单");return}const t=c.value.filter(a=>{if(!a.materialId)return!1;const d=Number(a.quantity);if(a.quantity===void 0||a.quantity===""||Number.isNaN(d)||d===0)return!1;if(l.inboundType===9){const s=(a.returnOrderQuantity||0)-(a.restockingQty||0);if(d>s)return u.error(`物料 ${a.name} 的补货数量不能超过剩余可补数量 ${s}`),!1}return!0});if(t.length===0){u.error("请至少选择一个物料并填写数量");return}t.length>0&&(l.inboundType===1?l.orderId=String(t[0].orderId):l.inboundType===9&&!l.orderId&&t[0].orderId&&(l.orderId=t[0].orderId));const e={delivery_note:l,materials:t};C.value?V.pms.delivery_note.update(e).then(a=>{u.success("修改成功"),M.push("/pms/delivery_note")}).catch(a=>{u.error(a.message||"修改失败")}):V.pms.delivery_note.add(e).then(a=>{u.success("创建成功"),M.push("/pms/delivery_note")}).catch(a=>{u.error(a.message||"创建失败")})}function Ie(t){console.log("selectPo 被调用:",{val:t,inboundType:l.inboundType,isLoadingData:B.value,isEdit:C.value}),t?l.inboundType===1&&!B.value?(console.log("显示模式选择对话框"),E.value=t,N.value=!0):console.log("不显示对话框，原因:",{inboundType:l.inboundType,isLoadingData:B.value}):(b.value=[],C.value||(c.value=[]))}function W(t){!t||!T.value||(Q.value=!0,V.pms.purchase.contract.request({url:"/getContractListByPoAndSupplierId",method:"GET",params:{po:t,supplier_id:T.value}}).then(e=>{e&&Array.isArray(e)?(b.value=e,m.value=e.map(a=>({id:a.materialId,code:a.material.code,name:a.material.name,model:a.material.model,size:a.material.size,material:a.material.material,unit:a.material.unit,process:a.material.process,coverColor:a.material.coverColor||"",contractId:a.id,expectedQuantity:a.expectedQuantity,receivedQuantity:a.receivedQuantity,purchaseId:a.purchaseId})),ke(e),u.success(`已加载${b.value.length}个物料`)):(b.value=[],c.value=[],m.value=[],u.warning("未找到相关物料")),Q.value=!1}).catch(e=>{u.error("获取物料列表失败"),b.value=[],c.value=[],m.value=[],Q.value=!1}))}function ke(t){c.value=t.map(e=>({quantity:void 0,contractId:e.id,materialId:e.materialId,createTime:"",status:0,remark:"",expectedInbound:e.expectedQuantity||0,receivedQuantity:e.receivedQuantity||0,name:e.material.name,model:e.material.model,size:e.material.size,material:e.material.material,unit:e.material.unit,process:e.material.process,code:e.material.code,coverColor:e.material.coverColor||"",orderId:e.purchaseId}))}async function Qe(t){if(!(!t||!T.value)){Q.value=!0;try{const e=await V.pms.purchase.contract.request({url:"/getContractListByPoAndSupplierId",method:"GET",params:{po:t,supplier_id:T.value}});e&&Array.isArray(e)?(b.value=e,m.value=e.map(a=>({id:a.materialId,code:a.material.code,name:a.material.name,model:a.material.model,size:a.material.size,material:a.material.material,unit:a.material.unit,process:a.material.process,coverColor:a.material.coverColor||"",contractId:a.id,expectedQuantity:a.expectedQuantity,receivedQuantity:a.receivedQuantity,purchaseId:a.purchaseId}))):(b.value=[],m.value=[])}catch{b.value=[],m.value=[]}finally{Q.value=!1}}}async function Te(t){if(!(!t||!T.value)){Q.value=!0;try{const e=await V.pms.purchase.contract.request({url:"/getContractListByPoAndSupplierId",method:"GET",params:{po:t,supplier_id:T.value}});let a=[];e&&Array.isArray(e)?(b.value=e,a=e.map(d=>({id:d.materialId,code:d.material.code,name:d.material.name,model:d.material.model,size:d.material.size,material:d.material.material,unit:d.material.unit,process:d.material.process,coverColor:d.material.coverColor||"",contractId:d.id,expectedQuantity:d.expectedQuantity,receivedQuantity:d.receivedQuantity,purchaseId:d.purchaseId}))):b.value=[],m.value=a,c.value=[];for(let d=0;d<10;d++)U();a.length>0?u.success(`已切换到新PO，加载了${a.length}个可选物料，创建了10行空白行`):u.warning("新PO暂无物料，已创建10行空白行")}catch{b.value=[],m.value=[],c.value=[];for(let a=0;a<10;a++)U();u.error("获取新PO物料失败，已创建10行空白行")}finally{Q.value=!1}}}function Ce(t){if(t===l.inboundType)return;c.value.length>0&&u.warning("切换入库类型将清空当前物料列表和表单信息"),l.inboundType=t;const e=l.supplierId,a=l.id;l.no="",l.po="",l.remark="",l.voucher="",l.orderId="",l.supplierOrderNo="",l.supplierId=e,l.id=a,c.value.length=0,m.value.length=0,b.value.length=0,E.value="",ee.value++,Se(()=>{t===1?u.info("已切换到采购入库模式，请选择PO号"):t===9&&u.info("已切换到补退货入库模式，请选择退货出库单")})}function xe(){j.value=!0}async function Oe(t){const e=t.row||t;if(!e||!e.products||e.products.length===0){u.error("该退货出库单没有物料");return}if(c.value.length>0)try{await de.confirm("当前物料列表中存在已选数据，如果确定，将先清除列表中的数据，是否继续？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"})}catch{return}c.value=[],m.value=[],e.id&&(l.orderId=String(e.id)),m.value=e.products.map(a=>({id:a.materialId,code:a.code,name:a.name,model:a.model,size:a.size,material:a.material,unit:a.unit,process:a.process,coverColor:a.coverColor||"",returnOrderQuantity:a.quantity,restockingQty:a.restockingQty||0,orderId:e.id||0}));try{await de.confirm("是否需要自动填充该退货单的全部物料？","提示",{confirmButtonText:"自动填充",cancelButtonText:"手动输入",type:"warning",showClose:!1}),ze()}catch{for(let d=0;d<10;d++)U()}j.value=!1,u.success(`已加载退货出库单物料，共${e.products.length}个物料`)}function ze(){c.value=m.value.map(t=>({quantity:0,contractId:void 0,materialId:t.id,createTime:"",status:0,remark:"",expectedInbound:void 0,receivedQuantity:void 0,name:t.name,model:t.model,size:t.size,material:t.material,unit:t.unit,process:t.process,coverColor:t.coverColor,orderId:t.orderId,code:t.code,returnOrderQuantity:t.returnOrderQuantity,restockingQty:t.restockingQty}))}return(t,e)=>{const a=h("el-radio"),d=h("el-radio-group"),s=h("el-form-item"),o=h("el-col"),p=h("el-row"),S=h("el-option"),$=h("el-select"),L=h("el-input"),g=h("el-button"),J=h("el-empty"),f=h("el-table-column"),K=h("el-input-number"),P=h("el-tooltip"),Z=h("el-table"),D=h("el-icon"),H=h("el-form"),F=h("el-dialog");return v(),w("div",$e,[y("div",De,[y("h2",null,z(C.value?"编辑送货单":"添加送货单"),1)]),n(H,{"label-width":"120px",class:"delivery-form"},{default:i(()=>[y("div",Fe,[Ge,n(p,{gutter:20},{default:i(()=>[n(o,{span:12},{default:i(()=>[n(s,{label:"入库类型",required:""},{default:i(()=>[n(d,{"model-value":l.inboundType,disabled:C.value,"onUpdate:modelValue":e[0]||(e[0]=r=>Ce(r))},{default:i(()=>[n(a,{value:1,label:"采购入库"}),n(a,{value:9,label:"补退货入库"})]),_:1},8,["model-value","disabled"])]),_:1})]),_:1})]),_:1}),l.inboundType===1?(v(),k(p,{key:0,gutter:20},{default:i(()=>[n(o,{span:12},{default:i(()=>[n(s,{label:"送货单PO号",required:""},{default:i(()=>[n($,{modelValue:l.po,"onUpdate:modelValue":e[1]||(e[1]=r=>l.po=r),filterable:"",placeholder:"请选择送货单PO号",style:{width:"100%"},loading:R.value,clearable:"",onChange:Ie},{default:i(()=>[(v(!0),w(re,null,ie(A.value,r=>(v(),k(S,{key:r.po,label:r.po,value:r.po},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading"]),A.value.length===0?(v(),w("div",Re," PO列表为空，请等待加载或刷新页面 ")):(v(),w("div",je," 共加载了 "+z(A.value.length)+" 个PO号 ",1))]),_:1})]),_:1}),n(o,{span:12},{default:i(()=>[n(s,{label:"送货单号"},{default:i(()=>[n(L,{modelValue:l.supplierOrderNo,"onUpdate:modelValue":e[2]||(e[2]=r=>l.supplierOrderNo=r),placeholder:"送货单号"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})):x("",!0),l.inboundType===9?(v(),k(p,{key:1,gutter:20},{default:i(()=>[n(o,{span:12},{default:i(()=>[n(s,{label:"选择退货出库单",required:""},{default:i(()=>[n(g,{type:"warning",disabled:c.value.length>0,onClick:xe},{default:i(()=>[O(" 选择退货出库单 ")]),_:1},8,["disabled"]),c.value.length===0?(v(),w("div",Je," 请选择退货出库单以加载物料信息 ")):(v(),w("div",Ke," 已加载 "+z(m.value.length)+" 个可选物料 ",1))]),_:1})]),_:1})]),_:1})):x("",!0),n(p,{gutter:20},{default:i(()=>[n(o,{span:12},{default:i(()=>[n(s,{label:"送货单备注"},{default:i(()=>[n(L,{modelValue:l.remark,"onUpdate:modelValue":e[3]||(e[3]=r=>l.remark=r),type:"textarea",placeholder:"请输入送货单备注",rows:2},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),y("div",Ze,[y("div",He,[We,l.po&&!C.value?(v(),k(g,{key:0,type:"primary",loading:Q.value,onClick:e[4]||(e[4]=()=>W(l.po))},{default:i(()=>[O(" 刷新物料列表 ")]),_:1},8,["loading"])):x("",!0),l.po&&C.value?(v(),k(g,{key:1,type:"warning",loading:Q.value,onClick:e[5]||(e[5]=()=>W(l.po))},{default:i(()=>[O(" 重新加载物料（将覆盖当前数据） ")]),_:1},8,["loading"])):x("",!0)]),c.value.length===0?(v(),w("div",Xe,[n(J,{description:C.value?"编辑模式：物料信息加载中...":"请先选择PO号以加载物料信息","image-size":100},null,8,["description"])])):(v(),k(Z,{key:`table-${l.inboundType}-${ee.value}`,data:c.value,border:"",size:"small",class:"compact-table"},{default:i(()=>[n(f,{label:"序号",type:"index",width:"70",align:"center"}),n(f,{label:"物料编号",prop:"materialId","min-width":"150"},{default:i(({row:r})=>[n($,{modelValue:r.materialId,"onUpdate:modelValue":_=>r.materialId=_,filterable:"",placeholder:"请选择物料",size:"small",style:{width:"100%"},onChange:_=>_e(_,r)},{default:i(()=>[(v(!0),w(re,null,ie(m.value,_=>(v(),k(S,{key:_.id,label:`${_.code}/${_.name}`,value:_.id,disabled:fe(_.id,r)},null,8,["label","value","disabled"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),n(f,{label:"数量",prop:"quantity","min-width":"120"},{header:i(()=>[Ye]),default:i(({row:r})=>[n(K,{modelValue:r.quantity,"onUpdate:modelValue":_=>r.quantity=_,min:0,max:ye(r),precision:2,style:{width:"100%"},placeholder:"请输入数量"},null,8,["modelValue","onUpdate:modelValue","max"])]),_:1}),n(f,{label:"备注",prop:"remark","min-width":"200"},{default:i(({row:r})=>[n(L,{modelValue:r.remark,"onUpdate:modelValue":_=>r.remark=_,placeholder:"请输入备注"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),n(f,{label:"合同ID",prop:"contractId","min-width":"50"}),l.inboundType===1?(v(),k(f,{key:0,label:"剩余入库数量",prop:"expectedInbound","min-width":"70"})):x("",!0),l.inboundType===1?(v(),k(f,{key:1,label:"已收数量",prop:"receivedQuantity","min-width":"60"})):x("",!0),l.inboundType===9?(v(),k(f,{key:2,label:"退货数量",prop:"returnOrderQuantity","min-width":"70"})):x("",!0),l.inboundType===9?(v(),k(f,{key:3,label:"已补货数量",prop:"restockingQty","min-width":"80"})):x("",!0),l.inboundType===9?(v(),k(f,{key:4,label:"剩余可补数量","min-width":"90"},{default:i(({row:r})=>[O(z((r.returnOrderQuantity||0)-(r.restockingQty||0)),1)]),_:1})):x("",!0),n(f,{label:"物料名称",prop:"name","min-width":"60"},{default:i(({row:r})=>[n(P,{content:r.name,placement:"top","show-after":500,enterable:!1},{default:i(()=>[y("div",ea,z(r.name),1)]),_:2},1032,["content"])]),_:1}),n(f,{label:"物料型号",prop:"model","min-width":"60"},{default:i(({row:r})=>[n(P,{content:r.model,placement:"top","show-after":500,enterable:!1},{default:i(()=>[y("div",aa,z(r.model),1)]),_:2},1032,["content"])]),_:1}),n(f,{label:"物料尺寸",prop:"size","min-width":"80"},{default:i(({row:r})=>[n(P,{content:r.size,placement:"top","show-after":500,enterable:!1},{default:i(()=>[y("div",ta,z(r.size),1)]),_:2},1032,["content"])]),_:1}),n(f,{label:"物料材质",prop:"material","min-width":"80"},{default:i(({row:r})=>[n(P,{content:r.material,placement:"top","show-after":500,enterable:!1},{default:i(()=>[y("div",la,z(r.material),1)]),_:2},1032,["content"])]),_:1}),n(f,{label:"物料单位",prop:"unit","min-width":"50"}),n(f,{label:"物料工艺",prop:"process","min-width":"80"},{default:i(({row:r})=>[n(P,{content:r.process,placement:"top","show-after":500,enterable:!1},{default:i(()=>[y("div",oa,z(r.process),1)]),_:2},1032,["content"])]),_:1}),n(f,{label:"操作",width:"100",align:"center"},{default:i(({$index:r})=>[n(g,{type:"danger",text:"",onClick:_=>ve(r)},{default:i(()=>[O(" 删除 ")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),c.value.length>0?(v(),w("div",na,[n(g,{type:"primary",class:"add-material-button",onClick:U},{default:i(()=>[n(D,null,{default:i(()=>[n(X(Pe))]),_:1}),O(" 添加物料 ")]),_:1})])):x("",!0)]),y("div",ra,[n(g,{onClick:e[6]||(e[6]=r=>X(M).go(-1))},{default:i(()=>[O(" 取消 ")]),_:1}),n(g,{type:"primary",onClick:ge},{default:i(()=>[O(" 保存 ")]),_:1})])]),_:1}),l.inboundType===1?(v(),k(F,{key:0,modelValue:N.value,"onUpdate:modelValue":e[9]||(e[9]=r=>N.value=r),title:"提示",width:"400px","close-on-click-modal":!1,"close-on-press-escape":!1,"show-close":!1},{default:i(()=>[y("div",ia,[y("div",da,[n(D,{size:"24",color:"#E6A23C"},{default:i(()=>[n(X(Me))]),_:1})]),y("div",ua,z(C.value?"是否需要自动填充该订单的全部物料（将覆盖当前物料列表）":"是否需要自动填充该订单的全部物料"),1),y("div",sa,[n(g,{onClick:be},{default:i(()=>[O(" 取消 ")]),_:1}),n(g,{onClick:e[7]||(e[7]=r=>ae("manual"))},{default:i(()=>[O(" 手动输入 ")]),_:1}),n(g,{type:"primary",onClick:e[8]||(e[8]=r=>ae("auto"))},{default:i(()=>[O(" 自动填充 ")]),_:1})])])]),_:1},8,["modelValue"])):x("",!0),n(F,{modelValue:j.value,"onUpdate:modelValue":e[10]||(e[10]=r=>j.value=r),title:"选择退货出库单",width:"80%","close-on-click-modal":!1},{default:i(()=>[n(Be,{"outbound-type":1,onSelected:Oe})]),_:1},8,["modelValue"])])}}}),ha=Ue(pa,[["__scopeId","data-v-da4368ec"]]);export{ha as default};
