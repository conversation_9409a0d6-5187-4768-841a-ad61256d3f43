import"./index-DkYL1aws.js";import{c as b,b as _,e as V,i as g,f as S,o as k,J as N,h as s,q as $,B as j,w as o,j as u,t as m,m as T,v as q,a0 as D}from"./.pnpm-hVqhwuVC.js";import{a as E}from"./index-C6cm1h61.js";import{_ as J}from"./space-inner.vue_vue_type_style_index_0_lang-Bvb2FfQe.js";import"./hook-CIEgpwJn.js";import"./viewer.vue_vue_type_script_setup_true_name_item-viewer_lang-6n4_YlHr.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";const O={class:"cl-upload-space__wrap"},P=b({name:"cl-upload-space"}),I=b({...P,props:{title:{type:String,default:"文件空间"},text:{type:String,default:"点击上传"},limit:{type:Number,default:9},accept:String,showBtn:{type:Boolean,default:!0}},emits:["confirm"],setup(l,{expose:x,emit:C}){const y=l,h=C,{refs:p,setRefs:B}=E(),a=_(!1),n=_({title:""}),i=V(()=>{var e;return((e=p.inner)==null?void 0:e.selection)||[]});function d(e){a.value=!0,n.value=Object.assign({...y},e),D(()=>{var t;(t=p.inner)==null||t.clear()})}function c(){a.value=!1}function f(e){h("confirm",e||i.value),c()}return x({open:d,close:c}),(e,t)=>{const r=g("el-button"),w=g("cl-dialog");return k(),S("div",O,[N(e.$slots,"default",{},()=>[l.showBtn?(k(),$(r,{key:0,onClick:d},{default:o(()=>[u(m(l.text),1)]),_:1})):j("",!0)]),s(w,{modelValue:a.value,"onUpdate:modelValue":t[1]||(t[1]=v=>a.value=v),title:n.value.title,height:"650px",width:"1070px","custom-class":"cl-upload-space__dialog","close-on-click-modal":!1,"close-on-press-escape":!1,"append-to-body":"","keep-alive":""},{footer:o(()=>[s(r,{onClick:c},{default:o(()=>[u(" 取消 ")]),_:1}),s(r,{disabled:i.value.length===0,type:"success",onClick:t[0]||(t[0]=v=>f())},{default:o(()=>[u(" 选择 "+m(i.value.length)+"/"+m(l.limit),1)]),_:1},8,["disabled"])]),default:o(()=>[s(J,T({ref:q(B)("inner")},n.value,{onConfirm:f}),null,16)]),_:1},8,["modelValue","title"])])}}});export{I as default};
